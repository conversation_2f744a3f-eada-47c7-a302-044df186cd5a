{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-I3VB6NM2.mjs"], "sourcesContent": ["// src/components/common/list-summary/list-summary.tsx\nimport { Tooltip, clx } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ListSummary = ({\n  list,\n  className,\n  variant = \"compact\",\n  inline,\n  n = 2\n}) => {\n  const { t } = useTranslation();\n  const title = t(\"general.plusCountMore\", {\n    count: list.length - n\n  });\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"text-ui-fg-subtle gap-x-1 overflow-hidden\",\n        {\n          \"inline-flex\": inline,\n          flex: !inline,\n          \"txt-compact-small\": variant === \"compact\",\n          \"txt-small\": variant === \"base\"\n        },\n        className\n      ),\n      children: [\n        /* @__PURE__ */ jsx(\"div\", { className: \"flex-1 truncate\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: list.slice(0, n).join(\", \") }) }),\n        list.length > n && /* @__PURE__ */ jsx(\"div\", { className: \"whitespace-nowrap\", children: /* @__PURE__ */ jsx(\n          Tooltip,\n          {\n            content: /* @__PURE__ */ jsx(\"ul\", { children: list.slice(n).map((c) => /* @__PURE__ */ jsx(\"li\", { children: c }, c)) }),\n            children: /* @__PURE__ */ jsx(\"span\", { className: \"cursor-default whitespace-nowrap\", children: title })\n          }\n        ) })\n      ]\n    }\n  );\n};\n\nexport {\n  ListSummary\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAGA,yBAA0B;AAC1B,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA,IAAI;AACN,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,QAAQ,EAAE,yBAAyB;AAAA,IACvC,OAAO,KAAK,SAAS;AAAA,EACvB,CAAC;AACD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,eAAe;AAAA,UACf,MAAM,CAAC;AAAA,UACP,qBAAqB,YAAY;AAAA,UACjC,aAAa,YAAY;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,YACQ,wBAAI,OAAO,EAAE,WAAW,mBAAmB,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,QACpK,KAAK,SAAS,SAAqB,wBAAI,OAAO,EAAE,WAAW,qBAAqB,cAA0B;AAAA,UACxG;AAAA,UACA;AAAA,YACE,aAAyB,wBAAI,MAAM,EAAE,UAAU,KAAK,MAAM,CAAC,EAAE,IAAI,CAAC,UAAsB,wBAAI,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AAAA,YACxH,cAA0B,wBAAI,QAAQ,EAAE,WAAW,oCAAoC,UAAU,MAAM,CAAC;AAAA,UAC1G;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": []}