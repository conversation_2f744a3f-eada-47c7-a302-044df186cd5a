import {
  VisuallyH<PERSON>den
} from "./chunk-TAVTGMIK.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-R325LWWE.js";
import "./chunk-WNW4SNUS.js";
import {
  DataTable
} from "./chunk-EPUS4TBC.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-EUY4KBIF.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useApiKey,
  useBatchAddSalesChannelsToApiKey
} from "./chunk-RX237AWS.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Hint,
  createDataTableColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/api-key-management-sales-channels-OMIWIBUF.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddSalesChannelsToApiKeySchema = objectType({
  sales_channel_ids: arrayType(stringType()).min(1)
});
var PAGE_SIZE = 50;
var PREFIX = "sc_add";
var ApiKeySalesChannelsForm = ({
  apiKey,
  preSelected = []
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      sales_channel_ids: []
    },
    resolver: t(AddSalesChannelsToApiKeySchema)
  });
  const { setValue } = form;
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { mutateAsync, isPending: isMutating } = useBatchAddSalesChannelsToApiKey(apiKey);
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const columns = useColumns();
  const filters = useSalesChannelTableFilters();
  const emptyState = useSalesChannelTableEmptyState();
  const { sales_channels, count, isPending } = useSalesChannels(
    { ...searchParams },
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (selection) => {
    const ids = Object.keys(selection);
    setValue("sales_channel_ids", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(selection);
  };
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(values.sales_channel_ids, {
      onSuccess: () => {
        toast.success(
          t2("apiKeyManagement.salesChannels.successToast", {
            count: values.sales_channel_ids.length
          })
        );
        handleSuccess();
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime.jsxs)(RouteFocusModal.Header, { children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("apiKeyManagement.salesChannels.title") }) }),
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("apiKeyManagement.salesChannels.description") }) }),
      (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end gap-x-2", children: form.formState.errors.sales_channel_ids && (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: form.formState.errors.sales_channel_ids.message }) })
    ] }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-auto", children: (0, import_jsx_runtime.jsx)(
      DataTable,
      {
        data: sales_channels,
        columns,
        filters,
        getRowId: (row) => row.id,
        rowCount: count,
        layout: "fill",
        emptyState,
        isLoading: isPending,
        rowSelection: {
          state: rowSelection,
          onRowSelectionChange: updater,
          enableRowSelection: (row) => !preSelected.includes(row.id)
        },
        prefix: PREFIX,
        pageSize: PAGE_SIZE,
        autoFocusSearch: true
      }
    ) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isMutating, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t: t2 } = useTranslation();
  const base = useSalesChannelTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.select({
        cell: ({ row }) => {
          const isPreSelected = !row.getCanSelect();
          const isSelected = row.getIsSelected() || isPreSelected;
          return (0, import_jsx_runtime.jsx)(
            ConditionalTooltip,
            {
              content: t2("apiKeyManagement.salesChannels.alreadyAddedTooltip"),
              showTooltip: isPreSelected,
              children: (0, import_jsx_runtime.jsx)("div", { children: (0, import_jsx_runtime.jsx)(
                Checkbox,
                {
                  checked: isSelected,
                  disabled: isPreSelected,
                  onCheckedChange: (value) => row.toggleSelected(!!value),
                  onClick: (e) => {
                    e.stopPropagation();
                  }
                }
              ) })
            }
          );
        }
      }),
      ...base
    ],
    [t2, base]
  );
};
var ApiKeyManagementAddSalesChannels = () => {
  var _a;
  const { id } = useParams();
  const { api_key, isLoading, isError, error } = useApiKey(id);
  const preSelected = (_a = api_key == null ? void 0 : api_key.sales_channels) == null ? void 0 : _a.map((sc) => sc.id);
  const ready = !isLoading && api_key;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime2.jsx)(ApiKeySalesChannelsForm, { apiKey: id, preSelected }) });
};
export {
  ApiKeyManagementAddSalesChannels as Component
};
//# sourceMappingURL=api-key-management-sales-channels-OMIWIBUF-2Q42R2OZ.js.map
