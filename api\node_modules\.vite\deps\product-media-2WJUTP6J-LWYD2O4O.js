import {
  EditProductMediaSchema,
  UploadMediaFormItem
} from "./chunk-E4NIVO46.js";
import {
  CSS,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  SortableContext,
  arrayMove,
  defaultDropAnimationSideEffects,
  rectSortingStrategy,
  sortableKeyboardCoordinates,
  useSensor,
  useSensors,
  useSortable
} from "./chunk-7ALMK6OQ.js";
import "./chunk-7LOZU53L.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-ZE42WU6O.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-5NX546NL.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-MPXR7HT5.js";
import {
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useProduct,
  useUpdateProduct
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLocation,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowDownTray,
  Button,
  Checkbox,
  CommandBar,
  IconButton,
  Text,
  ThumbnailBadge,
  Tooltip,
  Trash,
  TriangleLeftMini,
  TriangleRightMini,
  clx,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-media-2WJUTP6J.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var EditProductMediaForm = ({ product }) => {
  const [selection, setSelection] = (0, import_react.useState)({});
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      media: getDefaultValues(product.images, product.thumbnail)
    },
    resolver: t(EditProductMediaSchema)
  });
  const { fields, append, remove, update } = useFieldArray({
    name: "media",
    control: form.control,
    keyName: "field_id"
  });
  const [activeId, setActiveId] = (0, import_react.useState)(null);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };
  const handleDragEnd = (event) => {
    setActiveId(null);
    const { active, over } = event;
    if (active.id !== (over == null ? void 0 : over.id)) {
      const oldIndex = fields.findIndex((item) => item.field_id === active.id);
      const newIndex = fields.findIndex((item) => item.field_id === (over == null ? void 0 : over.id));
      form.setValue("media", arrayMove(fields, oldIndex, newIndex), {
        shouldDirty: true,
        shouldTouch: true
      });
    }
  };
  const handleDragCancel = () => {
    setActiveId(null);
  };
  const { mutateAsync, isPending } = useUpdateProduct(product.id);
  const handleSubmit = form.handleSubmit(async ({ media }) => {
    var _a;
    const filesToUpload = media.map((m, i) => ({ file: m.file, index: i })).filter((m) => !!m.file);
    let uploaded = [];
    if (filesToUpload.length) {
      const { files: uploads } = await sdk.admin.upload.create({ files: filesToUpload.map((m) => m.file) }).catch(() => {
        form.setError("media", {
          type: "invalid_file",
          message: t2("products.media.failedToUpload")
        });
        return { files: [] };
      });
      uploaded = uploads;
    }
    const withUpdatedUrls = media.map((entry, i) => {
      var _a2;
      const toUploadIndex = filesToUpload.findIndex((m) => m.index === i);
      if (toUploadIndex > -1) {
        return { ...entry, url: (_a2 = uploaded[toUploadIndex]) == null ? void 0 : _a2.url };
      }
      return entry;
    });
    const thumbnail = (_a = withUpdatedUrls.find((m) => m.isThumbnail)) == null ? void 0 : _a.url;
    await mutateAsync(
      {
        images: withUpdatedUrls.map((file) => ({ url: file.url, id: file.id })),
        thumbnail: thumbnail || null
      },
      {
        onSuccess: () => {
          toast.success(t2("products.media.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const handleCheckedChange = (0, import_react.useCallback)(
    (id) => {
      return (val) => {
        if (!val) {
          const { [id]: _, ...rest } = selection;
          setSelection(rest);
        } else {
          setSelection((prev) => ({ ...prev, [id]: true }));
        }
      };
    },
    [selection]
  );
  const handleDelete = () => {
    const ids = Object.keys(selection);
    const indices = ids.map((id) => fields.findIndex((m) => m.id === id));
    remove(indices);
    setSelection({});
  };
  const handlePromoteToThumbnail = () => {
    const ids = Object.keys(selection);
    if (!ids.length) {
      return;
    }
    const currentThumbnailIndex = fields.findIndex((m) => m.isThumbnail);
    if (currentThumbnailIndex > -1) {
      update(currentThumbnailIndex, {
        ...fields[currentThumbnailIndex],
        isThumbnail: false
      });
    }
    const index = fields.findIndex((m) => m.id === ids[0]);
    update(index, {
      ...fields[index],
      isThumbnail: true
    });
    setSelection({});
  };
  const selectionCount = Object.keys(selection).length;
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { blockSearchParams: true, form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex size-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end gap-x-2", children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: { pathname: ".", search: void 0 }, children: t2("products.media.galleryLabel") }) }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-col overflow-hidden", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full flex-col-reverse lg:grid lg:grid-cols-[1fr_560px]", children: [
          (0, import_jsx_runtime.jsx)(
            DndContext,
            {
              sensors,
              onDragEnd: handleDragEnd,
              onDragStart: handleDragStart,
              onDragCancel: handleDragCancel,
              children: (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-subtle size-full overflow-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid h-fit auto-rows-auto grid-cols-4 gap-6 p-6", children: [
                (0, import_jsx_runtime.jsx)(
                  SortableContext,
                  {
                    items: fields.map((m) => m.field_id),
                    strategy: rectSortingStrategy,
                    children: fields.map((m) => {
                      return (0, import_jsx_runtime.jsx)(
                        MediaGridItem,
                        {
                          onCheckedChange: handleCheckedChange(m.id),
                          checked: !!selection[m.id],
                          media: m
                        },
                        m.field_id
                      );
                    })
                  }
                ),
                (0, import_jsx_runtime.jsx)(DragOverlay, { dropAnimation: dropAnimationConfig, children: activeId ? (0, import_jsx_runtime.jsx)(
                  MediaGridItemOverlay,
                  {
                    media: fields.find((m) => m.field_id === activeId),
                    checked: !!selection[fields.find((m) => m.field_id === activeId).id]
                  }
                ) : null })
              ] }) })
            }
          ),
          (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-base overflow-auto border-b px-6 py-4 lg:border-b-0 lg:border-l", children: (0, import_jsx_runtime.jsx)(UploadMediaFormItem, { form, append }) })
        ] }) }),
        (0, import_jsx_runtime.jsx)(CommandBar, { open: !!selectionCount, children: (0, import_jsx_runtime.jsxs)(CommandBar.Bar, { children: [
          (0, import_jsx_runtime.jsx)(CommandBar.Value, { children: t2("general.countSelected", {
            count: selectionCount
          }) }),
          (0, import_jsx_runtime.jsx)(CommandBar.Seperator, {}),
          selectionCount === 1 && (0, import_jsx_runtime.jsxs)(import_react.Fragment, { children: [
            (0, import_jsx_runtime.jsx)(
              CommandBar.Command,
              {
                action: handlePromoteToThumbnail,
                label: t2("products.media.makeThumbnail"),
                shortcut: "t"
              }
            ),
            (0, import_jsx_runtime.jsx)(CommandBar.Seperator, {})
          ] }),
          (0, import_jsx_runtime.jsx)(
            CommandBar.Command,
            {
              action: handleDelete,
              label: t2("actions.delete"),
              shortcut: "d"
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var getDefaultValues = (images, thumbnail) => {
  const media = (images == null ? void 0 : images.map((image) => ({
    id: image.id,
    url: image.url,
    isThumbnail: image.url === thumbnail,
    file: null
  }))) || [];
  if (thumbnail && !media.some((mediaItem) => mediaItem.url === thumbnail)) {
    const id = Math.random().toString(36).substring(7);
    media.unshift({
      id,
      url: thumbnail,
      isThumbnail: true,
      file: null
    });
  }
  return media;
};
var dropAnimationConfig = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4"
      }
    }
  })
};
var MediaGridItem = ({
  media,
  checked,
  onCheckedChange
}) => {
  const { t: t2 } = useTranslation();
  const handleToggle = (0, import_react.useCallback)(
    (value) => {
      onCheckedChange(value);
    },
    [onCheckedChange]
  );
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: media.field_id });
  const style = {
    opacity: isDragging ? 0.4 : void 0,
    transform: CSS.Transform.toString(transform),
    transition
  };
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "shadow-elevation-card-rest hover:shadow-elevation-card-hover focus-visible:shadow-borders-focus bg-ui-bg-subtle-hover group relative aspect-square h-auto max-w-full overflow-hidden rounded-lg outline-none"
      ),
      style,
      ref: setNodeRef,
      children: [
        media.isThumbnail && (0, import_jsx_runtime.jsx)("div", { className: "absolute left-2 top-2", children: (0, import_jsx_runtime.jsx)(Tooltip, { content: t2("products.media.thumbnailTooltip"), children: (0, import_jsx_runtime.jsx)(ThumbnailBadge, {}) }) }),
        (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: clx("absolute inset-0 cursor-grab touch-none outline-none", {
              "cursor-grabbing": isDragging
            }),
            ref: setActivatorNodeRef,
            ...attributes,
            ...listeners
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: clx("transition-fg absolute right-2 top-2 opacity-0", {
              "group-focus-within:opacity-100 group-hover:opacity-100 group-focus:opacity-100": !isDragging && !checked,
              "opacity-100": checked
            }),
            children: (0, import_jsx_runtime.jsx)(
              Checkbox,
              {
                onClick: (e) => {
                  e.stopPropagation();
                },
                checked,
                onCheckedChange: handleToggle
              }
            )
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "img",
          {
            src: media.url,
            alt: "",
            className: "size-full object-cover object-center"
          }
        )
      ]
    }
  );
};
var MediaGridItemOverlay = ({
  media,
  checked
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "shadow-elevation-card-rest hover:shadow-elevation-card-hover focus-visible:shadow-borders-focus bg-ui-bg-subtle-hover group relative aspect-square h-auto max-w-full cursor-grabbing overflow-hidden rounded-lg outline-none", children: [
    media.isThumbnail && (0, import_jsx_runtime.jsx)("div", { className: "absolute left-2 top-2", children: (0, import_jsx_runtime.jsx)(ThumbnailBadge, {}) }),
    (0, import_jsx_runtime.jsx)(
      "div",
      {
        className: clx("transition-fg absolute right-2 top-2 opacity-0", {
          "opacity-100": checked
        }),
        children: (0, import_jsx_runtime.jsx)(Checkbox, { checked })
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "img",
      {
        src: media.url,
        alt: "",
        className: "size-full object-cover object-center"
      }
    )
  ] });
};
var ProductMediaGallery = ({ product }) => {
  const { state } = useLocation();
  const [curr, setCurr] = (0, import_react2.useState)((state == null ? void 0 : state.curr) || 0);
  const { t: t2 } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync, isPending } = useUpdateProduct(product.id);
  const media = getMedia(product.images, product.thumbnail);
  const next = (0, import_react2.useCallback)(() => {
    if (isPending) {
      return;
    }
    setCurr((prev2) => (prev2 + 1) % media.length);
  }, [media, isPending]);
  const prev = (0, import_react2.useCallback)(() => {
    if (isPending) {
      return;
    }
    setCurr((prev2) => (prev2 - 1 + media.length) % media.length);
  }, [media, isPending]);
  const goTo = (0, import_react2.useCallback)(
    (index) => {
      if (isPending) {
        return;
      }
      setCurr(index);
    },
    [isPending]
  );
  const handleDownloadCurrent = () => {
    if (isPending) {
      return;
    }
    const a = document.createElement("a");
    a.href = media[curr].url;
    a.download = "image";
    a.target = "_blank";
    a.click();
  };
  const handleDeleteCurrent = async () => {
    var _a;
    const current = media[curr];
    const res = await prompt({
      title: t2("general.areYouSure"),
      description: current.isThumbnail ? t2("products.media.deleteWarningWithThumbnail", { count: 1 }) : t2("products.media.deleteWarning", { count: 1 }),
      confirmText: t2("actions.delete"),
      cancelText: t2("actions.cancel")
    });
    if (!res) {
      return;
    }
    const mediaToKeep = ((_a = product.images) == null ? void 0 : _a.filter((i) => i.id !== current.id).map((i) => ({ url: i.url }))) || [];
    if (curr === media.length - 1) {
      setCurr((prev2) => prev2 - 1);
    }
    await mutateAsync({
      images: mediaToKeep,
      thumbnail: current.isThumbnail ? "" : void 0
    });
  };
  (0, import_react2.useEffect)(() => {
    const handleKeyDown = (e) => {
      if (e.key === "ArrowRight") {
        next();
      } else if (e.key === "ArrowLeft") {
        prev();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [next, prev]);
  const noMedia = !media.length;
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex size-full flex-col overflow-hidden", children: [
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime2.jsxs)(
        IconButton,
        {
          size: "small",
          type: "button",
          onClick: handleDeleteCurrent,
          disabled: noMedia,
          children: [
            (0, import_jsx_runtime2.jsx)(Trash, {}),
            (0, import_jsx_runtime2.jsx)("span", { className: "sr-only", children: t2("products.media.deleteImageLabel") })
          ]
        }
      ),
      (0, import_jsx_runtime2.jsxs)(
        IconButton,
        {
          size: "small",
          type: "button",
          onClick: handleDownloadCurrent,
          disabled: noMedia,
          children: [
            (0, import_jsx_runtime2.jsx)(ArrowDownTray, {}),
            (0, import_jsx_runtime2.jsx)("span", { className: "sr-only", children: t2("products.media.downloadImageLabel") })
          ]
        }
      ),
      (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: { pathname: ".", search: "view=edit" }, children: t2("actions.edit") }) })
    ] }) }),
    (0, import_jsx_runtime2.jsxs)(RouteFocusModal.Body, { className: "flex flex-col overflow-hidden", children: [
      (0, import_jsx_runtime2.jsx)(Canvas, { curr, media }),
      (0, import_jsx_runtime2.jsx)(
        Preview,
        {
          curr,
          media,
          prev,
          next,
          goTo
        }
      )
    ] })
  ] });
};
var Canvas = ({ media, curr }) => {
  const { t: t2 } = useTranslation();
  if (media.length === 0) {
    return (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-subtle flex size-full flex-col items-center justify-center gap-y-4 pb-8 pt-6", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col items-center", children: [
        (0, import_jsx_runtime2.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            weight: "plus",
            className: "text-ui-fg-subtle",
            children: t2("products.media.emptyState.header")
          }
        ),
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-muted", children: t2("products.media.emptyState.description") })
      ] }),
      (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "?view=edit", children: t2("products.media.emptyState.action") }) })
    ] });
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-subtle relative size-full overflow-hidden", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center justify-center p-6", children: (0, import_jsx_runtime2.jsxs)("div", { className: "relative inline-block max-h-full max-w-full", children: [
    media[curr].isThumbnail && (0, import_jsx_runtime2.jsx)("div", { className: "absolute left-2 top-2", children: (0, import_jsx_runtime2.jsx)(Tooltip, { content: t2("products.media.thumbnailTooltip"), children: (0, import_jsx_runtime2.jsx)(ThumbnailBadge, {}) }) }),
    (0, import_jsx_runtime2.jsx)(
      "img",
      {
        src: media[curr].url,
        alt: "",
        className: "object-fit shadow-elevation-card-rest max-h-[calc(100vh-200px)] w-auto rounded-xl object-contain"
      }
    )
  ] }) }) });
};
var MAX_VISIBLE_ITEMS = 8;
var Preview = ({
  media,
  curr,
  prev,
  next,
  goTo
}) => {
  if (!media.length) {
    return null;
  }
  const getVisibleItems = (media2, index) => {
    if (media2.length <= MAX_VISIBLE_ITEMS) {
      return media2;
    }
    const half = Math.floor(MAX_VISIBLE_ITEMS / 2);
    const start = (index - half + media2.length) % media2.length;
    const end = (start + MAX_VISIBLE_ITEMS) % media2.length;
    if (end < start) {
      return [...media2.slice(start), ...media2.slice(0, end)];
    } else {
      return media2.slice(start, end);
    }
  };
  const visibleItems = getVisibleItems(media, curr);
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex shrink-0 items-center justify-center gap-x-2 border-t p-3", children: [
    (0, import_jsx_runtime2.jsx)(
      IconButton,
      {
        size: "small",
        variant: "transparent",
        className: "text-ui-fg-muted",
        type: "button",
        onClick: prev,
        children: (0, import_jsx_runtime2.jsx)(TriangleLeftMini, {})
      }
    ),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center gap-x-2", children: visibleItems.map((item) => {
      const isCurrentImage = item.id === media[curr].id;
      const originalIndex = media.findIndex((i) => i.id === item.id);
      return (0, import_jsx_runtime2.jsx)(
        "button",
        {
          type: "button",
          onClick: () => goTo(originalIndex),
          className: clx(
            "transition-fg size-7 overflow-hidden rounded-[4px] outline-none",
            {
              "shadow-borders-focus": isCurrentImage
            }
          ),
          children: (0, import_jsx_runtime2.jsx)("img", { src: item.url, alt: "", className: "size-full object-cover" })
        },
        item.id
      );
    }) }),
    (0, import_jsx_runtime2.jsx)(
      IconButton,
      {
        size: "small",
        variant: "transparent",
        className: "text-ui-fg-muted",
        type: "button",
        onClick: next,
        children: (0, import_jsx_runtime2.jsx)(TriangleRightMini, {})
      }
    )
  ] });
};
var getMedia = (images, thumbnail) => {
  const media = (images == null ? void 0 : images.map((image) => ({
    id: image.id,
    url: image.url,
    isThumbnail: image.url === thumbnail
  }))) || [];
  if (thumbnail && !media.some((mediaItem) => mediaItem.isThumbnail)) {
    media.unshift({
      id: "thumbnail_only",
      url: thumbnail,
      isThumbnail: true
    });
  }
  return media;
};
var ProductMediaViewContext = (0, import_react3.createContext)(null);
var getView = (searchParams) => {
  const view = searchParams.get("view");
  if (view === "edit") {
    return "edit";
  }
  return "gallery";
};
var ProductMediaView = ({ product }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const view = getView(searchParams);
  const handleGoToView = (view2) => {
    return () => {
      setSearchParams({ view: view2 });
    };
  };
  return (0, import_jsx_runtime3.jsx)(
    ProductMediaViewContext.Provider,
    {
      value: {
        goToGallery: handleGoToView(
          "gallery"
          /* GALLERY */
        ),
        goToEdit: handleGoToView(
          "edit"
          /* EDIT */
        )
      },
      children: renderView(view, product)
    }
  );
};
var renderView = (view, product) => {
  switch (view) {
    case "gallery":
      return (0, import_jsx_runtime3.jsx)(ProductMediaGallery, { product });
    case "edit":
      return (0, import_jsx_runtime3.jsx)(EditProductMediaForm, { product });
  }
};
var ProductMedia = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { product, isLoading, isError, error } = useProduct(id);
  const ready = !isLoading && product;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(RouteFocusModal, { children: [
    (0, import_jsx_runtime4.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime4.jsx)("span", { className: "sr-only", children: t2("products.media.label") }) }),
    (0, import_jsx_runtime4.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime4.jsx)("span", { className: "sr-only", children: t2("products.media.editHint") }) }),
    ready && (0, import_jsx_runtime4.jsx)(ProductMediaView, { product })
  ] });
};
export {
  ProductMedia as Component
};
//# sourceMappingURL=product-media-2WJUTP6J-LWYD2O4O.js.map
