{"version": 3, "sources": ["../../@medusajs/dashboard/dist/edit-reservation-GEJHV6OI.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  useReservationItem,\n  useUpdateReservationItem\n} from \"./chunk-FVC7M755.mjs\";\nimport {\n  useInventoryItem\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/reservations/reservation-detail/components/edit-reservation/edit-reservation-modal.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/reservations/reservation-detail/components/edit-reservation/components/edit-reservation-form.tsx\nimport { Button, Input, Select, Text, Textarea, toast } from \"@medusajs/ui\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditReservationSchema = z.object({\n  location_id: z.string(),\n  description: z.string().optional(),\n  quantity: z.number().min(1)\n});\nvar AttributeGridRow = ({\n  title,\n  value\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 divide-x\", children: [\n    /* @__PURE__ */ jsx(Text, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: title }),\n    /* @__PURE__ */ jsx(Text, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: value })\n  ] });\n};\nvar getDefaultValues = (reservation) => {\n  return {\n    quantity: reservation.quantity,\n    location_id: reservation.location_id,\n    description: reservation.description ?? void 0\n  };\n};\nvar EditReservationForm = ({\n  reservation,\n  item,\n  locations\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: getDefaultValues(reservation),\n    resolver: zodResolver(EditReservationSchema)\n  });\n  const { mutateAsync } = useUpdateReservationItem(reservation.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    mutateAsync(values, {\n      onSuccess: () => {\n        toast.success(t(\"inventory.reservation.updateSuccessToast\"));\n        handleSuccess();\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  });\n  const reservedQuantity = form.watch(\"quantity\");\n  const locationId = form.watch(\"location_id\");\n  const level = item.location_levels.find(\n    (level2) => level2.location_id === locationId\n  );\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-8 overflow-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"location_id\",\n              render: ({ field: { onChange, value, ref, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"inventory.reservation.location\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                    Select,\n                    {\n                      value,\n                      onValueChange: (v) => {\n                        onChange(v);\n                      },\n                      ...field,\n                      children: [\n                        /* @__PURE__ */ jsx(Select.Trigger, { ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                        /* @__PURE__ */ jsx(Select.Content, { children: (locations || []).map((r) => /* @__PURE__ */ jsx(Select.Item, { value: r.id, children: r.name }, r.id)) })\n                      ]\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-4 divide-y rounded-lg border\", children: [\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"fields.title\"),\n                value: item.title ?? item.sku\n              }\n            ),\n            /* @__PURE__ */ jsx(AttributeGridRow, { title: t(\"fields.sku\"), value: item.sku }),\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"fields.inStock\"),\n                value: level.stocked_quantity\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"inventory.available\"),\n                value: level.stocked_quantity - (level.reserved_quantity - reservation.quantity) - reservedQuantity\n              }\n            )\n          ] }),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"quantity\",\n              render: ({ field: { onChange, value, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"inventory.reservation.reservedAmount\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Input,\n                    {\n                      type: \"number\",\n                      min: 0,\n                      max: (level.available_quantity || 0) + (reservation.quantity || 0),\n                      value: value || \"\",\n                      onChange: (e) => {\n                        const value2 = e.target.value;\n                        if (value2 === \"\") {\n                          onChange(null);\n                        } else {\n                          onChange(parseFloat(value2));\n                        }\n                      },\n                      ...field\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"description\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.description\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading: false, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/reservations/reservation-detail/components/edit-reservation/edit-reservation-modal.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ReservationEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { reservation, isPending, isError, error } = useReservationItem(id);\n  const { inventory_item: inventoryItem } = useInventoryItem(\n    reservation?.inventory_item_id,\n    {\n      enabled: !!reservation\n    }\n  );\n  const { stock_locations } = useStockLocations(\n    {\n      id: inventoryItem?.location_levels?.map(\n        (l) => l.location_id\n      )\n    },\n    {\n      enabled: !!inventoryItem?.location_levels\n    }\n  );\n  const ready = !isPending && reservation && inventoryItem && stock_locations;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"inventory.reservation.editItemDetails\") }) }),\n    ready && /* @__PURE__ */ jsx2(\n      EditReservationForm,\n      {\n        locations: stock_locations,\n        reservation,\n        item: inventoryItem\n      }\n    )\n  ] });\n};\nexport {\n  ReservationEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,yBAA0B;AAoK1B,IAAAA,sBAA2C;AAnK3C,IAAI,wBAAwB,EAAE,OAAO;AAAA,EACnC,aAAa,EAAE,OAAO;AAAA,EACtB,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC;AAC5B,CAAC;AACD,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QACrE,wBAAI,MAAM,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,MAAM,CAAC;AAAA,QAC1F,wBAAI,MAAM,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,MAAM,CAAC;AAAA,EAC5G,EAAE,CAAC;AACL;AACA,IAAI,mBAAmB,CAAC,gBAAgB;AACtC,SAAO;AAAA,IACL,UAAU,YAAY;AAAA,IACtB,aAAa,YAAY;AAAA,IACzB,aAAa,YAAY,eAAe;AAAA,EAC1C;AACF;AACA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,iBAAiB,WAAW;AAAA,IAC3C,UAAU,EAAY,qBAAqB;AAAA,EAC7C,CAAC;AACD,QAAM,EAAE,YAAY,IAAI,yBAAyB,YAAY,EAAE;AAC/D,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,gBAAY,QAAQ;AAAA,MAClB,WAAW,MAAM;AACf,cAAM,QAAQA,GAAE,0CAA0C,CAAC;AAC3D,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,QAAM,mBAAmB,KAAK,MAAM,UAAU;AAC9C,QAAM,aAAa,KAAK,MAAM,aAAa;AAC3C,QAAM,QAAQ,KAAK,gBAAgB;AAAA,IACjC,CAAC,WAAW,OAAO,gBAAgB;AAAA,EACrC;AACA,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,KAAK,GAAG,MAAM,EAAE,MAAM;AACzD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gCAAgC,EAAE,CAAC;AAAA,sBACjE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA,eAAe,CAAC,MAAM;AACpB,iCAAS,CAAC;AAAA,sBACZ;AAAA,sBACA,GAAG;AAAA,sBACH,UAAU;AAAA,4BACQ,wBAAI,OAAO,SAAS,EAAE,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,4BAC5E,wBAAI,OAAO,SAAS,EAAE,WAAW,aAAa,CAAC,GAAG,IAAI,CAAC,UAAsB,wBAAI,OAAO,MAAM,EAAE,OAAO,EAAE,IAAI,UAAU,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC;AAAA,sBAC3J;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB,yBAAK,OAAO,EAAE,WAAW,4FAA4F,UAAU;AAAA,gBAC7H;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,cAAc;AAAA,gBACvB,OAAO,KAAK,SAAS,KAAK;AAAA,cAC5B;AAAA,YACF;AAAA,gBACgB,wBAAI,kBAAkB,EAAE,OAAOA,GAAE,YAAY,GAAG,OAAO,KAAK,IAAI,CAAC;AAAA,gBACjE;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,gBAAgB;AAAA,gBACzB,OAAO,MAAM;AAAA,cACf;AAAA,YACF;AAAA,gBACgB;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,qBAAqB;AAAA,gBAC9B,OAAO,MAAM,oBAAoB,MAAM,oBAAoB,YAAY,YAAY;AAAA,cACrF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,sCAAsC,EAAE,CAAC;AAAA,sBACvE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,KAAK;AAAA,sBACL,MAAM,MAAM,sBAAsB,MAAM,YAAY,YAAY;AAAA,sBAChE,OAAO,SAAS;AAAA,sBAChB,UAAU,CAAC,MAAM;AACf,8BAAM,SAAS,EAAE,OAAO;AACxB,4BAAI,WAAW,IAAI;AACjB,mCAAS,IAAI;AAAA,wBACf,OAAO;AACL,mCAAS,WAAW,MAAM,CAAC;AAAA,wBAC7B;AAAA,sBACF;AAAA,sBACA,GAAG;AAAA,oBACL;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,oBAAoB,EAAE,CAAC;AAAA,sBACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,OAAO,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAC9G,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AA1M5B;AA2ME,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,aAAa,WAAW,SAAS,MAAM,IAAI,mBAAmB,EAAE;AACxE,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAAA,IACxC,2CAAa;AAAA,IACb;AAAA,MACE,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,EACF;AACA,QAAM,EAAE,gBAAgB,IAAI;AAAA,IAC1B;AAAA,MACE,KAAI,oDAAe,oBAAf,mBAAgC;AAAA,QAClC,CAAC,MAAM,EAAE;AAAA;AAAA,IAEb;AAAA,IACA;AAAA,MACE,SAAS,CAAC,EAAC,+CAAe;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,eAAe,iBAAiB;AAC5D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,uCAAuC,EAAE,CAAC,EAAE,CAAC;AAAA,IAC9I,aAAyB,oBAAAE;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}