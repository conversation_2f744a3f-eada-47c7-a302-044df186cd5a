{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PNU5HPGY.mjs"], "sourcesContent": ["import {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/sales-channels.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar SALES_CHANNELS_QUERY_KEY = \"sales-channels\";\nvar salesChannelsQueryKeys = queryKeysFactory(SALES_CHANNELS_QUERY_KEY);\nvar useSalesChannel = (id, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: salesChannelsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.salesChannel.retrieve(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useSalesChannels = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.salesChannel.list(query),\n    queryKey: salesChannelsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateSalesChannel = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.salesChannel.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateSalesChannel = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.salesChannel.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteSalesChannel = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.salesChannel.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteSalesChannelLazy = (options) => {\n  return useMutation({\n    mutationFn: (id) => sdk.admin.salesChannel.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.detail(variables)\n      });\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useSalesChannelRemoveProducts = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.salesChannel.batchProducts(id, { remove: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.detail(id)\n      });\n      for (const product of variables || []) {\n        queryClient.invalidateQueries({\n          queryKey: productsQueryKeys.detail(product)\n        });\n      }\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useSalesChannelAddProducts = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.salesChannel.batchProducts(id, { add: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.detail(id)\n      });\n      for (const product of variables || []) {\n        queryClient.invalidateQueries({\n          queryKey: productsQueryKeys.detail(product)\n        });\n      }\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  salesChannelsQueryKeys,\n  useSalesChannel,\n  useSalesChannels,\n  useCreateSalesChannel,\n  useUpdateSalesChannel,\n  useDeleteSalesChannel,\n  useDeleteSalesChannelLazy,\n  useSalesChannelRemoveProducts,\n  useSalesChannelAddProducts\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,2BAA2B;AAC/B,IAAI,yBAAyB,iBAAiB,wBAAwB;AACtE,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,uBAAuB,OAAO,EAAE;AAAA,IAC1C,SAAS,YAAY,IAAI,MAAM,aAAa,SAAS,EAAE;AAAA,IACvD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,mBAAmB,CAAC,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,aAAa,KAAK,KAAK;AAAA,IAChD,UAAU,uBAAuB,KAAK,KAAK;AAAA,IAC3C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,wBAAwB,CAAC,YAAY;AACvC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,aAAa,OAAO,OAAO;AAAA,IAC9D,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,wBAAwB,CAAC,IAAI,YAAY;AAC3C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,aAAa,OAAO,IAAI,OAAO;AAAA,IAClE,WAAW,CAAC,MAAM,WAAW,YAAY;AAnD7C;AAoDM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,EAAE;AAAA,MAC5C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,wBAAwB,CAAC,IAAI,YAAY;AAC3C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,aAAa,OAAO,EAAE;AAAA,IAClD,WAAW,CAAC,MAAM,WAAW,YAAY;AAlE7C;AAmEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,EAAE;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB;AAAA,MAC9B,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,4BAA4B,CAAC,YAAY;AAC3C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,OAAO,IAAI,MAAM,aAAa,OAAO,EAAE;AAAA,IACpD,WAAW,CAAC,MAAM,WAAW,YAAY;AApF7C;AAqFM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,SAAS;AAAA,MACnD,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB;AAAA,MAC9B,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,gCAAgC,CAAC,IAAI,YAAY;AACnD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,aAAa,cAAc,IAAI,EAAE,QAAQ,QAAQ,CAAC;AAAA,IACrF,WAAW,CAAC,MAAM,WAAW,YAAY;AAtG7C;AAuGM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,EAAE;AAAA,MAC5C,CAAC;AACD,iBAAW,WAAW,aAAa,CAAC,GAAG;AACrC,oBAAY,kBAAkB;AAAA,UAC5B,UAAU,kBAAkB,OAAO,OAAO;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,MAAM;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,6BAA6B,CAAC,IAAI,YAAY;AAChD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,aAAa,cAAc,IAAI,EAAE,KAAK,QAAQ,CAAC;AAAA,IAClF,WAAW,CAAC,MAAM,WAAW,YAAY;AA7H7C;AA8HM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,EAAE;AAAA,MAC5C,CAAC;AACD,iBAAW,WAAW,aAAa,CAAC,GAAG;AACrC,oBAAY,kBAAkB;AAAA,UAC5B,UAAU,kBAAkB,OAAO,OAAO;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,MAAM;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}