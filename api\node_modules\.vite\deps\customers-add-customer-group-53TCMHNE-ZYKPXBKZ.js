import {
  useCustomerGroupTableColumns
} from "./chunk-RYLAHPUE.js";
import {
  useCustomerGroupTableQuery
} from "./chunk-I2ZOQM4X.js";
import "./chunk-7HUCBNCQ.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import {
  useCustomerGroupTableFilters
} from "./chunk-XNFM7P3M.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import {
  useBatchCustomerCustomerGroups,
  useCustomerGroups
} from "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Hint,
  Tooltip,
  createColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customers-add-customer-group-53TCMHNE.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddCustomerGroupsSchema = objectType({
  customer_group_ids: arrayType(stringType()).min(1)
});
var PAGE_SIZE = 10;
var AddCustomerGroupsForm = ({
  customerId
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [isPending, setIsPending] = (0, import_react.useState)(false);
  const { mutateAsync: batchCustomerCustomerGroups } = useBatchCustomerCustomerGroups(customerId);
  const form = useForm({
    defaultValues: {
      customer_group_ids: []
    },
    resolver: t(AddCustomerGroupsSchema)
  });
  const { setValue } = form;
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  (0, import_react.useEffect)(() => {
    setValue(
      "customer_group_ids",
      Object.keys(rowSelection).filter((k) => rowSelection[k]),
      {
        shouldDirty: true,
        shouldTouch: true
      }
    );
  }, [rowSelection, setValue]);
  const { searchParams, raw } = useCustomerGroupTableQuery({
    pageSize: PAGE_SIZE
  });
  const filters = useCustomerGroupTableFilters();
  const {
    customer_groups,
    count,
    isPending: isLoading,
    isError,
    error
  } = useCustomerGroups({
    fields: "*customers",
    ...searchParams
  });
  const updater = (fn) => {
    const state = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(state);
    setValue("customer_group_ids", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(state);
  };
  const columns = useColumns();
  const { table } = useDataTable({
    data: customer_groups ?? [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: (row) => {
      var _a;
      return !((_a = row.original.customers) == null ? void 0 : _a.map((c) => c.id).includes(customerId));
    },
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  const handleSubmit = form.handleSubmit(async (data) => {
    setIsPending(true);
    try {
      await batchCustomerCustomerGroups({ add: data.customer_group_ids });
      toast.success(
        t2("customers.groups.add.success", {
          groups: data.customer_group_ids.map((id) => customer_groups == null ? void 0 : customer_groups.find((g) => g.id === id)).filter(Boolean).map((cg) => cg == null ? void 0 : cg.name)
        })
      );
      handleSuccess(`/customers/${customerId}`);
    } catch (e) {
      toast.error(e.message);
    } finally {
      setIsPending(false);
    }
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end gap-x-2", children: form.formState.errors.customer_group_ids && (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: form.formState.errors.customer_group_ids.message }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: (0, import_jsx_runtime.jsx)(
          _DataTable,
          {
            table,
            columns,
            pageSize: PAGE_SIZE,
            count,
            filters,
            orderBy: [
              { key: "name", label: t2("fields.name") },
              { key: "created_at", label: t2("fields.createdAt") },
              { key: "updated_at", label: t2("fields.updatedAt") }
            ],
            isLoading,
            layout: "fill",
            search: "autofocus",
            queryObject: raw,
            noRecords: {
              message: t2("customers.groups.add.list.noRecordsMessage")
            }
          }
        ) }),
        (0, import_jsx_runtime.jsxs)(RouteFocusModal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isPending,
              children: t2("actions.save")
            }
          )
        ] })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const { t: t2 } = useTranslation();
  const base = useCustomerGroupTableColumns();
  const columns = (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreSelected = !row.getCanSelect();
          const isSelected = row.getIsSelected() || isPreSelected;
          const Component = (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: isSelected,
              disabled: isPreSelected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
          if (isPreSelected) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("customers.groups.alreadyAddedTooltip"),
                side: "right",
                children: Component
              }
            );
          }
          return Component;
        }
      }),
      ...base
    ],
    [t2, base]
  );
  return columns;
};
var CustomerAddCustomerGroups = () => {
  const { id } = useParams();
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(AddCustomerGroupsForm, { customerId: id }) });
};
export {
  CustomerAddCustomerGroups as Component
};
//# sourceMappingURL=customers-add-customer-group-53TCMHNE-ZYKPXBKZ.js.map
