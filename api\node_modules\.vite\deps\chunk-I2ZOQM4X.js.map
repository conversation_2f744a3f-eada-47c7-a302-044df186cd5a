{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-MOSRJHJ3.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/query/use-customer-group-table-query.tsx\nvar useCustomerGroupTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"has_account\", \"order\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, created_at, updated_at, q, order } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useCustomerGroupTableQuery\n};\n"], "mappings": ";;;;;AAKA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,eAAe,SAAS,cAAc,YAAY;AAAA,IAClE;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,YAAY,YAAY,GAAG,MAAM,IAAI;AACrD,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}