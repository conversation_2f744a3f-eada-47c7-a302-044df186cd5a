{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-BC3M3N6P.mjs"], "sourcesContent": ["import {\n  getCountryByIso2\n} from \"./chunk-VDBOSWVE.mjs\";\nimport {\n  taxRegionsQueryKeys,\n  useTaxRegion\n} from \"./chunk-LDJKJLBJ.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/routes/tax-regions/tax-region-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar TaxRegionDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { tax_region } = useTaxRegion(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!tax_region) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: getCountryByIso2(tax_region.country_code)?.display_name || tax_region.country_code?.toUpperCase() });\n};\n\n// src/routes/tax-regions/tax-region-detail/loader.ts\nvar taxRegionDetailQuery = (id) => ({\n  queryKey: taxRegionsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.taxRegion.retrieve(id)\n});\nvar taxRegionLoader = async ({ params }) => {\n  const id = params.id;\n  const query = taxRegionDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\nexport {\n  TaxRegionDetailBreadcrumb,\n  taxRegionLoader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAeA,yBAAoB;AACpB,IAAI,4BAA4B,CAAC,UAAU;AAhB3C;AAiBE,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,WAAW,IAAI,aAAa,IAAI,QAAQ;AAAA,IAC9C,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,YAAU,sBAAiB,WAAW,YAAY,MAAxC,mBAA2C,mBAAgB,gBAAW,iBAAX,mBAAyB,eAAc,CAAC;AACpJ;AAGA,IAAI,uBAAuB,CAAC,QAAQ;AAAA,EAClC,UAAU,oBAAoB,OAAO,EAAE;AAAA,EACvC,SAAS,YAAY,IAAI,MAAM,UAAU,SAAS,EAAE;AACtD;AACA,IAAI,kBAAkB,OAAO,EAAE,OAAO,MAAM;AAC1C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,qBAAqB,EAAE;AACrC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": []}