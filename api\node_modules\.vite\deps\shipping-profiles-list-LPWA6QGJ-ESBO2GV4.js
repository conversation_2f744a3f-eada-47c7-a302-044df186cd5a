import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useDeleteShippingProfile,
  useShippingProfiles
} from "./chunk-KRJAVNJS.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  Text,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/shipping-profiles-list-LPWA6QGJ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ShippingOptionsRowActions = ({
  profile
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteShippingProfile(profile.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("shippingProfile.delete.title"),
      description: t("shippingProfile.delete.description", {
        name: profile.name
      }),
      verificationText: profile.name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("shippingProfile.delete.successToast", {
            name: profile.name
          })
        );
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useShippingProfileTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: t("fields.name"),
        cell: (cell) => cell.getValue()
      }),
      columnHelper.accessor("type", {
        header: t("fields.type"),
        cell: (cell) => cell.getValue()
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime2.jsx)(ShippingOptionsRowActions, { profile: row.original })
      })
    ],
    [t]
  );
};
var useShippingProfileTableFilters = () => {
  const { t } = useTranslation();
  let filters = [];
  filters.push({
    key: "name",
    label: t("fields.name"),
    type: "string"
  });
  filters.push({
    key: "type",
    label: t("fields.type"),
    type: "string"
  });
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at" },
    { label: t("fields.updatedAt"), key: "updated_at" }
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date"
  }));
  filters = [...filters, ...dateFilters];
  return filters;
};
var useShippingProfileTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(
    ["offset", "q", "order", "created_at", "updated_at", "name", "type"],
    prefix
  );
  const searchParams = {
    limit: pageSize,
    offset: raw.offset ? parseInt(raw.offset) : 0,
    q: raw.q,
    order: raw.order,
    created_at: raw.created_at ? JSON.parse(raw.created_at) : void 0,
    updated_at: raw.updated_at ? JSON.parse(raw.updated_at) : void 0,
    name: raw.name,
    type: raw.type
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE = 20;
var ShippingProfileListTable = () => {
  const { t } = useTranslation();
  const { raw, searchParams } = useShippingProfileTableQuery({
    pageSize: PAGE_SIZE
  });
  const { shipping_profiles, count, isLoading, isError, error } = useShippingProfiles(searchParams, {
    placeholderData: keepPreviousData
  });
  const columns = useShippingProfileTableColumns();
  const filters = useShippingProfileTableFilters();
  const { table } = useDataTable({
    data: shipping_profiles,
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsxs)("div", { children: [
        (0, import_jsx_runtime3.jsx)(Heading, { children: t("shippingProfile.domain") }),
        (0, import_jsx_runtime3.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("shippingProfile.subtitle") })
      ] }),
      (0, import_jsx_runtime3.jsx)("div", { children: (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime3.jsx)(Link, { to: "create", children: t("actions.create") }) }) })
    ] }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        pageSize: PAGE_SIZE,
        count,
        columns,
        filters,
        orderBy: [
          { key: "name", label: t("fields.name") },
          { key: "type", label: t("fields.type") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        isLoading,
        navigateTo: (row) => row.id,
        queryObject: raw,
        search: true,
        pagination: true
      }
    )
  ] });
};
var ShippingProfileList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime4.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("shipping_profile.list.before"),
        after: getWidgets("shipping_profile.list.after")
      },
      children: (0, import_jsx_runtime4.jsx)(ShippingProfileListTable, {})
    }
  );
};
export {
  ShippingProfileList as Component
};
//# sourceMappingURL=shipping-profiles-list-LPWA6QGJ-ESBO2GV4.js.map
