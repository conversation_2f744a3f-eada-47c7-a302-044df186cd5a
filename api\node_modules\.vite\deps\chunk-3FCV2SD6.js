import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import {
  Form,
  useFieldArray,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  usePromotionRuleAttributes,
  usePromotionRules
} from "./chunk-S32V3COL.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Badge,
  Button,
  Heading,
  IconButton,
  Input,
  Select,
  Text,
  XMarkMini
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NGEWNLV7.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var generateRuleAttributes = (rules) => (rules || []).map((rule) => {
  var _a, _b;
  return {
    id: rule.id,
    required: rule.required,
    field_type: rule.field_type,
    disguised: rule.disguised,
    attribute: rule.attribute,
    operator: rule.operator,
    values: rule.field_type === "number" || rule.operator === "eq" ? typeof rule.values === "object" ? (_a = rule.values[0]) == null ? void 0 : _a.value : rule.values : (_b = rule == null ? void 0 : rule.values) == null ? void 0 : _b.map((v) => v.value)
  };
});
var buildFilters = (attribute, store) => {
  var _a;
  if (!attribute || !store) {
    return {};
  }
  if (attribute === "currency_code") {
    return {
      value: (_a = store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)
    };
  }
  return {};
};
var RuleValueFormField = ({
  form,
  identifier,
  scope,
  name,
  operator,
  fieldRule,
  attributes,
  ruleType
}) => {
  const attribute = attributes == null ? void 0 : attributes.find(
    (attr) => attr.value === fieldRule.attribute
  );
  const { store, isLoading: isStoreLoading } = useStore();
  const comboboxData = useComboboxData({
    queryFn: async (params) => {
      return await sdk.admin.promotion.listRuleValues(
        ruleType,
        attribute == null ? void 0 : attribute.id,
        {
          ...params,
          ...buildFilters(attribute == null ? void 0 : attribute.id, store)
        }
      );
    },
    enabled: !!(attribute == null ? void 0 : attribute.id) && ["select", "multiselect"].includes(attribute.field_type) && !isStoreLoading,
    getOptions: (data) => data.values,
    queryKey: ["rule-value-options", ruleType, attribute == null ? void 0 : attribute.id]
  });
  const watchOperator = useWatch({
    control: form.control,
    name: operator
  });
  return (0, import_jsx_runtime.jsx)(
    Form.Field,
    {
      name,
      render: ({ field: { onChange, ref, ...field } }) => {
        if ((attribute == null ? void 0 : attribute.field_type) === "number") {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
              Input,
              {
                ...field,
                type: "number",
                onChange,
                className: "bg-ui-bg-base",
                ref,
                min: 1,
                disabled: !fieldRule.attribute
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        } else if ((attribute == null ? void 0 : attribute.field_type) === "text") {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
              Input,
              {
                ...field,
                ref,
                onChange,
                className: "bg-ui-bg-base",
                disabled: !fieldRule.attribute
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        } else {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
              Combobox,
              {
                ...field,
                ...comboboxData,
                multiple: watchOperator !== "eq",
                ref,
                placeholder: watchOperator === "eq" ? "Select Value" : "Select Values",
                onChange
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    },
    `${identifier}.${scope}.${name}-${fieldRule.attribute}`
  );
};
var requiredProductRule = {
  id: "product",
  attribute: "items.product.id",
  attribute_label: "Product",
  operator: "eq",
  operator_label: "Equal",
  values: [],
  required: true,
  field_type: "select",
  disguised: false
};
var RulesFormField = ({
  form,
  ruleType,
  setRulesToRemove,
  rulesToRemove,
  scope = "rules",
  promotion
}) => {
  var _a;
  const { t } = useTranslation();
  const formData = form.getValues();
  const { attributes } = usePromotionRuleAttributes(ruleType, formData.type);
  const { fields, append, remove, update, replace } = useFieldArray({
    control: form.control,
    name: scope,
    keyName: scope
  });
  const promotionType = useWatch({
    control: form.control,
    name: "type",
    defaultValue: promotion == null ? void 0 : promotion.type
  });
  const applicationMethodType = useWatch({
    control: form.control,
    name: "application_method.type",
    defaultValue: (_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.type
  });
  const query = promotionType ? {
    promotion_type: promotionType,
    application_method_type: applicationMethodType
  } : {};
  const { rules, isLoading } = usePromotionRules(
    (promotion == null ? void 0 : promotion.id) || null,
    ruleType,
    query,
    {
      enabled: !!(promotion == null ? void 0 : promotion.id) || !!promotionType && !!applicationMethodType
    }
  );
  (0, import_react.useEffect)(() => {
    if (isLoading) {
      return;
    }
    if (ruleType === "rules" && !fields.length) {
      form.resetField("rules");
      replace(generateRuleAttributes(rules));
    }
    if (ruleType === "buy-rules" && !fields.length) {
      form.resetField("application_method.buy_rules");
      const rulesToAppend = (promotion == null ? void 0 : promotion.id) || promotionType === "standard" ? rules : [...rules, requiredProductRule];
      replace(generateRuleAttributes(rulesToAppend));
    }
    if (ruleType === "target-rules" && !fields.length) {
      form.resetField("application_method.target_rules");
      const rulesToAppend = (promotion == null ? void 0 : promotion.id) || promotionType === "standard" ? rules : [...rules, requiredProductRule];
      replace(generateRuleAttributes(rulesToAppend));
    }
  }, [
    promotionType,
    isLoading,
    ruleType,
    fields.length,
    form,
    replace,
    rules,
    promotion == null ? void 0 : promotion.id
  ]);
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
    (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", className: "mb-2", children: t(`promotions.fields.conditions.${ruleType}.title`) }),
    (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle txt-small mb-6", children: t(`promotions.fields.conditions.${ruleType}.description`) }),
    fields.map((fieldRule, index) => {
      const identifier = fieldRule.id;
      return (0, import_jsx_runtime2.jsxs)(import_react.Fragment, { children: [
        (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-subtle border-ui-border-base flex flex-row gap-2 rounded-xl border px-2 py-2", children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "grow", children: [
            (0, import_jsx_runtime2.jsx)(
              Form.Field,
              {
                name: `${scope}.${index}.attribute`,
                render: ({ field }) => {
                  var _a2;
                  const { onChange, ref, ...fieldProps } = field;
                  const existingAttributes = (fields == null ? void 0 : fields.map((field2) => field2.attribute)) || [];
                  const attributeOptions = (attributes == null ? void 0 : attributes.filter((attr) => {
                    if (attr.value === fieldRule.attribute) {
                      return true;
                    }
                    return !existingAttributes.includes(attr.value);
                  })) || [];
                  const disabled = !!fieldRule.required;
                  const onValueChange = (e) => {
                    const currentAttributeOption = attributeOptions.find(
                      (ao) => ao.id === e
                    );
                    update(index, {
                      ...fieldRule,
                      values: [],
                      disguised: (currentAttributeOption == null ? void 0 : currentAttributeOption.disguised) || false
                    });
                    onChange(e);
                  };
                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { className: "mb-2", children: [
                    fieldRule.required && (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center px-2", children: (0, import_jsx_runtime2.jsx)("p", { className: "text text-ui-fg-muted txt-small", children: t("promotions.form.required") }) }),
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: !disabled ? (0, import_jsx_runtime2.jsxs)(
                      Select,
                      {
                        ...fieldProps,
                        onValueChange,
                        disabled: fieldRule.required,
                        children: [
                          (0, import_jsx_runtime2.jsx)(
                            Select.Trigger,
                            {
                              ref,
                              className: "bg-ui-bg-base",
                              children: (0, import_jsx_runtime2.jsx)(
                                Select.Value,
                                {
                                  placeholder: t(
                                    "promotions.form.selectAttribute"
                                  )
                                }
                              )
                            }
                          ),
                          (0, import_jsx_runtime2.jsx)(Select.Content, { children: attributeOptions == null ? void 0 : attributeOptions.map((c, i) => (0, import_jsx_runtime2.jsx)(
                            Select.Item,
                            {
                              value: c.value,
                              children: (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle", children: c.label })
                            },
                            `${identifier}-attribute-option-${i}`
                          )) })
                        ]
                      }
                    ) : (0, import_jsx_runtime2.jsx)(
                      DisabledField,
                      {
                        label: ((_a2 = attributeOptions == null ? void 0 : attributeOptions.find(
                          (ao) => ao.value === fieldRule.attribute
                        )) == null ? void 0 : _a2.label) || "",
                        field
                      }
                    ) }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime2.jsxs)("div", { className: "flex gap-2", children: [
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  name: `${scope}.${index}.operator`,
                  render: ({ field }) => {
                    var _a2, _b;
                    const { onChange, ref, ...fieldProps } = field;
                    const currentAttributeOption = attributes == null ? void 0 : attributes.find(
                      (attr) => attr.value === fieldRule.attribute
                    );
                    const options = ((_a2 = currentAttributeOption == null ? void 0 : currentAttributeOption.operators) == null ? void 0 : _a2.map((o, idx) => ({
                      label: o.label,
                      value: o.value,
                      key: `${identifier}-operator-option-${idx}`
                    }))) || [];
                    const disabled = !!fieldRule.attribute && (options == null ? void 0 : options.length) <= 1;
                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { className: "basis-1/2", children: [
                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: !disabled ? (0, import_jsx_runtime2.jsxs)(
                        Select,
                        {
                          ...fieldProps,
                          disabled: !fieldRule.attribute,
                          onValueChange: onChange,
                          children: [
                            (0, import_jsx_runtime2.jsx)(
                              Select.Trigger,
                              {
                                ref,
                                className: "bg-ui-bg-base",
                                children: (0, import_jsx_runtime2.jsx)(Select.Value, { placeholder: "Select Operator" })
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(Select.Content, { children: options == null ? void 0 : options.map((c) => (0, import_jsx_runtime2.jsx)(Select.Item, { value: c.value, children: (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle", children: c.label }) }, c.key)) })
                          ]
                        }
                      ) : (0, import_jsx_runtime2.jsx)(
                        DisabledField,
                        {
                          label: ((_b = options.find(
                            (o) => o.value === fieldProps.value
                          )) == null ? void 0 : _b.label) || "",
                          field
                        }
                      ) }),
                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                RuleValueFormField,
                {
                  form,
                  identifier,
                  scope,
                  name: `${scope}.${index}.values`,
                  operator: `${scope}.${index}.operator`,
                  fieldRule,
                  attributes,
                  ruleType
                }
              )
            ] })
          ] }),
          (0, import_jsx_runtime2.jsx)("div", { className: "size-7 flex-none self-center", children: !fieldRule.required && (0, import_jsx_runtime2.jsx)(
            IconButton,
            {
              size: "small",
              variant: "transparent",
              className: "text-ui-fg-muted",
              type: "button",
              onClick: () => {
                if (!fieldRule.required) {
                  setRulesToRemove && setRulesToRemove([...rulesToRemove, fieldRule]);
                  remove(index);
                }
              },
              children: (0, import_jsx_runtime2.jsx)(XMarkMini, {})
            }
          ) })
        ] }),
        index < fields.length - 1 && (0, import_jsx_runtime2.jsxs)("div", { className: "relative px-6 py-3", children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "border-ui-border-strong absolute bottom-0 left-[40px] top-0 z-[-1] w-px bg-[linear-gradient(var(--border-strong)_33%,rgba(255,255,255,0)_0%)] bg-[length:1px_3px] bg-repeat-y" }),
          (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", className: " text-xs", children: t("promotions.form.and") })
        ] })
      ] }, `${fieldRule.id}.${index}.${fieldRule.attribute}`);
    }),
    (0, import_jsx_runtime2.jsxs)("div", { className: fields.length ? "mt-6" : "", children: [
      (0, import_jsx_runtime2.jsx)(
        Button,
        {
          type: "button",
          variant: "secondary",
          className: "inline-block",
          onClick: () => {
            append({
              attribute: "",
              operator: "",
              values: [],
              required: false
            });
          },
          children: t("promotions.fields.addCondition")
        }
      ),
      !!fields.length && (0, import_jsx_runtime2.jsx)(
        Button,
        {
          type: "button",
          variant: "transparent",
          className: "text-ui-fg-muted hover:text-ui-fg-subtle ml-2 inline-block",
          onClick: () => {
            const indicesToRemove = fields.map((field, index) => field.required ? null : index).filter((f) => f !== null);
            setRulesToRemove && setRulesToRemove(fields.filter((field) => !field.required));
            remove(indicesToRemove);
          },
          children: t("promotions.fields.clearAll")
        }
      )
    ] })
  ] });
};
var DisabledField = (0, import_react.forwardRef)(
  ({ label, field }, ref) => {
    return (0, import_jsx_runtime2.jsxs)("div", { children: [
      (0, import_jsx_runtime2.jsx)("div", { className: "txt-compact-small bg-ui-bg-component shadow-borders-base text-ui-fg-base h-8 rounded-md px-2 py-1.5", children: label }),
      (0, import_jsx_runtime2.jsx)("input", { ...field, ref, disabled: true, hidden: true })
    ] });
  }
);
DisabledField.displayName = "DisabledField";

export {
  RulesFormField
};
//# sourceMappingURL=chunk-3FCV2SD6.js.map
