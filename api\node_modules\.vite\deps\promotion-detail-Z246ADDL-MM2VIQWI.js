import {
  formatPercentage
} from "./chunk-XYHEMHQ5.js";
import {
  BadgeListSummary
} from "./chunk-MDQQ3RPH.js";
import {
  DateRangeDisplay
} from "./chunk-USN2624C.js";
import {
  getPromotionStatus
} from "./chunk-KFESVZ55.js";
import {
  formatCurrency
} from "./chunk-SCWUY6XB.js";
import {
  NoRecords
} from "./chunk-QL4D35SJ.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-5NX546NL.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import {
  promotionsQueryKeys,
  useDeletePromotion,
  usePromotion,
  usePromotionRules
} from "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowUpRightOnBox,
  Badge,
  Container,
  Copy,
  Heading,
  PencilSquare,
  StatusBadge,
  Text,
  Trash,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-detail-Z246ADDL.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var PromotionDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { promotion } = usePromotion(id, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!promotion) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: promotion.code });
};
var promotionDetailQuery = (id) => ({
  queryKey: promotionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.promotion.retrieve(id)
});
var promotionLoader = async ({ params }) => {
  const id = params.id;
  const query = promotionDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var CampaignDetailSection = ({
  campaign
}) => {
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-3", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-muted flex items-center gap-x-1.5", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", className: "text-ui-fg-base", children: campaign.name }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", children: "·" }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", children: campaign.campaign_identifier })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      DateRangeDisplay,
      {
        startsAt: campaign.starts_at,
        endsAt: campaign.ends_at,
        showTime: true
      }
    )
  ] });
};
var CampaignSection = ({
  campaign
}) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const actions = [
    {
      label: t("actions.edit"),
      to: "add-to-campaign",
      icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
    }
  ];
  if (campaign) {
    actions.unshift({
      label: t("promotions.campaign.actions.goToCampaign"),
      to: `/campaigns/${campaign.id}`,
      icon: (0, import_jsx_runtime2.jsx)(ArrowUpRightOnBox, {})
    });
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("promotions.fields.campaign") }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions
            }
          ]
        }
      )
    ] }),
    campaign ? (0, import_jsx_runtime2.jsx)(CampaignDetailSection, { campaign }) : (0, import_jsx_runtime2.jsx)(
      NoRecords,
      {
        className: "h-[180px] pt-4 text-center",
        title: "Not part of a campaign",
        message: "Add this promotion to an existing campaign",
        action: {
          to: `/promotions/${id}/add-to-campaign`,
          label: "Add to Campaign"
        },
        buttonVariant: "transparentIconLeft"
      }
    )
  ] });
};
function RuleBlock({ rule }) {
  var _a;
  return (0, import_jsx_runtime3.jsx)("div", { className: "bg-ui-bg-subtle shadow-borders-base align-center flex justify-around rounded-md p-2", children: (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle txt-compact-xsmall flex items-center whitespace-nowrap", children: [
    (0, import_jsx_runtime3.jsx)(
      Badge,
      {
        size: "2xsmall",
        className: "txt-compact-xsmall-plus tag-neutral-text mx-1 inline-block truncate",
        children: rule.attribute_label
      },
      "rule-attribute"
    ),
    (0, import_jsx_runtime3.jsx)("span", { className: "txt-compact-2xsmall mx-1 inline-block", children: rule.operator_label }),
    (0, import_jsx_runtime3.jsx)(
      BadgeListSummary,
      {
        inline: true,
        className: "!txt-compact-small-plus",
        list: rule.field_type === "number" ? [rule.values] : (_a = rule.values) == null ? void 0 : _a.map((v) => v.label)
      }
    )
  ] }) });
}
var PromotionConditionsSection = ({
  rules,
  ruleType
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)("div", { className: "flex flex-col", children: (0, import_jsx_runtime3.jsx)(Heading, { children: t(`promotions.fields.conditions.${ruleType}.title`) }) }),
      (0, import_jsx_runtime3.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: `${ruleType}/edit`
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-2 px-6 pb-4 pt-2", children: [
      !rules.length && (0, import_jsx_runtime3.jsx)(
        NoRecords,
        {
          className: "h-[180px]",
          title: t("general.noRecordsTitle"),
          message: t("promotions.conditions.list.noRecordsMessage"),
          action: {
            to: `${ruleType}/edit`,
            label: t("promotions.conditions.add")
          },
          buttonVariant: "transparentIconLeft"
        }
      ),
      rules.map((rule) => (0, import_jsx_runtime3.jsx)(RuleBlock, { rule }, `${rule.id}-${rule.attribute}`))
    ] })
  ] });
};
function getDisplayValue(promotion) {
  var _a, _b, _c, _d;
  const value = (_a = promotion.application_method) == null ? void 0 : _a.value;
  if (!value) {
    return null;
  }
  if (((_b = promotion.application_method) == null ? void 0 : _b.type) === "fixed") {
    const currency = (_c = promotion.application_method) == null ? void 0 : _c.currency_code;
    if (!currency) {
      return null;
    }
    return formatCurrency(value, currency);
  } else if (((_d = promotion.application_method) == null ? void 0 : _d.type) === "percentage") {
    return formatPercentage(value);
  }
  return null;
}
var PromotionGeneralSection = ({
  promotion
}) => {
  var _a, _b, _c, _d;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeletePromotion(promotion.id);
  const handleDelete = async () => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("promotions.deleteWarning", {
        code: promotion.code
      }),
      verificationInstruction: t("general.typeToConfirm"),
      verificationText: promotion.code,
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!confirm) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        navigate("/promotions", { replace: true });
      }
    });
  };
  const [color, text] = getPromotionStatus(promotion);
  const displayValue = getDisplayValue(promotion);
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)("div", { className: "flex flex-col", children: (0, import_jsx_runtime4.jsx)(Heading, { children: promotion.code }) }),
      (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime4.jsx)(StatusBadge, { color, children: text }),
        (0, import_jsx_runtime4.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {}),
                    label: t("actions.edit"),
                    to: `/promotions/${promotion.id}/edit`
                  }
                ]
              },
              {
                actions: [
                  {
                    icon: (0, import_jsx_runtime4.jsx)(Trash, {}),
                    label: t("actions.delete"),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("promotions.fields.campaign") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", className: "text-pretty", children: promotion.is_automatic ? t("promotions.form.method.automatic.title") : t("promotions.form.method.code.title") })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("fields.code") }),
      (0, import_jsx_runtime4.jsx)(
        Copy,
        {
          content: promotion.code,
          className: "text-ui-tag-neutral-text",
          asChild: true,
          children: (0, import_jsx_runtime4.jsx)(
            Badge,
            {
              size: "2xsmall",
              rounded: "full",
              className: "cursor-pointer text-pretty",
              children: promotion.code
            }
          )
        }
      )
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("promotions.fields.type") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", className: "text-pretty capitalize", children: promotion.type })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("promotions.fields.value") }),
      (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime4.jsx)(Text, { className: "inline", size: "small", leading: "compact", children: displayValue || "-" }),
        ((_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.type) === "fixed" && (0, import_jsx_runtime4.jsx)(Badge, { size: "2xsmall", rounded: "full", children: (_c = (_b = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _b.currency_code) == null ? void 0 : _c.toUpperCase() })
      ] })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("promotions.fields.allocation") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", className: "text-pretty capitalize", children: (_d = promotion.application_method) == null ? void 0 : _d.allocation })
    ] })
  ] });
};
var PromotionDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const { promotion, isLoading } = usePromotion(id, { initialData });
  const query = {};
  if ((promotion == null ? void 0 : promotion.type) === "buyget") {
    query.promotion_type = promotion.type;
  }
  const { rules } = usePromotionRules(id, "rules", query);
  const { rules: targetRules } = usePromotionRules(id, "target-rules", query);
  const { rules: buyRules } = usePromotionRules(id, "buy-rules", query);
  const { getWidgets } = useExtension();
  if (isLoading || !promotion) {
    return (0, import_jsx_runtime5.jsx)(TwoColumnPageSkeleton, { mainSections: 3, sidebarSections: 1, showJSON: true });
  }
  return (0, import_jsx_runtime5.jsxs)(
    TwoColumnPage,
    {
      data: promotion,
      widgets: {
        after: getWidgets("promotion.details.after"),
        before: getWidgets("promotion.details.before"),
        sideAfter: getWidgets("promotion.details.side.after"),
        sideBefore: getWidgets("promotion.details.side.before")
      },
      hasOutlet: true,
      showJSON: true,
      children: [
        (0, import_jsx_runtime5.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime5.jsx)(PromotionGeneralSection, { promotion }),
          (0, import_jsx_runtime5.jsx)(PromotionConditionsSection, { rules: rules || [], ruleType: "rules" }),
          (0, import_jsx_runtime5.jsx)(
            PromotionConditionsSection,
            {
              rules: targetRules || [],
              ruleType: "target-rules"
            }
          ),
          promotion.type === "buyget" && (0, import_jsx_runtime5.jsx)(
            PromotionConditionsSection,
            {
              rules: buyRules || [],
              ruleType: "buy-rules"
            }
          )
        ] }),
        (0, import_jsx_runtime5.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime5.jsx)(CampaignSection, { campaign: promotion.campaign }) })
      ]
    }
  );
};
export {
  PromotionDetailBreadcrumb as Breadcrumb,
  PromotionDetail as Component,
  promotionLoader as loader
};
//# sourceMappingURL=promotion-detail-Z246ADDL-MM2VIQWI.js.map
