import {
  PriceListCustomerGroupRuleForm
} from "./chunk-WV6RFGPW.js";
import "./chunk-RYLAHPUE.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-7HUCBNCQ.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  <PERSON><PERSON><PERSON>er,
  StackedDrawer,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  usePriceList,
  useUpdatePriceList
} from "./chunk-XBIMCDU6.js";
import {
  useCustomerGroups
} from "./chunk-FSXJE4G7.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  DatePicker,
  Divider,
  Heading,
  IconButton,
  MagnifyingGlass,
  Text,
  XMark,
  clx,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-configuration-HVIVUV7C.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PriceListConfigurationSchema = z.object({
  ends_at: z.date().nullable(),
  starts_at: z.date().nullable(),
  customer_group_id: z.array(
    z.object({
      id: z.string(),
      name: z.string()
    })
  )
});
var STACKED_MODAL_ID = "cg";
var PriceListConfigurationForm = ({
  priceList,
  customerGroups
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { setIsOpen } = useStackedModal();
  const form = useForm({
    defaultValues: {
      ends_at: priceList.ends_at ? new Date(priceList.ends_at) : null,
      starts_at: priceList.starts_at ? new Date(priceList.starts_at) : null,
      customer_group_id: customerGroups
    },
    resolver: t(PriceListConfigurationSchema)
  });
  const { fields, remove, append } = useFieldArray({
    control: form.control,
    name: "customer_group_id",
    keyName: "cg_id"
  });
  const handleAddCustomerGroup = (groups) => {
    if (!groups.length) {
      form.setValue("customer_group_id", []);
      setIsOpen(STACKED_MODAL_ID, false);
      return;
    }
    const newIds = groups.map((group) => group.id);
    const fieldsToAdd = groups.filter(
      (group) => !fields.some((field) => field.id === group.id)
    );
    for (const field of fields) {
      if (!newIds.includes(field.id)) {
        remove(fields.indexOf(field));
      }
    }
    append(fieldsToAdd);
    setIsOpen(STACKED_MODAL_ID, false);
  };
  const { mutateAsync } = useUpdatePriceList(priceList.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a, _b;
    const groupIds = values.customer_group_id.map((group) => group.id);
    const rules = { ...priceList.rules };
    if (groupIds.length) {
      rules["customer.groups.id"] = groupIds;
    } else {
      delete rules["customer.groups.id"];
    }
    await mutateAsync(
      {
        starts_at: ((_a = values.starts_at) == null ? void 0 : _a.toISOString()) || null,
        ends_at: ((_b = values.ends_at) == null ? void 0 : _b.toISOString()) || null,
        rules
      },
      {
        onSuccess: () => {
          toast.success(t2("priceLists.configuration.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => toast.error(error.message)
      }
    );
  });
  return (0, import_jsx_runtime.jsxs)(RouteDrawer.Form, { form, children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Description, { className: "sr-only", children: t2("priceLists.configuration.edit.description") }),
    (0, import_jsx_runtime.jsxs)(
      KeyboundForm,
      {
        className: "flex flex-1 flex-col overflow-hidden",
        onSubmit: handleSubmit,
        children: [
          (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-auto", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "starts_at",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-3", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.startsAt.label") }),
                        (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.startsAt.hint") })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        DatePicker,
                        {
                          granularity: "minute",
                          shouldCloseOnSelect: false,
                          ...field
                        }
                      ) })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(Divider, {}),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "ends_at",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-3", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.endsAt.label") }),
                        (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.endsAt.hint") })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        DatePicker,
                        {
                          granularity: "minute",
                          shouldCloseOnSelect: false,
                          ...field
                        }
                      ) })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(Divider, {}),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "customer_group_id",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.customerAvailability.label") }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.customerAvailability.hint") })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                      "div",
                      {
                        className: clx(
                          "bg-ui-bg-component shadow-elevation-card-rest transition-fg grid gap-1.5 rounded-xl py-1.5",
                          "aria-[invalid='true']:shadow-borders-error"
                        ),
                        role: "application",
                        ref: field.ref,
                        children: [
                          (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid gap-1.5 px-1.5 md:grid-cols-2", children: [
                            (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: t2(
                              "priceLists.fields.customerAvailability.attribute"
                            ) }),
                            (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: t2("operators.in") })
                          ] }),
                          (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1.5 px-1.5", children: (0, import_jsx_runtime.jsxs)(StackedDrawer, { id: STACKED_MODAL_ID, children: [
                            (0, import_jsx_runtime.jsx)(StackedDrawer.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsxs)(
                              "button",
                              {
                                type: "button",
                                className: "bg-ui-bg-field shadow-borders-base txt-compact-small text-ui-fg-muted flex flex-1 items-center gap-x-2 rounded-md px-2 py-1.5",
                                children: [
                                  (0, import_jsx_runtime.jsx)(MagnifyingGlass, {}),
                                  t2(
                                    "priceLists.fields.customerAvailability.placeholder"
                                  )
                                ]
                              }
                            ) }),
                            (0, import_jsx_runtime.jsx)(StackedDrawer.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", children: t2("actions.browse") }) }),
                            (0, import_jsx_runtime.jsxs)(StackedDrawer.Content, { children: [
                              (0, import_jsx_runtime.jsxs)(StackedDrawer.Header, { children: [
                                (0, import_jsx_runtime.jsx)(StackedDrawer.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: t2(
                                  "priceLists.fields.customerAvailability.header"
                                ) }) }),
                                (0, import_jsx_runtime.jsx)(StackedDrawer.Description, { className: "sr-only", children: t2(
                                  "priceLists.fields.customerAvailability.hint"
                                ) })
                              ] }),
                              (0, import_jsx_runtime.jsx)(
                                PriceListCustomerGroupRuleForm,
                                {
                                  type: "drawer",
                                  setState: handleAddCustomerGroup,
                                  state: fields
                                }
                              )
                            ] })
                          ] }) }),
                          fields.length > 0 ? (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1.5", children: [
                            (0, import_jsx_runtime.jsx)(Divider, { variant: "dashed" }),
                            (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-1.5 px-1.5", children: fields.map((field2, index) => {
                              return (0, import_jsx_runtime.jsxs)(
                                "div",
                                {
                                  className: "bg-ui-bg-field-component shadow-borders-base flex items-center justify-between gap-2 rounded-md px-2 py-0.5",
                                  children: [
                                    (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: field2.name }),
                                    (0, import_jsx_runtime.jsx)(
                                      IconButton,
                                      {
                                        size: "small",
                                        variant: "transparent",
                                        type: "button",
                                        onClick: () => remove(index),
                                        children: (0, import_jsx_runtime.jsx)(XMark, {})
                                      }
                                    )
                                  ]
                                },
                                field2.cg_id
                              );
                            }) })
                          ] }) : null
                        ]
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { className: "shrink-0", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
            (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
            (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", children: t2("actions.save") })
          ] }) })
        ]
      }
    )
  ] });
};
var PriceListConfiguration = () => {
  var _a;
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { price_list, isPending, isError, error } = usePriceList(id);
  const customerGroupIds = (_a = price_list == null ? void 0 : price_list.rules) == null ? void 0 : _a["customer.groups.id"];
  const {
    customer_groups,
    isPending: isCustomerGroupsPending,
    isError: isCustomerGroupsError,
    error: customerGroupsError
  } = useCustomerGroups(
    {
      id: customerGroupIds
    },
    { enabled: !!(customerGroupIds == null ? void 0 : customerGroupIds.length) }
  );
  const initialCustomerGroups = (customer_groups == null ? void 0 : customer_groups.map((group) => ({
    id: group.id,
    name: group.name
  }))) || [];
  const isCustomerGroupsReady = isPending ? false : !!(customerGroupIds == null ? void 0 : customerGroupIds.length) && isCustomerGroupsPending ? false : true;
  const ready = !isPending && !!price_list && isCustomerGroupsReady;
  if (isError) {
    throw error;
  }
  if (isCustomerGroupsError) {
    throw customerGroupsError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("priceLists.configuration.edit.header") }) }) }),
    ready && (0, import_jsx_runtime2.jsx)(
      PriceListConfigurationForm,
      {
        priceList: price_list,
        customerGroups: initialCustomerGroups
      }
    )
  ] });
};
export {
  PriceListConfiguration as Component
};
//# sourceMappingURL=price-list-configuration-HVIVUV7C-IFGTBD66.js.map
