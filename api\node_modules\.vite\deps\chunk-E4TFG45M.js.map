{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3OHH43G6.mjs"], "sourcesContent": ["import {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/collections.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar COLLECTION_QUERY_KEY = \"collections\";\nvar collectionsQueryKeys = queryKeysFactory(COLLECTION_QUERY_KEY);\nvar useCollection = (id, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: collectionsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.productCollection.retrieve(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCollections = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: collectionsQueryKeys.list(query),\n    queryFn: async () => sdk.admin.productCollection.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateCollection = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCollection.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: collectionsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateCollectionProducts = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCollection.updateProducts(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: collectionsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateCollection = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCollection.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCollection = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.productCollection.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: collectionsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  collectionsQueryKeys,\n  useCollection,\n  useCollections,\n  useUpdateCollection,\n  useUpdateCollectionProducts,\n  useCreateCollection,\n  useDeleteCollection\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB,iBAAiB,oBAAoB;AAChE,IAAI,gBAAgB,CAAC,IAAI,YAAY;AACnC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,qBAAqB,OAAO,EAAE;AAAA,IACxC,SAAS,YAAY,IAAI,MAAM,kBAAkB,SAAS,EAAE;AAAA,IAC5D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,iBAAiB,CAAC,OAAO,YAAY;AACvC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,qBAAqB,KAAK,KAAK;AAAA,IACzC,SAAS,YAAY,IAAI,MAAM,kBAAkB,KAAK,KAAK;AAAA,IAC3D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,sBAAsB,CAAC,IAAI,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,kBAAkB,OAAO,IAAI,OAAO;AAAA,IACvE,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB,EAAE,UAAU,qBAAqB,MAAM,EAAE,CAAC;AACxE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,OAAO,EAAE;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,8BAA8B,CAAC,IAAI,YAAY;AACjD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,kBAAkB,eAAe,IAAI,OAAO;AAAA,IAC/E,WAAW,CAAC,MAAM,WAAW,YAAY;AApD7C;AAqDM,kBAAY,kBAAkB,EAAE,UAAU,qBAAqB,MAAM,EAAE,CAAC;AACxE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,OAAO,EAAE;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,MAAM;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,kBAAkB,OAAO,OAAO;AAAA,IACnE,WAAW,CAAC,MAAM,WAAW,YAAY;AApE7C;AAqEM,kBAAY,kBAAkB,EAAE,UAAU,qBAAqB,MAAM,EAAE,CAAC;AACxE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,IAAI,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,kBAAkB,OAAO,EAAE;AAAA,IACvD,WAAW,CAAC,MAAM,WAAW,YAAY;AA9E7C;AA+EM,kBAAY,kBAAkB,EAAE,UAAU,qBAAqB,MAAM,EAAE,CAAC;AACxE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,OAAO,EAAE;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}