import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-32IQRUVY.mjs
var FULFILLMENT_PROVIDERS_QUERY_KEY = "fulfillment_providers";
var fulfillmentProvidersQueryKeys = queryKeysFactory(
  FULFILLMENT_PROVIDERS_QUERY_KEY
);
var FULFILLMENT_PROVIDER_OPTIONS_QUERY_KEY = "fulfillment_provider_options";
var fulfillmentProviderOptionsQueryKeys = queryKeysFactory(
  FULFILLMENT_PROVIDER_OPTIONS_QUERY_KEY
);
var useFulfillmentProviders = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.fulfillmentProvider.list(query),
    queryKey: fulfillmentProvidersQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useFulfillmentProviderOptions = (providerId, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.fulfillmentProvider.listFulfillmentOptions(providerId),
    queryKey: fulfillmentProviderOptionsQueryKeys.list(providerId),
    ...options
  });
  return { ...data, ...rest };
};
var STOCK_LOCATIONS_QUERY_KEY = "stock_locations";
var stockLocationsQueryKeys = queryKeysFactory(
  STOCK_LOCATIONS_QUERY_KEY
);
var useStockLocation = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.stockLocation.retrieve(id, query),
    queryKey: stockLocationsQueryKeys.detail(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useStockLocations = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.stockLocation.list(query),
    queryKey: stockLocationsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateStockLocation = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.stockLocation.create(payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateStockLocation = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.stockLocation.update(id, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.details()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateStockLocationSalesChannels = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.stockLocation.updateSalesChannels(id, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.details()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteStockLocation = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.stockLocation.delete(id),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateStockLocationFulfillmentSet = (locationId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.stockLocation.createFulfillmentSet(locationId, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateStockLocationFulfillmentProviders = (id, options) => {
  return useMutation({
    mutationFn: async (payload) => await sdk.admin.stockLocation.updateFulfillmentProviders(id, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.details()
      });
      await queryClient.invalidateQueries({
        queryKey: fulfillmentProvidersQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useFulfillmentProviders,
  useFulfillmentProviderOptions,
  stockLocationsQueryKeys,
  useStockLocation,
  useStockLocations,
  useCreateStockLocation,
  useUpdateStockLocation,
  useUpdateStockLocationSalesChannels,
  useDeleteStockLocation,
  useCreateStockLocationFulfillmentSet,
  useUpdateStockLocationFulfillmentProviders
};
//# sourceMappingURL=chunk-CXC4I63N.js.map
