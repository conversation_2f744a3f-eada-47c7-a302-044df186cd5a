import {
  getPaymentsFromOrder
} from "./chunk-TBLRQ3E5.js";
import "./chunk-TWHCESJX.js";
import {
  formatCurrency
} from "./chunk-SCWUY6XB.js";
import "./chunk-5ZQBU3TD.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  getLocaleAmount
} from "./chunk-UDMOPZAP.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useRefundReasons
} from "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import {
  useRefundPayment
} from "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import {
  useOrder
} from "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useNavigate,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  CurrencyInput2 as CurrencyInput,
  Heading,
  Label,
  Select,
  Textarea,
  formatValue,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-refund-FXUURAS7.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateRefundSchema = objectType({
  amount: stringType().or(numberType()),
  refund_reason_id: stringType().nullish(),
  note: stringType().optional()
});
var CreateRefundForm = ({
  order,
  refundReasons
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const paymentId = searchParams.get("paymentId");
  const payments = getPaymentsFromOrder(order);
  const payment = payments.find((p) => p.id === paymentId);
  const paymentAmount = (payment == null ? void 0 : payment.amount) || 0;
  const currency = (0, import_react.useMemo)(
    () => currencies[order.currency_code.toUpperCase()],
    [order.currency_code]
  );
  const form = useForm({
    defaultValues: {
      amount: paymentAmount,
      note: ""
    },
    resolver: t(CreateRefundSchema)
  });
  (0, import_react.useEffect)(() => {
    const pendingDifference = order.summary.pending_difference;
    const paymentAmount2 = (payment == null ? void 0 : payment.amount) || 0;
    const pendingAmount = pendingDifference < 0 ? Math.min(pendingDifference, paymentAmount2) : paymentAmount2;
    const normalizedAmount = pendingAmount < 0 ? pendingAmount * -1 : pendingAmount;
    form.setValue("amount", normalizedAmount);
  }, [payment]);
  const { mutateAsync, isPending } = useRefundPayment(order.id, payment == null ? void 0 : payment.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        amount: parseFloat(data.amount),
        refund_reason_id: data.refund_reason_id,
        note: data.note
      },
      {
        onSuccess: () => {
          toast.success(
            t2("orders.payment.refundPaymentSuccess", {
              amount: formatCurrency(data.amount, payment == null ? void 0 : payment.currency_code)
            })
          );
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex-1 overflow-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
          (0, import_jsx_runtime.jsxs)(
            Select,
            {
              value: payment == null ? void 0 : payment.id,
              onValueChange: (value) => {
                navigate(`/orders/${order.id}/refund?paymentId=${value}`, {
                  replace: true
                });
              },
              children: [
                (0, import_jsx_runtime.jsx)(Label, { className: "txt-compact-small mb-[-6px] font-sans font-medium", children: t2("orders.payment.selectPaymentToRefund") }),
                (0, import_jsx_runtime.jsx)(Select.Trigger, { children: (0, import_jsx_runtime.jsx)(
                  Select.Value,
                  {
                    placeholder: t2("orders.payment.selectPaymentToRefund")
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Select.Content, { children: payments.map((payment2) => {
                  const totalRefunded = payment2.refunds.reduce(
                    (acc, next) => next.amount + acc,
                    0
                  );
                  return (0, import_jsx_runtime.jsxs)(
                    Select.Item,
                    {
                      value: payment2.id,
                      disabled: !!payment2.canceled_at || totalRefunded >= payment2.amount,
                      children: [
                        (0, import_jsx_runtime.jsxs)("span", { children: [
                          getLocaleAmount(
                            payment2.amount,
                            payment2.currency_code
                          ),
                          " - "
                        ] }),
                        (0, import_jsx_runtime.jsx)("span", { children: payment2.provider_id }),
                        (0, import_jsx_runtime.jsxs)("span", { children: [
                          " - (",
                          payment2.id.replace("pay_", ""),
                          ")"
                        ] })
                      ]
                    },
                    payment2.id
                  );
                }) })
              ]
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "amount",
              rules: {
                required: true,
                min: 0,
                max: paymentAmount
              },
              render: ({ field: { onChange, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.amount") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    CurrencyInput,
                    {
                      ...field,
                      min: 0,
                      placeholder: formatValue({
                        value: "0",
                        decimalScale: currency.decimal_digits
                      }),
                      decimalScale: currency.decimal_digits,
                      symbol: currency.symbol_native,
                      code: currency.code,
                      value: field.value,
                      onValueChange: (_value, _name, values) => onChange((values == null ? void 0 : values.value) ? values == null ? void 0 : values.value : ""),
                      autoFocus: true
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `note`,
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.note") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              isLoading: isPending,
              type: "submit",
              variant: "primary",
              size: "small",
              disabled: !!Object.keys(form.formState.errors || {}).length,
              children: t2("actions.save")
            }
          )
        ] }) })
      ]
    }
  ) });
};
var OrderCreateRefund = () => {
  const { t: t2 } = useTranslation();
  const params = useParams();
  const { order } = useOrder(params.id, {
    fields: DEFAULT_FIELDS
  });
  const {
    refund_reasons: refundReasons,
    isLoading: isRefundReasonsLoading,
    isError: isRefundReasonsError,
    error: refundReasonsError
  } = useRefundReasons();
  if (isRefundReasonsError) {
    throw refundReasonsError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("orders.payment.createRefund") }) }),
    order && !isRefundReasonsLoading && refundReasons && (0, import_jsx_runtime2.jsx)(CreateRefundForm, { order, refundReasons })
  ] });
};
export {
  OrderCreateRefund as Component
};
//# sourceMappingURL=order-create-refund-FXUURAS7-3KIQ6O2D.js.map
