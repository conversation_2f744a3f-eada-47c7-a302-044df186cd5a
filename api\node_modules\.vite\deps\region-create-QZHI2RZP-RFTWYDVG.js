import {
  useCountries,
  useCountryTableColumns,
  useCountryTableQuery
} from "./chunk-2VXAS6UY.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import {
  countries
} from "./chunk-XGFC5LFP.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  booleanType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  usePaymentProviders
} from "./chunk-HQL74AJG.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  useCreateRegion
} from "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Heading,
  Input,
  Select,
  Switch,
  Text,
  XMarkMini,
  clx,
  createColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-create-QZHI2RZP.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateRegionSchema = objectType({
  name: stringType().min(1),
  currency_code: stringType().min(2, "Select a currency"),
  automatic_taxes: booleanType(),
  is_tax_inclusive: booleanType(),
  countries: arrayType(objectType({ code: stringType(), name: stringType() })),
  payment_providers: arrayType(stringType()).min(1)
});
var PREFIX = "cr";
var PAGE_SIZE = 50;
var STACKED_MODAL_ID = "countries-modal";
var CreateRegionForm = ({
  currencies: currencies2,
  paymentProviders
}) => {
  const { setIsOpen } = useStackedModal();
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      currency_code: "",
      automatic_taxes: true,
      is_tax_inclusive: false,
      countries: [],
      payment_providers: []
    },
    resolver: t(CreateRegionSchema)
  });
  const selectedCountries = useWatch({
    control: form.control,
    name: "countries",
    defaultValue: []
  });
  const { t: t2 } = useTranslation();
  const { mutateAsync: createRegion, isPending: isPendingRegion } = useCreateRegion();
  const handleSubmit = form.handleSubmit(async (values) => {
    await createRegion(
      {
        name: values.name,
        countries: values.countries.map((c) => c.code),
        currency_code: values.currency_code,
        payment_providers: values.payment_providers,
        automatic_taxes: values.automatic_taxes,
        is_tax_inclusive: values.is_tax_inclusive
      },
      {
        onSuccess: ({ region }) => {
          toast.success(t2("regions.toast.create"));
          handleSuccess(`../${region.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  const { searchParams, raw } = useCountryTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { countries: countries2, count } = useCountries({
    countries: countries.map((c, i) => ({
      display_name: c.display_name,
      name: c.name,
      id: i,
      iso_2: c.iso_2,
      iso_3: c.iso_3,
      num_code: c.num_code,
      region_id: null,
      region: {}
    })),
    ...searchParams
  });
  const columns = useColumns();
  const { table } = useDataTable({
    data: countries2 || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    getRowId: (row) => row.iso_2,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const saveCountries = () => {
    const selected = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );
    form.setValue(
      "countries",
      selected.map((key) => ({
        code: key,
        name: countries.find((c) => c.iso_2 === key).display_name
      })),
      { shouldDirty: true, shouldTouch: true }
    );
    setIsOpen(STACKED_MODAL_ID, false);
  };
  const removeCountry = (code) => {
    const update = selectedCountries.filter((c) => c.code !== code);
    const ids = update.map((c) => c.code).reduce((acc, c) => {
      acc[c] = true;
      return acc;
    }, {});
    form.setValue("countries", update, { shouldDirty: true, shouldTouch: true });
    setRowSelection(ids);
  };
  const clearCountries = () => {
    form.setValue("countries", [], { shouldDirty: true, shouldTouch: true });
    setRowSelection({});
  };
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPendingRegion, children: t2("actions.save") })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex overflow-hidden", children: (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: clx(
              "flex h-full w-full flex-col items-center overflow-y-auto p-16"
            ),
            id: "form-section",
            children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
              (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsx)(Heading, { children: t2("regions.createRegion") }),
                (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("regions.createRegionHint") })
              ] }),
              (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
                (0, import_jsx_runtime.jsx)(
                  Form.Field,
                  {
                    control: form.control,
                    name: "name",
                    render: ({ field }) => {
                      return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                        (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                      ] });
                    }
                  }
                ),
                (0, import_jsx_runtime.jsx)(
                  Form.Field,
                  {
                    control: form.control,
                    name: "currency_code",
                    render: ({ field: { onChange, ref, ...field } }) => {
                      return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.currency") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                          (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                          (0, import_jsx_runtime.jsx)(Select.Content, { children: currencies2.map((currency) => (0, import_jsx_runtime.jsx)(
                            Select.Item,
                            {
                              value: currency.code,
                              children: currency.name
                            },
                            currency.code
                          )) })
                        ] }) }),
                        (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                      ] });
                    }
                  }
                )
              ] }) }),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "automatic_taxes",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsxs)("div", { children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.automaticTaxes") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            ...field,
                            checked: value,
                            onCheckedChange: onChange
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("regions.automaticTaxesHint") }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] }) });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "is_tax_inclusive",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsxs)("div", { children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.taxInclusivePricing") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            ...field,
                            checked: value,
                            onCheckedChange: onChange
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("regions.taxInclusiveHint") }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] }) });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-base h-px w-full" }),
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
                (0, import_jsx_runtime.jsxs)("div", { children: [
                  (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t2("fields.countries") }),
                  (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("regions.countriesHint") })
                ] }),
                selectedCountries.length > 0 && (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-wrap gap-2", children: [
                  selectedCountries.map((country) => (0, import_jsx_runtime.jsx)(
                    CountryTag,
                    {
                      country,
                      onRemove: removeCountry
                    },
                    country.code
                  )),
                  (0, import_jsx_runtime.jsx)(
                    Button,
                    {
                      variant: "transparent",
                      size: "small",
                      className: "text-ui-fg-muted hover:text-ui-fg-subtle",
                      onClick: clearCountries,
                      children: t2("actions.clearAll")
                    }
                  )
                ] }),
                (0, import_jsx_runtime.jsxs)(StackedFocusModal, { id: STACKED_MODAL_ID, children: [
                  (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("regions.addCountries") }) }) }),
                  (0, import_jsx_runtime.jsx)(StackedFocusModal.Content, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full flex-col overflow-hidden", children: [
                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Header, { children: (0, import_jsx_runtime.jsx)(StackedFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)("span", { className: "sr-only", children: t2("regions.addCountries") }) }) }),
                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Body, { className: "overflow-hidden", children: (0, import_jsx_runtime.jsx)(
                      _DataTable,
                      {
                        table,
                        columns,
                        count,
                        pageSize: PAGE_SIZE,
                        orderBy: [
                          { key: "display_name", label: t2("fields.name") },
                          { key: "iso_2", label: t2("fields.code") }
                        ],
                        pagination: true,
                        search: "autofocus",
                        layout: "fill",
                        queryObject: raw,
                        prefix: PREFIX
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
                      (0, import_jsx_runtime.jsx)(StackedFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
                      (0, import_jsx_runtime.jsx)(
                        Button,
                        {
                          size: "small",
                          type: "button",
                          onClick: saveCountries,
                          children: t2("actions.save")
                        }
                      )
                    ] }) })
                  ] }) })
                ] })
              ] }),
              (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-base h-px w-full" }),
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
                (0, import_jsx_runtime.jsxs)("div", { children: [
                  (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t2("fields.providers") }),
                  (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("regions.providersHint") })
                ] }),
                (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: (0, import_jsx_runtime.jsx)(
                  Form.Field,
                  {
                    control: form.control,
                    name: "payment_providers",
                    render: ({ field }) => {
                      return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.paymentProviders") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Combobox,
                          {
                            options: paymentProviders.map((pp) => ({
                              label: formatProvider(pp.id),
                              value: pp.id
                            })),
                            ...field
                          }
                        ) }),
                        (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                      ] });
                    }
                  }
                ) })
              ] })
            ] })
          }
        ) })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCountryTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreselected = !row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected() || isPreselected,
              disabled: isPreselected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var CountryTag = ({
  country,
  onRemove
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-field shadow-borders-base transition-fg hover:bg-ui-bg-field-hover flex h-7 items-center overflow-hidden rounded-md", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "txt-compact-small-plus flex h-full select-none items-center justify-center px-2 py-0.5", children: country.name }),
    (0, import_jsx_runtime.jsx)(
      "button",
      {
        type: "button",
        onClick: () => onRemove(country.code),
        className: "focus-visible:bg-ui-bg-field-hover transition-fg hover:bg-ui-bg-field-hover flex h-full w-7 items-center justify-center border-l outline-none",
        children: (0, import_jsx_runtime.jsx)(XMarkMini, { className: "text-ui-fg-muted" })
      }
    )
  ] });
};
var RegionCreate = () => {
  const { store, isPending: isLoading, isError, error } = useStore();
  const storeCurrencies = ((store == null ? void 0 : store.supported_currencies) ?? []).map(
    (c) => currencies[c.currency_code.toUpperCase()]
  );
  const { payment_providers: paymentProviders = [] } = usePaymentProviders({
    is_enabled: true
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: !isLoading && store && (0, import_jsx_runtime2.jsx)(
    CreateRegionForm,
    {
      currencies: storeCurrencies,
      paymentProviders
    }
  ) });
};
export {
  RegionCreate as Component
};
//# sourceMappingURL=region-create-QZHI2RZP-RFTWYDVG.js.map
