import {
  salesChannelsQuery<PERSON>eys
} from "./chunk-3H6LL6QL.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-F6IJV2I2.mjs
var API_KEYS_QUERY_KEY = "api_keys";
var apiKeysQueryKeys = queryKeysFactory(API_KEYS_QUERY_KEY);
var useApiKey = (id, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.apiKey.retrieve(id),
    queryKey: apiKeysQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var useApiKeys = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.apiKey.list(query),
    queryKey: apiKeysQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateApiKey = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.apiKey.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateApiKey = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.apiKey.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRevokeApiKey = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.apiKey.revoke(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    }
  });
};
var useDeleteApiKey = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.apiKey.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    }
  });
};
var useBatchRemoveSalesChannelsFromApiKey = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.apiKey.batchSalesChannels(id, { remove: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useBatchAddSalesChannelsToApiKey = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.apiKey.batchSalesChannels(id, { add: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  apiKeysQueryKeys,
  useApiKey,
  useApiKeys,
  useCreateApiKey,
  useUpdateApiKey,
  useRevokeApiKey,
  useDeleteApiKey,
  useBatchRemoveSalesChannelsFromApiKey,
  useBatchAddSalesChannelsToApiKey
};
//# sourceMappingURL=chunk-RX237AWS.js.map
