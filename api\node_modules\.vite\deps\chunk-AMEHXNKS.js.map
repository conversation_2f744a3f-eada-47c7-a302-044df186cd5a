{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-S22NYSST.mjs"], "sourcesContent": ["import {\n  useDeleteProductType\n} from \"./chunk-B4GODIOW.mjs\";\n\n// src/routes/product-types/common/hooks/use-delete-product-type-action.tsx\nimport { useNavigate } from \"react-router-dom\";\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nvar useDeleteProductTypeAction = (id, value) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteProductType(id);\n  const handleDelete = async () => {\n    const result = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"productTypes.delete.confirmation\", { value }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!result) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        navigate(\"/settings/product-types\", { replace: true });\n        toast.success(t(\"productTypes.delete.successToast\", { value }));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\nexport {\n  useDeleteProductTypeAction\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAI,6BAA6B,CAAC,IAAI,UAAU;AAC9C,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,qBAAqB,EAAE;AAC/C,QAAM,eAAe,YAAY;AAC/B,UAAM,SAAS,MAAM,OAAO;AAAA,MAC1B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,oCAAoC,EAAE,MAAM,CAAC;AAAA,MAC5D,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,iBAAS,2BAA2B,EAAE,SAAS,KAAK,CAAC;AACrD,cAAM,QAAQ,EAAE,oCAAoC,EAAE,MAAM,CAAC,CAAC;AAAA,MAChE;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": []}