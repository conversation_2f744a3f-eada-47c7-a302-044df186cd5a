{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3OHUAQUF.mjs"], "sourcesContent": ["import {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  useDate\n} from \"./chunk-Q5PHSNDY.mjs\";\n\n// src/components/table/table-cells/common/date-cell/date-cell.tsx\nimport { Tooltip } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DateCell = ({ date }) => {\n  const { getFullDate } = useDate();\n  if (!date) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx(\n    Tooltip,\n    {\n      className: \"z-10\",\n      content: /* @__PURE__ */ jsx(\"span\", { className: \"text-pretty\", children: `${getFullDate({\n        date,\n        includeTime: true\n      })}` }),\n      children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: getFullDate({ date, includeTime: false }) })\n    }\n  ) });\n};\nvar DateHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: t(\"fields.date\") }) });\n};\n\nexport {\n  DateCell,\n  DateHeader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAUA,yBAAoB;AACpB,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM;AAC3B,QAAM,EAAE,YAAY,IAAI,QAAQ;AAChC,MAAI,CAAC,MAAM;AACT,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,mDAAmD,cAA0B;AAAA,IAC1H;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,aAAyB,wBAAI,QAAQ,EAAE,WAAW,eAAe,UAAU,GAAG,YAAY;AAAA,QACxF;AAAA,QACA,aAAa;AAAA,MACf,CAAC,CAAC,GAAG,CAAC;AAAA,MACN,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,YAAY,EAAE,MAAM,aAAa,MAAM,CAAC,EAAE,CAAC;AAAA,IACtH;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AAClL;", "names": []}