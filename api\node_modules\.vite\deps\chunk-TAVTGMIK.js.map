{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-F6ZOHZVB.mjs"], "sourcesContent": ["// src/components/utilities/visually-hidden/visually-hidden.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar VisuallyHidden = ({ children }) => {\n  return /* @__PURE__ */ jsx(\"span\", { className: \"sr-only\", children });\n};\n\nexport {\n  VisuallyHidden\n};\n"], "mappings": ";;;;;;;;AACA,yBAAoB;AACpB,IAAI,iBAAiB,CAAC,EAAE,SAAS,MAAM;AACrC,aAAuB,wBAAI,QAAQ,EAAE,WAAW,WAAW,SAAS,CAAC;AACvE;", "names": []}