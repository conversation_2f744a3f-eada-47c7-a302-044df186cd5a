import {
  <PERSON><PERSON><PERSON>,
  ProductHeader
} from "./chunk-EUULPFXI.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  <PERSON><PERSON><PERSON>,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-G3QXMPRB.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var CollectionCell = ({ collection }) => {
  if (!collection) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: collection.title }) });
};
var CollectionHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { children: t("fields.collection") }) });
};
var ProductStatusCell = ({ status }) => {
  const { t } = useTranslation();
  const [color, text] = {
    draft: ["grey", t("products.productStatus.draft")],
    proposed: ["orange", t("products.productStatus.proposed")],
    published: ["green", t("products.productStatus.published")],
    rejected: ["red", t("products.productStatus.rejected")]
  }[status];
  return (0, import_jsx_runtime2.jsx)(StatusCell, { color, children: text });
};
var ProductStatusHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { children: t("fields.status") }) });
};
var SalesChannelsCell = ({
  salesChannels
}) => {
  const { t } = useTranslation();
  if (!salesChannels || !salesChannels.length) {
    return (0, import_jsx_runtime3.jsx)(PlaceholderCell, {});
  }
  if (salesChannels.length > 2) {
    return (0, import_jsx_runtime3.jsxs)("div", { className: "flex h-full w-full items-center gap-x-1 overflow-hidden", children: [
      (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: salesChannels.slice(0, 2).map((sc) => sc.name).join(", ") }),
      (0, import_jsx_runtime3.jsx)(
        Tooltip,
        {
          content: (0, import_jsx_runtime3.jsx)("ul", { children: salesChannels.slice(2).map((sc) => (0, import_jsx_runtime3.jsx)("li", { children: sc.name }, sc.id)) }),
          children: (0, import_jsx_runtime3.jsx)("span", { className: "text-xs", children: t("general.plusCountMore", {
            count: salesChannels.length - 2
          }) })
        }
      )
    ] });
  }
  const channels = salesChannels.map((sc) => sc.name).join(", ");
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden max-w-[250px]", children: (0, import_jsx_runtime3.jsx)("span", { title: channels, className: "truncate", children: channels }) });
};
var SalesChannelHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime3.jsx)("span", { children: t("fields.salesChannels") }) });
};
var VariantCell = ({ variants }) => {
  const { t } = useTranslation();
  if (!variants || !variants.length) {
    return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime4.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: t("products.variantCount", { count: variants.length }) }) });
};
var VariantHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime4.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime4.jsx)("span", { children: t("fields.variants") }) });
};
var columnHelper = createColumnHelper();
var useProductTableColumns = () => {
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "product",
        header: () => (0, import_jsx_runtime5.jsx)(ProductHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(ProductCell, { product: row.original })
      }),
      columnHelper.accessor("collection", {
        header: () => (0, import_jsx_runtime5.jsx)(CollectionHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(CollectionCell, { collection: row.original.collection })
      }),
      columnHelper.accessor("sales_channels", {
        header: () => (0, import_jsx_runtime5.jsx)(SalesChannelHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(SalesChannelsCell, { salesChannels: row.original.sales_channels })
      }),
      columnHelper.accessor("variants", {
        header: () => (0, import_jsx_runtime5.jsx)(VariantHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(VariantCell, { variants: row.original.variants })
      }),
      columnHelper.accessor("status", {
        header: () => (0, import_jsx_runtime5.jsx)(ProductStatusHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(ProductStatusCell, { status: row.original.status })
      })
    ],
    []
  );
};

export {
  useProductTableColumns
};
//# sourceMappingURL=chunk-MWHYWZYO.js.map
