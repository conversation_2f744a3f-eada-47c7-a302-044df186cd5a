{"version": 3, "sources": ["../../@medusajs/dashboard/dist/category-list-VKQRZO2Z.mjs"], "sourcesContent": ["import {\n  Text<PERSON><PERSON>,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  useDeleteProductCategoryAction\n} from \"./chunk-TP7N4YBP.mjs\";\nimport {\n  getCategoryPath,\n  getIsActiveProps,\n  getIsInternalProps\n} from \"./chunk-54IEHX46.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  useProductCategories\n} from \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/categories/category-list/components/category-list-table/category-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading, Text as Text2 } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper as createColumnHelper2 } from \"@tanstack/react-table\";\nimport { useMemo as useMemo2 } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/categories/category-list/components/category-list-table/use-category-table-columns.tsx\nimport { TriangleRightMini } from \"@medusajs/icons\";\nimport { IconButton, Text, clx } from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCategoryTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.name\") }),\n        cell: ({ getValue, row }) => {\n          const expandHandler = row.getToggleExpandedHandler();\n          if (row.original.parent_category !== void 0) {\n            const path = getCategoryPath(row.original);\n            return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full items-center gap-1 overflow-hidden\", children: path.map((chip, index) => /* @__PURE__ */ jsxs(\n              \"div\",\n              {\n                className: clx(\"overflow-hidden\", {\n                  \"text-ui-fg-muted flex items-center gap-x-1\": index !== path.length - 1\n                }),\n                children: [\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", className: \"truncate\", children: chip.name }),\n                  index !== path.length - 1 && /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: \"/\" })\n                ]\n              },\n              chip.id\n            )) });\n          }\n          return /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full items-center gap-x-3 overflow-hidden\", children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"flex size-7 items-center justify-center\", children: row.getCanExpand() ? /* @__PURE__ */ jsx(\n              IconButton,\n              {\n                type: \"button\",\n                onClick: (e) => {\n                  e.stopPropagation();\n                  e.preventDefault();\n                  expandHandler();\n                },\n                size: \"small\",\n                variant: \"transparent\",\n                className: \"text-ui-fg-subtle\",\n                children: /* @__PURE__ */ jsx(\n                  TriangleRightMini,\n                  {\n                    className: clx({\n                      \"rotate-90 transition-transform will-change-transform\": row.getIsExpanded()\n                    })\n                  }\n                )\n              }\n            ) : null }),\n            /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: getValue() })\n          ] });\n        }\n      }),\n      columnHelper.accessor(\"handle\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.handle\") }),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx(TextCell, { text: `/${getValue()}` });\n        }\n      }),\n      columnHelper.accessor(\"is_active\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.status\") }),\n        cell: ({ getValue }) => {\n          const { color, label } = getIsActiveProps(getValue(), t);\n          return /* @__PURE__ */ jsx(StatusCell, { color, children: label });\n        }\n      }),\n      columnHelper.accessor(\"is_internal\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"categories.fields.visibility.label\") }),\n        cell: ({ getValue }) => {\n          const { color, label } = getIsInternalProps(getValue(), t);\n          return /* @__PURE__ */ jsx(StatusCell, { color, children: label });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/categories/category-list/components/category-list-table/use-category-table-query.tsx\nvar useCategoryTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams([\"q\", \"offset\", \"order\"], prefix);\n  const searchParams = {\n    q: raw.q,\n    limit: pageSize,\n    offset: raw.offset ? Number(raw.offset) : 0,\n    order: raw.order\n  };\n  return {\n    raw,\n    searchParams\n  };\n};\n\n// src/routes/categories/category-list/components/category-list-table/category-list-table.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar CategoryListTable = () => {\n  const { t } = useTranslation2();\n  const { raw, searchParams } = useCategoryTableQuery({ pageSize: PAGE_SIZE });\n  const query = raw.q ? {\n    include_ancestors_tree: true,\n    fields: \"id,name,handle,is_active,is_internal,parent_category\",\n    ...searchParams\n  } : {\n    include_descendants_tree: true,\n    parent_category_id: \"null\",\n    fields: \"id,name,category_children,handle,is_internal,is_active\",\n    ...searchParams\n  };\n  const { product_categories, count, isLoading, isError, error } = useProductCategories(\n    {\n      ...query\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: product_categories || [],\n    columns,\n    count,\n    getRowId: (original) => original.id,\n    getSubRows: (original) => original.category_children,\n    enableExpandableRows: true,\n    pageSize: PAGE_SIZE\n  });\n  const showRankingAction = !!product_categories && product_categories.length > 0;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { children: [\n        /* @__PURE__ */ jsx2(Heading, { children: t(\"categories.domain\") }),\n        /* @__PURE__ */ jsx2(Text2, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"categories.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        showRankingAction && /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx2(Link, { to: \"organize\", children: t(\"categories.organize.action\") }) }),\n        /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx2(Link, { to: \"create\", children: t(\"actions.create\") }) })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        isLoading,\n        navigateTo: (row) => row.id,\n        queryObject: raw,\n        search: true,\n        pagination: true\n      }\n    )\n  ] });\n};\nvar CategoryRowActions = ({\n  category\n}) => {\n  const { t } = useTranslation2();\n  const handleDelete = useDeleteProductCategoryAction(category);\n  return /* @__PURE__ */ jsx2(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n              to: `${category.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              icon: /* @__PURE__ */ jsx2(Trash, {}),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper2 = createColumnHelper2();\nvar useColumns = () => {\n  const base = useCategoryTableColumns();\n  return useMemo2(\n    () => [\n      ...base,\n      columnHelper2.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx2(CategoryRowActions, { category: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/categories/category-list/category-list.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar CategoryList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_category.list.after\"),\n        before: getWidgets(\"product_category.list.before\")\n      },\n      hasOutlet: true,\n      children: /* @__PURE__ */ jsx3(CategoryListTable, {})\n    }\n  );\n};\nexport {\n  CategoryList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA,mBAAoC;AAQpC,IAAAA,gBAAwB;AAExB,yBAA0B;AAiG1B,IAAAC,sBAA2C;AAiH3C,IAAAA,sBAA4B;AAjN5B,IAAI,eAAe,mBAAmB;AACtC,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,QACxE,MAAM,CAAC,EAAE,UAAU,IAAI,MAAM;AAC3B,gBAAM,gBAAgB,IAAI,yBAAyB;AACnD,cAAI,IAAI,SAAS,oBAAoB,QAAQ;AAC3C,kBAAM,OAAO,gBAAgB,IAAI,QAAQ;AACzC,uBAAuB,wBAAI,OAAO,EAAE,WAAW,qDAAqD,UAAU,KAAK,IAAI,CAAC,MAAM,cAA0B;AAAA,cACtJ;AAAA,cACA;AAAA,gBACE,WAAW,IAAI,mBAAmB;AAAA,kBAChC,8CAA8C,UAAU,KAAK,SAAS;AAAA,gBACxE,CAAC;AAAA,gBACD,UAAU;AAAA,sBACQ,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,YAAY,UAAU,KAAK,KAAK,CAAC;AAAA,kBAC3G,UAAU,KAAK,SAAS,SAAqB,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,IAAI,CAAC;AAAA,gBAC7G;AAAA,cACF;AAAA,cACA,KAAK;AAAA,YACP,CAAC,EAAE,CAAC;AAAA,UACN;AACA,qBAAuB,yBAAK,OAAO,EAAE,WAAW,uDAAuD,UAAU;AAAA,gBAC/F,wBAAI,OAAO,EAAE,WAAW,2CAA2C,UAAU,IAAI,aAAa,QAAoB;AAAA,cAChI;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,SAAS,CAAC,MAAM;AACd,oBAAE,gBAAgB;AAClB,oBAAE,eAAe;AACjB,gCAAc;AAAA,gBAChB;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,cAA0B;AAAA,kBACxB;AAAA,kBACA;AAAA,oBACE,WAAW,IAAI;AAAA,sBACb,wDAAwD,IAAI,cAAc;AAAA,oBAC5E,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF;AAAA,YACF,IAAI,KAAK,CAAC;AAAA,gBACM,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,SAAS,EAAE,CAAC;AAAA,UAC7E,EAAE,CAAC;AAAA,QACL;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,UAAU;AAAA,QAC9B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;AAAA,QAC1E,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,wBAAI,UAAU,EAAE,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;AAAA,QAC1E,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,EAAE,OAAO,MAAM,IAAI,iBAAiB,SAAS,GAAG,CAAC;AACvD,qBAAuB,wBAAI,YAAY,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,QACnE;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,oCAAoC,EAAE,CAAC;AAAA,QAC/F,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,EAAE,OAAO,MAAM,IAAI,mBAAmB,SAAS,GAAG,CAAC;AACzD,qBAAuB,wBAAI,YAAY,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,QACnE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,wBAAwB,CAAC;AAAA,EAC3B,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM,eAAe,CAAC,KAAK,UAAU,OAAO,GAAG,MAAM;AAC3D,QAAM,eAAe;AAAA,IACnB,GAAG,IAAI;AAAA,IACP,OAAO;AAAA,IACP,QAAQ,IAAI,SAAS,OAAO,IAAI,MAAM,IAAI;AAAA,IAC1C,OAAO,IAAI;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,KAAK,aAAa,IAAI,sBAAsB,EAAE,UAAU,UAAU,CAAC;AAC3E,QAAM,QAAQ,IAAI,IAAI;AAAA,IACpB,wBAAwB;AAAA,IACxB,QAAQ;AAAA,IACR,GAAG;AAAA,EACL,IAAI;AAAA,IACF,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,GAAG;AAAA,EACL;AACA,QAAM,EAAE,oBAAoB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC/D;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,sBAAsB,CAAC;AAAA,IAC7B;AAAA,IACA;AAAA,IACA,UAAU,CAAC,aAAa,SAAS;AAAA,IACjC,YAAY,CAAC,aAAa,SAAS;AAAA,IACnC,sBAAsB;AAAA,IACtB,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,oBAAoB,CAAC,CAAC,sBAAsB,mBAAmB,SAAS;AAC9E,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC7D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAA,MAAM,OAAO,EAAE,UAAU;AAAA,YACvB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,mBAAmB,EAAE,CAAC;AAAA,YAClD,oBAAAA,KAAK,MAAO,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,MACnH,EAAE,CAAC;AAAA,UACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QAC/E,yBAAqC,oBAAAC,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,YAAY,UAAU,EAAE,4BAA4B,EAAE,CAAC,EAAE,CAAC;AAAA,YAC7L,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,MAC5K,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,QAAQ,IAAI;AAAA,QACzB,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,qBAAqB,CAAC;AAAA,EACxB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe,+BAA+B,QAAQ;AAC5D,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,IAAI,GAAG,SAAS,EAAE;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,cACpC,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,mBAAoB;AACxC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,wBAAwB;AACrC,aAAO,aAAAC;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,cAAc,QAAQ;AAAA,QACpB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAD,KAAK,oBAAoB,EAAE,UAAU,IAAI,SAAS,CAAC;AAAA,QAC5E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAE;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,6BAA6B;AAAA,QAC/C,QAAQ,WAAW,8BAA8B;AAAA,MACnD;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AACF;", "names": ["import_react", "import_jsx_runtime", "jsxs2", "jsx2", "useMemo2", "jsx3"]}