{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-CF64SRBE.mjs"], "sourcesContent": ["import {\n  isProductRow\n} from \"./chunk-G2J2T2QU.mjs\";\nimport {\n  DataGrid,\n  createDataGridHelper,\n  createDataGridPriceColumns\n} from \"./chunk-GE4APTT2.mjs\";\nimport {\n  Thumbnail\n} from \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport {\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\n\n// src/routes/price-lists/common/hooks/use-price-list-currency-data.tsx\nvar usePriceListCurrencyData = () => {\n  const {\n    store,\n    isPending: isStorePending,\n    isError: isStoreError,\n    error: storeError\n  } = useStore({\n    fields: \"+supported_currencies\"\n  });\n  const currencies = store?.supported_currencies;\n  const {\n    regions,\n    isPending: isRegionsPending,\n    isError: isRegionsError,\n    error: regionsError\n  } = useRegions({\n    fields: \"id,name,currency_code\",\n    limit: 999\n  });\n  const {\n    price_preferences: pricePreferences,\n    isPending: isPreferencesPending,\n    isError: isPreferencesError,\n    error: preferencesError\n  } = usePricePreferences({});\n  const isReady = !!currencies && !!regions && !!pricePreferences && !isStorePending && !isRegionsPending && !isPreferencesPending;\n  if (isRegionsError) {\n    throw regionsError;\n  }\n  if (isStoreError) {\n    throw storeError;\n  }\n  if (isPreferencesError) {\n    throw preferencesError;\n  }\n  if (!isReady) {\n    return {\n      regions: void 0,\n      currencies: void 0,\n      pricePreferences: void 0,\n      isReady: false\n    };\n  }\n  return { regions, currencies, pricePreferences, isReady };\n};\n\n// src/routes/price-lists/common/hooks/use-price-list-grid-columns.tsx\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar columnHelper = createDataGridHelper();\nvar usePriceListGridColumns = ({\n  currencies = [],\n  regions = [],\n  pricePreferences = []\n}) => {\n  const { t } = useTranslation();\n  const colDefs = useMemo(() => {\n    return [\n      columnHelper.column({\n        id: t(\"fields.title\"),\n        header: t(\"fields.title\"),\n        cell: (context) => {\n          const entity = context.row.original;\n          if (isProductRow(entity)) {\n            return /* @__PURE__ */ jsx(DataGrid.ReadonlyCell, { context, children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full w-full items-center gap-x-2 overflow-hidden\", children: [\n              /* @__PURE__ */ jsx(Thumbnail, { src: entity.thumbnail, size: \"small\" }),\n              /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: entity.title })\n            ] }) });\n          }\n          return /* @__PURE__ */ jsx(DataGrid.ReadonlyCell, { context, color: \"normal\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center gap-x-2 overflow-hidden\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: entity.title }) }) });\n        },\n        disableHiding: true\n      }),\n      ...createDataGridPriceColumns({\n        currencies: currencies.map((c) => c.currency_code),\n        regions,\n        pricePreferences,\n        isReadyOnly: (context) => {\n          const entity = context.row.original;\n          return isProductRow(entity);\n        },\n        getFieldName: (context, value) => {\n          const entity = context.row.original;\n          if (isProductRow(entity)) {\n            return null;\n          }\n          if (context.column.id?.startsWith(\"currency_prices\")) {\n            return `products.${entity.product_id}.variants.${entity.id}.currency_prices.${value}.amount`;\n          }\n          return `products.${entity.product_id}.variants.${entity.id}.region_prices.${value}.amount`;\n        },\n        t\n      })\n    ];\n  }, [t, currencies, regions, pricePreferences]);\n  return colDefs;\n};\n\n// src/routes/price-lists/common/schemas.ts\nimport { z } from \"zod\";\nvar PriceListCustomerGroupSchema = z.object({\n  id: z.string(),\n  name: z.string()\n});\nvar PriceListRulesSchema = z.object({\n  customer_group_id: z.array(PriceListCustomerGroupSchema).nullish()\n});\nvar PriceListCreateCurrencyPriceSchema = z.object({\n  amount: z.string().or(z.number()).optional()\n});\nvar PriceListCreateRegionPriceSchema = z.object({\n  amount: z.string().or(z.number()).optional()\n});\nvar PriceListCreateProductVariantSchema = z.object({\n  currency_prices: z.record(PriceListCreateCurrencyPriceSchema.optional()),\n  region_prices: z.record(PriceListCreateRegionPriceSchema.optional())\n});\nvar PriceListCreateProductVariantsSchema = z.record(\n  PriceListCreateProductVariantSchema\n);\nvar PriceListCreateProductsSchema = z.record(\n  z.object({\n    variants: PriceListCreateProductVariantsSchema\n  })\n);\nvar PriceListUpdateCurrencyPriceSchema = z.object({\n  amount: z.string().or(z.number()).optional(),\n  id: z.string().nullish()\n});\nvar PriceListUpdateRegionPriceSchema = z.object({\n  amount: z.string().or(z.number()).optional(),\n  id: z.string().nullish()\n});\nvar PriceListUpdateProductVariantsSchema = z.record(\n  z.object({\n    currency_prices: z.record(PriceListUpdateCurrencyPriceSchema.optional()),\n    region_prices: z.record(PriceListUpdateRegionPriceSchema.optional())\n  })\n);\nvar PriceListUpdateProductsSchema = z.record(\n  z.object({\n    variants: PriceListUpdateProductVariantsSchema\n  })\n);\n\nexport {\n  usePriceListCurrencyData,\n  usePriceListGridColumns,\n  PriceListRulesSchema,\n  PriceListCreateProductsSchema,\n  PriceListUpdateProductsSchema\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,mBAAwB;AAExB,yBAA0B;AAjD1B,IAAI,2BAA2B,MAAM;AACnC,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,SAAS;AAAA,IACX,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,aAAa,+BAAO;AAC1B,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,WAAW;AAAA,IACb,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,CAAC;AACD,QAAM;AAAA,IACJ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,oBAAoB,CAAC,CAAC;AAC1B,QAAM,UAAU,CAAC,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,oBAAoB,CAAC;AAC5G,MAAI,gBAAgB;AAClB,UAAM;AAAA,EACR;AACA,MAAI,cAAc;AAChB,UAAM;AAAA,EACR;AACA,MAAI,oBAAoB;AACtB,UAAM;AAAA,EACR;AACA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,MACL,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,SAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO,EAAE,SAAS,YAAY,kBAAkB,QAAQ;AAC1D;AAMA,IAAI,eAAe,qBAAqB;AACxC,IAAI,0BAA0B,CAAC;AAAA,EAC7B,aAAa,CAAC;AAAA,EACd,UAAU,CAAC;AAAA,EACX,mBAAmB,CAAC;AACtB,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAU,sBAAQ,MAAM;AAC5B,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,QAClB,IAAI,EAAE,cAAc;AAAA,QACpB,QAAQ,EAAE,cAAc;AAAA,QACxB,MAAM,CAAC,YAAY;AACjB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,cAAI,aAAa,MAAM,GAAG;AACxB,uBAAuB,wBAAI,SAAS,cAAc,EAAE,SAAS,cAA0B,yBAAK,OAAO,EAAE,WAAW,2DAA2D,UAAU;AAAA,kBACnK,wBAAI,WAAW,EAAE,KAAK,OAAO,WAAW,MAAM,QAAQ,CAAC;AAAA,kBACvD,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,OAAO,MAAM,CAAC;AAAA,YAC/E,EAAE,CAAC,EAAE,CAAC;AAAA,UACR;AACA,qBAAuB,wBAAI,SAAS,cAAc,EAAE,SAAS,OAAO,UAAU,cAA0B,wBAAI,OAAO,EAAE,WAAW,2DAA2D,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QAC1R;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,MACD,GAAG,2BAA2B;AAAA,QAC5B,YAAY,WAAW,IAAI,CAAC,MAAM,EAAE,aAAa;AAAA,QACjD;AAAA,QACA;AAAA,QACA,aAAa,CAAC,YAAY;AACxB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,iBAAO,aAAa,MAAM;AAAA,QAC5B;AAAA,QACA,cAAc,CAAC,SAAS,UAAU;AAxG1C;AAyGU,gBAAM,SAAS,QAAQ,IAAI;AAC3B,cAAI,aAAa,MAAM,GAAG;AACxB,mBAAO;AAAA,UACT;AACA,eAAI,aAAQ,OAAO,OAAf,mBAAmB,WAAW,oBAAoB;AACpD,mBAAO,YAAY,OAAO,UAAU,aAAa,OAAO,EAAE,oBAAoB,KAAK;AAAA,UACrF;AACA,iBAAO,YAAY,OAAO,UAAU,aAAa,OAAO,EAAE,kBAAkB,KAAK;AAAA,QACnF;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,GAAG,YAAY,SAAS,gBAAgB,CAAC;AAC7C,SAAO;AACT;AAIA,IAAI,+BAA+B,EAAE,OAAO;AAAA,EAC1C,IAAI,EAAE,OAAO;AAAA,EACb,MAAM,EAAE,OAAO;AACjB,CAAC;AACD,IAAI,uBAAuB,EAAE,OAAO;AAAA,EAClC,mBAAmB,EAAE,MAAM,4BAA4B,EAAE,QAAQ;AACnE,CAAC;AACD,IAAI,qCAAqC,EAAE,OAAO;AAAA,EAChD,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS;AAC7C,CAAC;AACD,IAAI,mCAAmC,EAAE,OAAO;AAAA,EAC9C,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS;AAC7C,CAAC;AACD,IAAI,sCAAsC,EAAE,OAAO;AAAA,EACjD,iBAAiB,EAAE,OAAO,mCAAmC,SAAS,CAAC;AAAA,EACvE,eAAe,EAAE,OAAO,iCAAiC,SAAS,CAAC;AACrE,CAAC;AACD,IAAI,uCAAuC,EAAE;AAAA,EAC3C;AACF;AACA,IAAI,gCAAgC,EAAE;AAAA,EACpC,EAAE,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACH;AACA,IAAI,qCAAqC,EAAE,OAAO;AAAA,EAChD,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC3C,IAAI,EAAE,OAAO,EAAE,QAAQ;AACzB,CAAC;AACD,IAAI,mCAAmC,EAAE,OAAO;AAAA,EAC9C,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC3C,IAAI,EAAE,OAAO,EAAE,QAAQ;AACzB,CAAC;AACD,IAAI,uCAAuC,EAAE;AAAA,EAC3C,EAAE,OAAO;AAAA,IACP,iBAAiB,EAAE,OAAO,mCAAmC,SAAS,CAAC;AAAA,IACvE,eAAe,EAAE,OAAO,iCAAiC,SAAS,CAAC;AAAA,EACrE,CAAC;AACH;AACA,IAAI,gCAAgC,EAAE;AAAA,EACpC,EAAE,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACH;", "names": []}