{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-sales-channels-D3EBY5FT.mjs"], "sourcesContent": ["import {\n  useSalesChannelTableColumns,\n  useSalesChannelTableEmptyState,\n  useSalesChannelTableFilters,\n  useSalesChannelTableQuery\n} from \"./chunk-44QN6VEG.mjs\";\nimport \"./chunk-4BTG27L5.mjs\";\nimport {\n  DataTable\n} from \"./chunk-3IIOXMXN.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useProduct,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-sales-channels/product-sales-channels.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-sales-channels/components/edit-sales-channels-form/edit-sales-channels-form.tsx\nimport { Button, createDataTableColumnHelper } from \"@medusajs/ui\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useForm } from \"react-hook-form\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditSalesChannelsSchema = zod.object({\n  sales_channels: zod.array(zod.string()).optional()\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"sc\";\nvar EditSalesChannelsForm = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      sales_channels: product.sales_channels?.map((sc) => sc.id) ?? []\n    },\n    resolver: zodResolver(EditSalesChannelsSchema)\n  });\n  const { setValue } = form;\n  const initialState = product.sales_channels?.reduce((acc, curr) => {\n    acc[curr.id] = true;\n    return acc;\n  }, {}) ?? {};\n  const [rowSelection, setRowSelection] = useState(initialState);\n  useEffect(() => {\n    const ids = Object.keys(rowSelection);\n    setValue(\"sales_channels\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n  }, [rowSelection, setValue]);\n  const searchParams = useSalesChannelTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { sales_channels, count, isLoading, isError, error } = useSalesChannels(\n    {\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useSalesChannelTableFilters();\n  const emptyState = useSalesChannelTableEmptyState();\n  const columns = useColumns();\n  const { mutateAsync, isPending: isMutating } = useUpdateProduct(product.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const arr = data.sales_channels ?? [];\n    const sales_channels2 = arr.map((id) => {\n      return {\n        id\n      };\n    });\n    await mutateAsync(\n      {\n        sales_channels: sales_channels2\n      },\n      {\n        onSuccess: () => {\n          handleSuccess();\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex-1 overflow-hidden\", children: /* @__PURE__ */ jsx(\n      DataTable,\n      {\n        data: sales_channels,\n        columns,\n        getRowId: (row) => row.id,\n        rowCount: count,\n        isLoading,\n        filters,\n        rowSelection: {\n          state: rowSelection,\n          onRowSelectionChange: setRowSelection\n        },\n        autoFocusSearch: true,\n        layout: \"fill\",\n        emptyState,\n        prefix: PREFIX\n      }\n    ) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", isLoading: isMutating, onClick: handleSubmit, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const columns = useSalesChannelTableColumns();\n  return useMemo(() => [columnHelper.select(), ...columns], [columns]);\n};\n\n// src/routes/products/product-sales-channels/product-sales-channels.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProductSalesChannels = () => {\n  const { id } = useParams();\n  const { product, isLoading, isError, error } = useProduct(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && product && /* @__PURE__ */ jsx2(EditSalesChannelsForm, { product }) });\n};\nexport {\n  ProductSalesChannels as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,mBAA6C;AAM7C,yBAA0B;AAqG1B,IAAAA,sBAA4B;AApG5B,IAAI,0BAA8B,WAAO;AAAA,EACvC,gBAAoB,UAAU,WAAO,CAAC,EAAE,SAAS;AACnD,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AAnDN;AAoDE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,kBAAgB,aAAQ,mBAAR,mBAAwB,IAAI,CAAC,OAAO,GAAG,QAAO,CAAC;AAAA,IACjE;AAAA,IACA,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,iBAAe,aAAQ,mBAAR,mBAAwB,OAAO,CAAC,KAAK,SAAS;AACjE,QAAI,KAAK,EAAE,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,OAAM,CAAC;AACX,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,YAAY;AAC7D,8BAAU,MAAM;AACd,UAAM,MAAM,OAAO,KAAK,YAAY;AACpC,aAAS,kBAAkB,KAAK;AAAA,MAC9B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AAAA,EACH,GAAG,CAAC,cAAc,QAAQ,CAAC;AAC3B,QAAM,eAAe,0BAA0B;AAAA,IAC7C,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,gBAAgB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC3D;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,4BAA4B;AAC5C,QAAM,aAAa,+BAA+B;AAClD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,iBAAiB,QAAQ,EAAE;AAC1E,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,MAAM,KAAK,kBAAkB,CAAC;AACpC,UAAM,kBAAkB,IAAI,IAAI,CAAC,OAAO;AACtC,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,QACE,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU;AAAA,QAC5I,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,QAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,0BAA0B,cAA0B;AAAA,MACzG;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN;AAAA,QACA,UAAU,CAAC,QAAQ,IAAI;AAAA,QACvB,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,cAAc;AAAA,UACZ,OAAO;AAAA,UACP,sBAAsB;AAAA,QACxB;AAAA,QACA,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,WAAW,YAAY,SAAS,cAAc,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAC1H,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,UAAU,4BAA4B;AAC5C,aAAO,sBAAQ,MAAM,CAAC,aAAa,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC;AACrE;AAIA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,EAAE;AAC5D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,eAA2B,oBAAAA,KAAK,uBAAuB,EAAE,QAAQ,CAAC,EAAE,CAAC;AAC9I;", "names": ["import_jsx_runtime", "t", "jsx2"]}