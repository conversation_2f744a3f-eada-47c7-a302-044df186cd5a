{"version": 3, "sources": ["../../@medusajs/dashboard/dist/shipping-profile-metadata-UB73OBPJ.mjs"], "sourcesContent": ["import {\n  MetadataForm\n} from \"./chunk-AL4WDQTN.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport {\n  useShippingProfile,\n  useUpdateShippingProfile\n} from \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/shipping-profiles/shipping-profile-metadata/shipping-profile-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ShippingProfileMetadata = () => {\n  const { shipping_profile_id } = useParams();\n  const { shipping_profile, isPending, isError, error } = useShippingProfile(\n    shipping_profile_id\n  );\n  const { mutateAsync, isPending: isMutating } = useUpdateShippingProfile(\n    shipping_profile?.id\n  );\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      metadata: shipping_profile?.metadata,\n      hook: mutateAsync,\n      isPending,\n      isMutating\n    }\n  );\n};\nexport {\n  ShippingProfileMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,yBAAoB;AACpB,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,oBAAoB,IAAI,UAAU;AAC1C,QAAM,EAAE,kBAAkB,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,EACF;AACA,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI;AAAA,IAC7C,qDAAkB;AAAA,EACpB;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,UAAU,qDAAkB;AAAA,MAC5B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}