{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LFLGEXIG.mjs"], "sourcesContent": ["// src/components/common/section/section-row.tsx\nimport { Text, clx } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SectionRow = ({ title, value, actions }) => {\n  const isValueString = typeof value === \"string\" || !value;\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        `text-ui-fg-subtle grid w-full grid-cols-2 items-center gap-4 px-6 py-4`,\n        {\n          \"grid-cols-[1fr_1fr_28px]\": !!actions\n        }\n      ),\n      children: [\n        /* @__PURE__ */ jsx(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: title }),\n        isValueString ? /* @__PURE__ */ jsx(\n          Text,\n          {\n            size: \"small\",\n            leading: \"compact\",\n            className: \"whitespace-pre-line text-pretty\",\n            children: value ?? \"-\"\n          }\n        ) : /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-wrap gap-1\", children: value }),\n        actions && /* @__PURE__ */ jsx(\"div\", { children: actions })\n      ]\n    }\n  );\n};\n\nexport {\n  SectionRow\n};\n"], "mappings": ";;;;;;;;;;;;AAEA,yBAA0B;AAC1B,IAAI,aAAa,CAAC,EAAE,OAAO,OAAO,QAAQ,MAAM;AAC9C,QAAM,gBAAgB,OAAO,UAAU,YAAY,CAAC;AACpD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,4BAA4B,CAAC,CAAC;AAAA,QAChC;AAAA,MACF;AAAA,MACA,UAAU;AAAA,YACQ,wBAAI,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,MAAM,CAAC;AAAA,QAChG,oBAAgC;AAAA,UAC9B;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,YACX,UAAU,SAAS;AAAA,UACrB;AAAA,QACF,QAAoB,wBAAI,OAAO,EAAE,WAAW,wBAAwB,UAAU,MAAM,CAAC;AAAA,QACrF,eAA2B,wBAAI,OAAO,EAAE,UAAU,QAAQ,CAAC;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AACF;", "names": []}