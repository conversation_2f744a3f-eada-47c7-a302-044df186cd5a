{"version": 3, "sources": ["../../@medusajs/dashboard/dist/category-organize-HUXXVJAB.mjs"], "sourcesContent": ["import {\n  CategoryTree\n} from \"./chunk-4UY4U2B5.mjs\";\nimport \"./chunk-ACBS6KFT.mjs\";\nimport {\n  RouteFocusModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  categoriesQueryKeys,\n  useProductCategories\n} from \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/categories/category-organize/components/organize-category-form/organize-category-form.tsx\nimport { useMutation } from \"@tanstack/react-query\";\nimport { Spinner } from \"@medusajs/icons\";\nimport { toast } from \"@medusajs/ui\";\nimport { useState } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar QUERY = {\n  fields: \"id,name,parent_category_id,rank,*category_children\",\n  parent_category_id: \"null\",\n  include_descendants_tree: true,\n  limit: 9999\n};\nvar OrganizeCategoryForm = () => {\n  const {\n    product_categories,\n    isPending,\n    isError,\n    error: fetchError\n  } = useProductCategories(QUERY);\n  const [snapshot, setSnapshot] = useState([]);\n  const { mutateAsync, isPending: isMutating } = useMutation({\n    mutationFn: async ({\n      value\n    }) => {\n      await sdk.admin.productCategory.update(value.id, {\n        rank: value.rank ?? 0,\n        parent_category_id: value.parent_category_id\n      });\n    },\n    onMutate: async (update) => {\n      await queryClient.cancelQueries({\n        queryKey: categoriesQueryKeys.list(QUERY)\n      });\n      const previousValue = queryClient.getQueryData(categoriesQueryKeys.list(QUERY));\n      const nextValue = {\n        ...previousValue,\n        product_categories: update.arr\n      };\n      queryClient.setQueryData(categoriesQueryKeys.list(QUERY), nextValue);\n      return {\n        previousValue\n      };\n    },\n    onError: (error, _newValue, context) => {\n      queryClient.setQueryData(\n        categoriesQueryKeys.list(QUERY),\n        context?.previousValue\n      );\n      toast.error(error.message);\n    },\n    onSettled: async () => {\n      await queryClient.invalidateQueries({\n        queryKey: categoriesQueryKeys.all\n      });\n    }\n  });\n  const handleRankChange = async (value, arr) => {\n    const val = {\n      id: value.id,\n      parent_category_id: value.parentId,\n      rank: value.index\n    };\n    setSnapshot(arr);\n    await mutateAsync({ value: val, arr });\n  };\n  const loading = isPending || isMutating;\n  if (isError) {\n    throw fetchError;\n  }\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end\", children: loading && /* @__PURE__ */ jsx(Spinner, { className: \"animate-spin\" }) }) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"bg-ui-bg-subtle flex flex-1 flex-col overflow-y-auto\", children: /* @__PURE__ */ jsx(\n      CategoryTree,\n      {\n        renderValue: (item) => item.name,\n        value: loading ? snapshot : product_categories || [],\n        onChange: handleRankChange\n      }\n    ) })\n  ] });\n};\n\n// src/routes/categories/category-organize/category-organize.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CategoryOrganize = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(OrganizeCategoryForm, {}) });\n};\nexport {\n  CategoryOrganize as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,mBAAyB;AACzB,yBAA0B;AA8E1B,IAAAA,sBAA4B;AA7E5B,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,oBAAoB;AAAA,EACpB,0BAA0B;AAAA,EAC1B,OAAO;AACT;AACA,IAAI,uBAAuB,MAAM;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,IAAI,qBAAqB,KAAK;AAC9B,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,CAAC,CAAC;AAC3C,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,YAAY;AAAA,IACzD,YAAY,OAAO;AAAA,MACjB;AAAA,IACF,MAAM;AACJ,YAAM,IAAI,MAAM,gBAAgB,OAAO,MAAM,IAAI;AAAA,QAC/C,MAAM,MAAM,QAAQ;AAAA,QACpB,oBAAoB,MAAM;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,IACA,UAAU,OAAO,WAAW;AAC1B,YAAM,YAAY,cAAc;AAAA,QAC9B,UAAU,oBAAoB,KAAK,KAAK;AAAA,MAC1C,CAAC;AACD,YAAM,gBAAgB,YAAY,aAAa,oBAAoB,KAAK,KAAK,CAAC;AAC9E,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH,oBAAoB,OAAO;AAAA,MAC7B;AACA,kBAAY,aAAa,oBAAoB,KAAK,KAAK,GAAG,SAAS;AACnE,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS,CAAC,OAAO,WAAW,YAAY;AACtC,kBAAY;AAAA,QACV,oBAAoB,KAAK,KAAK;AAAA,QAC9B,mCAAS;AAAA,MACX;AACA,YAAM,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,YAAY;AACrB,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,oBAAoB;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,OAAO,OAAO,QAAQ;AAC7C,UAAM,MAAM;AAAA,MACV,IAAI,MAAM;AAAA,MACV,oBAAoB,MAAM;AAAA,MAC1B,MAAM,MAAM;AAAA,IACd;AACA,gBAAY,GAAG;AACf,UAAM,YAAY,EAAE,OAAO,KAAK,IAAI,CAAC;AAAA,EACvC;AACA,QAAM,UAAU,aAAa;AAC7B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU;AAAA,QAChF,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,iCAAiC,UAAU,eAA2B,wBAAI,SAAS,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QACtM,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wDAAwD,cAA0B;AAAA,MACvI;AAAA,MACA;AAAA,QACE,aAAa,CAAC,SAAS,KAAK;AAAA,QAC5B,OAAO,UAAU,WAAW,sBAAsB,CAAC;AAAA,QACnD,UAAU;AAAA,MACZ;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAIA,IAAI,mBAAmB,MAAM;AAC3B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,sBAAsB,CAAC,CAAC,EAAE,CAAC;AAC3G;", "names": ["import_jsx_runtime", "jsx2"]}