import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-QTCZFYFH.mjs
var WORKFLOW_EXECUTIONS_QUERY_KEY = "workflow_executions";
var workflowExecutionsQueryKeys = queryKeysFactory(
  WORKFLOW_EXECUTIONS_QUERY_KEY
);
var useWorkflowExecutions = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.workflowExecution.list(query),
    queryKey: workflowExecutionsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useWorkflowExecution = (id, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.workflowExecution.retrieve(id),
    queryKey: workflowExecutionsQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};

export {
  workflowExecutionsQueryKeys,
  useWorkflowExecutions,
  useWorkflowExecution
};
//# sourceMappingURL=chunk-BEVRGZXI.js.map
