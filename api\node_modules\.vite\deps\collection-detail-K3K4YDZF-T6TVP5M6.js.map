{"version": 3, "sources": ["../../@medusajs/dashboard/dist/collection-detail-K3K4YDZF.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport {\n  collectionsQueryKeys,\n  useCollection,\n  useDeleteCollection,\n  useUpdateCollectionProducts\n} from \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/collections/collection-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CollectionDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { collection } = useCollection(id, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!collection) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: collection.title });\n};\n\n// src/routes/collections/collection-detail/collection-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/collections/collection-detail/components/collection-general-section/collection-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, Heading, Text, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar CollectionGeneralSection = ({\n  collection\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteCollection(collection.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"collections.deleteWarning\", {\n        count: 1,\n        title: collection.title\n      })\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync();\n    navigate(\"../\", { replace: true });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: collection.title }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                  label: t(\"actions.edit\"),\n                  to: `/collections/${collection.id}/edit`,\n                  disabled: !collection.id\n                }\n              ]\n            },\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx2(Trash, {}),\n                  label: t(\"actions.delete\"),\n                  onClick: handleDelete,\n                  disabled: !collection.id\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.handle\") }),\n      /* @__PURE__ */ jsxs(Text, { size: \"small\", children: [\n        \"/\",\n        collection.handle\n      ] })\n    ] })\n  ] });\n};\n\n// src/routes/collections/collection-detail/components/collection-product-section/collection-product-section.tsx\nimport { PencilSquare as PencilSquare2, Plus, Trash as Trash2 } from \"@medusajs/icons\";\nimport { Checkbox, Container as Container2, Heading as Heading2, toast, usePrompt as usePrompt2 } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar CollectionProductSection = ({\n  collection\n}) => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useProductTableQuery({ pageSize: PAGE_SIZE });\n  const { products, count, isLoading, isError, error } = useProducts(\n    {\n      limit: PAGE_SIZE,\n      ...searchParams,\n      collection_id: [collection.id]\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useProductTableFilters([\"collections\"]);\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: products ?? [],\n    columns,\n    getRowId: (row) => row.id,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    pageSize: PAGE_SIZE,\n    meta: {\n      collectionId: collection.id\n    }\n  });\n  const prompt = usePrompt2();\n  const { mutateAsync } = useUpdateCollectionProducts(collection.id);\n  const handleRemove = async (selection) => {\n    const ids = Object.keys(selection);\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"collections.removeProductsWarning\", {\n        count: ids.length\n      }),\n      confirmText: t(\"actions.remove\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      {\n        remove: ids\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"collections.products.remove.successToast\", {\n              count: ids.length\n            })\n          );\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  };\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"products.domain\") }),\n      /* @__PURE__ */ jsx3(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx3(Plus, {}),\n                  label: t(\"actions.add\"),\n                  to: \"products\"\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        columns,\n        search: true,\n        pagination: true,\n        pageSize: PAGE_SIZE,\n        navigateTo: ({ original }) => `/products/${original.id}`,\n        count,\n        filters,\n        isLoading,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        commands: [\n          {\n            action: handleRemove,\n            label: t(\"actions.remove\"),\n            shortcut: \"r\"\n          }\n        ],\n        noRecords: {\n          message: t(\"collections.products.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar ProductActions = ({\n  product,\n  collectionId\n}) => {\n  const { t } = useTranslation2();\n  const prompt = usePrompt2();\n  const { mutateAsync } = useUpdateCollectionProducts(collectionId);\n  const handleRemove = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"collections.removeSingleProductWarning\", {\n        title: product.title\n      }),\n      confirmText: t(\"actions.remove\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      {\n        remove: [product.id]\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"collections.products.remove.successToast\", {\n              count: 1\n            })\n          );\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  };\n  return /* @__PURE__ */ jsx3(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n              label: t(\"actions.edit\"),\n              to: `/products/${product.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(Trash2, {}),\n              label: t(\"actions.remove\"),\n              onClick: handleRemove\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const columns = useProductTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx3(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx3(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...columns,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row, table }) => {\n          const { collectionId } = table.options.meta;\n          return /* @__PURE__ */ jsx3(\n            ProductActions,\n            {\n              product: row.original,\n              collectionId\n            }\n          );\n        }\n      })\n    ],\n    [columns]\n  );\n};\n\n// src/routes/collections/collection-detail/collection-detail.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar CollectionDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const { collection, isLoading, isError, error } = useCollection(id, {\n    initialData\n  });\n  const { getWidgets } = useExtension();\n  if (isLoading || !collection) {\n    return /* @__PURE__ */ jsx4(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_collection.details.after\"),\n        before: getWidgets(\"product_collection.details.before\")\n      },\n      showJSON: true,\n      showMetadata: true,\n      data: collection,\n      children: [\n        /* @__PURE__ */ jsx4(CollectionGeneralSection, { collection }),\n        /* @__PURE__ */ jsx4(CollectionProductSection, { collection })\n      ]\n    }\n  );\n};\n\n// src/routes/collections/collection-detail/loader.ts\nvar collectionDetailQuery = (id) => ({\n  queryKey: collectionsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.productCollection.retrieve(id)\n});\nvar collectionLoader = async ({ params }) => {\n  const id = params.id;\n  const query = collectionDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  CollectionDetailBreadcrumb as Breadcrumb,\n  CollectionDetail as Component,\n  collectionLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,yBAAoB;AAqBpB,IAAAA,sBAAkC;AAoElC,mBAAwB;AAExB,IAAAC,sBAA2C;AAuO3C,IAAAA,sBAA2C;AAjU3C,IAAI,6BAA6B,CAAC,UAAU;AAC1C,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AAAA,IACvC,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,WAAW,MAAM,CAAC;AACnE;AAWA,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,oBAAoB,WAAW,EAAE;AACzD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,6BAA6B;AAAA,QAC1C,OAAO;AAAA,QACP,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAClB,aAAS,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,EACnC;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,UAAU,WAAW,MAAM,CAAC;AAAA,UAC5C,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI,gBAAgB,WAAW,EAAE;AAAA,kBACjC,UAAU,CAAC,WAAW;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,kBACpC,OAAO,EAAE,gBAAgB;AAAA,kBACzB,SAAS;AAAA,kBACT,UAAU,CAAC,WAAW;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,0BAAK,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC9F,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,UAC9F,0BAAK,MAAM,EAAE,MAAM,SAAS,UAAU;AAAA,QACpD;AAAA,QACA,WAAW;AAAA,MACb,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAUA,IAAI,YAAY;AAChB,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB,EAAE,UAAU,UAAU,CAAC;AAC1E,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACrD;AAAA,MACE,OAAO;AAAA,MACP,GAAG;AAAA,MACH,eAAe,CAAC,WAAW,EAAE;AAAA,IAC/B;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,uBAAuB,CAAC,aAAa,CAAC;AACtD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,MAAM;AAAA,MACJ,cAAc,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,SAAS,UAAW;AAC1B,QAAM,EAAE,YAAY,IAAI,4BAA4B,WAAW,EAAE;AACjE,QAAM,eAAe,OAAO,cAAc;AACxC,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,qCAAqC;AAAA,QAClD,OAAO,IAAI;AAAA,MACb,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJ,EAAE,4CAA4C;AAAA,cAC5C,OAAO,IAAI;AAAA,YACb,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC;AAAA,UAC9D,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,MAAM,CAAC,CAAC;AAAA,kBACnC,OAAO,EAAE,aAAa;AAAA,kBACtB,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAY,CAAC,EAAE,SAAS,MAAM,aAAa,SAAS,EAAE;AAAA,QACtD;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,QAAQ;AAAA,YACR,OAAO,EAAE,gBAAgB;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS,EAAE,4CAA4C;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAW;AAC1B,QAAM,EAAE,YAAY,IAAI,4BAA4B,YAAY;AAChE,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,0CAA0C;AAAA,QACvD,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ,CAAC,QAAQ,EAAE;AAAA,MACrB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJ,EAAE,4CAA4C;AAAA,cAC5C,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,aAAa,QAAQ,EAAE;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,cACrC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,UAAU,uBAAuB;AACvC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AACxB,gBAAM,EAAE,aAAa,IAAI,MAAM,QAAQ;AACvC,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AACF;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI,cAAc,IAAI;AAAA,IAClE;AAAA,EACF,CAAC;AACD,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,YAAY;AAC5B,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,kCAAkC;AAAA,QACpD,QAAQ,WAAW,mCAAmC;AAAA,MACxD;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAD,KAAK,0BAA0B,EAAE,WAAW,CAAC;AAAA,YAC7C,oBAAAA,KAAK,0BAA0B,EAAE,WAAW,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,wBAAwB,CAAC,QAAQ;AAAA,EACnC,UAAU,qBAAqB,OAAO,EAAE;AAAA,EACxC,SAAS,YAAY,IAAI,MAAM,kBAAkB,SAAS,EAAE;AAC9D;AACA,IAAI,mBAAmB,OAAO,EAAE,OAAO,MAAM;AAC3C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,sBAAsB,EAAE;AACtC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsx4", "jsxs3"]}