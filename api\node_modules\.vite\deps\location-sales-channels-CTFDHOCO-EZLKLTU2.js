import {
  VisuallyH<PERSON>den
} from "./chunk-TAVTGMIK.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-R325LWWE.js";
import "./chunk-WNW4SNUS.js";
import {
  DataTable
} from "./chunk-EPUS4TBC.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useStockLocation,
  useUpdateStockLocationSalesChannels
} from "./chunk-CXC4I63N.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  createDataTableColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-sales-channels-CTFDHOCO.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditSalesChannelsSchema = objectType({
  sales_channels: arrayType(stringType()).optional()
});
var PAGE_SIZE = 50;
var PREFIX = "sc";
var LocationEditSalesChannelsForm = ({
  location
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      sales_channels: ((_a = location.sales_channels) == null ? void 0 : _a.map((sc) => sc.id)) ?? []
    },
    resolver: t(EditSalesChannelsSchema)
  });
  const { setValue } = form;
  const [rowSelection, setRowSelection] = (0, import_react.useState)(
    getInitialState(location)
  );
  const onRowSelectionChange = (selection) => {
    const ids = Object.keys(selection);
    setValue("sales_channels", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(selection);
  };
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { sales_channels, count, isPending, isError, error } = useSalesChannels(
    {
      ...searchParams
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useSalesChannelTableFilters();
  const columns = useColumns();
  const emptyState = useSalesChannelTableEmptyState();
  const { mutateAsync, isPending: isMutating } = useUpdateStockLocationSalesChannels(location.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a2;
    const originalIds = (_a2 = location.sales_channels) == null ? void 0 : _a2.map((sc) => sc.id);
    const arr = data.sales_channels ?? [];
    await mutateAsync(
      {
        add: arr.filter((i) => !(originalIds == null ? void 0 : originalIds.includes(i))),
        remove: originalIds == null ? void 0 : originalIds.filter((i) => !arr.includes(i))
      },
      {
        onSuccess: () => {
          toast.success(t2("stockLocations.salesChannels.successToast"));
          handleSuccess(`/settings/locations/${location.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime.jsxs)(RouteFocusModal.Header, { children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("stockLocations.salesChannels.header") }) }),
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("stockLocations.salesChannels.hint") }) })
    ] }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-auto", children: (0, import_jsx_runtime.jsx)(
      DataTable,
      {
        data: sales_channels,
        columns,
        filters,
        emptyState,
        prefix: PREFIX,
        rowSelection: {
          state: rowSelection,
          onRowSelectionChange
        },
        pageSize: PAGE_SIZE,
        isLoading: isPending,
        rowCount: count,
        layout: "fill",
        getRowId: (row) => row.id
      }
    ) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", isLoading: isMutating, type: "submit", children: t2("actions.save") })
    ] }) })
  ] }) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const base = useSalesChannelTableColumns();
  return (0, import_react.useMemo)(() => [columnHelper.select(), ...base], [base]);
};
function getInitialState(location) {
  var _a;
  return ((_a = location.sales_channels) == null ? void 0 : _a.reduce((acc, curr) => {
    acc[curr.id] = true;
    return acc;
  }, {})) ?? {};
}
var LocationSalesChannels = () => {
  const { location_id } = useParams();
  const { stock_location, isPending, isError, error } = useStockLocation(
    location_id,
    {
      fields: "id,*sales_channels"
    }
  );
  const ready = !isPending && !!stock_location;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime2.jsx)(LocationEditSalesChannelsForm, { location: stock_location }) });
};
export {
  LocationSalesChannels as Component
};
//# sourceMappingURL=location-sales-channels-CTFDHOCO-EZLKLTU2.js.map
