{"version": 3, "sources": ["../../@medusajs/dashboard/dist/no-match-YRNHGOT3.mjs"], "sourcesContent": ["import \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/no-match/no-match.tsx\nimport { ExclamationCircle } from \"@medusajs/icons\";\nimport { Button, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar NoMatch = () => {\n  const { t } = useTranslation();\n  const title = t(\"errorBoundary.notFoundTitle\");\n  const message = t(\"errorBoundary.noMatchMessage\");\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full min-h-screen items-center justify-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col items-center gap-y-6\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle flex flex-col items-center gap-y-3\", children: [\n      /* @__PURE__ */ jsx(ExclamationCircle, {}),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col items-center justify-center gap-y-1\", children: [\n        /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: title }),\n        /* @__PURE__ */ jsx(\n          Text,\n          {\n            size: \"small\",\n            className: \"text-ui-fg-muted text-balance text-center\",\n            children: message\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx(Button, { asChild: true, size: \"small\", variant: \"secondary\", children: /* @__PURE__ */ jsx(Link, { to: \"/\", children: t(\"errorBoundary.backToDashboard\") }) })\n  ] }) });\n};\nexport {\n  NoMatch as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAOA,yBAA0B;AAC1B,IAAI,UAAU,MAAM;AAClB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,QAAQ,EAAE,6BAA6B;AAC7C,QAAM,UAAU,EAAE,8BAA8B;AAChD,aAAuB,wBAAI,OAAO,EAAE,WAAW,2DAA2D,cAA0B,yBAAK,OAAO,EAAE,WAAW,sCAAsC,UAAU;AAAA,QAC3L,yBAAK,OAAO,EAAE,WAAW,wDAAwD,UAAU;AAAA,UACzF,wBAAI,mBAAmB,CAAC,CAAC;AAAA,UACzB,yBAAK,OAAO,EAAE,WAAW,qDAAqD,UAAU;AAAA,YACtF,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,MAAM,CAAC;AAAA,YAChF;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,wBAAI,QAAQ,EAAE,SAAS,MAAM,MAAM,SAAS,SAAS,aAAa,cAA0B,wBAAI,MAAM,EAAE,IAAI,KAAK,UAAU,EAAE,+BAA+B,EAAE,CAAC,EAAE,CAAC;AAAA,EACpL,EAAE,CAAC,EAAE,CAAC;AACR;", "names": []}