{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-group-add-customers-G7G2JYJI.mjs"], "sourcesContent": ["import {\n  useCustomerTableColumns\n} from \"./chunk-DT7QVGFJ.mjs\";\nimport {\n  useCustomerTableQuery\n} from \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-3OHUAQUF.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useCustomerTableFilters\n} from \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useAddCustomersToGroup,\n  useCustomers\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/customer-groups/customer-group-add-customers/customer-group-add-customers.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/customer-groups/customer-group-add-customers/components/add-customers-form/add-customers-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddCustomersSchema = zod.object({\n  customer_ids: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 10;\nvar AddCustomersForm = ({\n  customerGroupId\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      customer_ids: []\n    },\n    resolver: zodResolver(AddCustomersSchema)\n  });\n  const { setValue } = form;\n  const [rowSelection, setRowSelection] = useState({});\n  useEffect(() => {\n    setValue(\n      \"customer_ids\",\n      Object.keys(rowSelection).filter((k) => rowSelection[k]),\n      {\n        shouldDirty: true,\n        shouldTouch: true\n      }\n    );\n  }, [rowSelection, setValue]);\n  const { searchParams, raw } = useCustomerTableQuery({ pageSize: PAGE_SIZE });\n  const filters = useCustomerTableFilters();\n  const { customers, count, isLoading, isError, error } = useCustomers({\n    fields: \"id,email,first_name,last_name,*groups\",\n    ...searchParams\n  });\n  const updater = (fn) => {\n    const state = typeof fn === \"function\" ? fn(rowSelection) : fn;\n    const ids = Object.keys(state);\n    setValue(\"customer_ids\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(state);\n  };\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: customers ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: (row) => {\n      return !row.original.groups?.map((gc) => gc.id).includes(customerGroupId);\n    },\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    }\n  });\n  const { mutateAsync, isPending } = useAddCustomersToGroup(customerGroupId);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data.customer_ids, {\n      onSuccess: () => {\n        toast.success(\n          t(\"customerGroups.customers.add.successToast\", {\n            count: data.customer_ids.length\n          })\n        );\n        handleSuccess(`/customer-groups/${customerGroupId}`);\n      },\n      onError: (error2) => {\n        toast.error(error2.message);\n      }\n    });\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          form.formState.errors.customer_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.customer_ids.message }),\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              isLoading: isPending,\n              children: t(\"actions.save\")\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            columns,\n            pageSize: PAGE_SIZE,\n            count,\n            filters,\n            orderBy: [\n              { key: \"email\", label: t(\"fields.email\") },\n              { key: \"first_name\", label: t(\"fields.firstName\") },\n              { key: \"last_name\", label: t(\"fields.lastName\") },\n              { key: \"has_account\", label: t(\"customers.hasAccount\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            isLoading,\n            layout: \"fill\",\n            search: \"autofocus\",\n            queryObject: raw,\n            noRecords: {\n              message: t(\"customerGroups.customers.add.list.noRecordsMessage\")\n            }\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const base = useCustomerTableColumns();\n  const columns = useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isSelected = row.getIsSelected() || isPreSelected;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isPreSelected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isPreSelected) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"customerGroups.customers.alreadyAddedTooltip\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n  return columns;\n};\n\n// src/routes/customer-groups/customer-group-add-customers/customer-group-add-customers.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerGroupAddCustomers = () => {\n  const { id } = useParams();\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(AddCustomersForm, { customerGroupId: id }) });\n};\nexport {\n  CustomerGroupAddCustomers as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,mBAA6C;AAI7C,yBAA0B;AAmL1B,IAAAA,sBAA4B;AAlL5B,IAAI,qBAAyB,WAAO;AAAA,EAClC,cAAkB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAC7C,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,mBAAmB,CAAC;AAAA,EACtB;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,cAAc,CAAC;AAAA,IACjB;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,8BAAU,MAAM;AACd;AAAA,MACE;AAAA,MACA,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;AAAA,MACvD;AAAA,QACE,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,QAAQ,CAAC;AAC3B,QAAM,EAAE,cAAc,IAAI,IAAI,sBAAsB,EAAE,UAAU,UAAU,CAAC;AAC3E,QAAM,UAAU,wBAAwB;AACxC,QAAM,EAAE,WAAW,OAAO,WAAW,SAAS,MAAM,IAAI,aAAa;AAAA,IACnE,QAAQ;AAAA,IACR,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,QAAQ,OAAO,OAAO,aAAa,GAAG,YAAY,IAAI;AAC5D,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,aAAS,gBAAgB,KAAK;AAAA,MAC5B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,KAAK;AAAA,EACvB;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,aAAa,CAAC;AAAA,IACpB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB,CAAC,QAAQ;AA1GjC;AA2GM,aAAO,GAAC,SAAI,SAAS,WAAb,mBAAqB,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS;AAAA,IAC3D;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,uBAAuB,eAAe;AACzE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,KAAK,cAAc;AAAA,MACnC,WAAW,MAAM;AACf,cAAM;AAAA,UACJA,GAAE,6CAA6C;AAAA,YAC7C,OAAO,KAAK,aAAa;AAAA,UAC3B,CAAC;AAAA,QACH;AACA,sBAAc,oBAAoB,eAAe,EAAE;AAAA,MACrD;AAAA,MACA,SAAS,CAAC,WAAW;AACnB,cAAM,MAAM,OAAO,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClJ,KAAK,UAAU,OAAO,oBAAgC,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,aAAa,QAAQ,CAAC;AAAA,cAC1H,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAUA,GAAE,cAAc;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,cAA0B;AAAA,UAC5G;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,SAAS,OAAOA,GAAE,cAAc,EAAE;AAAA,cACzC,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,aAAa,OAAOA,GAAE,iBAAiB,EAAE;AAAA,cAChD,EAAE,KAAK,eAAe,OAAOA,GAAE,sBAAsB,EAAE;AAAA,cACvD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,WAAW;AAAA,cACT,SAASA,GAAE,oDAAoD;AAAA,YACjE;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,wBAAwB;AACrC,QAAM,cAAU;AAAA,IACd,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe;AACjB,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,8CAA8C;AAAA,gBACzD,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACA,SAAO;AACT;AAIA,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,kBAAkB,EAAE,iBAAiB,GAAG,CAAC,EAAE,CAAC;AAC5H;", "names": ["import_jsx_runtime", "t", "jsx2"]}