{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3NJTXRIY.mjs"], "sourcesContent": ["import {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\n\n// src/components/common/action-menu/action-menu.tsx\nimport { DropdownMenu, IconButton, clx } from \"@medusajs/ui\";\nimport { EllipsisHorizontal } from \"@medusajs/icons\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ActionMenu = ({\n  groups,\n  variant = \"transparent\",\n  children\n}) => {\n  const inner = children ?? /* @__PURE__ */ jsx(IconButton, { size: \"small\", variant, children: /* @__PURE__ */ jsx(EllipsisHorizontal, {}) });\n  return /* @__PURE__ */ jsxs(DropdownMenu, { children: [\n    /* @__PURE__ */ jsx(DropdownMenu.Trigger, { asChild: true, children: inner }),\n    /* @__PURE__ */ jsx(DropdownMenu.Content, { children: groups.map((group, index) => {\n      if (!group.actions.length) {\n        return null;\n      }\n      const isLast = index === groups.length - 1;\n      return /* @__PURE__ */ jsxs(DropdownMenu.Group, { children: [\n        group.actions.map((action, index2) => {\n          const Wrapper = action.disabledTooltip ? ({ children: children2 }) => /* @__PURE__ */ jsx(\n            ConditionalTooltip,\n            {\n              showTooltip: action.disabled,\n              content: action.disabledTooltip,\n              side: \"right\",\n              children: /* @__PURE__ */ jsx(\"div\", { children: children2 })\n            }\n          ) : \"div\";\n          if (action.onClick) {\n            return /* @__PURE__ */ jsx(Wrapper, { children: /* @__PURE__ */ jsxs(\n              DropdownMenu.Item,\n              {\n                disabled: action.disabled,\n                onClick: (e) => {\n                  e.stopPropagation();\n                  action.onClick();\n                },\n                className: clx(\n                  \"[&_svg]:text-ui-fg-subtle flex items-center gap-x-2\",\n                  {\n                    \"[&_svg]:text-ui-fg-disabled\": action.disabled\n                  }\n                ),\n                children: [\n                  action.icon,\n                  /* @__PURE__ */ jsx(\"span\", { children: action.label })\n                ]\n              }\n            ) }, index2);\n          }\n          return /* @__PURE__ */ jsx(Wrapper, { children: /* @__PURE__ */ jsx(\n            DropdownMenu.Item,\n            {\n              className: clx(\n                \"[&_svg]:text-ui-fg-subtle flex items-center gap-x-2\",\n                {\n                  \"[&_svg]:text-ui-fg-disabled\": action.disabled\n                }\n              ),\n              asChild: true,\n              disabled: action.disabled,\n              children: /* @__PURE__ */ jsxs(Link, { to: action.to, onClick: (e) => e.stopPropagation(), children: [\n                action.icon,\n                /* @__PURE__ */ jsx(\"span\", { children: action.label })\n              ] })\n            }\n          ) }, index2);\n        }),\n        !isLast && /* @__PURE__ */ jsx(DropdownMenu.Separator, {})\n      ] }, index);\n    }) })\n  ] });\n};\n\nexport {\n  ActionMenu\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA,yBAA0B;AAC1B,IAAI,aAAa,CAAC;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,EACV;AACF,MAAM;AACJ,QAAM,QAAQ,gBAA4B,wBAAI,YAAY,EAAE,MAAM,SAAS,SAAS,cAA0B,wBAAI,oBAAoB,CAAC,CAAC,EAAE,CAAC;AAC3I,aAAuB,yBAAK,cAAc,EAAE,UAAU;AAAA,QACpC,wBAAI,aAAa,SAAS,EAAE,SAAS,MAAM,UAAU,MAAM,CAAC;AAAA,QAC5D,wBAAI,aAAa,SAAS,EAAE,UAAU,OAAO,IAAI,CAAC,OAAO,UAAU;AACjF,UAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,eAAO;AAAA,MACT;AACA,YAAM,SAAS,UAAU,OAAO,SAAS;AACzC,iBAAuB,yBAAK,aAAa,OAAO,EAAE,UAAU;AAAA,QAC1D,MAAM,QAAQ,IAAI,CAAC,QAAQ,WAAW;AACpC,gBAAM,UAAU,OAAO,kBAAkB,CAAC,EAAE,UAAU,UAAU,UAAsB;AAAA,YACpF;AAAA,YACA;AAAA,cACE,aAAa,OAAO;AAAA,cACpB,SAAS,OAAO;AAAA,cAChB,MAAM;AAAA,cACN,cAA0B,wBAAI,OAAO,EAAE,UAAU,UAAU,CAAC;AAAA,YAC9D;AAAA,UACF,IAAI;AACJ,cAAI,OAAO,SAAS;AAClB,uBAAuB,wBAAI,SAAS,EAAE,cAA0B;AAAA,cAC9D,aAAa;AAAA,cACb;AAAA,gBACE,UAAU,OAAO;AAAA,gBACjB,SAAS,CAAC,MAAM;AACd,oBAAE,gBAAgB;AAClB,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,WAAW;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,+BAA+B,OAAO;AAAA,kBACxC;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,kBACR,OAAO;AAAA,sBACS,wBAAI,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC;AAAA,gBACxD;AAAA,cACF;AAAA,YACF,EAAE,GAAG,MAAM;AAAA,UACb;AACA,qBAAuB,wBAAI,SAAS,EAAE,cAA0B;AAAA,YAC9D,aAAa;AAAA,YACb;AAAA,cACE,WAAW;AAAA,gBACT;AAAA,gBACA;AAAA,kBACE,+BAA+B,OAAO;AAAA,gBACxC;AAAA,cACF;AAAA,cACA,SAAS;AAAA,cACT,UAAU,OAAO;AAAA,cACjB,cAA0B,yBAAK,MAAM,EAAE,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,gBAAgB,GAAG,UAAU;AAAA,gBACnG,OAAO;AAAA,oBACS,wBAAI,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC;AAAA,cACxD,EAAE,CAAC;AAAA,YACL;AAAA,UACF,EAAE,GAAG,MAAM;AAAA,QACb,CAAC;AAAA,QACD,CAAC,cAA0B,wBAAI,aAAa,WAAW,CAAC,CAAC;AAAA,MAC3D,EAAE,GAAG,KAAK;AAAA,IACZ,CAAC,EAAE,CAAC;AAAA,EACN,EAAE,CAAC;AACL;", "names": []}