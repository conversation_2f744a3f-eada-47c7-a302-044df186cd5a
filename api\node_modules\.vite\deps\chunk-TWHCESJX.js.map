{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-K7S5TX6I.mjs"], "sourcesContent": ["import {\n  __commonJS\n} from \"./chunk-GH77ZQI2.mjs\";\n\n// ../../../node_modules/toggle-selection/index.js\nvar require_toggle_selection = __commonJS({\n  \"../../../node_modules/toggle-selection/index.js\"(exports, module) {\n    \"use strict\";\n    module.exports = function() {\n      var selection = document.getSelection();\n      if (!selection.rangeCount) {\n        return function() {\n        };\n      }\n      var active = document.activeElement;\n      var ranges = [];\n      for (var i = 0; i < selection.rangeCount; i++) {\n        ranges.push(selection.getRangeAt(i));\n      }\n      switch (active.tagName.toUpperCase()) {\n        case \"INPUT\":\n        case \"TEXTAREA\":\n          active.blur();\n          break;\n        default:\n          active = null;\n          break;\n      }\n      selection.removeAllRanges();\n      return function() {\n        selection.type === \"Caret\" && selection.removeAllRanges();\n        if (!selection.rangeCount) {\n          ranges.forEach(function(range) {\n            selection.addRange(range);\n          });\n        }\n        active && active.focus();\n      };\n    };\n  }\n});\n\n// ../../../node_modules/copy-to-clipboard/index.js\nvar require_copy_to_clipboard = __commonJS({\n  \"../../../node_modules/copy-to-clipboard/index.js\"(exports, module) {\n    \"use strict\";\n    var deselectCurrent = require_toggle_selection();\n    var clipboardToIE11Formatting = {\n      \"text/plain\": \"Text\",\n      \"text/html\": \"Url\",\n      \"default\": \"Text\"\n    };\n    var defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n    function format(message) {\n      var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"\\u2318\" : \"Ctrl\") + \"+C\";\n      return message.replace(/#{\\s*key\\s*}/g, copyKey);\n    }\n    function copy(text, options) {\n      var debug, message, reselectPrevious, range, selection, mark, success = false;\n      if (!options) {\n        options = {};\n      }\n      debug = options.debug || false;\n      try {\n        reselectPrevious = deselectCurrent();\n        range = document.createRange();\n        selection = document.getSelection();\n        mark = document.createElement(\"span\");\n        mark.textContent = text;\n        mark.ariaHidden = \"true\";\n        mark.style.all = \"unset\";\n        mark.style.position = \"fixed\";\n        mark.style.top = 0;\n        mark.style.clip = \"rect(0, 0, 0, 0)\";\n        mark.style.whiteSpace = \"pre\";\n        mark.style.webkitUserSelect = \"text\";\n        mark.style.MozUserSelect = \"text\";\n        mark.style.msUserSelect = \"text\";\n        mark.style.userSelect = \"text\";\n        mark.addEventListener(\"copy\", function(e) {\n          e.stopPropagation();\n          if (options.format) {\n            e.preventDefault();\n            if (typeof e.clipboardData === \"undefined\") {\n              debug && console.warn(\"unable to use e.clipboardData\");\n              debug && console.warn(\"trying IE specific stuff\");\n              window.clipboardData.clearData();\n              var format2 = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"];\n              window.clipboardData.setData(format2, text);\n            } else {\n              e.clipboardData.clearData();\n              e.clipboardData.setData(options.format, text);\n            }\n          }\n          if (options.onCopy) {\n            e.preventDefault();\n            options.onCopy(e.clipboardData);\n          }\n        });\n        document.body.appendChild(mark);\n        range.selectNodeContents(mark);\n        selection.addRange(range);\n        var successful = document.execCommand(\"copy\");\n        if (!successful) {\n          throw new Error(\"copy command was unsuccessful\");\n        }\n        success = true;\n      } catch (err) {\n        debug && console.error(\"unable to copy using execCommand: \", err);\n        debug && console.warn(\"trying IE specific stuff\");\n        try {\n          window.clipboardData.setData(options.format || \"text\", text);\n          options.onCopy && options.onCopy(window.clipboardData);\n          success = true;\n        } catch (err2) {\n          debug && console.error(\"unable to copy using clipboardData: \", err2);\n          debug && console.error(\"falling back to prompt\");\n          message = format(\"message\" in options ? options.message : defaultMessage);\n          window.prompt(message, text);\n        }\n      } finally {\n        if (selection) {\n          if (typeof selection.removeRange == \"function\") {\n            selection.removeRange(range);\n          } else {\n            selection.removeAllRanges();\n          }\n        }\n        if (mark) {\n          document.body.removeChild(mark);\n        }\n        reselectPrevious();\n      }\n      return success;\n    }\n    module.exports = copy;\n  }\n});\n\nexport {\n  require_copy_to_clipboard\n};\n"], "mappings": ";;;;;AAKA,IAAI,2BAA2B,WAAW;AAAA,EACxC,kDAAkD,SAAS,QAAQ;AACjE;AACA,WAAO,UAAU,WAAW;AAC1B,UAAI,YAAY,SAAS,aAAa;AACtC,UAAI,CAAC,UAAU,YAAY;AACzB,eAAO,WAAW;AAAA,QAClB;AAAA,MACF;AACA,UAAI,SAAS,SAAS;AACtB,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,UAAU,YAAY,KAAK;AAC7C,eAAO,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,MACrC;AACA,cAAQ,OAAO,QAAQ,YAAY,GAAG;AAAA,QACpC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK;AACZ;AAAA,QACF;AACE,mBAAS;AACT;AAAA,MACJ;AACA,gBAAU,gBAAgB;AAC1B,aAAO,WAAW;AAChB,kBAAU,SAAS,WAAW,UAAU,gBAAgB;AACxD,YAAI,CAAC,UAAU,YAAY;AACzB,iBAAO,QAAQ,SAAS,OAAO;AAC7B,sBAAU,SAAS,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH;AACA,kBAAU,OAAO,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,IAAI,4BAA4B,WAAW;AAAA,EACzC,mDAAmD,SAAS,QAAQ;AAClE;AACA,QAAI,kBAAkB,yBAAyB;AAC/C,QAAI,4BAA4B;AAAA,MAC9B,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AACA,QAAI,iBAAiB;AACrB,aAAS,OAAO,SAAS;AACvB,UAAI,WAAW,YAAY,KAAK,UAAU,SAAS,IAAI,MAAW,UAAU;AAC5E,aAAO,QAAQ,QAAQ,iBAAiB,OAAO;AAAA,IACjD;AACA,aAAS,KAAK,MAAM,SAAS;AAC3B,UAAI,OAAO,SAAS,kBAAkB,OAAO,WAAW,MAAM,UAAU;AACxE,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,QAAQ,SAAS;AACzB,UAAI;AACF,2BAAmB,gBAAgB;AACnC,gBAAQ,SAAS,YAAY;AAC7B,oBAAY,SAAS,aAAa;AAClC,eAAO,SAAS,cAAc,MAAM;AACpC,aAAK,cAAc;AACnB,aAAK,aAAa;AAClB,aAAK,MAAM,MAAM;AACjB,aAAK,MAAM,WAAW;AACtB,aAAK,MAAM,MAAM;AACjB,aAAK,MAAM,OAAO;AAClB,aAAK,MAAM,aAAa;AACxB,aAAK,MAAM,mBAAmB;AAC9B,aAAK,MAAM,gBAAgB;AAC3B,aAAK,MAAM,eAAe;AAC1B,aAAK,MAAM,aAAa;AACxB,aAAK,iBAAiB,QAAQ,SAAS,GAAG;AACxC,YAAE,gBAAgB;AAClB,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,gBAAI,OAAO,EAAE,kBAAkB,aAAa;AAC1C,uBAAS,QAAQ,KAAK,+BAA+B;AACrD,uBAAS,QAAQ,KAAK,0BAA0B;AAChD,qBAAO,cAAc,UAAU;AAC/B,kBAAI,UAAU,0BAA0B,QAAQ,MAAM,KAAK,0BAA0B,SAAS;AAC9F,qBAAO,cAAc,QAAQ,SAAS,IAAI;AAAA,YAC5C,OAAO;AACL,gBAAE,cAAc,UAAU;AAC1B,gBAAE,cAAc,QAAQ,QAAQ,QAAQ,IAAI;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,oBAAQ,OAAO,EAAE,aAAa;AAAA,UAChC;AAAA,QACF,CAAC;AACD,iBAAS,KAAK,YAAY,IAAI;AAC9B,cAAM,mBAAmB,IAAI;AAC7B,kBAAU,SAAS,KAAK;AACxB,YAAI,aAAa,SAAS,YAAY,MAAM;AAC5C,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,kBAAU;AAAA,MACZ,SAAS,KAAK;AACZ,iBAAS,QAAQ,MAAM,sCAAsC,GAAG;AAChE,iBAAS,QAAQ,KAAK,0BAA0B;AAChD,YAAI;AACF,iBAAO,cAAc,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AAC3D,kBAAQ,UAAU,QAAQ,OAAO,OAAO,aAAa;AACrD,oBAAU;AAAA,QACZ,SAAS,MAAM;AACb,mBAAS,QAAQ,MAAM,wCAAwC,IAAI;AACnE,mBAAS,QAAQ,MAAM,wBAAwB;AAC/C,oBAAU,OAAO,aAAa,UAAU,QAAQ,UAAU,cAAc;AACxE,iBAAO,OAAO,SAAS,IAAI;AAAA,QAC7B;AAAA,MACF,UAAE;AACA,YAAI,WAAW;AACb,cAAI,OAAO,UAAU,eAAe,YAAY;AAC9C,sBAAU,YAAY,KAAK;AAAA,UAC7B,OAAO;AACL,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AACA,YAAI,MAAM;AACR,mBAAS,KAAK,YAAY,IAAI;AAAA,QAChC;AACA,yBAAiB;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA,EACnB;AACF,CAAC;", "names": []}