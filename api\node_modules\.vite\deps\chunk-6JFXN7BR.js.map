{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3ISBJK7K.mjs"], "sourcesContent": ["import {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\n\n// src/lib/form-helpers.ts\nfunction transformNullableFormValue(value, nullify = true) {\n  if (typeof value === \"string\" && value.trim() === \"\") {\n    return nullify ? null : void 0;\n  }\n  if (Array.isArray(value) && value.length === 0) {\n    return nullify ? null : void 0;\n  }\n  return value;\n}\nfunction transformNullableFormData(data, nullify = true) {\n  return Object.entries(data).reduce((acc, [key, value]) => {\n    return {\n      ...acc,\n      [key]: transformNullableFormValue(value, nullify)\n    };\n  }, {});\n}\nfunction transformNullableFormNumber(value, nullify = true) {\n  if (typeof value === \"undefined\" || typeof value === \"string\" && value.trim() === \"\") {\n    return nullify ? null : void 0;\n  }\n  if (typeof value === \"string\") {\n    return castNumber(value);\n  }\n  return value;\n}\nfunction transformNullableFormNumbers(data, nullify = true) {\n  return Object.entries(data).reduce((acc, [key, value]) => {\n    return {\n      ...acc,\n      [key]: transformNullableFormNumber(value, nullify)\n    };\n  }, {});\n}\n\nexport {\n  transformNullableFormData,\n  transformNullableFormNumber,\n  transformNullableFormNumbers\n};\n"], "mappings": ";;;;;AAKA,SAAS,2BAA2B,OAAO,UAAU,MAAM;AACzD,MAAI,OAAO,UAAU,YAAY,MAAM,KAAK,MAAM,IAAI;AACpD,WAAO,UAAU,OAAO;AAAA,EAC1B;AACA,MAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG;AAC9C,WAAO,UAAU,OAAO;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,0BAA0B,MAAM,UAAU,MAAM;AACvD,SAAO,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACxD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,GAAG,GAAG,2BAA2B,OAAO,OAAO;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,4BAA4B,OAAO,UAAU,MAAM;AAC1D,MAAI,OAAO,UAAU,eAAe,OAAO,UAAU,YAAY,MAAM,KAAK,MAAM,IAAI;AACpF,WAAO,UAAU,OAAO;AAAA,EAC1B;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,WAAW,KAAK;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,6BAA6B,MAAM,UAAU,MAAM;AAC1D,SAAO,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACxD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,GAAG,GAAG,4BAA4B,OAAO,OAAO;AAAA,IACnD;AAAA,EACF,GAAG,CAAC,CAAC;AACP;", "names": []}