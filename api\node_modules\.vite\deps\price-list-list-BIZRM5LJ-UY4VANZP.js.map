{"version": 3, "sources": ["../../@medusajs/dashboard/dist/price-list-list-BIZRM5LJ.mjs"], "sourcesContent": ["import {\n  useDeletePriceListAction\n} from \"./chunk-LTC6LGS4.mjs\";\nimport {\n  getPriceListStatus\n} from \"./chunk-G2J2T2QU.mjs\";\nimport \"./chunk-XUQVQCAO.mjs\";\nimport {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useDateTableFilters\n} from \"./chunk-W7625H47.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-6GU6IDUA.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  usePriceLists\n} from \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/price-list-list-table.tsx\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/use-pricing-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/price-list-list-table-actions.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PriceListListTableActions = ({\n  priceList\n}) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeletePriceListAction({ priceList });\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              to: `${priceList.id}/edit`,\n              icon: /* @__PURE__ */ jsx(PencilSquare, {})\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              onClick: handleDelete,\n              icon: /* @__PURE__ */ jsx(Trash, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/use-pricing-table-columns.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar usePricingTableColumns = () => {\n  const { t } = useTranslation2();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"title\", {\n        header: () => /* @__PURE__ */ jsx2(TextHeader, { text: t(\"fields.title\") }),\n        cell: (info) => info.getValue()\n      }),\n      columnHelper.accessor(\"status\", {\n        header: t(\"priceLists.fields.status.label\"),\n        cell: ({ row }) => {\n          const { color, text } = getPriceListStatus(t, row.original);\n          return /* @__PURE__ */ jsx2(StatusCell, { color, children: text });\n        }\n      }),\n      columnHelper.accessor(\"prices\", {\n        header: t(\"priceLists.fields.priceOverrides.header\"),\n        cell: (info) => /* @__PURE__ */ jsx2(TextCell, { text: `${info.getValue()?.length || \"-\"}` })\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx2(PriceListListTableActions, { priceList: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/use-pricing-table-filters.tsx\nvar usePricingTableFilters = () => {\n  const dateFilters = useDateTableFilters();\n  return dateFilters;\n};\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/use-pricing-table-query.tsx\nvar usePricingTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams([\"offset\", \"q\", \"order\", \"status\"], prefix);\n  const searchParams = {\n    limit: pageSize,\n    offset: raw.offset ? Number(raw.offset) : 0,\n    order: raw.order,\n    status: raw.status?.split(\",\"),\n    q: raw.q\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/price-lists/price-list-list/components/price-list-list-table/price-list-list-table.tsx\nimport { jsx as jsx3, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar PriceListListTable = () => {\n  const { t } = useTranslation3();\n  const { searchParams, raw } = usePricingTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { price_lists, count, isLoading, isError, error } = usePriceLists(\n    searchParams,\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = usePricingTableFilters();\n  const columns = usePricingTableColumns();\n  const { table } = useDataTable({\n    data: price_lists || [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx3(Heading, { children: t(\"priceLists.domain\") }),\n        /* @__PURE__ */ jsx3(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"priceLists.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx3(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx3(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        filters,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"status\", label: t(\"fields.status\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        pageSize: PAGE_SIZE,\n        navigateTo: (row) => row.original.id,\n        isLoading,\n        pagination: true,\n        search: true\n      }\n    )\n  ] });\n};\n\n// src/routes/price-lists/price-list-list/price-list-list.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar PriceListList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx4(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"price_list.list.after\"),\n        before: getWidgets(\"price_list.list.before\")\n      },\n      children: /* @__PURE__ */ jsx4(PriceListListTable, {})\n    }\n  );\n};\nexport {\n  PriceListList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,mBAAwB;AAMxB,yBAAoB;AAkCpB,IAAAA,sBAA4B;AAwD5B,IAAAA,sBAAkC;AA2DlC,IAAAA,sBAA4B;AApJ5B,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,yBAAyB,EAAE,UAAU,CAAC;AAC3D,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,GAAG,UAAU,EAAE;AAAA,cACnB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,UAAsB,oBAAAC,KAAK,YAAY,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAC1E,MAAM,CAAC,SAAS,KAAK,SAAS;AAAA,MAChC,CAAC;AAAA,MACD,aAAa,SAAS,UAAU;AAAA,QAC9B,QAAQ,EAAE,gCAAgC;AAAA,QAC1C,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,EAAE,OAAO,KAAK,IAAI,mBAAmB,GAAG,IAAI,QAAQ;AAC1D,qBAAuB,oBAAAA,KAAK,YAAY,EAAE,OAAO,UAAU,KAAK,CAAC;AAAA,QACnE;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,UAAU;AAAA,QAC9B,QAAQ,EAAE,yCAAyC;AAAA,QACnD,MAAM,CAAC,SAAM;AA1HrB;AA0HwC,yCAAAA,KAAK,UAAU,EAAE,MAAM,KAAG,UAAK,SAAS,MAAd,mBAAiB,WAAU,GAAG,GAAG,CAAC;AAAA;AAAA,MAC9F,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,2BAA2B,EAAE,WAAW,IAAI,SAAS,CAAC;AAAA,MAChG,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,yBAAyB,MAAM;AACjC,QAAM,cAAc,oBAAoB;AACxC,SAAO;AACT;AAGA,IAAI,uBAAuB,CAAC;AAAA,EAC1B,WAAW;AAAA,EACX;AACF,MAAM;AA/IN;AAgJE,QAAM,MAAM,eAAe,CAAC,UAAU,KAAK,SAAS,QAAQ,GAAG,MAAM;AACrE,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,IAAI,SAAS,OAAO,IAAI,MAAM,IAAI;AAAA,IAC1C,OAAO,IAAI;AAAA,IACX,SAAQ,SAAI,WAAJ,mBAAY,MAAM;AAAA,IAC1B,GAAG,IAAI;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,aAAa,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACxD;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,uBAAuB;AACvC,QAAM,UAAU,uBAAuB;AACvC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,eAAe,CAAC;AAAA,IACtB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,mBAAmB,EAAE,CAAC;AAAA,YAClD,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,MAClH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC5K,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,UAAU,OAAO,EAAE,eAAe,EAAE;AAAA,UAC3C,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,UAAU;AAAA,QACV,YAAY,CAAC,QAAQ,IAAI,SAAS;AAAA,QAClC;AAAA,QACA,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,uBAAuB;AAAA,QACzC,QAAQ,WAAW,wBAAwB;AAAA,MAC7C;AAAA,MACA,cAA0B,oBAAAA,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACvD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3", "jsx4"]}