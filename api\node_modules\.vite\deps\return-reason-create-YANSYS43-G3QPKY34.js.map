{"version": 3, "sources": ["../../@medusajs/dashboard/dist/return-reason-create-YANSYS43.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  useCreateReturnReason\n} from \"./chunk-2VTICXJR.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/return-reasons/return-reason-create/components/return-reason-create-form/return-reason-create-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, Textarea, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ReturnReasonCreateSchema = z.object({\n  value: z.string().min(1),\n  label: z.string().min(1),\n  description: z.string().optional()\n});\nvar ReturnReasonCreateForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      value: \"\",\n      label: \"\",\n      description: \"\"\n    },\n    resolver: zodResolver(ReturnReasonCreateSchema)\n  });\n  const { mutateAsync, isPending } = useCreateReturnReason();\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: ({ return_reason }) => {\n        toast.success(\n          t(\"returnReasons.create.successToast\", {\n            label: return_reason.label\n          })\n        );\n        handleSuccess(`../`);\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex size-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 justify-center overflow-auto px-6 py-16\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-1\", children: [\n            /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { children: t(\"returnReasons.create.header\") }) }),\n            /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"returnReasons.create.subtitle\") }) })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"grid gap-4 md:grid-cols-2\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"value\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(\n                      Form.Label,\n                      {\n                        tooltip: t(\"returnReasons.fields.value.tooltip\"),\n                        children: t(\"returnReasons.fields.value.label\")\n                      }\n                    ),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Input,\n                      {\n                        ...field,\n                        placeholder: t(\n                          \"returnReasons.fields.value.placeholder\"\n                        )\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"label\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"returnReasons.fields.label.label\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Input,\n                      {\n                        ...field,\n                        placeholder: t(\n                          \"returnReasons.fields.label.placeholder\"\n                        )\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] }),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"description\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"returnReasons.fields.description.label\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Textarea,\n                    {\n                      ...field,\n                      placeholder: t(\n                        \"returnReasons.fields.description.placeholder\"\n                      )\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", type: \"button\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/return-reasons/return-reason-create/return-reason-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ReturnReasonCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(ReturnReasonCreateForm, {}) });\n};\nexport {\n  ReturnReasonCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yBAA0B;AAkI1B,IAAAA,sBAA4B;AAjI5B,IAAI,2BAA2B,EAAE,OAAO;AAAA,EACtC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,aAAa,EAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AACD,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,UAAU,EAAY,wBAAwB;AAAA,EAChD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,sBAAsB;AACzD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,CAAC,EAAE,cAAc,MAAM;AAChC,cAAM;AAAA,UACJA,GAAE,qCAAqC;AAAA,YACrC,OAAO,cAAc;AAAA,UACvB,CAAC;AAAA,QACH;AACA,sBAAc,KAAK;AAAA,MACrB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,uDAAuD,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,cACvM,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,gBAC1D,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,UAAUA,GAAE,6BAA6B,EAAE,CAAC,EAAE,CAAC;AAAA,gBACpI,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,+BAA+B,EAAE,CAAC,EAAE,CAAC;AAAA,UAC1M,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,gBAC9D;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC;AAAA,sBACd,KAAK;AAAA,sBACL;AAAA,wBACE,SAASA,GAAE,oCAAoC;AAAA,wBAC/C,UAAUA,GAAE,kCAAkC;AAAA,sBAChD;AAAA,oBACF;AAAA,wBACgB,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,aAAaA;AAAA,0BACX;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,wBACnE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,aAAaA;AAAA,0BACX;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,wCAAwC,EAAE,CAAC;AAAA,sBACzF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,uCAAuC,UAAU;AAAA,cAChI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,MAAM,UAAU,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3K,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,qBAAqB,MAAM;AAC7B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,wBAAwB,CAAC,CAAC,EAAE,CAAC;AAC7G;", "names": ["import_jsx_runtime", "t", "jsx2"]}