{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-RPAL6FHW.mjs"], "sourcesContent": ["// src/routes/workflow-executions/constants.ts\nvar STEP_IN_PROGRESS_STATES = [\n  \"compensating\" /* COMPENSATING */,\n  \"invoking\" /* INVOKING */\n];\nvar STEP_SKIPPED_STATES = [\n  \"skipped\" /* SKIPPED */,\n  \"skipped_failure\" /* SKIPPED_FAILURE */\n];\nvar STEP_OK_STATES = [\n  \"done\" /* DONE */\n];\nvar STEP_ERROR_STATES = [\n  \"failed\" /* FAILED */,\n  \"reverted\" /* REVERTED */,\n  \"timeout\" /* TIMEOUT */,\n  \"dormant\" /* DORMANT */\n];\nvar STEP_INACTIVE_STATES = [\n  \"not_started\" /* NOT_STARTED */\n];\nvar TRANSACTION_OK_STATES = [\n  \"done\" /* DONE */\n];\nvar TRANSACTION_ERROR_STATES = [\n  \"failed\" /* FAILED */,\n  \"reverted\" /* REVERTED */\n];\nvar TRANSACTION_IN_PROGRESS_STATES = [\n  \"invoking\" /* INVOKING */,\n  \"waiting_to_compensate\" /* WAITING_TO_COMPENSATE */,\n  \"compensating\" /* COMPENSATING */\n];\n\n// src/routes/workflow-executions/utils.ts\nvar getTransactionStateColor = (state) => {\n  let statusColor = \"green\";\n  if (TRANSACTION_ERROR_STATES.includes(state)) {\n    statusColor = \"red\";\n  }\n  if (TRANSACTION_IN_PROGRESS_STATES.includes(state)) {\n    statusColor = \"orange\";\n  }\n  return statusColor;\n};\nvar getTransactionState = (t, state) => {\n  switch (state) {\n    case \"done\" /* DONE */:\n      return t(\"workflowExecutions.state.done\");\n    case \"failed\" /* FAILED */:\n      return t(\"workflowExecutions.state.failed\");\n    case \"reverted\" /* REVERTED */:\n      return t(\"workflowExecutions.state.reverted\");\n    case \"invoking\" /* INVOKING */:\n      return t(\"workflowExecutions.state.invoking\");\n    case \"waiting_to_compensate\" /* WAITING_TO_COMPENSATE */:\n      return t(\"workflowExecutions.transaction.state.waitingToCompensate\");\n    case \"compensating\" /* COMPENSATING */:\n      return t(\"workflowExecutions.state.compensating\");\n    case \"not_started\" /* NOT_STARTED */:\n      return t(\"workflowExecutions.state.notStarted\");\n  }\n};\n\nexport {\n  STEP_IN_PROGRESS_STATES,\n  STEP_SKIPPED_STATES,\n  STEP_OK_STATES,\n  STEP_ERROR_STATES,\n  STEP_INACTIVE_STATES,\n  getTransactionStateColor,\n  getTransactionState\n};\n"], "mappings": ";AACA,IAAI,0BAA0B;AAAA,EAC5B;AAAA,EACA;AAAA;AACF;AACA,IAAI,sBAAsB;AAAA,EACxB;AAAA,EACA;AAAA;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB;AAAA;AACF;AACA,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB;AAAA;AACF;AAIA,IAAI,2BAA2B;AAAA,EAC7B;AAAA,EACA;AAAA;AACF;AACA,IAAI,iCAAiC;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA;AACF;AAGA,IAAI,2BAA2B,CAAC,UAAU;AACxC,MAAI,cAAc;AAClB,MAAI,yBAAyB,SAAS,KAAK,GAAG;AAC5C,kBAAc;AAAA,EAChB;AACA,MAAI,+BAA+B,SAAS,KAAK,GAAG;AAClD,kBAAc;AAAA,EAChB;AACA,SAAO;AACT;AACA,IAAI,sBAAsB,CAAC,GAAG,UAAU;AACtC,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO,EAAE,+BAA+B;AAAA,IAC1C,KAAK;AACH,aAAO,EAAE,iCAAiC;AAAA,IAC5C,KAAK;AACH,aAAO,EAAE,mCAAmC;AAAA,IAC9C,KAAK;AACH,aAAO,EAAE,mCAAmC;AAAA,IAC9C,KAAK;AACH,aAAO,EAAE,0DAA0D;AAAA,IACrE,KAAK;AACH,aAAO,EAAE,uCAAuC;AAAA,IAClD,KAAK;AACH,aAAO,EAAE,qCAAqC;AAAA,EAClD;AACF;", "names": []}