import {
  Form
} from "./chunk-DPO7J5IQ.js";
import {
  Switch
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-D7H6ZNK4.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var SwitchBox = ({
  label,
  description,
  optional = false,
  tooltip,
  onCheckedChange,
  ...props
}) => {
  return (0, import_jsx_runtime.jsx)(
    Form.Field,
    {
      ...props,
      render: ({ field: { value, onChange, ...field } }) => {
        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-component shadow-elevation-card-rest flex items-start gap-x-3 rounded-lg p-3", children: [
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
              Switch,
              {
                ...field,
                checked: value,
                onCheckedChange: (e) => {
                  onCheckedChange == null ? void 0 : onCheckedChange(e);
                  onChange(e);
                }
              }
            ) }),
            (0, import_jsx_runtime.jsxs)("div", { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional, tooltip, children: label }),
              (0, import_jsx_runtime.jsx)(Form.Hint, { children: description })
            ] })
          ] }),
          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
        ] });
      }
    }
  );
};

export {
  SwitchBox
};
//# sourceMappingURL=chunk-HK2C7UM6.js.map
