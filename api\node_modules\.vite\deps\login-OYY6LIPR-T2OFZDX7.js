import {
  AvatarBox
} from "./chunk-SE4O2R74.js";
import "./chunk-73I4NEMG.js";
import {
  isFetchError
} from "./chunk-XBF43SLF.js";
import "./chunk-7M4ICL3D.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import {
  useSignInWithEmailPass
} from "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLocation,
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  Heading,
  Hint,
  Input,
  Text
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/login-OYY6LIPR.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var LoginSchema = objectType({
  email: stringType().email(),
  password: stringType()
});
var Login = () => {
  var _a, _b, _c, _d, _e, _f, _g;
  const { t: t2 } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { getWidgets } = useExtension();
  const from = ((_b = (_a = location.state) == null ? void 0 : _a.from) == null ? void 0 : _b.pathname) || "/orders";
  const form = useForm({
    resolver: t(LoginSchema),
    defaultValues: {
      email: "",
      password: ""
    }
  });
  const { mutateAsync, isPending } = useSignInWithEmailPass();
  const handleSubmit = form.handleSubmit(async ({ email, password }) => {
    await mutateAsync(
      {
        email,
        password
      },
      {
        onError: (error) => {
          if (isFetchError(error)) {
            if (error.status === 401) {
              form.setError("email", {
                type: "manual",
                message: error.message
              });
              return;
            }
          }
          form.setError("root.serverError", {
            type: "manual",
            message: error.message
          });
        },
        onSuccess: () => {
          navigate(from, { replace: true });
        }
      }
    );
  });
  const serverError = (_e = (_d = (_c = form.formState.errors) == null ? void 0 : _c.root) == null ? void 0 : _d.serverError) == null ? void 0 : _e.message;
  const validationError = ((_f = form.formState.errors.email) == null ? void 0 : _f.message) || ((_g = form.formState.errors.password) == null ? void 0 : _g.message);
  return (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-subtle flex min-h-dvh w-dvw items-center justify-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "m-4 flex w-full max-w-[280px] flex-col items-center", children: [
    (0, import_jsx_runtime.jsx)(AvatarBox, {}),
    (0, import_jsx_runtime.jsxs)("div", { className: "mb-4 flex flex-col items-center", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("login.title") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("login.hint") })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col gap-y-3", children: [
      getWidgets("login.before").map((Component, i) => {
        return (0, import_jsx_runtime.jsx)(Component, {}, i);
      }),
      (0, import_jsx_runtime.jsx)(Form, { ...form, children: (0, import_jsx_runtime.jsxs)(
        "form",
        {
          onSubmit: handleSubmit,
          className: "flex w-full flex-col gap-y-6",
          children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "email",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        autoComplete: "email",
                        ...field,
                        className: "bg-ui-bg-field-component",
                        placeholder: t2("fields.email")
                      }
                    ) }) });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "password",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, {}),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Input,
                        {
                          type: "password",
                          autoComplete: "current-password",
                          ...field,
                          className: "bg-ui-bg-field-component",
                          placeholder: t2("fields.password")
                        }
                      ) })
                    ] });
                  }
                }
              )
            ] }),
            validationError && (0, import_jsx_runtime.jsx)("div", { className: "text-center", children: (0, import_jsx_runtime.jsx)(Hint, { className: "inline-flex", variant: "error", children: validationError }) }),
            serverError && (0, import_jsx_runtime.jsx)(
              Alert,
              {
                className: "bg-ui-bg-base items-center p-2",
                dismissible: true,
                variant: "error",
                children: serverError
              }
            ),
            (0, import_jsx_runtime.jsx)(Button, { className: "w-full", type: "submit", isLoading: isPending, children: t2("actions.continueWithEmail") })
          ]
        }
      ) }),
      getWidgets("login.after").map((Component, i) => {
        return (0, import_jsx_runtime.jsx)(Component, {}, i);
      })
    ] }),
    (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted txt-small my-6", children: (0, import_jsx_runtime.jsx)(
      Trans,
      {
        i18nKey: "login.forgotPassword",
        components: [
          (0, import_jsx_runtime.jsx)(
            Link,
            {
              to: "/reset-password",
              className: "text-ui-fg-interactive transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover font-medium outline-none"
            },
            "reset-password-link"
          )
        ]
      }
    ) })
  ] }) });
};
export {
  Login as Component
};
//# sourceMappingURL=login-OYY6LIPR-T2OFZDX7.js.map
