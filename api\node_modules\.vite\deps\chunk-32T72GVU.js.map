{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-C76H5USB.mjs"], "sourcesContent": ["// src/hooks/use-query-params.tsx\nimport { useSearchParams } from \"react-router-dom\";\nfunction useQueryParams(keys, prefix) {\n  const [params] = useSearchParams();\n  const result = {};\n  keys.forEach((key) => {\n    const prefixedKey = prefix ? `${prefix}_${key}` : key;\n    const value = params.get(prefixedKey) || void 0;\n    result[key] = value;\n  });\n  return result;\n}\n\nexport {\n  useQueryParams\n};\n"], "mappings": ";;;;;AAEA,SAAS,eAAe,MAAM,QAAQ;AACpC,QAAM,CAAC,MAAM,IAAI,gBAAgB;AACjC,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,QAAQ;AACpB,UAAM,cAAc,SAAS,GAAG,MAAM,IAAI,GAAG,KAAK;AAClD,UAAM,QAAQ,OAAO,IAAI,WAAW,KAAK;AACzC,WAAO,GAAG,IAAI;AAAA,EAChB,CAAC;AACD,SAAO;AACT;", "names": []}