import {
  useDeleteProductCategoryAction
} from "./chunk-TM7HU4GE.js";
import {
  getCategoryChildren,
  getCategoryPath,
  getIsActiveProps,
  getIsInternalProps
} from "./chunk-RGG2AVZE.js";
import {
  LinkButton
} from "./chunk-W2IWODMV.js";
import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-EUULPFXI.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-XYBN3KRC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Skeleton,
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import {
  categoriesQueryKeys,
  useProductCategory,
  useUpdateProductCategoryProducts
} from "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Checkbox,
  CommandBar,
  Container,
  FolderIllustration,
  Heading,
  PencilSquare,
  PlusMini,
  StatusBadge,
  Text,
  Tooltip,
  Trash,
  TriangleRightMini,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/category-detail-BDPQPVQS.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var CategoryDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { product_category } = useProductCategory(
    id,
    {
      fields: "name"
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!product_category) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: product_category.name });
};
var CategoryGeneralSection = ({
  category
}) => {
  const { t } = useTranslation();
  const activeProps = getIsActiveProps(category.is_active, t);
  const internalProps = getIsInternalProps(category.is_internal, t);
  const handleDelete = useDeleteProductCategoryAction(category);
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: category.name }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(StatusBadge, { color: activeProps.color, children: activeProps.label }),
          (0, import_jsx_runtime2.jsx)(StatusBadge, { color: internalProps.color, children: internalProps.label })
        ] }),
        (0, import_jsx_runtime2.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                    to: "edit"
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 gap-3 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.description") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: category.description || "-" })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 gap-3 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.handle") }),
      (0, import_jsx_runtime2.jsxs)(Text, { size: "small", leading: "compact", children: [
        "/",
        category.handle
      ] })
    ] })
  ] });
};
var CategoryOrganizeSection = ({
  category
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("categories.organize.header") }),
      (0, import_jsx_runtime3.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("categories.organize.action"),
                  icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {}),
                  to: `organize`
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start gap-3 px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("categories.fields.path.label") }),
      (0, import_jsx_runtime3.jsx)(PathDisplay, { category })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start gap-3 px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("categories.fields.children.label") }),
      (0, import_jsx_runtime3.jsx)(ChildrenDisplay, { category })
    ] })
  ] });
};
var PathDisplay = ({
  category
}) => {
  const [expanded, setExpanded] = (0, import_react.useState)(false);
  const { t } = useTranslation();
  const {
    product_category: withParents,
    isLoading,
    isError,
    error
  } = useProductCategory(category.id, {
    include_ancestors_tree: true,
    fields: "id,name,*parent_category"
  });
  const chips = (0, import_react.useMemo)(() => getCategoryPath(withParents), [withParents]);
  if (isLoading || !withParents) {
    return (0, import_jsx_runtime3.jsx)(Skeleton, { className: "h-5 w-16" });
  }
  if (isError) {
    throw error;
  }
  if (!chips.length) {
    return (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: "-" });
  }
  if (chips.length > 1 && !expanded) {
    return (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-[20px_1fr] items-start gap-x-2", children: [
      (0, import_jsx_runtime3.jsx)(FolderIllustration, {}),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex w-full items-center gap-x-0.5 overflow-hidden", children: [
        (0, import_jsx_runtime3.jsx)(Tooltip, { content: t("categories.fields.path.tooltip"), children: (0, import_jsx_runtime3.jsx)(
          "button",
          {
            className: "outline-none",
            type: "button",
            onClick: () => setExpanded(true),
            children: (0, import_jsx_runtime3.jsx)(Text, { size: "xsmall", leading: "compact", weight: "plus", children: "..." })
          }
        ) }),
        (0, import_jsx_runtime3.jsx)("div", { className: "flex size-[15px] shrink-0 items-center justify-center", children: (0, import_jsx_runtime3.jsx)(TriangleRightMini, {}) }),
        (0, import_jsx_runtime3.jsx)(
          Text,
          {
            size: "xsmall",
            leading: "compact",
            weight: "plus",
            className: "truncate",
            children: chips[chips.length - 1].name
          }
        )
      ] })
    ] });
  }
  if (chips.length > 1 && expanded) {
    return (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-[20px_1fr] items-start gap-x-2", children: [
      (0, import_jsx_runtime3.jsx)(FolderIllustration, {}),
      (0, import_jsx_runtime3.jsx)("div", { className: "gap- flex flex-wrap items-center gap-x-0.5 gap-y-1", children: chips.map((chip, index) => {
        return (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-0.5", children: [
          index === chips.length - 1 ? (0, import_jsx_runtime3.jsx)(Text, { size: "xsmall", leading: "compact", weight: "plus", children: chip.name }) : (0, import_jsx_runtime3.jsx)(
            LinkButton,
            {
              to: `/categories/${chip.id}`,
              className: "txt-compact-xsmall-plus text-ui-fg-subtle hover:text-ui-fg-base focus-visible:text-ui-fg-base",
              children: chip.name
            }
          ),
          index < chips.length - 1 && (0, import_jsx_runtime3.jsx)(TriangleRightMini, {})
        ] }, chip.id);
      }) })
    ] });
  }
  return (0, import_jsx_runtime3.jsx)("div", { className: "grid grid-cols-1 items-start gap-x-2", children: chips.map((chip, index) => (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-0.5", children: [
    (0, import_jsx_runtime3.jsx)(Text, { size: "xsmall", leading: "compact", weight: "plus", children: chip.name }),
    index < chips.length - 1 && (0, import_jsx_runtime3.jsx)(TriangleRightMini, {})
  ] }, chip.id)) });
};
var ChildrenDisplay = ({
  category
}) => {
  const {
    product_category: withChildren,
    isLoading,
    isError,
    error
  } = useProductCategory(category.id, {
    include_descendants_tree: true,
    fields: "id,name,category_children"
  });
  const chips = (0, import_react.useMemo)(() => getCategoryChildren(withChildren), [withChildren]);
  if (isLoading || !withChildren) {
    return (0, import_jsx_runtime3.jsx)(Skeleton, { className: "h-5 w-16" });
  }
  if (isError) {
    throw error;
  }
  if (!chips.length) {
    return (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: "-" });
  }
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex w-full flex-wrap gap-1", children: chips.map((chip) => (0, import_jsx_runtime3.jsx)(Badge, { size: "2xsmall", className: "max-w-full", asChild: true, children: (0, import_jsx_runtime3.jsx)(Link, { to: `/categories/${chip.id}`, children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: chip.name }) }) }, chip.id)) });
};
var PAGE_SIZE = 10;
var CategoryProductSection = ({
  category
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const [selection, setSelection] = (0, import_react2.useState)({});
  const { raw, searchParams } = useProductTableQuery({ pageSize: PAGE_SIZE });
  const { products, count, isLoading, isError, error } = useProducts(
    {
      ...searchParams,
      category_id: [category.id]
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useProductTableFilters(["categories"]);
  const { table } = useDataTable({
    data: products || [],
    columns,
    count,
    getRowId: (original) => original.id,
    pageSize: PAGE_SIZE,
    enableRowSelection: true,
    enablePagination: true,
    rowSelection: {
      state: selection,
      updater: setSelection
    }
  });
  const { mutateAsync } = useUpdateProductCategoryProducts(category.id);
  const handleRemove = async () => {
    const selected = Object.keys(selection);
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("categories.products.remove.confirmation", {
        count: selected.length
      }),
      confirmText: t("actions.remove"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(
      {
        remove: selected
      },
      {
        onSuccess: () => {
          toast.success(
            t("categories.products.remove.successToast", {
              count: selected.length
            })
          );
          setSelection({});
        },
        onError: (error2) => {
          toast.error(error2.message);
        }
      }
    );
  };
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("products.domain") }),
      (0, import_jsx_runtime4.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.add"),
                  icon: (0, import_jsx_runtime4.jsx)(PlusMini, {}),
                  to: "products"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime4.jsx)(
      _DataTable,
      {
        table,
        filters,
        columns,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        pageSize: PAGE_SIZE,
        count,
        navigateTo: (row) => `/products/${row.id}`,
        isLoading,
        queryObject: raw,
        noRecords: {
          message: t("categories.products.list.noRecordsMessage")
        }
      }
    ),
    (0, import_jsx_runtime4.jsx)(CommandBar, { open: !!Object.keys(selection).length, children: (0, import_jsx_runtime4.jsxs)(CommandBar.Bar, { children: [
      (0, import_jsx_runtime4.jsx)(CommandBar.Value, { children: t("general.countSelected", {
        count: Object.keys(selection).length
      }) }),
      (0, import_jsx_runtime4.jsx)(CommandBar.Seperator, {}),
      (0, import_jsx_runtime4.jsx)(
        CommandBar.Command,
        {
          action: handleRemove,
          label: t("actions.remove"),
          shortcut: "r"
        }
      )
    ] }) })
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTableColumns();
  return (0, import_react2.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var CategoryDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const { getWidgets } = useExtension();
  const { product_category, isLoading, isError, error } = useProductCategory(
    id,
    void 0,
    {
      initialData
    }
  );
  if (isLoading || !product_category) {
    return (0, import_jsx_runtime5.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 2,
        sidebarSections: 1,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("product_category.details.after"),
        before: getWidgets("product_category.details.before"),
        sideAfter: getWidgets("product_category.details.side.after"),
        sideBefore: getWidgets("product_category.details.side.before")
      },
      showJSON: true,
      showMetadata: true,
      data: product_category,
      children: [
        (0, import_jsx_runtime5.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime5.jsx)(CategoryGeneralSection, { category: product_category }),
          (0, import_jsx_runtime5.jsx)(CategoryProductSection, { category: product_category })
        ] }),
        (0, import_jsx_runtime5.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime5.jsx)(CategoryOrganizeSection, { category: product_category }) })
      ]
    }
  );
};
var categoryDetailQuery = (id) => ({
  queryKey: categoriesQueryKeys.detail(id),
  queryFn: async () => sdk.admin.productCategory.retrieve(id)
});
var categoryLoader = async ({ params }) => {
  const id = params.id;
  const query = categoryDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  CategoryDetailBreadcrumb as Breadcrumb,
  CategoryDetail as Component,
  categoryLoader as loader
};
//# sourceMappingURL=category-detail-BDPQPVQS-TWYNN3T2.js.map
