import {
  getProvinceByIso2,
  isProvinceInCountry
} from "./chunk-KL72CHML.js";
import {
  react_country_flag_esm_default
} from "./chunk-QF476XOZ.js";
import {
  IconAvatar
} from "./chunk-73I4NEMG.js";
import {
  getCountryByIso2
} from "./chunk-XGFC5LFP.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import {
  useDeleteTaxRate
} from "./chunk-Y5G57T2Y.js";
import {
  useDeleteTaxRegion
} from "./chunk-PBNFBMP6.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Link,
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  ExclamationCircle,
  Heading,
  MapPin,
  Plus,
  Table,
  Text,
  Tooltip,
  Trash,
  clx,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-X2Y4KNQI.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var useDeleteTaxRegionAction = ({
  taxRegion,
  to = "/settings/tax-regions"
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteTaxRegion(taxRegion.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("taxRegions.delete.confirmation"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("taxRegions.delete.successToast"));
        navigate(to, { replace: true });
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return handleDelete;
};
var useDeleteTaxRateAction = (taxRate) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteTaxRate(taxRate.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("taxRegions.taxRates.delete.confirmation", {
        name: taxRate.name
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("taxRegions.taxRates.delete.successToast"));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return handleDelete;
};
var TaxRegionCard = ({
  taxRegion,
  type = "list",
  variant = "country",
  asLink = true,
  badge
}) => {
  const { t } = useTranslation();
  const { id, country_code, province_code } = taxRegion;
  const country = getCountryByIso2(country_code);
  const province = getProvinceByIso2(province_code);
  let name = "N/A";
  let misconfiguredSublevelTooltip = null;
  if (province || province_code) {
    name = province ? province : province_code.toUpperCase();
  } else if (country || country_code) {
    name = country ? country.display_name : country_code.toUpperCase();
  }
  if (country_code && province_code && !isProvinceInCountry(country_code, province_code)) {
    name = province_code.toUpperCase();
    misconfiguredSublevelTooltip = t(
      "taxRegions.fields.sublevels.tooltips.notPartOfCountry",
      {
        country: country == null ? void 0 : country.display_name,
        province: province_code.toUpperCase()
      }
    );
  }
  const showCreateDefaultTaxRate = !taxRegion.tax_rates.filter((tr) => tr.is_default).length && type === "header";
  const Component = (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "group-data-[link=true]:hover:bg-ui-bg-base-hover transition-fg flex flex-col justify-between gap-y-4 px-6 md:flex-row md:items-center md:gap-y-0",
        {
          "py-4": type === "header",
          "py-3": type === "list"
        }
      ),
      children: [
        (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-4", children: [
            (0, import_jsx_runtime.jsx)(IconAvatar, { size: type === "list" ? "small" : "large", children: country_code && !province_code ? (0, import_jsx_runtime.jsx)(
              "div",
              {
                className: clx(
                  "flex size-fit items-center justify-center overflow-hidden rounded-[1px]",
                  {
                    "rounded-sm": type === "header"
                  }
                ),
                children: (0, import_jsx_runtime.jsx)(
                  react_country_flag_esm_default,
                  {
                    countryCode: country_code,
                    svg: true,
                    style: type === "list" ? { width: "12px", height: "9px" } : { width: "16px", height: "12px" },
                    "aria-label": country == null ? void 0 : country.display_name
                  }
                )
              }
            ) : (0, import_jsx_runtime.jsx)(MapPin, { className: "text-ui-fg-subtle" }) }),
            (0, import_jsx_runtime.jsx)("div", { children: type === "list" ? (0, import_jsx_runtime.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: name }) : (0, import_jsx_runtime.jsx)(Heading, { children: name }) })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex size-fit items-center gap-x-2 md:hidden", children: [
            misconfiguredSublevelTooltip && (0, import_jsx_runtime.jsx)(Tooltip, { content: misconfiguredSublevelTooltip, children: (0, import_jsx_runtime.jsx)(ExclamationCircle, { className: "text-ui-tag-orange-icon" }) }),
            badge,
            (0, import_jsx_runtime.jsx)(
              TaxRegionCardActions,
              {
                taxRegion,
                showCreateDefaultTaxRate
              }
            )
          ] })
        ] }),
        (0, import_jsx_runtime.jsxs)("div", { className: "hidden size-fit items-center gap-x-2 md:flex", children: [
          misconfiguredSublevelTooltip && (0, import_jsx_runtime.jsx)(Tooltip, { content: misconfiguredSublevelTooltip, children: (0, import_jsx_runtime.jsx)(ExclamationCircle, { className: "text-ui-tag-orange-icon" }) }),
          badge,
          (0, import_jsx_runtime.jsx)(
            TaxRegionCardActions,
            {
              taxRegion,
              showCreateDefaultTaxRate
            }
          )
        ] })
      ]
    }
  );
  if (asLink) {
    return (0, import_jsx_runtime.jsx)(
      Link,
      {
        to: variant === "country" ? `${id}` : `provinces/${id}`,
        "data-link": "true",
        className: "group block",
        children: Component
      }
    );
  }
  return Component;
};
var TaxRegionCardActions = ({
  taxRegion,
  showCreateDefaultTaxRate
}) => {
  const { t } = useTranslation();
  const to = taxRegion.parent_id ? `/settings/tax-regions/${taxRegion.parent_id}` : void 0;
  const handleDelete = useDeleteTaxRegionAction({ taxRegion, to });
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        ...showCreateDefaultTaxRate ? [
          {
            actions: [
              {
                icon: (0, import_jsx_runtime.jsx)(Plus, {}),
                label: t("taxRegions.fields.defaultTaxRate.action"),
                to: `tax-rates/create`
              }
            ]
          }
        ] : [],
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var LocalizedTablePagination = (0, import_react.forwardRef)((props, ref) => {
  const { t } = useTranslation();
  const translations = {
    of: t("general.of"),
    results: t("general.results"),
    pages: t("general.pages"),
    prev: t("general.prev"),
    next: t("general.next")
  };
  return (0, import_jsx_runtime2.jsx)(Table.Pagination, { ...props, translations, ref });
});
LocalizedTablePagination.displayName = "LocalizedTablePagination";

export {
  useDeleteTaxRateAction,
  TaxRegionCard,
  LocalizedTablePagination
};
//# sourceMappingURL=chunk-HJV4BH76.js.map
