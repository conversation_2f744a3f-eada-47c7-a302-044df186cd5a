import "./chunk-XMKQFEQ4.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import "./chunk-RPCDYKBN.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/home-KSB2J7CS.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Home = () => {
  const navigate = useNavigate();
  (0, import_react.useEffect)(() => {
    navigate("/orders", { replace: true });
  }, [navigate]);
  return (0, import_jsx_runtime.jsx)("div", {});
};
export {
  Home as Component
};
//# sourceMappingURL=home-KSB2J7CS-O2DLHN4P.js.map
