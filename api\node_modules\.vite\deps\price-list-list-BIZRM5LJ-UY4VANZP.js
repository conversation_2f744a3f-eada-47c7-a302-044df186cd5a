import {
  useDeletePriceListAction
} from "./chunk-Q2DLRTSI.js";
import {
  getPriceListStatus
} from "./chunk-DC7EOWEK.js";
import "./chunk-EG6IR476.js";
import {
  TextCell,
  TextHeader
} from "./chunk-7HUCBNCQ.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-EZLR4STK.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useDateTableFilters
} from "./chunk-IONS3C54.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  usePriceLists
} from "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-list-BIZRM5LJ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var PriceListListTableActions = ({
  priceList
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeletePriceListAction({ priceList });
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              to: `${priceList.id}/edit`,
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {})
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              onClick: handleDelete,
              icon: (0, import_jsx_runtime.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var usePricingTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("title", {
        header: () => (0, import_jsx_runtime2.jsx)(TextHeader, { text: t("fields.title") }),
        cell: (info) => info.getValue()
      }),
      columnHelper.accessor("status", {
        header: t("priceLists.fields.status.label"),
        cell: ({ row }) => {
          const { color, text } = getPriceListStatus(t, row.original);
          return (0, import_jsx_runtime2.jsx)(StatusCell, { color, children: text });
        }
      }),
      columnHelper.accessor("prices", {
        header: t("priceLists.fields.priceOverrides.header"),
        cell: (info) => {
          var _a;
          return (0, import_jsx_runtime2.jsx)(TextCell, { text: `${((_a = info.getValue()) == null ? void 0 : _a.length) || "-"}` });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime2.jsx)(PriceListListTableActions, { priceList: row.original })
      })
    ],
    [t]
  );
};
var usePricingTableFilters = () => {
  const dateFilters = useDateTableFilters();
  return dateFilters;
};
var usePricingTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  var _a;
  const raw = useQueryParams(["offset", "q", "order", "status"], prefix);
  const searchParams = {
    limit: pageSize,
    offset: raw.offset ? Number(raw.offset) : 0,
    order: raw.order,
    status: (_a = raw.status) == null ? void 0 : _a.split(","),
    q: raw.q
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE = 20;
var PriceListListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = usePricingTableQuery({
    pageSize: PAGE_SIZE
  });
  const { price_lists, count, isLoading, isError, error } = usePriceLists(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = usePricingTableFilters();
  const columns = usePricingTableColumns();
  const { table } = useDataTable({
    data: price_lists || [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsxs)("div", { children: [
        (0, import_jsx_runtime3.jsx)(Heading, { children: t("priceLists.domain") }),
        (0, import_jsx_runtime3.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("priceLists.subtitle") })
      ] }),
      (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime3.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        columns,
        count,
        filters,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "status", label: t("fields.status") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        queryObject: raw,
        pageSize: PAGE_SIZE,
        navigateTo: (row) => row.original.id,
        isLoading,
        pagination: true,
        search: true
      }
    )
  ] });
};
var PriceListList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime4.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("price_list.list.after"),
        before: getWidgets("price_list.list.before")
      },
      children: (0, import_jsx_runtime4.jsx)(PriceListListTable, {})
    }
  );
};
export {
  PriceListList as Component
};
//# sourceMappingURL=price-list-list-BIZRM5LJ-UY4VANZP.js.map
