{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-KD3INMVA.mjs"], "sourcesContent": ["import {\n  i18n\n} from \"./chunk-QQ3CHZKV.mjs\";\n\n// src/lib/promotions.ts\nvar promotionStatusMap = {\n  [\"ACTIVE\" /* ACTIVE */]: [\"green\", i18n.t(\"statuses.active\")],\n  [\"INACTIVE\" /* INACTIVE */]: [\"red\", i18n.t(\"statuses.inactive\")],\n  [\"DRAFT\" /* DRAFT */]: [\"grey\", i18n.t(\"statuses.draft\")],\n  [\"SCHEDULED\" /* SCHEDULED */]: [\n    \"orange\",\n    `${i18n.t(\"promotions.fields.campaign\")} ${i18n.t(\"statuses.scheduled\").toLowerCase()}`\n  ],\n  [\"EXPIRED\" /* EXPIRED */]: [\n    \"red\",\n    `${i18n.t(\"promotions.fields.campaign\")} ${i18n.t(\"statuses.expired\").toLowerCase()}`\n  ]\n};\nvar getPromotionStatus = (promotion) => {\n  const date = /* @__PURE__ */ new Date();\n  const campaign = promotion.campaign;\n  if (!campaign) {\n    return promotionStatusMap[promotion.status.toUpperCase()];\n  }\n  if (campaign.starts_at && new Date(campaign.starts_at) > date) {\n    return promotionStatusMap[\"SCHEDULED\" /* SCHEDULED */];\n  }\n  const campaignBudget = campaign.budget;\n  const overBudget = campaignBudget && campaignBudget.used > campaignBudget.limit;\n  if (campaign.ends_at && new Date(campaign.ends_at) < date || overBudget) {\n    return promotionStatusMap[\"EXPIRED\" /* EXPIRED */];\n  }\n  return promotionStatusMap[promotion.status.toUpperCase()];\n};\n\nexport {\n  getPromotionStatus\n};\n"], "mappings": ";;;;;AAKA,IAAI,qBAAqB;AAAA,EACvB;AAAA,IAAC;AAAA;AAAA,EAAqB,GAAG,CAAC,SAAS,SAAK,EAAE,iBAAiB,CAAC;AAAA,EAC5D;AAAA,IAAC;AAAA;AAAA,EAAyB,GAAG,CAAC,OAAO,SAAK,EAAE,mBAAmB,CAAC;AAAA,EAChE;AAAA,IAAC;AAAA;AAAA,EAAmB,GAAG,CAAC,QAAQ,SAAK,EAAE,gBAAgB,CAAC;AAAA,EACxD;AAAA,IAAC;AAAA;AAAA,EAA2B,GAAG;AAAA,IAC7B;AAAA,IACA,GAAG,SAAK,EAAE,4BAA4B,CAAC,IAAI,SAAK,EAAE,oBAAoB,EAAE,YAAY,CAAC;AAAA,EACvF;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAuB,GAAG;AAAA,IACzB;AAAA,IACA,GAAG,SAAK,EAAE,4BAA4B,CAAC,IAAI,SAAK,EAAE,kBAAkB,EAAE,YAAY,CAAC;AAAA,EACrF;AACF;AACA,IAAI,qBAAqB,CAAC,cAAc;AACtC,QAAM,OAAuB,oBAAI,KAAK;AACtC,QAAM,WAAW,UAAU;AAC3B,MAAI,CAAC,UAAU;AACb,WAAO,mBAAmB,UAAU,OAAO,YAAY,CAAC;AAAA,EAC1D;AACA,MAAI,SAAS,aAAa,IAAI,KAAK,SAAS,SAAS,IAAI,MAAM;AAC7D,WAAO;AAAA,MAAmB;AAAA;AAAA,IAA2B;AAAA,EACvD;AACA,QAAM,iBAAiB,SAAS;AAChC,QAAM,aAAa,kBAAkB,eAAe,OAAO,eAAe;AAC1E,MAAI,SAAS,WAAW,IAAI,KAAK,SAAS,OAAO,IAAI,QAAQ,YAAY;AACvE,WAAO;AAAA,MAAmB;AAAA;AAAA,IAAuB;AAAA,EACnD;AACA,SAAO,mBAAmB,UAAU,OAAO,YAAY,CAAC;AAC1D;", "names": []}