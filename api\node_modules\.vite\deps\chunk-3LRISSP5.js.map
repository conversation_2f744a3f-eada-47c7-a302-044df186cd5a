{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-GW6TVOAA.mjs"], "sourcesContent": ["import {\n  useDateTableFilters\n} from \"./chunk-W7625H47.mjs\";\n\n// src/hooks/table/filters/use-collection-table-filters.tsx\nvar useCollectionTableFilters = () => {\n  const dateFilters = useDateTableFilters();\n  return dateFilters;\n};\n\n// src/hooks/table/filters/use-product-tag-table-filters.tsx\nvar useProductTagTableFilters = () => {\n  const dateFilters = useDateTableFilters();\n  return dateFilters;\n};\n\n// src/hooks/table/filters/use-shipping-option-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\n\n// src/hooks/table/filters/use-tax-rate-table-filters.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\nexport {\n  useCollectionTableFilters,\n  useProductTagTableFilters\n};\n"], "mappings": ";;;;;AAKA,IAAI,4BAA4B,MAAM;AACpC,QAAM,cAAc,oBAAoB;AACxC,SAAO;AACT;AAGA,IAAI,4BAA4B,MAAM;AACpC,QAAM,cAAc,oBAAoB;AACxC,SAAO;AACT;", "names": []}