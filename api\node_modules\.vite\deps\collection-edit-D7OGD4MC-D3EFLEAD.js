import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCollection,
  useUpdateCollection
} from "./chunk-E4TFG45M.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  <PERSON><PERSON>,
  Heading,
  Input,
  Text
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/collection-edit-D7OGD4MC.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditCollectionSchema = objectType({
  title: stringType().min(1),
  handle: stringType().min(1)
});
var EditCollectionForm = ({ collection }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      title: collection.title,
      handle: collection.handle
    },
    resolver: t(EditCollectionSchema)
  });
  const { mutateAsync, isPending } = useUpdateCollection(collection.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(data, {
      onSuccess: () => {
        handleSuccess();
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "title",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "handle",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { tooltip: t2("collections.handleTooltip"), children: t2("fields.handle") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
                (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r", children: (0, import_jsx_runtime.jsx)(
                  Text,
                  {
                    className: "text-ui-fg-muted",
                    size: "small",
                    leading: "compact",
                    weight: "plus",
                    children: "/"
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Input, { ...field, className: "pl-10" })
              ] }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var CollectionEdit = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { collection, isLoading, isError, error } = useCollection(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("collections.editCollection") }) }),
    !isLoading && collection && (0, import_jsx_runtime2.jsx)(EditCollectionForm, { collection })
  ] });
};
export {
  CollectionEdit as Component
};
//# sourceMappingURL=collection-edit-D7OGD4MC-D3EFLEAD.js.map
