import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  booleanType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateSalesChannel
} from "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  <PERSON><PERSON>,
  Heading,
  Input,
  Switch,
  Text,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/sales-channel-create-HVZPII6E.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateSalesChannelSchema = objectType({
  name: stringType().min(1),
  description: stringType().min(1),
  enabled: booleanType()
});
var CreateSalesChannelForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      description: "",
      enabled: true
    },
    resolver: t(CreateSalesChannelSchema)
  });
  const { mutateAsync, isPending } = useCreateSalesChannel();
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        name: values.name,
        description: values.description,
        is_disabled: !values.enabled
      },
      {
        onSuccess: ({ sales_channel }) => {
          toast.success(t2("salesChannels.toast.create"));
          handleSuccess(`../${sales_channel.id}`);
        },
        onError: (error) => toast.error(error.message)
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("salesChannels.createSalesChannel") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("salesChannels.createSalesChannelHint") })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2", children: (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "name",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ) }),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "description",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.description") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "enabled",
              render: ({ field: { value, onChange, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("general.enabled") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Switch,
                      {
                        ...field,
                        checked: value,
                        onCheckedChange: onChange
                      }
                    ) })
                  ] }),
                  (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("salesChannels.enabledHint") }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var SalesChannelCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateSalesChannelForm, {}) });
};
export {
  SalesChannelCreate as Component
};
//# sourceMappingURL=sales-channel-create-HVZPII6E-G7VZ44LX.js.map
