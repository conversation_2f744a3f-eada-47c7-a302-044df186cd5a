{"version": 3, "sources": ["../../@medusajs/dashboard/dist/edit-rules-7MMNZKEQ.mjs"], "sourcesContent": ["import {\n  RulesFormField\n} from \"./chunk-NGEWNLV7.mjs\";\nimport \"./chunk-YIZSVS2R.mjs\";\nimport \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  usePromotion,\n  usePromotionAddRules,\n  usePromotionRemoveRules,\n  usePromotionUpdateRules,\n  useUpdatePromotion\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/promotions/common/edit-rules/edit-rules.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/promotions/common/edit-rules/components/edit-rules-form/edit-rules-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button } from \"@medusajs/ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\n\n// src/routes/promotions/common/edit-rules/components/edit-rules-form/form-schema.ts\nimport i18n from \"i18next\";\nimport { z } from \"zod\";\nvar EditRules = z.object({\n  type: z.string().optional(),\n  rules: z.array(\n    z.object({\n      id: z.string().optional(),\n      attribute: z.string().min(1, { message: i18n.t(\"promotions.form.required\") }),\n      operator: z.string().min(1, { message: i18n.t(\"promotions.form.required\") }),\n      values: z.union([\n        z.number().min(1, { message: i18n.t(\"promotions.form.required\") }),\n        z.string().min(1, { message: i18n.t(\"promotions.form.required\") }),\n        z.array(z.string()).min(1, { message: i18n.t(\"promotions.form.required\") })\n      ]),\n      required: z.boolean().optional(),\n      disguised: z.boolean().optional(),\n      field_type: z.string().optional()\n    })\n  )\n});\n\n// src/routes/promotions/common/edit-rules/components/edit-rules-form/edit-rules-form.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditRulesForm = ({\n  promotion,\n  ruleType,\n  handleSubmit,\n  isSubmitting\n}) => {\n  const { t } = useTranslation();\n  const [rulesToRemove, setRulesToRemove] = useState([]);\n  const form = useForm({\n    defaultValues: { rules: [], type: promotion.type },\n    resolver: zodResolver(EditRules)\n  });\n  const handleFormSubmit = form.handleSubmit(handleSubmit(rulesToRemove));\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleFormSubmit,\n      className: \"flex h-full flex-col\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\n          RulesFormField,\n          {\n            form,\n            ruleType,\n            setRulesToRemove,\n            rulesToRemove,\n            promotion\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", disabled: isSubmitting, children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isSubmitting, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/promotions/common/edit-rules/components/edit-rules-wrapper/utils.ts\nvar getRuleValue = (rule) => {\n  if (rule.field_type === \"number\") {\n    return parseInt(rule.values);\n  }\n  return rule.values;\n};\n\n// src/routes/promotions/common/edit-rules/components/edit-rules-wrapper/edit-rules-wrapper.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar EditRulesWrapper = ({\n  promotion,\n  rules,\n  ruleType\n}) => {\n  const { handleSuccess } = useRouteModal();\n  const { mutateAsync: updatePromotion } = useUpdatePromotion(promotion.id);\n  const { mutateAsync: addPromotionRules } = usePromotionAddRules(\n    promotion.id,\n    ruleType\n  );\n  const { mutateAsync: removePromotionRules } = usePromotionRemoveRules(\n    promotion.id,\n    ruleType\n  );\n  const { mutateAsync: updatePromotionRules, isPending } = usePromotionUpdateRules(promotion.id, ruleType);\n  const handleSubmit = (rulesToRemove) => {\n    return async function(data) {\n      const applicationMethodData = {};\n      const { rules: allRules = [] } = data;\n      const disguisedRules = allRules.filter((rule) => rule.disguised);\n      const disguisedRulesToRemove = rulesToRemove?.filter((r) => r.disguised) || [];\n      for (const rule of disguisedRules) {\n        applicationMethodData[rule.attribute] = getRuleValue(rule);\n      }\n      for (const rule of disguisedRulesToRemove) {\n        applicationMethodData[rule.attribute] = null;\n      }\n      const rulesData = allRules.filter((rule) => !rule.disguised);\n      const rulesToCreate = rulesData.filter(\n        (rule) => !(\"id\" in rule)\n      );\n      const rulesToUpdate = rulesData.filter(\n        (rule) => typeof rule.id === \"string\"\n      );\n      if (Object.keys(applicationMethodData).length) {\n        await updatePromotion({\n          application_method: applicationMethodData\n        });\n      }\n      rulesToCreate.length && await addPromotionRules({\n        rules: rulesToCreate.map((rule) => {\n          return {\n            attribute: rule.attribute,\n            operator: rule.operator,\n            values: rule.values\n          };\n        })\n      });\n      rulesToRemove?.length && await removePromotionRules({\n        rule_ids: rulesToRemove.map((r) => r.id).filter(Boolean)\n      });\n      rulesToUpdate.length && await updatePromotionRules({\n        rules: rulesToUpdate.map((rule) => {\n          return {\n            id: rule.id,\n            attribute: rule.attribute,\n            operator: rule.operator,\n            values: rule.values\n          };\n        })\n      });\n      handleSuccess();\n    };\n  };\n  return /* @__PURE__ */ jsx2(\n    EditRulesForm,\n    {\n      promotion,\n      rules,\n      ruleType,\n      handleSubmit,\n      isSubmitting: isPending\n    }\n  );\n};\n\n// src/routes/promotions/common/edit-rules/edit-rules.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar EditRules2 = () => {\n  const params = useParams();\n  const allowedParams = [\n    \"rules\" /* RULES */,\n    \"buy-rules\" /* BUY_RULES */,\n    \"target-rules\" /* TARGET_RULES */\n  ];\n  if (!allowedParams.includes(params.ruleType)) {\n    throw \"invalid page\";\n  }\n  const { t } = useTranslation2();\n  const ruleType = params.ruleType;\n  const id = params.id;\n  const rules = [];\n  const { promotion, isPending: isLoading, isError, error } = usePromotion(id);\n  if (promotion) {\n    if (ruleType === \"rules\" /* RULES */) {\n      rules.push(...promotion.rules || []);\n    } else if (ruleType === \"target-rules\" /* TARGET_RULES */) {\n      rules.push(...promotion?.application_method?.target_rules || []);\n    } else if (ruleType === \"buy-rules\" /* BUY_RULES */) {\n      rules.push(...promotion.application_method?.buy_rules || []);\n    }\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx3(RouteDrawer.Header, { children: /* @__PURE__ */ jsx3(Heading, { children: t(`promotions.edit.${ruleType}.title`) }) }),\n    !isLoading && promotion && /* @__PURE__ */ jsx3(\n      EditRulesWrapper,\n      {\n        promotion,\n        rules,\n        ruleType\n      }\n    )\n  ] });\n};\nexport {\n  EditRules2 as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,mBAAyB;AA2BzB,yBAA0B;AAgD1B,IAAAA,sBAA4B;AA+E5B,IAAAA,sBAA2C;AAnJ3C,IAAI,YAAY,EAAE,OAAO;AAAA,EACvB,MAAM,EAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,OAAO,EAAE;AAAA,IACP,EAAE,OAAO;AAAA,MACP,IAAI,EAAE,OAAO,EAAE,SAAS;AAAA,MACxB,WAAW,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,SAAK,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAC5E,UAAU,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,SAAK,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAC3E,QAAQ,EAAE,MAAM;AAAA,QACd,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,SAAK,EAAE,0BAA0B,EAAE,CAAC;AAAA,QACjE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,SAAK,EAAE,0BAA0B,EAAE,CAAC;AAAA,QACjE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,EAAE,SAAS,SAAK,EAAE,0BAA0B,EAAE,CAAC;AAAA,MAC5E,CAAC;AAAA,MACD,UAAU,EAAE,QAAQ,EAAE,SAAS;AAAA,MAC/B,WAAW,EAAE,QAAQ,EAAE,SAAS;AAAA,MAChC,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,IAClC,CAAC;AAAA,EACH;AACF,CAAC;AAID,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,CAAC,CAAC;AACrD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,EAAE,OAAO,CAAC,GAAG,MAAM,UAAU,KAAK;AAAA,IACjD,UAAU,EAAY,SAAS;AAAA,EACjC,CAAC;AACD,QAAM,mBAAmB,KAAK,aAAa,aAAa,aAAa,CAAC;AACtE,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,cAA0B;AAAA,UAChE;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,cAAc,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC/K,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,cAAc,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACrH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAGA,IAAI,eAAe,CAAC,SAAS;AAC3B,MAAI,KAAK,eAAe,UAAU;AAChC,WAAO,SAAS,KAAK,MAAM;AAAA,EAC7B;AACA,SAAO,KAAK;AACd;AAIA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,aAAa,gBAAgB,IAAI,mBAAmB,UAAU,EAAE;AACxE,QAAM,EAAE,aAAa,kBAAkB,IAAI;AAAA,IACzC,UAAU;AAAA,IACV;AAAA,EACF;AACA,QAAM,EAAE,aAAa,qBAAqB,IAAI;AAAA,IAC5C,UAAU;AAAA,IACV;AAAA,EACF;AACA,QAAM,EAAE,aAAa,sBAAsB,UAAU,IAAI,wBAAwB,UAAU,IAAI,QAAQ;AACvG,QAAM,eAAe,CAAC,kBAAkB;AACtC,WAAO,eAAe,MAAM;AAC1B,YAAM,wBAAwB,CAAC;AAC/B,YAAM,EAAE,OAAO,WAAW,CAAC,EAAE,IAAI;AACjC,YAAM,iBAAiB,SAAS,OAAO,CAAC,SAAS,KAAK,SAAS;AAC/D,YAAM,0BAAyB,+CAAe,OAAO,CAAC,MAAM,EAAE,eAAc,CAAC;AAC7E,iBAAW,QAAQ,gBAAgB;AACjC,8BAAsB,KAAK,SAAS,IAAI,aAAa,IAAI;AAAA,MAC3D;AACA,iBAAW,QAAQ,wBAAwB;AACzC,8BAAsB,KAAK,SAAS,IAAI;AAAA,MAC1C;AACA,YAAM,YAAY,SAAS,OAAO,CAAC,SAAS,CAAC,KAAK,SAAS;AAC3D,YAAM,gBAAgB,UAAU;AAAA,QAC9B,CAAC,SAAS,EAAE,QAAQ;AAAA,MACtB;AACA,YAAM,gBAAgB,UAAU;AAAA,QAC9B,CAAC,SAAS,OAAO,KAAK,OAAO;AAAA,MAC/B;AACA,UAAI,OAAO,KAAK,qBAAqB,EAAE,QAAQ;AAC7C,cAAM,gBAAgB;AAAA,UACpB,oBAAoB;AAAA,QACtB,CAAC;AAAA,MACH;AACA,oBAAc,UAAU,MAAM,kBAAkB;AAAA,QAC9C,OAAO,cAAc,IAAI,CAAC,SAAS;AACjC,iBAAO;AAAA,YACL,WAAW,KAAK;AAAA,YAChB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,sDAAe,WAAU,MAAM,qBAAqB;AAAA,QAClD,UAAU,cAAc,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,OAAO,OAAO;AAAA,MACzD,CAAC;AACD,oBAAc,UAAU,MAAM,qBAAqB;AAAA,QACjD,OAAO,cAAc,IAAI,CAAC,SAAS;AACjC,iBAAO;AAAA,YACL,IAAI,KAAK;AAAA,YACT,WAAW,KAAK;AAAA,YAChB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,IAChB;AAAA,EACF;AACF;AAIA,IAAI,aAAa,MAAM;AA/LvB;AAgME,QAAM,SAAS,UAAU;AACzB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EACF;AACA,MAAI,CAAC,cAAc,SAAS,OAAO,QAAQ,GAAG;AAC5C,UAAM;AAAA,EACR;AACA,QAAM,EAAE,GAAAD,GAAE,IAAI,eAAgB;AAC9B,QAAM,WAAW,OAAO;AACxB,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,CAAC;AACf,QAAM,EAAE,WAAW,WAAW,WAAW,SAAS,MAAM,IAAI,aAAa,EAAE;AAC3E,MAAI,WAAW;AACb,QAAI,aAAa,SAAqB;AACpC,YAAM,KAAK,GAAG,UAAU,SAAS,CAAC,CAAC;AAAA,IACrC,WAAW,aAAa,gBAAmC;AACzD,YAAM,KAAK,KAAG,4CAAW,uBAAX,mBAA+B,iBAAgB,CAAC,CAAC;AAAA,IACjE,WAAW,aAAa,aAA6B;AACnD,YAAM,KAAK,KAAG,eAAU,uBAAV,mBAA8B,cAAa,CAAC,CAAC;AAAA,IAC7D;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUH,GAAE,mBAAmB,QAAQ,QAAQ,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1I,CAAC,aAAa,iBAA6B,oBAAAG;AAAA,MACzC;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2", "jsxs2", "jsx3"]}