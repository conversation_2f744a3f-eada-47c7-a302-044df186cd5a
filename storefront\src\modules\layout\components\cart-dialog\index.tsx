"use client"

import { updateCart } from "@lib/data/cart"
import { convertCurrencyToLocale } from "@lib/util/money"
import ItemsTemplate from "@modules/cart/templates/items"
import MobileCartLayout from "@modules/cart/templates/mobile-layout"
import CheckoutSummary from "@modules/checkout/templates/checkout-summary"
import Divider from "@modules/common/components/divider"
import OrderCompletedDialog from "@modules/order/components/order-completed-dialog"
import { Button } from "components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "components/ui/dialog"
import { Input } from "components/ui/input"
import Typography from "components/ui/typography"
import { motion } from "framer-motion"

import React, { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useCartStateStore } from "zustand-store/useCartStore"
import { CartDropdownProps } from "../cart-dropdown"
import CartButton from "../main-nav-content/header/cart-button"

// Import checkout components directly to avoid lazy loading issues
import Addresses from "@modules/checkout/components/addresses"
import Payment from "@modules/checkout/components/payment"
import Shipping from "@modules/checkout/components/shipping"

export default function CartDialog({
  cart: propsCart,
  currency_code,
  customer,
  locationList,
  promotions,
  shippingMethods,
  paymentMethods,
}: CartDropdownProps & {
  customer: any
  promotions: any
  locationList: any[]
  shippingMethods: any[]
  paymentMethods: any[]
}) {
  const { t } = useTranslation("checkout")
  const { t: tCart } = useTranslation("cart")
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [brewingNote, setBrewingNote] = useState<string>(
    (propsCart?.metadata?.brewing_note as string) || ""
  )
  const [cachedShippingMethodId, setCachedShippingMethodId] = useState<
    string | null
  >(null)
  const [hasLogged, setHasLogged] = useState(false)
  const [shippingMethodSetForCartId, setShippingMethodSetForCartId] = useState<
    string | null
  >(null)

  const {
    cart: storeCart,
    fetchCart,
    step,
    completedOrder,
    setStep,
  } = useCartStateStore()

  // Simplified cart selection - prefer propsCart for fresh server data
  const cart: any = React.useMemo(() => {
    // Use propsCart if available (fresh server data), fallback to storeCart
    return propsCart || storeCart || null
  }, [propsCart, storeCart])

  const handleProceedToCheckout = async () => {
    setStep("checkout")
    setIsLoading(true)

    try {
      // Update brewing note if exists
      if (cart?.id && brewingNote) {
        await updateCart({
          metadata: { ...cart.metadata, brewing_note: brewingNote },
        })
      }

      // Only refresh cart if brewing note was updated
      if (cart?.id && brewingNote) {
        await fetchCart()
      }
    } catch (err) {
      console.error("Lỗi khi chuẩn bị checkout:", err)
      setError(t("errors.checkout_preparation_failed"))
      setStep("cart")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (!storeCart) fetchCart()
  }, [storeCart])

  useEffect(() => {
    if (!isOpen) {
      setStep("cart")
    } else {
      // Refresh cart when dialog opens to ensure fresh data
      fetchCart(true).catch(console.error)
    }
  }, [isOpen, setStep, fetchCart])

  // Listen for custom event to open CartDialog
  useEffect(() => {
    const handleOpenCartDialog = (event: CustomEvent) => {
      const { step: targetStep } = event.detail || {}
      if (targetStep) {
        setStep(targetStep)
      }
      setIsOpen(true)
    }

    window.addEventListener(
      "openCartDialog",
      handleOpenCartDialog as EventListener
    )

    return () => {
      window.removeEventListener(
        "openCartDialog",
        handleOpenCartDialog as EventListener
      )
    }
  }, [setStep])

  // Combined effect for shipping method management
  useEffect(() => {
    // Cache shipping method when it's set
    if (cart?.shipping_methods?.length > 0) {
      const currentMethodId =
        cart.shipping_methods[cart.shipping_methods.length - 1]
          ?.shipping_option_id
      if (currentMethodId && currentMethodId !== cachedShippingMethodId) {
        setCachedShippingMethodId(currentMethodId)
      }
    }

    // Shipping method is now handled by the Shipping component itself
  }, [
    step,
    cart?.id,
    cart?.shipping_methods?.length,
    shippingMethods,
    isLoading,
    fetchCart,
    cachedShippingMethodId,
    shippingMethodSetForCartId,
  ])

  const renderCartContent = () => (
    <div className="flex flex-col gap-y-6 p-2 sm:p-0">
      {/* Mobile Layout */}
      <div className="block md:hidden">
        <MobileCartLayout cart={cart as any} />
      </div>

      {/* Desktop/Tablet Layout */}
      <div className="hidden md:block">
        <ItemsTemplate cart={cart as any} />
      </div>

      {cart?.items && cart?.items?.length > 0 && (
        <Input
          className="w-full sm:h-18"
          placeholder={t("brewing_note")}
          value={brewingNote}
          onChange={(e) => setBrewingNote(e.target.value)}
        />
      )}
      <Divider />
      <div className="flex w-full flex-col items-end gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-x-2">
          <Typography variant="p" className="font-semibold text-gray-800">
            {t("subtotal")}
          </Typography>
          <Typography variant="p" color="primary" className="font-semibold">
            {convertCurrencyToLocale({
              amount: cart?.item_subtotal ?? 0,
              currency_code: currency_code,
            })}
          </Typography>
        </div>
        <Button
          disabled={
            !cart?.items?.length ||
            isLoading ||
            (step === "checkout" && isShippingLoading)
          }
          className="h-10 rounded-rounded text-base"
          onClick={handleProceedToCheckout}
        >
          {isLoading ? (
            <span className="flex items-center gap-2">{t("processing")}</span>
          ) : (
            t("proceed_to_checkout")
          )}
        </Button>
      </div>
    </div>
  )

  const isShippingLoading =
    isLoading ||
    (step === "checkout" &&
      (!cart?.shipping_methods || cart.shipping_methods.length === 0))

  const renderCheckoutContent = () => {
    return (
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {cart ? (
          <div className="flex flex-col space-y-8 border-r border-gray-200 sm:pr-4 lg:col-span-2">
            <Addresses
              cart={cart}
              setIsCheckout={(value: any) =>
                setStep(value ? "checkout" : "cart")
              }
              customer={customer}
              locationList={locationList}
            />
            <div className="hidden">
              <Shipping
                cart={cart}
                availableShippingMethods={shippingMethods}
              />
            </div>
            <Payment cart={cart} availablePaymentMethods={paymentMethods} />
          </div>
        ) : (
          <div className="flex flex-col space-y-8 border-r border-gray-200 sm:pr-4 lg:col-span-2">
            <div className="p-8 text-center">
              <Typography className="text-gray-500">
                {tCart("cart_loading")}
              </Typography>
            </div>
          </div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="sticky top-0 self-start rounded-lg border-none !outline-none"
        >
          {cart && (
            <CheckoutSummary
              cart={cart}
              promotionList={promotions}
              currency_code={currency_code}
              isCheckout={step === "checkout"}
              setIsCheckout={(value) => setStep(value ? "checkout" : "cart")}
              passThrough={{ card: "border-none outline-none !p-0" }}
              isShippingLoading={isShippingLoading}
            />
          )}
        </motion.div>
      </div>
    )
  }

  const renderOrderConfirmedContent = () => (
    <OrderCompletedDialog
      order={completedOrder}
      onClose={() => setIsOpen(false)}
    />
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <CartButton
          quantity={cart?.items?.length ?? 0}
          onClick={() => setIsOpen(true)}
        />
      </DialogTrigger>
      <DialogContent
        aria-describedby={undefined}
        className="max-h-[90vh] w-[95vw] max-w-[1280px] overflow-y-auto rounded-lg md:w-full"
      >
        <DialogTitle className="flex items-center gap-x-2 px-4 pt-0 sm:px-0">
          {step === "cart" && (
            <div className="flex items-center gap-3">
              <p className="text-base18">{t("cart")}</p>
              <p className="text-base font-medium text-gray-700">
                (
                {cart?.items && cart?.items?.length > 0
                  ? `${cart.items.length} ${t("item(s)")}`
                  : t("empty_cart_message")}
                )
              </p>
            </div>
          )}
        </DialogTitle>
        <DialogDescription className="sr-only">
          {t("cart_dialog_description")}
        </DialogDescription>
        {error && <Typography color="destructive">{error}</Typography>}
        {step === "cart" && renderCartContent()}
        {step === "checkout" && cart && renderCheckoutContent()}
        {step === "confirmed" &&
          completedOrder &&
          renderOrderConfirmedContent()}
      </DialogContent>
    </Dialog>
  )
}
