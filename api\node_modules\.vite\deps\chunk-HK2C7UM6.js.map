{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-D7H6ZNK4.mjs"], "sourcesContent": ["import {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\n\n// src/components/common/switch-box/switch-box.tsx\nimport { Switch } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SwitchBox = ({\n  label,\n  description,\n  optional = false,\n  tooltip,\n  onCheckedChange,\n  ...props\n}) => {\n  return /* @__PURE__ */ jsx(\n    Form.Field,\n    {\n      ...props,\n      render: ({ field: { value, onChange, ...field } }) => {\n        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-component shadow-elevation-card-rest flex items-start gap-x-3 rounded-lg p-3\", children: [\n            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n              Switch,\n              {\n                ...field,\n                checked: value,\n                onCheckedChange: (e) => {\n                  onCheckedChange?.(e);\n                  onChange(e);\n                }\n              }\n            ) }),\n            /* @__PURE__ */ jsxs(\"div\", { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional, tooltip, children: label }),\n              /* @__PURE__ */ jsx(Form.Hint, { children: description })\n            ] })\n          ] }),\n          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n        ] });\n      }\n    }\n  );\n};\n\nexport {\n  SwitchBox\n};\n"], "mappings": ";;;;;;;;;;;;;;AAMA,yBAA0B;AAC1B,IAAI,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAM;AACJ,aAAuB;AAAA,IACrB,KAAK;AAAA,IACL;AAAA,MACE,GAAG;AAAA,MACH,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,mBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,cACjC,yBAAK,OAAO,EAAE,WAAW,yFAAyF,UAAU;AAAA,gBAC1H,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,cAC5D;AAAA,cACA;AAAA,gBACE,GAAG;AAAA,gBACH,SAAS;AAAA,gBACT,iBAAiB,CAAC,MAAM;AACtB,qEAAkB;AAClB,2BAAS,CAAC;AAAA,gBACZ;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,yBAAK,OAAO,EAAE,UAAU;AAAA,kBACtB,wBAAI,KAAK,OAAO,EAAE,UAAU,SAAS,UAAU,MAAM,CAAC;AAAA,kBACtD,wBAAI,KAAK,MAAM,EAAE,UAAU,YAAY,CAAC;AAAA,YAC1D,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,cACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,QAC3C,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": []}