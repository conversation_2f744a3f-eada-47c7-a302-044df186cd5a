{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-XR4GEMGR.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/query/use-region-table-query.tsx\nvar useRegionTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, q, order, created_at, updated_at } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useRegionTableQuery\n};\n"], "mappings": ";;;;;AAKA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,SAAS,cAAc,YAAY;AAAA,IACnD;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,OAAO,YAAY,WAAW,IAAI;AACrD,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}