{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-FVC7M755.mjs"], "sourcesContent": ["import {\n  inventoryItemLevelsQueryKeys,\n  inventoryItemsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/reservations.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar RESERVATION_ITEMS_QUERY_KEY = \"reservation_items\";\nvar reservationItemsQueryKeys = queryKeysFactory(\n  RESERVATION_ITEMS_QUERY_KEY\n);\nvar useReservationItem = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: reservationItemsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.reservation.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useReservationItems = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.reservation.list(query),\n    queryKey: reservationItemsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateReservationItem = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.reservation.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemLevelsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateReservationItem = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.reservation.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemLevelsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteReservationItem = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.reservation.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemLevelsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  reservationItemsQueryKeys,\n  useReservationItem,\n  useReservationItems,\n  useUpdateReservationItem,\n  useCreateReservationItem,\n  useDeleteReservationItem\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA,IAAI,8BAA8B;AAClC,IAAI,4BAA4B;AAAA,EAC9B;AACF;AACA,IAAI,qBAAqB,CAAC,IAAI,OAAO,YAAY;AAC/C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,0BAA0B,OAAO,EAAE;AAAA,IAC7C,SAAS,YAAY,IAAI,MAAM,YAAY,SAAS,IAAI,KAAK;AAAA,IAC7D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,sBAAsB,CAAC,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,YAAY,KAAK,KAAK;AAAA,IAC/C,UAAU,0BAA0B,KAAK,KAAK;AAAA,IAC9C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,OAAO,IAAI,OAAO;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AA1C7C;AA2CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,OAAO,EAAE;AAAA,MAC/C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,MAAM;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,6BAA6B,QAAQ;AAAA,MACjD,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,OAAO,OAAO;AAAA,IAC7D,WAAW,CAAC,MAAM,WAAW,YAAY;AA/D7C;AAgEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,MAAM;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,6BAA6B,QAAQ;AAAA,MACjD,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,YAAY,OAAO,EAAE;AAAA,IACjD,WAAW,CAAC,MAAM,WAAW,YAAY;AAjF7C;AAkFM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,MAAM;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,OAAO,EAAE;AAAA,MAC/C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,6BAA6B,QAAQ;AAAA,MACjD,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}