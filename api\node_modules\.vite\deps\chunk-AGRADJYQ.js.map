{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-Q5PHSNDY.mjs"], "sourcesContent": ["import {\n  languages\n} from \"./chunk-R2NIHOCX.mjs\";\n\n// src/hooks/use-date.tsx\nimport { format, formatDistance, sub } from \"date-fns\";\nimport { enUS } from \"date-fns/locale\";\nimport { useTranslation } from \"react-i18next\";\nvar useDate = () => {\n  const { i18n } = useTranslation();\n  const locale = languages.find((l) => l.code === i18n.language)?.date_locale || enUS;\n  const getFullDate = ({\n    date,\n    includeTime = false\n  }) => {\n    const ensuredDate = new Date(date);\n    if (isNaN(ensuredDate.getTime())) {\n      return \"\";\n    }\n    const timeFormat = includeTime ? \"p\" : \"\";\n    return format(ensuredDate, `PP ${timeFormat}`, {\n      locale\n    });\n  };\n  function getRelativeDate(date) {\n    const now = /* @__PURE__ */ new Date();\n    return formatDistance(sub(new Date(date), { minutes: 0 }), now, {\n      addSuffix: true,\n      locale\n    });\n  }\n  return {\n    getFullDate,\n    getRelativeDate\n  };\n};\n\nexport {\n  useDate\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAQA,IAAI,UAAU,MAAM;AARpB;AASE,QAAM,EAAE,KAAK,IAAI,eAAe;AAChC,QAAM,WAAS,eAAU,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,QAAQ,MAA9C,mBAAiD,gBAAe;AAC/E,QAAM,cAAc,CAAC;AAAA,IACnB;AAAA,IACA,cAAc;AAAA,EAChB,MAAM;AACJ,UAAM,cAAc,IAAI,KAAK,IAAI;AACjC,QAAI,MAAM,YAAY,QAAQ,CAAC,GAAG;AAChC,aAAO;AAAA,IACT;AACA,UAAM,aAAa,cAAc,MAAM;AACvC,WAAO,OAAO,aAAa,MAAM,UAAU,IAAI;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,gBAAgB,MAAM;AAC7B,UAAM,MAAsB,oBAAI,KAAK;AACrC,WAAO,eAAe,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK;AAAA,MAC9D,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": []}