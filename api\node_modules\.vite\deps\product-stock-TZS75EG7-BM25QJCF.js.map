{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-stock-TZS75EG7.mjs"], "sourcesContent": ["import {\n  DataGridTogglableNumberCell\n} from \"./chunk-NOZD6HRU.mjs\";\nimport {\n  DataGrid,\n  DataGridReadonlyCell,\n  DataGridSkeleton,\n  createDataGridHel<PERSON>,\n  useDataGridDuplicateCell\n} from \"./chunk-GE4APTT2.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  PRODUCT_VARIANT_IDS_KEY\n} from \"./chunk-AM2BU2RH.mjs\";\nimport {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  ProgressBar\n} from \"./chunk-D3YQN7HV.mjs\";\nimport {\n  Thumbnail\n} from \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  Skeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useBatchInventoryItemsLocationLevels\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-stock/loader.ts\nimport { defer } from \"react-router-dom\";\nasync function getProductStockData(id, productVariantIds) {\n  const CHUNK_SIZE = 20;\n  let offset = 0;\n  let totalCount = 0;\n  let allVariants = [];\n  do {\n    const { variants: chunk, count } = await sdk.admin.product.listVariants(\n      id,\n      {\n        id: productVariantIds,\n        offset,\n        limit: CHUNK_SIZE,\n        fields: \"id,title,sku,inventory_items,inventory_items.*,inventory_items.inventory,inventory_items.inventory.id,inventory_items.inventory.title,inventory_items.inventory.sku,*inventory_items.inventory.location_levels,product.thumbnail\"\n      }\n    );\n    allVariants = [...allVariants, ...chunk];\n    totalCount = count;\n    offset += CHUNK_SIZE;\n  } while (allVariants.length < totalCount);\n  const { stock_locations } = await sdk.admin.stockLocation.list({\n    limit: 9999,\n    fields: \"id,name\"\n  });\n  return {\n    variants: allVariants,\n    locations: stock_locations\n  };\n}\nvar productStockLoader = async ({\n  params,\n  request\n}) => {\n  const id = params.id;\n  const searchParams = new URLSearchParams(request.url);\n  const productVariantIds = searchParams.get(PRODUCT_VARIANT_IDS_KEY)?.split(\",\") || void 0;\n  const dataPromise = getProductStockData(id, productVariantIds);\n  return defer({\n    data: dataPromise\n  });\n};\n\n// src/routes/products/product-stock/product-stock.tsx\nimport { AnimatePresence } from \"motion/react\";\nimport { Suspense, useEffect as useEffect2, useRef as useRef2, useState as useState2 } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { Await, useLoaderData } from \"react-router-dom\";\n\n// src/routes/products/product-stock/components/product-stock-form/product-stock-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, toast, usePrompt } from \"@medusajs/ui\";\nimport { useEffect, useMemo as useMemo2, useRef, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/products/product-stock/hooks/use-product-stock-columns.tsx\nimport { InformationCircle } from \"@medusajs/icons\";\nimport { Switch, Tooltip } from \"@medusajs/ui\";\nimport { useCallback, useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\n// src/components/data-grid/components/data-grid-duplicate-cell.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar DataGridDuplicateCell = ({\n  duplicateOf,\n  children\n}) => {\n  const { watchedValue } = useDataGridDuplicateCell({ duplicateOf });\n  return /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-base txt-compact-small text-ui-fg-subtle flex size-full cursor-not-allowed items-center justify-between overflow-hidden px-4 py-2.5 outline-none\", children: typeof children === \"function\" ? children({ value: watchedValue }) : children });\n};\n\n// src/routes/products/product-stock/utils.ts\nfunction isProductVariant(row) {\n  return row.id.startsWith(\"variant_\");\n}\nfunction isProductVariantWithInventoryPivot(row) {\n  return row.inventory_items && row.inventory_items.length > 0;\n}\nfunction getDisabledInventoryRows(variants) {\n  const seen = {};\n  const disabled = {};\n  variants.forEach((variant) => {\n    const inventoryItems = variant.inventory_items;\n    if (!inventoryItems) {\n      return;\n    }\n    inventoryItems.forEach((item) => {\n      const existing = seen[item.inventory_item_id];\n      if (existing) {\n        disabled[item.inventory_item_id] = {\n          id: existing.id,\n          title: existing.title || \"\",\n          sku: existing.sku || \"\"\n        };\n        return;\n      }\n      seen[item.inventory_item_id] = variant;\n    });\n  });\n  return disabled;\n}\n\n// src/routes/products/product-stock/hooks/use-product-stock-columns.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar helper = createDataGridHelper();\nvar useProductStockColumns = (locations = [], disabled = {}) => {\n  const { t } = useTranslation();\n  const getIsDisabled = useCallback(\n    (item) => {\n      const disabledItem = disabled[item.inventory_item_id];\n      const isDisabled = !!disabledItem && disabledItem.id !== item.variant_id;\n      if (!isDisabled) {\n        return {\n          isDisabled: false,\n          item: void 0\n        };\n      }\n      return {\n        isDisabled,\n        item: disabledItem\n      };\n    },\n    [disabled]\n  );\n  return useMemo(\n    () => [\n      helper.column({\n        id: \"title\",\n        name: \"Title\",\n        header: \"Title\",\n        cell: (context) => {\n          const item = context.row.original;\n          if (isProductVariant(item)) {\n            return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n              /* @__PURE__ */ jsx2(Thumbnail, { size: \"small\", src: item.product?.thumbnail }),\n              /* @__PURE__ */ jsx2(\"span\", { children: item.title || \"-\" })\n            ] }) });\n          }\n          const { isDisabled, item: disabledItem } = getIsDisabled(item);\n          if (isDisabled) {\n            return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, color: \"normal\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full items-center justify-between gap-x-2\", children: [\n              /* @__PURE__ */ jsx2(\n                \"span\",\n                {\n                  title: item.inventory?.title || void 0,\n                  className: \"text-ui-fg-disabled\",\n                  children: item.inventory?.title || \"-\"\n                }\n              ),\n              /* @__PURE__ */ jsx2(\n                Tooltip,\n                {\n                  content: disabledItem.sku ? t(\"products.stock.tooltips.alreadyManagedWithSku\", {\n                    title: disabledItem.title,\n                    sku: disabledItem.sku\n                  }) : t(\"products.stock.tooltips.alreadyManaged\", {\n                    title: disabledItem.title\n                  }),\n                  children: /* @__PURE__ */ jsx2(InformationCircle, {})\n                }\n              )\n            ] }) });\n          }\n          return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, color: \"normal\", children: item.inventory?.title || \"-\" });\n        },\n        disableHiding: true\n      }),\n      helper.column({\n        id: \"sku\",\n        name: \"SKU\",\n        header: \"SKU\",\n        cell: (context) => {\n          const item = context.row.original;\n          if (isProductVariant(item)) {\n            return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, children: item.sku || \"-\" });\n          }\n          const { isDisabled } = getIsDisabled(item);\n          if (isDisabled) {\n            return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, color: \"normal\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-disabled\", children: item.inventory?.sku || \"-\" }) });\n          }\n          return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context, color: \"normal\", children: item.inventory?.sku || \"-\" });\n        },\n        disableHiding: true\n      }),\n      ...locations.map(\n        (location) => helper.column({\n          id: `location_${location.id}`,\n          name: location.name,\n          header: location.name,\n          field: (context) => {\n            const item = context.row.original;\n            if (isProductVariant(item)) {\n              return null;\n            }\n            const { isDisabled } = getIsDisabled(item);\n            if (isDisabled) {\n              return null;\n            }\n            return `variants.${item.variant_id}.inventory_items.${item.inventory_item_id}.locations.${location.id}`;\n          },\n          type: \"togglable-number\",\n          cell: (context) => {\n            const item = context.row.original;\n            if (isProductVariant(item)) {\n              return /* @__PURE__ */ jsx2(DataGridReadonlyCell, { context });\n            }\n            const { isDisabled, item: disabledItem } = getIsDisabled(item);\n            if (isDisabled) {\n              return /* @__PURE__ */ jsx2(\n                DataGridDuplicateCell,\n                {\n                  duplicateOf: `variants.${disabledItem.id}.inventory_items.${item.inventory_item_id}.locations.${location.id}`,\n                  children: ({ value }) => {\n                    const { checked, quantity } = value;\n                    return /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full items-center gap-x-2\", children: [\n                      /* @__PURE__ */ jsx2(\n                        Switch,\n                        {\n                          className: \"shrink-0 cursor-not-allowed\",\n                          tabIndex: -1,\n                          size: \"small\",\n                          checked,\n                          disabled: true\n                        }\n                      ),\n                      /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-disabled flex size-full items-center justify-end\", children: quantity })\n                    ] });\n                  }\n                }\n              );\n            }\n            return /* @__PURE__ */ jsx2(\n              DataGridTogglableNumberCell,\n              {\n                context,\n                disabledToggleTooltip: t(\n                  \"inventory.stock.disabledToggleTooltip\"\n                ),\n                placeholder: t(\"inventory.stock.placeholder\")\n              }\n            );\n          }\n        })\n      )\n    ],\n    [locations, getIsDisabled, t]\n  );\n};\n\n// src/routes/products/product-stock/schema.ts\nimport { z } from \"zod\";\nvar LocationQuantitySchema = z.object({\n  id: z.string().optional(),\n  quantity: z.union([z.number(), z.string()]),\n  checked: z.boolean(),\n  disabledToggle: z.boolean()\n});\nvar ProductStockLocationsSchema = z.record(LocationQuantitySchema);\nvar ProductStockInventoryItemSchema = z.object({\n  locations: ProductStockLocationsSchema\n});\nvar ProductStockVariantSchema = z.object({\n  inventory_items: z.record(ProductStockInventoryItemSchema)\n});\nvar ProductStockSchema = z.object({\n  variants: z.record(ProductStockVariantSchema)\n});\n\n// src/routes/products/product-stock/components/product-stock-form/product-stock-form.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductStockForm = ({\n  variants,\n  locations,\n  onLoaded\n}) => {\n  const { t } = useTranslation2();\n  const { handleSuccess, setCloseOnEscape } = useRouteModal();\n  const prompt = usePrompt();\n  useEffect(() => {\n    onLoaded();\n  }, [onLoaded]);\n  const [isPromptOpen, setIsPromptOpen] = useState(false);\n  const form = useForm({\n    defaultValues: getDefaultValue(variants, locations),\n    resolver: zodResolver(ProductStockSchema)\n  });\n  const initialValues = useRef(getDefaultValue(variants, locations));\n  const disabled = useMemo2(() => getDisabledInventoryRows(variants), [variants]);\n  const columns = useProductStockColumns(locations, disabled);\n  const { mutateAsync, isPending } = useBatchInventoryItemsLocationLevels();\n  const onSubmit = form.handleSubmit(async (data) => {\n    const payload = {\n      create: [],\n      update: [],\n      delete: [],\n      force: true\n    };\n    for (const [variantId, variant] of Object.entries(data.variants)) {\n      for (const [inventory_item_id, item] of Object.entries(\n        variant.inventory_items\n      )) {\n        for (const [location_id, level] of Object.entries(item.locations)) {\n          if (level.id) {\n            const wasChecked = initialValues.current?.variants?.[variantId]?.inventory_items?.[inventory_item_id]?.locations?.[location_id]?.checked;\n            if (wasChecked && !level.checked) {\n              payload.delete.push(level.id);\n            } else {\n              const newQuantity = level.quantity !== \"\" ? castNumber(level.quantity) : 0;\n              const originalQuantity = initialValues.current?.variants?.[variantId]?.inventory_items?.[inventory_item_id]?.locations?.[location_id]?.quantity;\n              if (newQuantity !== originalQuantity) {\n                payload.update.push({\n                  inventory_item_id,\n                  location_id,\n                  stocked_quantity: newQuantity\n                });\n              }\n            }\n          }\n          if (!level.id && level.quantity !== \"\") {\n            payload.create.push({\n              inventory_item_id,\n              location_id,\n              stocked_quantity: castNumber(level.quantity)\n            });\n          }\n        }\n      }\n    }\n    if (payload.delete.length > 0) {\n      setIsPromptOpen(true);\n      const confirm = await prompt({\n        title: t(\"general.areYouSure\"),\n        description: t(\"inventory.stock.disablePrompt\", {\n          count: payload.delete.length\n        }),\n        confirmText: t(\"actions.continue\"),\n        cancelText: t(\"actions.cancel\"),\n        variant: \"confirmation\"\n      });\n      setIsPromptOpen(false);\n      if (!confirm) {\n        return;\n      }\n    }\n    await mutateAsync(payload, {\n      onSuccess: () => {\n        toast.success(t(\"inventory.stock.successToast\"));\n        handleSuccess();\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx3(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs2(KeyboundForm, { onSubmit, className: \"flex size-full flex-col\", children: [\n    /* @__PURE__ */ jsx3(RouteFocusModal.Header, {}),\n    /* @__PURE__ */ jsx3(RouteFocusModal.Body, { className: \"flex flex-col overflow-hidden\", children: /* @__PURE__ */ jsx3(\n      DataGrid,\n      {\n        state: form,\n        columns,\n        data: variants,\n        getSubRows,\n        onEditingChange: (editing) => setCloseOnEscape(!editing),\n        disableInteractions: isPending || isPromptOpen,\n        multiColumnSelection: true\n      }\n    ) }),\n    /* @__PURE__ */ jsx3(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-end gap-2\", children: [\n      /* @__PURE__ */ jsx3(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx3(Button, { variant: \"secondary\", size: \"small\", type: \"button\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx3(Button, { type: \"submit\", size: \"small\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\nfunction getSubRows(row) {\n  if (isProductVariantWithInventoryPivot(row)) {\n    return row.inventory_items;\n  }\n}\nfunction getDefaultValue(variants, locations) {\n  return {\n    variants: variants.reduce((variantAcc, variant) => {\n      const inventoryItems = variant.inventory_items?.reduce(\n        (itemAcc, item) => {\n          const locationsMap = locations.reduce((locationAcc, location) => {\n            const level = item.inventory?.location_levels?.find(\n              (level2) => level2.location_id === location.id\n            );\n            locationAcc[location.id] = {\n              id: level?.id,\n              quantity: level?.stocked_quantity !== void 0 ? level?.stocked_quantity : \"\",\n              checked: !!level,\n              disabledToggle: (level?.incoming_quantity || 0) > 0 || (level?.reserved_quantity || 0) > 0\n            };\n            return locationAcc;\n          }, {});\n          itemAcc[item.inventory_item_id] = { locations: locationsMap };\n          return itemAcc;\n        },\n        {}\n      );\n      variantAcc[variant.id] = { inventory_items: inventoryItems || {} };\n      return variantAcc;\n    }, {})\n  };\n}\n\n// src/routes/products/product-stock/product-stock.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ProductStock = () => {\n  const { t } = useTranslation3();\n  const data = useLoaderData();\n  const [isLoading, setIsLoading] = useState2(false);\n  const timeoutRef = useRef2();\n  useEffect2(() => {\n    timeoutRef.current = setTimeout(() => {\n      setIsLoading(true);\n    }, 200);\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, []);\n  const onLoaded = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    setIsLoading(false);\n  };\n  return /* @__PURE__ */ jsxs3(\"div\", { children: [\n    /* @__PURE__ */ jsx4(\"div\", { className: \"fixed inset-x-0 top-0 z-50 h-1\", children: /* @__PURE__ */ jsx4(AnimatePresence, { children: isLoading ? /* @__PURE__ */ jsx4(ProgressBar, { duration: 5 }) : null }) }),\n    /* @__PURE__ */ jsxs3(RouteFocusModal, { children: [\n      /* @__PURE__ */ jsx4(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx4(\"span\", { className: \"sr-only\", children: t(\"products.stock.heading\") }) }),\n      /* @__PURE__ */ jsx4(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx4(\"span\", { className: \"sr-only\", children: t(\"products.stock.description\") }) }),\n      /* @__PURE__ */ jsx4(Suspense, { fallback: /* @__PURE__ */ jsx4(ProductStockFallback, {}), children: /* @__PURE__ */ jsx4(Await, { resolve: data.data, children: (data2) => {\n        return /* @__PURE__ */ jsx4(\n          ProductStockForm,\n          {\n            variants: data2.variants,\n            locations: data2.locations,\n            onLoaded\n          }\n        );\n      } }) })\n    ] })\n  ] });\n};\nvar ProductStockFallback = () => {\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"relative flex size-full flex-col items-center justify-center divide-y\", children: /* @__PURE__ */ jsxs3(\"div\", { className: \"flex size-full flex-col divide-y\", children: [\n    /* @__PURE__ */ jsx4(\"div\", { className: \"px-4 py-2\", children: /* @__PURE__ */ jsx4(Skeleton, { className: \"h-7 w-7\" }) }),\n    /* @__PURE__ */ jsx4(\"div\", { className: \"flex-1 overflow-auto\", children: /* @__PURE__ */ jsx4(\n      DataGridSkeleton,\n      {\n        columns: Array.from({ length: 10 })\n      }\n    ) }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"bg-ui-bg-base flex items-center justify-end gap-x-2 p-4\", children: [\n      /* @__PURE__ */ jsx4(Skeleton, { className: \"h-7 w-[59px]\" }),\n      /* @__PURE__ */ jsx4(Skeleton, { className: \"h-7 w-[46px]\" })\n    ] })\n  ] }) });\n};\nexport {\n  ProductStock as Component,\n  productStockLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,IAAAA,gBAA4F;AAO5F,IAAAC,gBAAiE;AAOjE,IAAAC,gBAAqC;AAIrC,yBAAoB;AAyCpB,IAAAC,sBAAkC;AAsKlC,IAAAC,sBAA2C;AA2I3C,IAAAA,sBAA2C;AAvZ3C,eAAe,oBAAoB,IAAI,mBAAmB;AACxD,QAAM,aAAa;AACnB,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,MAAI,cAAc,CAAC;AACnB,KAAG;AACD,UAAM,EAAE,UAAU,OAAO,MAAM,IAAI,MAAM,IAAI,MAAM,QAAQ;AAAA,MACzD;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ;AAAA,QACA,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AACA,kBAAc,CAAC,GAAG,aAAa,GAAG,KAAK;AACvC,iBAAa;AACb,cAAU;AAAA,EACZ,SAAS,YAAY,SAAS;AAC9B,QAAM,EAAE,gBAAgB,IAAI,MAAM,IAAI,MAAM,cAAc,KAAK;AAAA,IAC7D,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,SAAO;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AACF;AACA,IAAI,qBAAqB,OAAO;AAAA,EAC9B;AAAA,EACA;AACF,MAAM;AAzGN;AA0GE,QAAM,KAAK,OAAO;AAClB,QAAM,eAAe,IAAI,gBAAgB,QAAQ,GAAG;AACpD,QAAM,sBAAoB,kBAAa,IAAI,uBAAuB,MAAxC,mBAA2C,MAAM,SAAQ;AACnF,QAAM,cAAc,oBAAoB,IAAI,iBAAiB;AAC7D,SAAO,MAAM;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AACH;AAuBA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,aAAa,IAAI,yBAAyB,EAAE,YAAY,CAAC;AACjE,aAAuB,wBAAI,OAAO,EAAE,WAAW,6JAA6J,UAAU,OAAO,aAAa,aAAa,SAAS,EAAE,OAAO,aAAa,CAAC,IAAI,SAAS,CAAC;AACvS;AAGA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,IAAI,GAAG,WAAW,UAAU;AACrC;AACA,SAAS,mCAAmC,KAAK;AAC/C,SAAO,IAAI,mBAAmB,IAAI,gBAAgB,SAAS;AAC7D;AACA,SAAS,yBAAyB,UAAU;AAC1C,QAAM,OAAO,CAAC;AACd,QAAM,WAAW,CAAC;AAClB,WAAS,QAAQ,CAAC,YAAY;AAC5B,UAAM,iBAAiB,QAAQ;AAC/B,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,mBAAe,QAAQ,CAAC,SAAS;AAC/B,YAAM,WAAW,KAAK,KAAK,iBAAiB;AAC5C,UAAI,UAAU;AACZ,iBAAS,KAAK,iBAAiB,IAAI;AAAA,UACjC,IAAI,SAAS;AAAA,UACb,OAAO,SAAS,SAAS;AAAA,UACzB,KAAK,SAAS,OAAO;AAAA,QACvB;AACA;AAAA,MACF;AACA,WAAK,KAAK,iBAAiB,IAAI;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAIA,IAAI,SAAS,qBAAqB;AAClC,IAAI,yBAAyB,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC,MAAM;AAC9D,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,oBAAgB;AAAA,IACpB,CAAC,SAAS;AACR,YAAM,eAAe,SAAS,KAAK,iBAAiB;AACpD,YAAM,aAAa,CAAC,CAAC,gBAAgB,aAAa,OAAO,KAAK;AAC9D,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,MAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACA,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,OAAO,OAAO;AAAA,QACZ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM,CAAC,YAAY;AA3M3B;AA4MU,gBAAM,OAAO,QAAQ,IAAI;AACzB,cAAI,iBAAiB,IAAI,GAAG;AAC1B,uBAAuB,oBAAAC,KAAK,sBAAsB,EAAE,SAAS,cAA0B,0BAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,kBACrI,oBAAAA,KAAK,WAAW,EAAE,MAAM,SAAS,MAAK,UAAK,YAAL,mBAAc,UAAU,CAAC;AAAA,kBAC/D,oBAAAA,KAAK,QAAQ,EAAE,UAAU,KAAK,SAAS,IAAI,CAAC;AAAA,YAC9D,EAAE,CAAC,EAAE,CAAC;AAAA,UACR;AACA,gBAAM,EAAE,YAAY,MAAM,aAAa,IAAI,cAAc,IAAI;AAC7D,cAAI,YAAY;AACd,uBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,OAAO,UAAU,cAA0B,0BAAK,OAAO,EAAE,WAAW,uDAAuD,UAAU;AAAA,kBAChL,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,SAAO,UAAK,cAAL,mBAAgB,UAAS;AAAA,kBAChC,WAAW;AAAA,kBACX,YAAU,UAAK,cAAL,mBAAgB,UAAS;AAAA,gBACrC;AAAA,cACF;AAAA,kBACgB,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,SAAS,aAAa,MAAMD,GAAE,iDAAiD;AAAA,oBAC7E,OAAO,aAAa;AAAA,oBACpB,KAAK,aAAa;AAAA,kBACpB,CAAC,IAAIA,GAAE,0CAA0C;AAAA,oBAC/C,OAAO,aAAa;AAAA,kBACtB,CAAC;AAAA,kBACD,cAA0B,oBAAAC,KAAK,mBAAmB,CAAC,CAAC;AAAA,gBACtD;AAAA,cACF;AAAA,YACF,EAAE,CAAC,EAAE,CAAC;AAAA,UACR;AACA,qBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,OAAO,UAAU,YAAU,UAAK,cAAL,mBAAgB,UAAS,IAAI,CAAC;AAAA,QACxH;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,MACD,OAAO,OAAO;AAAA,QACZ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM,CAAC,YAAY;AApP3B;AAqPU,gBAAM,OAAO,QAAQ,IAAI;AACzB,cAAI,iBAAiB,IAAI,GAAG;AAC1B,uBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,UAAU,KAAK,OAAO,IAAI,CAAC;AAAA,UAC1F;AACA,gBAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,cAAI,YAAY;AACd,uBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,OAAO,UAAU,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,uBAAuB,YAAU,UAAK,cAAL,mBAAgB,QAAO,IAAI,CAAC,EAAE,CAAC;AAAA,UACpM;AACA,qBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,OAAO,UAAU,YAAU,UAAK,cAAL,mBAAgB,QAAO,IAAI,CAAC;AAAA,QACtH;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,MACD,GAAG,UAAU;AAAA,QACX,CAAC,aAAa,OAAO,OAAO;AAAA,UAC1B,IAAI,YAAY,SAAS,EAAE;AAAA,UAC3B,MAAM,SAAS;AAAA,UACf,QAAQ,SAAS;AAAA,UACjB,OAAO,CAAC,YAAY;AAClB,kBAAM,OAAO,QAAQ,IAAI;AACzB,gBAAI,iBAAiB,IAAI,GAAG;AAC1B,qBAAO;AAAA,YACT;AACA,kBAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,gBAAI,YAAY;AACd,qBAAO;AAAA,YACT;AACA,mBAAO,YAAY,KAAK,UAAU,oBAAoB,KAAK,iBAAiB,cAAc,SAAS,EAAE;AAAA,UACvG;AAAA,UACA,MAAM;AAAA,UACN,MAAM,CAAC,YAAY;AACjB,kBAAM,OAAO,QAAQ,IAAI;AACzB,gBAAI,iBAAiB,IAAI,GAAG;AAC1B,yBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,QAAQ,CAAC;AAAA,YAC/D;AACA,kBAAM,EAAE,YAAY,MAAM,aAAa,IAAI,cAAc,IAAI;AAC7D,gBAAI,YAAY;AACd,yBAAuB,oBAAAA;AAAA,gBACrB;AAAA,gBACA;AAAA,kBACE,aAAa,YAAY,aAAa,EAAE,oBAAoB,KAAK,iBAAiB,cAAc,SAAS,EAAE;AAAA,kBAC3G,UAAU,CAAC,EAAE,MAAM,MAAM;AACvB,0BAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,+BAAuB,0BAAK,OAAO,EAAE,WAAW,uCAAuC,UAAU;AAAA,0BAC/E,oBAAAA;AAAA,wBACd;AAAA,wBACA;AAAA,0BACE,WAAW;AAAA,0BACX,UAAU;AAAA,0BACV,MAAM;AAAA,0BACN;AAAA,0BACA,UAAU;AAAA,wBACZ;AAAA,sBACF;AAAA,0BACgB,oBAAAA,KAAK,QAAQ,EAAE,WAAW,+DAA+D,UAAU,SAAS,CAAC;AAAA,oBAC/H,EAAE,CAAC;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,uBAAuB,oBAAAA;AAAA,cACrB;AAAA,cACA;AAAA,gBACE;AAAA,gBACA,uBAAuBD;AAAA,kBACrB;AAAA,gBACF;AAAA,gBACA,aAAaA,GAAE,6BAA6B;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,CAAC,WAAW,eAAeA,EAAC;AAAA,EAC9B;AACF;AAIA,IAAI,yBAAyB,EAAE,OAAO;AAAA,EACpC,IAAI,EAAE,OAAO,EAAE,SAAS;AAAA,EACxB,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC;AAAA,EAC1C,SAAS,EAAE,QAAQ;AAAA,EACnB,gBAAgB,EAAE,QAAQ;AAC5B,CAAC;AACD,IAAI,8BAA8B,EAAE,OAAO,sBAAsB;AACjE,IAAI,kCAAkC,EAAE,OAAO;AAAA,EAC7C,WAAW;AACb,CAAC;AACD,IAAI,4BAA4B,EAAE,OAAO;AAAA,EACvC,iBAAiB,EAAE,OAAO,+BAA+B;AAC3D,CAAC;AACD,IAAI,qBAAqB,EAAE,OAAO;AAAA,EAChC,UAAU,EAAE,OAAO,yBAAyB;AAC9C,CAAC;AAID,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,eAAe,iBAAiB,IAAI,cAAc;AAC1D,QAAM,SAAS,UAAU;AACzB,+BAAU,MAAM;AACd,aAAS;AAAA,EACX,GAAG,CAAC,QAAQ,CAAC;AACb,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,KAAK;AACtD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,gBAAgB,UAAU,SAAS;AAAA,IAClD,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,oBAAgB,sBAAO,gBAAgB,UAAU,SAAS,CAAC;AACjE,QAAM,eAAW,cAAAE,SAAS,MAAM,yBAAyB,QAAQ,GAAG,CAAC,QAAQ,CAAC;AAC9E,QAAM,UAAU,uBAAuB,WAAW,QAAQ;AAC1D,QAAM,EAAE,aAAa,UAAU,IAAI,qCAAqC;AACxE,QAAM,WAAW,KAAK,aAAa,OAAO,SAAS;AA3WrD;AA4WI,UAAM,UAAU;AAAA,MACd,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,OAAO;AAAA,IACT;AACA,eAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ,GAAG;AAChE,iBAAW,CAAC,mBAAmB,IAAI,KAAK,OAAO;AAAA,QAC7C,QAAQ;AAAA,MACV,GAAG;AACD,mBAAW,CAAC,aAAa,KAAK,KAAK,OAAO,QAAQ,KAAK,SAAS,GAAG;AACjE,cAAI,MAAM,IAAI;AACZ,kBAAM,cAAa,uDAAc,YAAd,mBAAuB,aAAvB,mBAAkC,eAAlC,mBAA8C,oBAA9C,mBAAgE,uBAAhE,mBAAoF,cAApF,mBAAgG,iBAAhG,mBAA8G;AACjI,gBAAI,cAAc,CAAC,MAAM,SAAS;AAChC,sBAAQ,OAAO,KAAK,MAAM,EAAE;AAAA,YAC9B,OAAO;AACL,oBAAM,cAAc,MAAM,aAAa,KAAK,WAAW,MAAM,QAAQ,IAAI;AACzE,oBAAM,oBAAmB,uDAAc,YAAd,mBAAuB,aAAvB,mBAAkC,eAAlC,mBAA8C,oBAA9C,mBAAgE,uBAAhE,mBAAoF,cAApF,mBAAgG,iBAAhG,mBAA8G;AACvI,kBAAI,gBAAgB,kBAAkB;AACpC,wBAAQ,OAAO,KAAK;AAAA,kBAClB;AAAA,kBACA;AAAA,kBACA,kBAAkB;AAAA,gBACpB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,MAAM,MAAM,MAAM,aAAa,IAAI;AACtC,oBAAQ,OAAO,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA,kBAAkB,WAAW,MAAM,QAAQ;AAAA,YAC7C,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,SAAS,GAAG;AAC7B,sBAAgB,IAAI;AACpB,YAAM,UAAU,MAAM,OAAO;AAAA,QAC3B,OAAOF,GAAE,oBAAoB;AAAA,QAC7B,aAAaA,GAAE,iCAAiC;AAAA,UAC9C,OAAO,QAAQ,OAAO;AAAA,QACxB,CAAC;AAAA,QACD,aAAaA,GAAE,kBAAkB;AAAA,QACjC,YAAYA,GAAE,gBAAgB;AAAA,QAC9B,SAAS;AAAA,MACX,CAAC;AACD,sBAAgB,KAAK;AACrB,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,SAAS;AAAA,MACzB,WAAW,MAAM;AACf,cAAM,QAAQA,GAAE,8BAA8B,CAAC;AAC/C,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,oBAAAG,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,oBAAAC,MAAM,cAAc,EAAE,UAAU,WAAW,2BAA2B,UAAU;AAAA,QAClJ,oBAAAD,KAAK,gBAAgB,QAAQ,CAAC,CAAC;AAAA,QAC/B,oBAAAA,KAAK,gBAAgB,MAAM,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA;AAAA,MACjH;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA,iBAAiB,CAAC,YAAY,iBAAiB,CAAC,OAAO;AAAA,QACvD,qBAAqB,aAAa;AAAA,QAClC,sBAAsB;AAAA,MACxB;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,uCAAuC,UAAU;AAAA,UAClI,oBAAAD,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,MAAM,UAAU,UAAUH,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7K,oBAAAG,KAAK,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,WAAW,UAAUH,GAAE,cAAc,EAAE,CAAC;AAAA,IACnH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AACA,SAAS,WAAW,KAAK;AACvB,MAAI,mCAAmC,GAAG,GAAG;AAC3C,WAAO,IAAI;AAAA,EACb;AACF;AACA,SAAS,gBAAgB,UAAU,WAAW;AAC5C,SAAO;AAAA,IACL,UAAU,SAAS,OAAO,CAAC,YAAY,YAAY;AAtcvD;AAucM,YAAM,kBAAiB,aAAQ,oBAAR,mBAAyB;AAAA,QAC9C,CAAC,SAAS,SAAS;AACjB,gBAAM,eAAe,UAAU,OAAO,CAAC,aAAa,aAAa;AAzc3E,gBAAAK,KAAA;AA0cY,kBAAM,SAAQ,MAAAA,MAAA,KAAK,cAAL,gBAAAA,IAAgB,oBAAhB,mBAAiC;AAAA,cAC7C,CAAC,WAAW,OAAO,gBAAgB,SAAS;AAAA;AAE9C,wBAAY,SAAS,EAAE,IAAI;AAAA,cACzB,IAAI,+BAAO;AAAA,cACX,WAAU,+BAAO,sBAAqB,SAAS,+BAAO,mBAAmB;AAAA,cACzE,SAAS,CAAC,CAAC;AAAA,cACX,kBAAiB,+BAAO,sBAAqB,KAAK,OAAM,+BAAO,sBAAqB,KAAK;AAAA,YAC3F;AACA,mBAAO;AAAA,UACT,GAAG,CAAC,CAAC;AACL,kBAAQ,KAAK,iBAAiB,IAAI,EAAE,WAAW,aAAa;AAC5D,iBAAO;AAAA,QACT;AAAA,QACA,CAAC;AAAA;AAEH,iBAAW,QAAQ,EAAE,IAAI,EAAE,iBAAiB,kBAAkB,CAAC,EAAE;AACjE,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACF;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,GAAAL,GAAE,IAAI,eAAgB;AAC9B,QAAM,OAAO,cAAc;AAC3B,QAAM,CAAC,WAAW,YAAY,QAAI,cAAAM,UAAU,KAAK;AACjD,QAAM,iBAAa,cAAAC,QAAQ;AAC3B,oBAAAC,WAAW,MAAM;AACf,eAAW,UAAU,WAAW,MAAM;AACpC,mBAAa,IAAI;AAAA,IACnB,GAAG,GAAG;AACN,WAAO,MAAM;AACX,UAAI,WAAW,SAAS;AACtB,qBAAa,WAAW,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,WAAW,MAAM;AACrB,QAAI,WAAW,SAAS;AACtB,mBAAa,WAAW,OAAO;AAAA,IACjC;AACA,iBAAa,KAAK;AAAA,EACpB;AACA,aAAuB,oBAAAC,MAAM,OAAO,EAAE,UAAU;AAAA,QAC9B,oBAAAC,KAAK,OAAO,EAAE,WAAW,kCAAkC,cAA0B,oBAAAA,KAAK,iBAAiB,EAAE,UAAU,gBAA4B,oBAAAA,KAAK,aAAa,EAAE,UAAU,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,QACjM,oBAAAD,MAAM,iBAAiB,EAAE,UAAU;AAAA,UACjC,oBAAAC,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUV,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,UACtJ,oBAAAU,KAAK,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUV,GAAE,4BAA4B,EAAE,CAAC,EAAE,CAAC;AAAA,UAChK,oBAAAU,KAAK,wBAAU,EAAE,cAA0B,oBAAAA,KAAK,sBAAsB,CAAC,CAAC,GAAG,cAA0B,oBAAAA,KAAK,OAAO,EAAE,SAAS,KAAK,MAAM,UAAU,CAAC,UAAU;AAC1K,mBAAuB,oBAAAA;AAAA,UACrB;AAAA,UACA;AAAA,YACE,UAAU,MAAM;AAAA,YAChB,WAAW,MAAM;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC,EAAE,CAAC;AAAA,IACR,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,uBAAuB,MAAM;AAC/B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,yEAAyE,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,QACzM,oBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,cAA0B,oBAAAA,KAAK,UAAU,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC;AAAA,QAC1G,oBAAAA,KAAK,OAAO,EAAE,WAAW,wBAAwB,cAA0B,oBAAAA;AAAA,MACzF;AAAA,MACA;AAAA,QACE,SAAS,MAAM,KAAK,EAAE,QAAQ,GAAG,CAAC;AAAA,MACpC;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,2DAA2D,UAAU;AAAA,UAC7F,oBAAAC,KAAK,UAAU,EAAE,WAAW,eAAe,CAAC;AAAA,UAC5C,oBAAAA,KAAK,UAAU,EAAE,WAAW,eAAe,CAAC;AAAA,IAC9D,EAAE,CAAC;AAAA,EACL,EAAE,CAAC,EAAE,CAAC;AACR;", "names": ["import_react", "import_react", "import_react", "import_jsx_runtime", "import_jsx_runtime", "t", "jsx2", "useMemo2", "jsx3", "jsxs2", "_a", "useState2", "useRef2", "useEffect2", "jsxs3", "jsx4"]}