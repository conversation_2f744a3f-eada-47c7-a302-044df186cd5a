{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-C5LYZZZ5.mjs"], "sourcesContent": ["import {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/currencies.tsx\nimport { useQuery } from \"@tanstack/react-query\";\nvar CURRENCIES_QUERY_KEY = \"currencies\";\nvar currenciesQueryKeys = queryKeysFactory(CURRENCIES_QUERY_KEY);\nvar useCurrencies = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.currency.list(query),\n    queryKey: currenciesQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\nexport {\n  useCurrencies\n};\n"], "mappings": ";;;;;;;;;;;AASA,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB,iBAAiB,oBAAoB;AAC/D,IAAI,gBAAgB,CAAC,OAAO,YAAY;AACtC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,SAAS,KAAK,KAAK;AAAA,IAC5C,UAAU,oBAAoB,KAAK,KAAK;AAAA,IACxC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;", "names": []}