{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-F6PXCY3N.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/customers.tsx\nimport {\n  useMutation as useMutation2,\n  useQuery as useQuery2\n} from \"@tanstack/react-query\";\n\n// src/hooks/api/customer-groups.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar CUSTOMER_GROUPS_QUERY_KEY = \"customer_groups\";\nvar customerGroupsQueryKeys = queryKeysFactory(\n  CUSTOMER_GROUPS_QUERY_KEY\n);\nvar useCustomerGroup = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: customerGroupsQueryKeys.detail(id, query),\n    queryFn: async () => sdk.admin.customerGroup.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCustomerGroups = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.customerGroup.list(query),\n    queryKey: customerGroupsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateCustomerGroup = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.customerGroup.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateCustomerGroup = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.customerGroup.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCustomerGroup = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.customerGroup.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCustomerGroupLazy = (options) => {\n  return useMutation({\n    mutationFn: ({ id }) => sdk.admin.customerGroup.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.detail(variables.id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useAddCustomersToGroup = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.customerGroup.batchCustomers(id, { add: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: customersQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRemoveCustomersFromGroup = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.customerGroup.batchCustomers(id, { remove: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: customersQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\n// src/hooks/api/customers.tsx\nvar CUSTOMERS_QUERY_KEY = \"customers\";\nvar customersQueryKeys = queryKeysFactory(CUSTOMERS_QUERY_KEY);\nvar customerAddressesQueryKeys = queryKeysFactory(\n  `${CUSTOMERS_QUERY_KEY}-addresses`\n);\nvar useCustomer = (id, query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryKey: customersQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.customer.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCustomers = (query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryFn: () => sdk.admin.customer.list(query),\n    queryKey: customersQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateCustomer = (options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.customer.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateCustomer = (id, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.customer.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCustomer = (id, options) => {\n  return useMutation2({\n    mutationFn: () => sdk.admin.customer.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: customersQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useBatchCustomerCustomerGroups = (id, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.customer.batchCustomerGroups(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customerGroupsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customersQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: customersQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateCustomerAddress = (id, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.customer.createAddress(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });\n      queryClient.invalidateQueries({\n        queryKey: customerAddressesQueryKeys.list(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCustomerAddress = (id, options) => {\n  return useMutation2({\n    mutationFn: (addressId) => sdk.admin.customer.deleteAddress(id, addressId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });\n      queryClient.invalidateQueries({\n        queryKey: customerAddressesQueryKeys.list(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useCustomer,\n  useCustomers,\n  useCreateCustomer,\n  useUpdateCustomer,\n  useDeleteCustomer,\n  useBatchCustomerCustomerGroups,\n  useCreateCustomerAddress,\n  useDeleteCustomerAddress,\n  customerGroupsQueryKeys,\n  useCustomerGroup,\n  useCustomerGroups,\n  useCreateCustomerGroup,\n  useUpdateCustomerGroup,\n  useDeleteCustomerGroup,\n  useDeleteCustomerGroupLazy,\n  useAddCustomersToGroup,\n  useRemoveCustomersFromGroup\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAqBA,IAAI,4BAA4B;AAChC,IAAI,0BAA0B;AAAA,EAC5B;AACF;AACA,IAAI,mBAAmB,CAAC,IAAI,OAAO,YAAY;AAC7C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,wBAAwB,OAAO,IAAI,KAAK;AAAA,IAClD,SAAS,YAAY,IAAI,MAAM,cAAc,SAAS,IAAI,KAAK;AAAA,IAC/D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,OAAO,YAAY;AAC1C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,cAAc,KAAK,KAAK;AAAA,IACjD,UAAU,wBAAwB,KAAK,KAAK;AAAA,IAC5C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,yBAAyB,CAAC,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,OAAO,OAAO;AAAA,IAC/D,WAAW,CAAC,MAAM,WAAW,YAAY;AA5C7C;AA6CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,OAAO,IAAI,OAAO;AAAA,IACnE,WAAW,CAAC,MAAM,WAAW,YAAY;AAxD7C;AAyDM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,OAAO,EAAE;AAAA,MAC7C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,cAAc,OAAO,EAAE;AAAA,IACnD,WAAW,CAAC,MAAM,WAAW,YAAY;AAvE7C;AAwEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,OAAO,EAAE;AAAA,MAC7C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,6BAA6B,CAAC,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,cAAc,OAAO,EAAE;AAAA,IACzD,WAAW,CAAC,MAAM,WAAW,YAAY;AAtF7C;AAuFM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,OAAO,UAAU,EAAE;AAAA,MACvD,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,eAAe,IAAI,EAAE,KAAK,QAAQ,CAAC;AAAA,IACpF,WAAW,CAAC,MAAM,WAAW,YAAY;AArG7C;AAsGM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,OAAO,EAAE;AAAA,MAC7C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,mBAAmB,MAAM;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,8BAA8B,CAAC,IAAI,YAAY;AACjD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,eAAe,IAAI,EAAE,QAAQ,QAAQ,CAAC;AAAA,IACvF,WAAW,CAAC,MAAM,WAAW,YAAY;AAvH7C;AAwHM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,OAAO,EAAE;AAAA,MAC7C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,mBAAmB,MAAM;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AAGA,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB,iBAAiB,mBAAmB;AAC7D,IAAI,6BAA6B;AAAA,EAC/B,GAAG,mBAAmB;AACxB;AACA,IAAI,cAAc,CAAC,IAAI,OAAO,YAAY;AACxC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,mBAAmB,OAAO,EAAE;AAAA,IACtC,SAAS,YAAY,IAAI,MAAM,SAAS,SAAS,IAAI,KAAK;AAAA,IAC1D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,eAAe,CAAC,OAAO,YAAY;AACrC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,SAAS,MAAM,IAAI,MAAM,SAAS,KAAK,KAAK;AAAA,IAC5C,UAAU,mBAAmB,KAAK,KAAK;AAAA,IACvC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,YAAY;AACnC,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,OAAO,OAAO;AAAA,IAC1D,WAAW,CAAC,MAAM,WAAW,YAAY;AAhK7C;AAiKM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,oBAAoB,CAAC,IAAI,YAAY;AACvC,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,OAAO,IAAI,OAAO;AAAA,IAC9D,WAAW,CAAC,MAAM,WAAW,YAAY;AA1K7C;AA2KM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,OAAO,EAAE,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,oBAAoB,CAAC,IAAI,YAAY;AACvC,SAAO,YAAa;AAAA,IAClB,YAAY,MAAM,IAAI,MAAM,SAAS,OAAO,EAAE;AAAA,IAC9C,WAAW,CAAC,MAAM,WAAW,YAAY;AArL7C;AAsLM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,mBAAmB,OAAO,EAAE;AAAA,MACxC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,iCAAiC,CAAC,IAAI,YAAY;AACpD,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,oBAAoB,IAAI,OAAO;AAAA,IAC3E,WAAW,CAAC,MAAM,WAAW,YAAY;AAlM7C;AAmMM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,mBAAmB,MAAM;AAAA,MACrC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,mBAAmB,QAAQ;AAAA,MACvC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,cAAc,IAAI,OAAO;AAAA,IACrE,WAAW,CAAC,MAAM,WAAW,YAAY;AAvN7C;AAwNM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,OAAO,EAAE,EAAE,CAAC;AACzE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,2BAA2B,KAAK,EAAE;AAAA,MAC9C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,cAAc,IAAI,MAAM,SAAS,cAAc,IAAI,SAAS;AAAA,IACzE,WAAW,CAAC,MAAM,WAAW,YAAY;AArO7C;AAsOM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,OAAO,EAAE,EAAE,CAAC;AACzE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,2BAA2B,KAAK,EAAE;AAAA,MAC9C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}