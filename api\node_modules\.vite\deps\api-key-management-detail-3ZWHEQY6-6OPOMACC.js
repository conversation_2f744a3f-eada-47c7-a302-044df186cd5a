import {
  getApiKeyStatusProps,
  getApiKeyTypeProps,
  prettifyRedactedToken
} from "./chunk-R3MT5J4J.js";
import {
  UserLink
} from "./chunk-Z4VZQPVO.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-R325LWWE.js";
import "./chunk-WNW4SNUS.js";
import {
  DataTable
} from "./chunk-EPUS4TBC.js";
import "./chunk-32T72GVU.js";
import {
  useDate
} from "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton,
  Skeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import {
  apiKeysQueryKeys,
  useApiKey,
  useBatchRemoveSalesChannelsFromApiKey,
  useDeleteApiKey,
  useRevokeApiKey
} from "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import {
  useUser
} from "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Container,
  Copy,
  Heading,
  PencilSquare,
  StatusBadge,
  Text,
  Trash,
  XCircle,
  createDataTableColumnHelper,
  createDataTableCommandHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/api-key-management-detail-3ZWHEQY6.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ApiKeyGeneralSection = ({ apiKey }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { getFullDate } = useDate();
  const { mutateAsync: revokeAsync } = useRevokeApiKey(apiKey.id);
  const { mutateAsync: deleteAsync } = useDeleteApiKey(apiKey.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("apiKeyManagement.delete.warning", {
        title: apiKey.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await deleteAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("apiKeyManagement.delete.successToast", {
            title: apiKey.title
          })
        );
        navigate("..", { replace: true });
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const handleRevoke = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("apiKeyManagement.revoke.warning", {
        title: apiKey.title
      }),
      confirmText: t("apiKeyManagement.actions.revoke"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await revokeAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("apiKeyManagement.revoke.successToast", {
            title: apiKey.title
          })
        );
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const dangerousActions = [
    {
      icon: (0, import_jsx_runtime.jsx)(Trash, {}),
      label: t("actions.delete"),
      onClick: handleDelete,
      disabled: !apiKey.revoked_at
    }
  ];
  if (!apiKey.revoked_at) {
    dangerousActions.unshift({
      icon: (0, import_jsx_runtime.jsx)(XCircle, {}),
      label: t("apiKeyManagement.actions.revoke"),
      onClick: handleRevoke,
      disabled: !!apiKey.revoked_at
    });
  }
  const apiKeyStatus = getApiKeyStatusProps(apiKey.revoked_at, t);
  const apiKeyType = getApiKeyTypeProps(apiKey.type, t);
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: apiKey.title }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-x-2", children: (0, import_jsx_runtime.jsx)(StatusBadge, { color: apiKeyStatus.color, children: apiKeyStatus.label }) }),
        (0, import_jsx_runtime.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                    to: "edit"
                  }
                ]
              },
              {
                actions: dangerousActions
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.key") }),
      apiKey.type === "secret" ? (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", className: "inline-block w-fit", children: prettifyRedactedToken(apiKey.redacted) }) : (0, import_jsx_runtime.jsx)(Copy, { asChild: true, content: apiKey.token, className: "cursor-pointer", children: (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", className: "text-ui-tag-neutral-text", children: prettifyRedactedToken(apiKey.redacted) }) })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.type") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: apiKeyType.label })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("apiKeyManagement.fields.lastUsedAtLabel") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: apiKey.last_used_at ? getFullDate({ date: apiKey.last_used_at, includeTime: true }) : "-" })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("apiKeyManagement.fields.createdByLabel") }),
      (0, import_jsx_runtime.jsx)(ActionBy, { userId: apiKey.created_by })
    ] }),
    apiKey.revoked_at && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("apiKeyManagement.fields.revokedAtLabel") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: getFullDate({ date: apiKey.revoked_at, includeTime: true }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("apiKeyManagement.fields.revokedByLabel") }),
        (0, import_jsx_runtime.jsx)(ActionBy, { userId: apiKey.revoked_by })
      ] })
    ] })
  ] });
};
var ActionBy = ({ userId }) => {
  const { user, isLoading, isError, error } = useUser(userId, void 0, {
    enabled: !!userId
  });
  if (!userId) {
    return (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: "-" });
  }
  if (isError) {
    throw error;
  }
  if (isLoading) {
    return (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-[20px_1fr]", children: [
      (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-5 w-5 rounded-full" }),
      (0, import_jsx_runtime.jsx)(Skeleton, { className: "w-full max-w-[220px]" })
    ] });
  }
  if (!user) {
    return (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: "-" });
  }
  return (0, import_jsx_runtime.jsx)(UserLink, { ...user });
};
var PAGE_SIZE = 10;
var PREFIX = "sc";
var ApiKeySalesChannelSection = ({
  apiKey
}) => {
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { t } = useTranslation();
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { sales_channels, count, isPending } = useSalesChannels(
    { ...searchParams, publishable_key_id: apiKey.id },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns(apiKey.id);
  const filters = useSalesChannelTableFilters();
  const commands = useCommands(apiKey.id, setRowSelection);
  const emptyState = useSalesChannelTableEmptyState();
  return (0, import_jsx_runtime2.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime2.jsx)(
    DataTable,
    {
      data: sales_channels,
      columns,
      filters,
      commands,
      heading: t("salesChannels.domain"),
      getRowId: (row) => row.id,
      rowCount: count,
      isLoading: isPending,
      emptyState,
      rowSelection: {
        state: rowSelection,
        onRowSelectionChange: setRowSelection
      },
      rowHref: (row) => `/settings/sales-channels/${row.id}`,
      action: {
        label: t("actions.add"),
        to: "sales-channels"
      },
      prefix: PREFIX,
      pageSize: PAGE_SIZE
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = (id) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const base = useSalesChannelTableColumns();
  const { mutateAsync } = useBatchRemoveSalesChannelsFromApiKey(id);
  const handleDelete = (0, import_react.useCallback)(
    async (salesChannel) => {
      const res = await prompt({
        title: t("general.areYouSure"),
        description: t("apiKeyManagement.removeSalesChannel.warning", {
          name: salesChannel.name
        }),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!res) {
        return;
      }
      await mutateAsync([salesChannel.id], {
        onSuccess: () => {
          toast.success(
            t("apiKeyManagement.removeSalesChannel.successToast", {
              count: 1
            })
          );
        },
        onError: (err) => {
          toast.error(err.message);
        }
      });
    },
    [mutateAsync, prompt, t]
  );
  return (0, import_react.useMemo)(
    () => [
      columnHelper.select(),
      ...base,
      columnHelper.action({
        actions: (ctx) => [
          [
            {
              label: t("actions.edit"),
              icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
              onClick: () => {
                navigate(`/settings/sales-channels/${ctx.row.original.id}/edit`);
              }
            }
          ],
          [
            {
              icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: () => handleDelete(ctx.row.original)
            }
          ]
        ]
      })
    ],
    [base, handleDelete, navigate, t]
  );
};
var commandHelper = createDataTableCommandHelper();
var useCommands = (id, setRowSelection) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useBatchRemoveSalesChannelsFromApiKey(id);
  const handleRemove = (0, import_react.useCallback)(
    async (rowSelection) => {
      const keys = Object.keys(rowSelection);
      const res = await prompt({
        title: t("general.areYouSure"),
        description: t("apiKeyManagement.removeSalesChannel.warningBatch", {
          count: keys.length
        }),
        confirmText: t("actions.continue"),
        cancelText: t("actions.cancel")
      });
      if (!res) {
        return;
      }
      await mutateAsync(keys, {
        onSuccess: () => {
          toast.success(
            t("apiKeyManagement.removeSalesChannel.successToastBatch", {
              count: keys.length
            })
          );
          setRowSelection({});
        },
        onError: (err) => {
          toast.error(err.message);
        }
      });
    },
    [mutateAsync, prompt, t, setRowSelection]
  );
  return (0, import_react.useMemo)(
    () => [
      commandHelper.command({
        action: handleRemove,
        label: t("actions.remove"),
        shortcut: "r"
      })
    ],
    [handleRemove, t]
  );
};
var ApiKeyManagementDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const { getWidgets } = useExtension();
  const { api_key, isLoading, isError, error } = useApiKey(id, {
    initialData
  });
  if (isLoading || !api_key) {
    return (0, import_jsx_runtime3.jsx)(SingleColumnPageSkeleton, { showJSON: true, sections: 1 });
  }
  const isPublishable = (api_key == null ? void 0 : api_key.type) === "publishable";
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    SingleColumnPage,
    {
      hasOutlet: true,
      showJSON: true,
      widgets: {
        before: getWidgets("api_key.details.before"),
        after: getWidgets("api_key.details.after")
      },
      data: api_key,
      children: [
        (0, import_jsx_runtime3.jsx)(ApiKeyGeneralSection, { apiKey: api_key }),
        isPublishable && (0, import_jsx_runtime3.jsx)(ApiKeySalesChannelSection, { apiKey: api_key })
      ]
    }
  );
};
var ApiKeyManagementDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { api_key } = useApiKey(id, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!api_key) {
    return null;
  }
  return (0, import_jsx_runtime4.jsx)("span", { children: api_key.title });
};
var apiKeyDetailQuery = (id) => ({
  queryKey: apiKeysQueryKeys.detail(id),
  queryFn: async () => sdk.admin.apiKey.retrieve(id)
});
var apiKeyLoader = async ({ params }) => {
  const id = params.id;
  const query = apiKeyDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  ApiKeyManagementDetailBreadcrumb as Breadcrumb,
  ApiKeyManagementDetail as Component,
  apiKeyLoader as loader
};
//# sourceMappingURL=api-key-management-detail-3ZWHEQY6-6OPOMACC.js.map
