import {
  useDeletePriceList
} from "./chunk-XBIMCDU6.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";

// node_modules/@medusajs/dashboard/dist/chunk-LTC6LGS4.mjs
var useDeletePriceListAction = ({
  priceList
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeletePriceList(priceList.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("priceLists.delete.confirmation", {
        title: priceList.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("priceLists.delete.successToast", {
            title: priceList.title
          })
        );
        navigate("/price-lists");
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return handleDelete;
};

export {
  useDeletePriceListAction
};
//# sourceMappingURL=chunk-Q2DLRTSI.js.map
