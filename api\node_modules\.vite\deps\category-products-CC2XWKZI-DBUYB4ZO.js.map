{"version": 3, "sources": ["../../@medusajs/dashboard/dist/category-products-CC2XWKZI.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport {\n  useProductCategory,\n  useUpdateProductCategoryProducts\n} from \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/categories/category-products/category-products.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/categories/category-products/components/edit-category-products-form/edit-category-products-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useForm } from \"react-hook-form\";\nimport { z } from \"zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditCategoryProductsSchema = z.object({\n  product_ids: z.array(z.string())\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"p\";\nvar EditCategoryProductsForm = ({\n  categoryId,\n  products = []\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const [selection, setSelection] = useState(\n    products.reduce((acc, p) => {\n      acc[p.id] = true;\n      return acc;\n    }, {})\n  );\n  const form = useForm({\n    defaultValues: {\n      product_ids: []\n    },\n    resolver: zodResolver(EditCategoryProductsSchema)\n  });\n  const updater = (newSelection) => {\n    const value = typeof newSelection === \"function\" ? newSelection(selection) : newSelection;\n    form.setValue(\"product_ids\", Object.keys(value), {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setSelection(value);\n  };\n  const { searchParams, raw } = useProductTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const {\n    products: data,\n    count,\n    isPending,\n    isError,\n    error\n  } = useProducts({\n    ...searchParams\n  });\n  const columns = useColumns();\n  const filters = useProductTableFilters([\"categories\"]);\n  const { table } = useDataTable({\n    data,\n    columns,\n    getRowId: (original) => original.id,\n    count,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX,\n    enableRowSelection: (row) => {\n      return !products.some((p) => p.id === row.original.id);\n    },\n    enablePagination: true,\n    rowSelection: {\n      state: selection,\n      updater\n    }\n  });\n  const { mutateAsync, isPending: isMutating } = useUpdateProductCategoryProducts(categoryId);\n  const handleSubmit = form.handleSubmit(async (data2) => {\n    await mutateAsync(\n      {\n        add: data2.product_ids\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"categories.products.add.successToast\", {\n              count: data2.product_ids.length - products.length\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error2) => {\n          toast.error(error2.message);\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          form.formState.errors.product_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.product_ids.message }),\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isMutating, children: t(\"actions.save\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            columns,\n            pageSize: PAGE_SIZE,\n            count,\n            queryObject: raw,\n            filters,\n            orderBy: [\n              { key: \"title\", label: t(\"fields.title\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            prefix: PREFIX,\n            isLoading: isPending,\n            layout: \"fill\",\n            pagination: true,\n            search: \"autofocus\"\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const base = useProductTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isSelected = row.getIsSelected() || isPreSelected;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isPreSelected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isPreSelected) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"categories.products.add.disabledTooltip\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n};\n\n// src/routes/categories/category-products/category-products.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CategoryProducts = () => {\n  const { id } = useParams();\n  const { product_category, isPending, isFetching, isError, error } = useProductCategory(id, {\n    fields: \"products.id\"\n  });\n  const ready = !isPending && !isFetching && !!product_category;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(\n    EditCategoryProductsForm,\n    {\n      categoryId: product_category.id,\n      products: product_category.products\n    }\n  ) });\n};\nexport {\n  CategoryProducts as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,mBAAkC;AAElC,yBAA0B;AA8K1B,IAAAA,sBAA4B;AA7K5B,IAAI,6BAA6B,EAAE,OAAO;AAAA,EACxC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC;AACjC,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AAAA,EACA,WAAW,CAAC;AACd,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,CAAC,WAAW,YAAY,QAAI;AAAA,IAChC,SAAS,OAAO,CAAC,KAAK,MAAM;AAC1B,UAAI,EAAE,EAAE,IAAI;AACZ,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,aAAa,CAAC;AAAA,IAChB;AAAA,IACA,UAAU,EAAY,0BAA0B;AAAA,EAClD,CAAC;AACD,QAAM,UAAU,CAAC,iBAAiB;AAChC,UAAM,QAAQ,OAAO,iBAAiB,aAAa,aAAa,SAAS,IAAI;AAC7E,SAAK,SAAS,eAAe,OAAO,KAAK,KAAK,GAAG;AAAA,MAC/C,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,iBAAa,KAAK;AAAA,EACpB;AACA,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM;AAAA,IACJ,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YAAY;AAAA,IACd,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,uBAAuB,CAAC,YAAY,CAAC;AACrD,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B;AAAA,IACA;AAAA,IACA,UAAU,CAAC,aAAa,SAAS;AAAA,IACjC;AAAA,IACA,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,oBAAoB,CAAC,QAAQ;AAC3B,aAAO,CAAC,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,SAAS,EAAE;AAAA,IACvD;AAAA,IACA,kBAAkB;AAAA,IAClB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,iCAAiC,UAAU;AAC1F,QAAM,eAAe,KAAK,aAAa,OAAO,UAAU;AACtD,UAAM;AAAA,MACJ;AAAA,QACE,KAAK,MAAM;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,wCAAwC;AAAA,cACxC,OAAO,MAAM,YAAY,SAAS,SAAS;AAAA,YAC7C,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,WAAW;AACnB,gBAAM,MAAM,OAAO,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClJ,KAAK,UAAU,OAAO,mBAA+B,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,YAAY,QAAQ,CAAC;AAAA,cACxH,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,YAAY,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACnH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,cAA0B;AAAA,UAC5G;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,aAAa;AAAA,YACb;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,SAAS,OAAOA,GAAE,cAAc,EAAE;AAAA,cACzC,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe;AACjB,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,yCAAyC;AAAA,gBACpD,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACF;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,kBAAkB,WAAW,YAAY,SAAS,MAAM,IAAI,mBAAmB,IAAI;AAAA,IACzF,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;AAC7C,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA;AAAA,IAChF;AAAA,IACA;AAAA,MACE,YAAY,iBAAiB;AAAA,MAC7B,UAAU,iBAAiB;AAAA,IAC7B;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}