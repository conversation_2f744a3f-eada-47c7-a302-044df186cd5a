import {
  useDataTableDateColumns
} from "./chunk-WNW4SNUS.js";
import {
  useDataTableDateFilters
} from "./chunk-EPUS4TBC.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Tooltip,
  clx,
  createDataTableColumnHelper,
  createDataTableFilterHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-44QN6VEG.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var DataTableStatusCell = ({
  color,
  children
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "txt-compact-small text-ui-fg-subtle flex h-full w-full items-center gap-x-2 overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(
      "div",
      {
        role: "presentation",
        className: "flex h-5 w-2 items-center justify-center",
        children: (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: clx(
              "h-2 w-2 rounded-sm shadow-[0px_0px_0px_1px_rgba(0,0,0,0.12)_inset]",
              {
                "bg-ui-tag-neutral-icon": color === "grey",
                "bg-ui-tag-green-icon": color === "green",
                "bg-ui-tag-red-icon": color === "red",
                "bg-ui-tag-blue-icon": color === "blue",
                "bg-ui-tag-orange-icon": color === "orange",
                "bg-ui-tag-purple-icon": color === "purple"
              }
            )
          }
        )
      }
    ),
    (0, import_jsx_runtime.jsx)("span", { className: "truncate", children })
  ] });
};
var columnHelper = createDataTableColumnHelper();
var useSalesChannelTableColumns = () => {
  const { t } = useTranslation();
  const dateColumns = useDataTableDateColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: () => t("fields.name"),
        enableSorting: true,
        sortLabel: t("fields.name"),
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("description", {
        header: () => t("fields.description"),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime2.jsx)(Tooltip, { content: getValue(), children: (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: getValue() }) }) });
        },
        enableSorting: true,
        sortLabel: t("fields.description"),
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
        maxSize: 250,
        minSize: 100
      }),
      columnHelper.accessor("is_disabled", {
        header: () => t("fields.status"),
        enableSorting: true,
        sortLabel: t("fields.status"),
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
        cell: ({ getValue }) => {
          const value = getValue();
          return (0, import_jsx_runtime2.jsx)(DataTableStatusCell, { color: value ? "grey" : "green", children: value ? t("general.disabled") : t("general.enabled") });
        }
      }),
      ...dateColumns
    ],
    [t, dateColumns]
  );
};
var useSalesChannelTableEmptyState = () => {
  const { t } = useTranslation();
  return (0, import_react2.useMemo)(() => {
    const content = {
      empty: {
        heading: t("salesChannels.list.empty.heading"),
        description: t("salesChannels.list.empty.description")
      },
      filtered: {
        heading: t("salesChannels.list.filtered.heading"),
        description: t("salesChannels.list.filtered.description")
      }
    };
    return content;
  }, [t]);
};
var filterHelper = createDataTableFilterHelper();
var useSalesChannelTableFilters = () => {
  const { t } = useTranslation();
  const dateFilters = useDataTableDateFilters();
  return (0, import_react3.useMemo)(
    () => [
      filterHelper.accessor("is_disabled", {
        label: t("fields.status"),
        type: "radio",
        options: [
          {
            label: t("general.enabled"),
            value: "false"
          },
          {
            label: t("general.disabled"),
            value: "true"
          }
        ]
      }),
      ...dateFilters
    ],
    [dateFilters, t]
  );
};
var useSalesChannelTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "order", "created_at", "updated_at", "is_disabled"],
    prefix
  );
  const { offset, created_at, updated_at, is_disabled, ...rest } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    is_disabled: is_disabled ? JSON.parse(is_disabled) : void 0,
    ...rest
  };
  return searchParams;
};

export {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
};
//# sourceMappingURL=chunk-R325LWWE.js.map
