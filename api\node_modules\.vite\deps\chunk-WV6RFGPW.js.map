{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-AKESWRB7.mjs"], "sourcesContent": ["import {\n  useCustomerGroupTableColumns\n} from \"./chunk-ZJRFL6ZN.mjs\";\nimport {\n  useCustomerGroupTableQuery\n} from \"./chunk-MOSRJHJ3.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport {\n  useCustomerGroupTableFilters\n} from \"./chunk-DLZWPHHO.mjs\";\nimport {\n  StackedDrawer,\n  StackedFocusModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  useCustomerGroups\n} from \"./chunk-F6PXCY3N.mjs\";\n\n// src/routes/price-lists/common/components/price-list-customer-group-rule-form/price-list-customer-group-rule-form.tsx\nimport { Button, Checkbox } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 50;\nvar PREFIX = \"cg\";\nvar initRowSelection = (state) => {\n  return state.reduce((acc, group) => {\n    acc[group.id] = true;\n    return acc;\n  }, {});\n};\nvar PriceListCustomerGroupRuleForm = ({\n  state,\n  setState,\n  type\n}) => {\n  const { t } = useTranslation();\n  const [rowSelection, setRowSelection] = useState(\n    initRowSelection(state)\n  );\n  const [intermediate, setIntermediate] = useState(state);\n  useEffect(() => {\n    setRowSelection(initRowSelection(state));\n    setIntermediate(state);\n  }, [state]);\n  const { searchParams, raw } = useCustomerGroupTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { customer_groups, count, isLoading, isError, error } = useCustomerGroups(\n    { ...searchParams, fields: \"id,name,customers.id\" },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const updater = (value) => {\n    const state2 = typeof value === \"function\" ? value(rowSelection) : value;\n    const currentIds = Object.keys(rowSelection);\n    const ids = Object.keys(state2);\n    const newIds = ids.filter((id) => !currentIds.includes(id));\n    const removedIds = currentIds.filter((id) => !ids.includes(id));\n    const newCustomerGroups = customer_groups?.filter((cg) => newIds.includes(cg.id)).map((cg) => ({ id: cg.id, name: cg.name })) || [];\n    const filteredIntermediate = intermediate.filter(\n      (cg) => !removedIds.includes(cg.id)\n    );\n    setIntermediate([...filteredIntermediate, ...newCustomerGroups]);\n    setRowSelection(state2);\n  };\n  const handleSave = () => {\n    setState(intermediate);\n  };\n  const filters = useCustomerGroupTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: customer_groups || [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    getRowId: (row) => row.id,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    },\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const Component = type === \"focus\" ? StackedFocusModal : StackedDrawer;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(Component.Body, { className: \"min-h-0 p-0\", children: /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        isLoading,\n        filters,\n        orderBy: [\n          { key: \"name\", label: t(\"fields.name\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        layout: \"fill\",\n        pagination: true,\n        search: true,\n        prefix: PREFIX,\n        queryObject: raw\n      }\n    ) }),\n    /* @__PURE__ */ jsxs(Component.Footer, { children: [\n      /* @__PURE__ */ jsx(Component.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", type: \"button\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { type: \"button\", size: \"small\", onClick: handleSave, children: t(\"actions.save\") })\n    ] })\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCustomerGroupTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [base]\n  );\n};\n\nexport {\n  PriceListCustomerGroupRuleForm\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,mBAA6C;AAE7C,yBAA0B;AAC1B,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,mBAAmB,CAAC,UAAU;AAChC,SAAO,MAAM,OAAO,CAAC,KAAK,UAAU;AAClC,QAAI,MAAM,EAAE,IAAI;AAChB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAI,iCAAiC,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,CAAC,cAAc,eAAe,QAAI;AAAA,IACtC,iBAAiB,KAAK;AAAA,EACxB;AACA,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,KAAK;AACtD,8BAAU,MAAM;AACd,oBAAgB,iBAAiB,KAAK,CAAC;AACvC,oBAAgB,KAAK;AAAA,EACvB,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,EAAE,cAAc,IAAI,IAAI,2BAA2B;AAAA,IACvD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,iBAAiB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC5D,EAAE,GAAG,cAAc,QAAQ,uBAAuB;AAAA,IAClD;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,YAAY,IAAI;AACnE,UAAM,aAAa,OAAO,KAAK,YAAY;AAC3C,UAAM,MAAM,OAAO,KAAK,MAAM;AAC9B,UAAM,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC;AAC1D,UAAM,aAAa,WAAW,OAAO,CAAC,OAAO,CAAC,IAAI,SAAS,EAAE,CAAC;AAC9D,UAAM,qBAAoB,mDAAiB,OAAO,CAAC,OAAO,OAAO,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK,QAAO,CAAC;AAClI,UAAM,uBAAuB,aAAa;AAAA,MACxC,CAAC,OAAO,CAAC,WAAW,SAAS,GAAG,EAAE;AAAA,IACpC;AACA,oBAAgB,CAAC,GAAG,sBAAsB,GAAG,iBAAiB,CAAC;AAC/D,oBAAgB,MAAM;AAAA,EACxB;AACA,QAAM,aAAa,MAAM;AACvB,aAAS,YAAY;AAAA,EACvB;AACA,QAAM,UAAU,6BAA6B;AAC7C,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,mBAAmB,CAAC;AAAA,IAC1B;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,UAAU,oBAAoB;AACzD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,QACnF,wBAAI,UAAU,MAAM,EAAE,WAAW,eAAe,cAA0B;AAAA,MACxF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,MACf;AAAA,IACF,EAAE,CAAC;AAAA,QACa,yBAAK,UAAU,QAAQ,EAAE,UAAU;AAAA,UACjC,wBAAI,UAAU,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,MAAM,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACrK,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,SAAS,YAAY,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,IACjH,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,6BAA6B;AAC1C,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;", "names": []}