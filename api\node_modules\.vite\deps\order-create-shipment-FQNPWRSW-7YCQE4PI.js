import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import {
  useCreateOrderShipment,
  useOrder
} from "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Switch,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-shipment-FQNPWRSW.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateShipmentSchema = z.object({
  labels: z.array(
    z.object({
      tracking_number: z.string(),
      // TODO: this 2 are not optional in the API
      tracking_url: z.string().optional(),
      label_url: z.string().optional()
    })
  ),
  send_notification: z.boolean().optional()
});
function OrderCreateShipmentForm({
  order,
  fulfillment
}) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { mutateAsync: createShipment, isPending: isMutating } = useCreateOrderShipment(order.id, fulfillment == null ? void 0 : fulfillment.id);
  const form = useForm({
    defaultValues: {
      send_notification: !order.no_notification
    },
    resolver: t(CreateShipmentSchema)
  });
  const { fields: labels, append } = useFieldArray({
    name: "labels",
    control: form.control
  });
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a;
    const addedLabels = data.labels.filter((l) => !!l.tracking_number).map((l) => ({
      tracking_number: l.tracking_number,
      tracking_url: "#",
      label_url: "#"
    }));
    await createShipment(
      {
        items: (_a = fulfillment == null ? void 0 : fulfillment.items) == null ? void 0 : _a.map((i) => ({
          id: i.line_item_id,
          quantity: i.quantity
        })),
        labels: [...addedLabels, ...(fulfillment == null ? void 0 : fulfillment.labels) || []],
        no_notification: !data.send_notification
      },
      {
        onSuccess: () => {
          toast.success(t2("orders.shipment.toastCreated"));
          handleSuccess(`/orders/${order.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isMutating, children: t2("actions.save") })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex h-full w-full flex-col items-center divide-y overflow-y-auto", children: (0, import_jsx_runtime.jsx)("div", { className: "flex size-full flex-col items-center overflow-auto p-16", children: (0, import_jsx_runtime.jsx)("div", { className: "flex w-full max-w-[736px] flex-col justify-center px-2 pb-2", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col divide-y", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 flex-col", children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "mb-4", children: t2("orders.shipment.title") }),
            labels.map((label, index) => (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: `labels.${index}.tracking_number`,
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "mb-4", children: [
                    index === 0 && (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("orders.shipment.trackingNumber") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field, placeholder: "123-456-789" }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              },
              label.id
            )),
            (0, import_jsx_runtime.jsx)(
              Button,
              {
                type: "button",
                onClick: () => append({ tracking_number: "" }),
                className: "self-end",
                variant: "secondary",
                children: t2("orders.shipment.addTracking")
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "mt-8 pt-8 ", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "send_notification",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("orders.shipment.sendNotification") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Switch,
                      {
                        checked: !!value,
                        onCheckedChange: onChange,
                        ...field
                      }
                    ) }) })
                  ] }),
                  (0, import_jsx_runtime.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.shipment.sendNotificationHint") }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) })
        ] }) }) }) })
      ]
    }
  ) });
}
function OrderCreateShipment() {
  var _a;
  const { id, f_id } = useParams();
  const { order, isLoading, isError, error } = useOrder(id, {
    fields: "*fulfillments,*fulfillments.items,*fulfillments.labels"
  });
  if (isError) {
    throw error;
  }
  const ready = !isLoading && order;
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime2.jsx)(
    OrderCreateShipmentForm,
    {
      order,
      fulfillment: (_a = order.fulfillments) == null ? void 0 : _a.find((f) => f.id === f_id)
    }
  ) });
}
export {
  OrderCreateShipment as Component
};
//# sourceMappingURL=order-create-shipment-FQNPWRSW-7YCQE4PI.js.map
