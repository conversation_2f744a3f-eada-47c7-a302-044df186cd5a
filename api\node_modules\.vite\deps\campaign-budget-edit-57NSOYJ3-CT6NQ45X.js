import {
  getCurrencySymbol
} from "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  numberType,
  objectType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCampaign,
  useUpdateCampaign
} from "./chunk-S32V3COL.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  CurrencyInput2 as CurrencyInput,
  Heading,
  Input,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/campaign-budget-edit-57NSOYJ3.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditCampaignSchema = objectType({
  limit: numberType().min(0).optional().nullable()
});
var EditCampaignBudgetForm = ({
  campaign
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      limit: ((_a = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _a.limit) || void 0
    },
    resolver: t(EditCampaignSchema)
  });
  const { mutateAsync, isPending } = useUpdateCampaign(campaign.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        budget: {
          limit: data.limit ? data.limit : null
        }
      },
      {
        onSuccess: ({ campaign: campaign2 }) => {
          toast.success(
            t2("campaigns.edit.successToast", {
              name: campaign2.name
            })
          );
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "limit",
        render: ({ field: { onChange, value, ...field } }) => {
          var _a2, _b, _c, _d;
          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("campaigns.budget.fields.limit") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: ((_a2 = campaign.budget) == null ? void 0 : _a2.type) === "spend" ? (0, import_jsx_runtime.jsx)(
              CurrencyInput,
              {
                min: 0,
                onValueChange: (value2) => onChange(value2 ? parseInt(value2) : null),
                code: (_b = campaign.budget) == null ? void 0 : _b.currency_code,
                symbol: ((_c = campaign.budget) == null ? void 0 : _c.currency_code) ? getCurrencySymbol(
                  (_d = campaign.budget) == null ? void 0 : _d.currency_code
                ) : "",
                ...field,
                value: value || void 0
              }
            ) : (0, import_jsx_runtime.jsx)(
              Input,
              {
                min: 0,
                ...field,
                value,
                onChange: (e) => {
                  onChange(
                    e.target.value === "" ? null : parseInt(e.target.value)
                  );
                }
              },
              "usage"
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ) }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(
        Button,
        {
          isLoading: isPending,
          type: "submit",
          variant: "primary",
          size: "small",
          children: t2("actions.save")
        }
      )
    ] }) })
  ] }) });
};
var CampaignBudgetEdit = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { campaign, isLoading, isError, error } = useCampaign(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("campaigns.budget.edit.header") }) }) }),
    !isLoading && campaign && (0, import_jsx_runtime2.jsx)(EditCampaignBudgetForm, { campaign })
  ] });
};
export {
  CampaignBudgetEdit as Component
};
//# sourceMappingURL=campaign-budget-edit-57NSOYJ3-CT6NQ45X.js.map
