import {
  STEP_ERROR_STATES,
  STEP_INACTIVE_STATES,
  STEP_IN_PROGRESS_STATES,
  STEP_OK_STATES,
  STEP_SKIPPED_STATES,
  getTransactionState,
  getTransactionStateColor
} from "./chunk-MFHP2J6Q.js";
import {
  JsonViewSection,
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  motion,
  useAnimationControls,
  useDragControls,
  useMotionValue
} from "./chunk-7M4ICL3D.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  format
} from "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import {
  useWorkflowExecution,
  workflowExecutionsQueryKeys
} from "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLocation,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowPathMini,
  Badge,
  CodeBlock,
  Container,
  Copy,
  DropdownMenu,
  Heading,
  IconButton,
  MinusMini,
  PlusMini,
  Spinner,
  StatusBadge,
  Text,
  TriangleDownMini,
  clx,
  dist_exports2 as dist_exports
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/workflow-execution-detail-D26MDYFC.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var WorkflowExecutionDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { workflow_execution } = useWorkflowExecution(id, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!workflow_execution) {
    return null;
  }
  const cleanId = workflow_execution.id.replace("wf_exec_", "");
  return (0, import_jsx_runtime.jsx)("span", { children: cleanId });
};
var executionDetailQuery = (id) => ({
  queryKey: workflowExecutionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.workflowExecution.retrieve(id)
});
var workflowExecutionLoader = async ({
  params
}) => {
  const id = params.id;
  const query = executionDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var WorkflowExecutionGeneralSection = ({
  execution
}) => {
  var _a;
  const { t } = useTranslation();
  const cleanId = execution.id.replace("wf_exec_", "");
  const translatedState = getTransactionState(
    t,
    execution.state
  );
  const stateColor = getTransactionStateColor(
    execution.state
  );
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-0.5", children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: cleanId }),
        (0, import_jsx_runtime2.jsx)(Copy, { content: cleanId, className: "text-ui-fg-muted" })
      ] }),
      (0, import_jsx_runtime2.jsx)(StatusBadge, { color: stateColor, children: translatedState })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("workflowExecutions.workflowIdLabel") }),
      (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", className: "w-fit", children: execution.workflow_id })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("workflowExecutions.transactionIdLabel") }),
      (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", className: "w-fit", children: execution.transaction_id })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("workflowExecutions.progressLabel") }),
      (0, import_jsx_runtime2.jsx)(Progress, { steps: (_a = execution.execution) == null ? void 0 : _a.steps })
    ] })
  ] });
};
var ROOT_PREFIX = "_root";
var Progress = ({
  steps
}) => {
  const { t } = useTranslation();
  if (!steps) {
    return (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: t("workflowExecutions.stepsCompletedLabel", {
      completed: 0,
      total: 0
    }) });
  }
  const actionableSteps = Object.values(steps).filter(
    (step) => step.id !== ROOT_PREFIX
  );
  const completedSteps = actionableSteps.filter(
    (step) => step.invoke.state === "done"
    /* DONE */
  );
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex w-fit items-center gap-x-2", children: [
    (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center gap-x-[3px]", children: actionableSteps.map((step) => (0, import_jsx_runtime2.jsx)(
      "div",
      {
        className: clx(
          "bg-ui-bg-switch-off shadow-details-switch-background h-3 w-1.5 rounded-full",
          {
            "bg-ui-fg-muted": step.invoke.state === "done"
            /* DONE */
          }
        )
      },
      step.id
    )) }),
    (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: t("workflowExecutions.stepsCompletedLabel", {
      completed: completedSteps.length,
      count: actionableSteps.length
    }) })
  ] });
};
var WorkflowExecutionHistorySection = ({
  execution
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const map = Object.values(((_a = execution.execution) == null ? void 0 : _a.steps) || {});
  const steps = map.filter((step) => step.id !== "_root");
  const unreachableStepId = (_b = steps.find(
    (step) => step.invoke.status === "permanent_failure"
    /* PERMANENT_FAILURE */
  )) == null ? void 0 : _b.id;
  const unreachableSteps = unreachableStepId ? steps.filter(
    (step) => step.id !== unreachableStepId && step.id.includes(unreachableStepId)
  ).map((step) => step.id) : [];
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("workflowExecutions.history.sectionTitle") }) }),
    (0, import_jsx_runtime3.jsx)("div", { className: "flex flex-col gap-y-0.5 px-6 py-4", children: steps.map((step, index) => {
      var _a2, _b2;
      const stepId = step.id.split(".").pop();
      if (!stepId) {
        return null;
      }
      const context = (_a2 = execution.context) == null ? void 0 : _a2.data.invoke[stepId];
      const error = (_b2 = execution.context) == null ? void 0 : _b2.errors.find(
        (e) => e.action === stepId
      );
      return (0, import_jsx_runtime3.jsx)(
        Event,
        {
          step,
          stepInvokeContext: context,
          stepError: error,
          isLast: index === steps.length - 1,
          isUnreachable: unreachableSteps.includes(step.id)
        },
        step.id
      );
    }) })
  ] });
};
var Event = ({
  step,
  stepInvokeContext,
  stepError,
  isLast,
  isUnreachable
}) => {
  var _a, _b, _c;
  const [open, setOpen] = (0, import_react.useState)(false);
  const ref = (0, import_react.useRef)(null);
  const { hash } = useLocation();
  const { t } = useTranslation();
  const stepId = step.id.split(".").pop();
  (0, import_react.useEffect)(() => {
    if (hash === `#${stepId}`) {
      setOpen(true);
    }
  }, [hash, stepId]);
  const identifier = step.id.split(".").pop();
  return (0, import_jsx_runtime3.jsxs)(
    "div",
    {
      className: "grid grid-cols-[20px_1fr] items-start gap-x-2 px-2",
      id: stepId,
      children: [
        (0, import_jsx_runtime3.jsxs)("div", { className: "grid h-full grid-rows-[20px_1fr] items-center justify-center gap-y-0.5", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "flex size-5 items-center justify-center", children: (0, import_jsx_runtime3.jsx)("div", { className: "bg-ui-bg-base shadow-borders-base flex size-2.5 items-center justify-center rounded-full", children: (0, import_jsx_runtime3.jsx)(
            "div",
            {
              className: clx("size-1.5 rounded-full", {
                "bg-ui-tag-neutral-bg": STEP_SKIPPED_STATES.includes(
                  step.invoke.state
                ),
                "bg-ui-tag-green-icon": STEP_OK_STATES.includes(
                  step.invoke.state
                ),
                "bg-ui-tag-orange-icon": STEP_IN_PROGRESS_STATES.includes(
                  step.invoke.state
                ),
                "bg-ui-tag-red-icon": STEP_ERROR_STATES.includes(
                  step.invoke.state
                ),
                "bg-ui-tag-neutral-icon": STEP_INACTIVE_STATES.includes(
                  step.invoke.state
                )
              })
            }
          ) }) }),
          (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full flex-col items-center", children: (0, import_jsx_runtime3.jsx)(
            "div",
            {
              "aria-hidden": true,
              role: "presentation",
              className: clx({
                "bg-ui-border-base h-full min-h-[14px] w-px": !isLast
              })
            }
          ) })
        ] }),
        (0, import_jsx_runtime3.jsxs)(dist_exports.Root, { open, onOpenChange: setOpen, children: [
          (0, import_jsx_runtime3.jsx)(dist_exports.Trigger, { asChild: true, children: (0, import_jsx_runtime3.jsxs)("div", { className: "group flex cursor-pointer items-start justify-between outline-none", children: [
            (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: identifier }),
            (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime3.jsx)(
                StepState,
                {
                  state: step.invoke.state,
                  startedAt: step.startedAt,
                  isUnreachable
                }
              ),
              (0, import_jsx_runtime3.jsx)(IconButton, { size: "2xsmall", variant: "transparent", children: (0, import_jsx_runtime3.jsx)(TriangleDownMini, { className: "text-ui-fg-muted transition-transform group-data-[state=open]:rotate-180" }) })
            ] })
          ] }) }),
          (0, import_jsx_runtime3.jsx)(dist_exports.Content, { ref, children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col gap-y-2 pb-4 pt-2", children: [
            (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-y-2", children: [
              (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: t("workflowExecutions.history.definitionLabel") }),
              (0, import_jsx_runtime3.jsx)(
                CodeBlock,
                {
                  snippets: [
                    {
                      code: JSON.stringify(step.definition, null, 2),
                      label: t("workflowExecutions.history.definitionLabel"),
                      language: "json",
                      hideLineNumbers: true
                    }
                  ],
                  children: (0, import_jsx_runtime3.jsx)(CodeBlock.Body, {})
                }
              )
            ] }),
            stepInvokeContext && (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-y-2", children: [
              (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: t("workflowExecutions.history.outputLabel") }),
              (0, import_jsx_runtime3.jsx)(
                CodeBlock,
                {
                  snippets: [
                    {
                      code: JSON.stringify(
                        // TODO: Apply resolve value: packages/core/workflows-sdk/src/utils/composer/helpers/resolve-value.ts
                        ((_a = stepInvokeContext == null ? void 0 : stepInvokeContext.output) == null ? void 0 : _a.output) ?? {},
                        null,
                        2
                      ),
                      label: t("workflowExecutions.history.outputLabel"),
                      language: "json",
                      hideLineNumbers: true
                    }
                  ],
                  children: (0, import_jsx_runtime3.jsx)(CodeBlock.Body, {})
                }
              )
            ] }),
            !!((_b = stepInvokeContext == null ? void 0 : stepInvokeContext.output) == null ? void 0 : _b.compensateInput) && step.compensate.state === "reverted" && (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-y-2", children: [
              (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: t("workflowExecutions.history.compensateInputLabel") }),
              (0, import_jsx_runtime3.jsx)(
                CodeBlock,
                {
                  snippets: [
                    {
                      // TODO: Apply resolve value: packages/core/workflows-sdk/src/utils/composer/helpers/resolve-value.ts
                      code: JSON.stringify(
                        ((_c = stepInvokeContext == null ? void 0 : stepInvokeContext.output) == null ? void 0 : _c.compensateInput) ?? {},
                        null,
                        2
                      ),
                      label: t(
                        "workflowExecutions.history.compensateInputLabel"
                      ),
                      language: "json",
                      hideLineNumbers: true
                    }
                  ],
                  children: (0, import_jsx_runtime3.jsx)(CodeBlock.Body, {})
                }
              )
            ] }),
            stepError && (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-y-2", children: [
              (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: t("workflowExecutions.history.errorLabel") }),
              (0, import_jsx_runtime3.jsx)(
                CodeBlock,
                {
                  snippets: [
                    {
                      code: JSON.stringify(
                        {
                          error: stepError.error,
                          handlerType: stepError.handlerType
                        },
                        null,
                        2
                      ),
                      label: t("workflowExecutions.history.errorLabel"),
                      language: "json",
                      hideLineNumbers: true
                    }
                  ],
                  children: (0, import_jsx_runtime3.jsx)(CodeBlock.Body, {})
                }
              )
            ] })
          ] }) })
        ] })
      ]
    }
  );
};
var StepState = ({
  state,
  startedAt,
  isUnreachable
}) => {
  const { t } = useTranslation();
  const isFailed = state === "failed";
  const isRunning = state === "invoking";
  const isSkipped = state === "skipped";
  const isSkippedFailure = state === "skipped_failure";
  if (isUnreachable) {
    return null;
  }
  if (isRunning) {
    return (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-1", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: t("workflowExecutions.history.runningState") }),
      (0, import_jsx_runtime3.jsx)(Spinner, { className: "text-ui-fg-interactive animate-spin" })
    ] });
  }
  let stateText;
  if (isSkipped) {
    stateText = t("workflowExecutions.history.skippedState");
  } else if (isSkippedFailure) {
    stateText = t("workflowExecutions.history.skippedFailureState");
  } else if (isFailed) {
    stateText = t("workflowExecutions.history.failedState");
  }
  if (stateText !== null) {
    return (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: stateText });
  }
  if (startedAt) {
    return (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: format(startedAt, "dd MMM yyyy HH:mm:ss") });
  }
};
var WorkflowExecutionPayloadSection = ({
  execution
}) => {
  var _a, _b;
  let payload = (_b = (_a = execution.context) == null ? void 0 : _a.data) == null ? void 0 : _b.payload;
  if (!payload) {
    return null;
  }
  if (typeof payload !== "object") {
    payload = { input: payload };
  }
  return (0, import_jsx_runtime4.jsx)(JsonViewSection, { data: payload });
};
var WorkflowExecutionTimelineSection = ({
  execution
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "overflow-hidden px-0 pb-8 pt-0", children: [
    (0, import_jsx_runtime5.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t("general.timeline") }) }),
    (0, import_jsx_runtime5.jsx)("div", { className: "w-full overflow-hidden border-y", children: (0, import_jsx_runtime5.jsx)(Canvas, { execution }) })
  ] });
};
var createNodeClusters = (steps) => {
  const actionableSteps = Object.values(steps).filter(
    (step) => step.id !== "_root"
  );
  const clusters = {};
  actionableSteps.forEach((step) => {
    if (!clusters[step.depth]) {
      clusters[step.depth] = [];
    }
    clusters[step.depth].push(step);
  });
  return clusters;
};
var getNextCluster = (clusters, depth) => {
  const nextDepth = depth + 1;
  return clusters[nextDepth];
};
var defaultState = {
  x: -860,
  y: -1020,
  scale: 1
};
var MAX_ZOOM = 1.5;
var MIN_ZOOM = 0.5;
var ZOOM_STEP = 0.25;
var Canvas = ({
  execution
}) => {
  var _a;
  const [zoom, setZoom] = (0, import_react3.useState)(1);
  const [isDragging, setIsDragging] = (0, import_react3.useState)(false);
  const scale = useMotionValue(defaultState.scale);
  const x = useMotionValue(defaultState.x);
  const y = useMotionValue(defaultState.y);
  const controls = useAnimationControls();
  const dragControls = useDragControls();
  const dragConstraints = (0, import_react3.useRef)(null);
  const canZoomIn = zoom < MAX_ZOOM;
  const canZoomOut = zoom > MIN_ZOOM;
  (0, import_react3.useEffect)(() => {
    const unsubscribe = scale.on("change", (latest) => {
      setZoom(latest);
    });
    return () => {
      unsubscribe();
    };
  }, [scale]);
  const clusters = createNodeClusters(((_a = execution.execution) == null ? void 0 : _a.steps) || {});
  function scaleXandY(prevScale, newScale, x2, y2) {
    const scaleRatio = newScale / prevScale;
    return {
      x: x2 * scaleRatio,
      y: y2 * scaleRatio
    };
  }
  const changeZoom = (newScale) => {
    const { x: newX, y: newY } = scaleXandY(zoom, newScale, x.get(), y.get());
    setZoom(newScale);
    controls.set({ scale: newScale, x: newX, y: newY });
  };
  const zoomIn = () => {
    const curr = scale.get();
    if (curr < 1.5) {
      const newScale = curr + ZOOM_STEP;
      changeZoom(newScale);
    }
  };
  const zoomOut = () => {
    const curr = scale.get();
    if (curr > 0.5) {
      const newScale = curr - ZOOM_STEP;
      changeZoom(newScale);
    }
  };
  const resetCanvas = () => {
    controls.start(defaultState);
  };
  return (0, import_jsx_runtime5.jsx)("div", { className: "h-[400px] w-full", children: (0, import_jsx_runtime5.jsxs)("div", { ref: dragConstraints, className: "relative size-full", children: [
    (0, import_jsx_runtime5.jsx)("div", { className: "relative size-full overflow-hidden object-contain", children: (0, import_jsx_runtime5.jsx)("div", { children: (0, import_jsx_runtime5.jsx)(
      motion.div,
      {
        onMouseDown: () => setIsDragging(true),
        onMouseUp: () => setIsDragging(false),
        drag: true,
        dragConstraints,
        dragElastic: 0,
        dragMomentum: false,
        dragControls,
        initial: false,
        animate: controls,
        transition: { duration: 0.25 },
        style: {
          x,
          y,
          scale
        },
        className: clx(
          "bg-ui-bg-subtle relative size-[500rem] origin-top-left items-start justify-start overflow-hidden",
          "bg-[radial-gradient(var(--border-base)_1.5px,transparent_0)] bg-[length:20px_20px] bg-repeat",
          {
            "cursor-grab": !isDragging,
            "cursor-grabbing": isDragging
          }
        ),
        children: (0, import_jsx_runtime5.jsx)("main", { className: "size-full", children: (0, import_jsx_runtime5.jsx)("div", { className: "absolute left-[1100px] top-[1100px] flex select-none items-start", children: Object.entries(clusters).map(([depth, cluster]) => {
          const next = getNextCluster(clusters, Number(depth));
          return (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-start", children: [
            (0, import_jsx_runtime5.jsx)("div", { className: "flex flex-col justify-center gap-y-2", children: cluster.map((step) => (0, import_jsx_runtime5.jsx)(Node, { step }, step.id)) }),
            (0, import_jsx_runtime5.jsx)(Line, { next })
          ] }, depth);
        }) }) })
      }
    ) }) }),
    (0, import_jsx_runtime5.jsxs)("div", { className: "bg-ui-bg-base shadow-borders-base text-ui-fg-subtle absolute bottom-4 left-6 flex h-7 items-center overflow-hidden rounded-md", children: [
      (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center", children: [
        (0, import_jsx_runtime5.jsx)(
          "button",
          {
            onClick: zoomIn,
            type: "button",
            disabled: !canZoomIn,
            "aria-label": "Zoom in",
            className: "disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed border-r p-1 outline-none",
            children: (0, import_jsx_runtime5.jsx)(PlusMini, {})
          }
        ),
        (0, import_jsx_runtime5.jsx)("div", { children: (0, import_jsx_runtime5.jsxs)(DropdownMenu, { children: [
          (0, import_jsx_runtime5.jsx)(DropdownMenu.Trigger, { className: "disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed flex w-[50px] items-center justify-center border-r p-1 outline-none", children: (0, import_jsx_runtime5.jsxs)(
            Text,
            {
              as: "span",
              size: "xsmall",
              leading: "compact",
              className: "select-none tabular-nums",
              children: [
                Math.round(zoom * 100),
                "%"
              ]
            }
          ) }),
          (0, import_jsx_runtime5.jsx)(DropdownMenu.Content, { children: [50, 75, 100, 125, 150].map((value) => (0, import_jsx_runtime5.jsxs)(
            DropdownMenu.Item,
            {
              onClick: () => changeZoom(value / 100),
              children: [
                value,
                "%"
              ]
            },
            value
          )) })
        ] }) }),
        (0, import_jsx_runtime5.jsx)(
          "button",
          {
            onClick: zoomOut,
            type: "button",
            disabled: !canZoomOut,
            "aria-label": "Zoom out",
            className: "disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed border-r p-1 outline-none",
            children: (0, import_jsx_runtime5.jsx)(MinusMini, {})
          }
        )
      ] }),
      (0, import_jsx_runtime5.jsx)(
        "button",
        {
          onClick: resetCanvas,
          type: "button",
          "aria-label": "Reset canvas",
          className: "disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed p-1 outline-none",
          children: (0, import_jsx_runtime5.jsx)(ArrowPathMini, {})
        }
      )
    ] })
  ] }) });
};
var HorizontalArrow = () => {
  return (0, import_jsx_runtime5.jsx)(
    "svg",
    {
      width: "42",
      height: "12",
      viewBox: "0 0 42 12",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      children: (0, import_jsx_runtime5.jsx)(
        "path",
        {
          d: "M41.5303 6.53033C41.8232 6.23744 41.8232 5.76256 41.5303 5.46967L36.7574 0.696699C36.4645 0.403806 35.9896 0.403806 35.6967 0.696699C35.4038 0.989593 35.4038 1.46447 35.6967 1.75736L39.9393 6L35.6967 10.2426C35.4038 10.5355 35.4038 11.0104 35.6967 11.3033C35.9896 11.5962 36.4645 11.5962 36.7574 11.3033L41.5303 6.53033ZM0.999996 5.25C0.585785 5.25 0.249996 5.58579 0.249996 6C0.249996 6.41421 0.585785 6.75 0.999996 6.75V5.25ZM41 5.25L0.999996 5.25V6.75L41 6.75V5.25Z",
          fill: "var(--border-strong)"
        }
      )
    }
  );
};
var MiddleArrow = () => {
  return (0, import_jsx_runtime5.jsx)(
    "svg",
    {
      width: "22",
      height: "38",
      viewBox: "0 0 22 38",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      className: "-mt-[6px]",
      children: (0, import_jsx_runtime5.jsx)(
        "path",
        {
          d: "M0.999878 32H0.249878V32.75H0.999878V32ZM21.5284 32.5303C21.8213 32.2374 21.8213 31.7626 21.5284 31.4697L16.7554 26.6967C16.4625 26.4038 15.9876 26.4038 15.6947 26.6967C15.4019 26.9896 15.4019 27.4645 15.6947 27.7574L19.9374 32L15.6947 36.2426C15.4019 36.5355 15.4019 37.0104 15.6947 37.3033C15.9876 37.5962 16.4625 37.5962 16.7554 37.3033L21.5284 32.5303ZM0.249878 0L0.249878 32H1.74988L1.74988 0H0.249878ZM0.999878 32.75L20.998 32.75V31.25L0.999878 31.25V32.75Z",
          fill: "var(--border-strong)"
        }
      )
    }
  );
};
var EndArrow = () => {
  return (0, import_jsx_runtime5.jsx)(
    "svg",
    {
      width: "22",
      height: "38",
      viewBox: "0 0 22 38",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      className: "-mt-[6px]",
      children: (0, import_jsx_runtime5.jsx)(
        "path",
        {
          d: "M21.5284 32.5303C21.8213 32.2374 21.8213 31.7626 21.5284 31.4697L16.7554 26.6967C16.4625 26.4038 15.9876 26.4038 15.6947 26.6967C15.4019 26.9896 15.4019 27.4645 15.6947 27.7574L19.9374 32L15.6947 36.2426C15.4019 36.5355 15.4019 37.0104 15.6947 37.3033C15.9876 37.5962 16.4625 37.5962 16.7554 37.3033L21.5284 32.5303ZM0.249878 0L0.249878 28H1.74988L1.74988 0H0.249878ZM4.99988 32.75L20.998 32.75V31.25L4.99988 31.25V32.75ZM0.249878 28C0.249878 30.6234 2.37653 32.75 4.99988 32.75V31.25C3.20495 31.25 1.74988 29.7949 1.74988 28H0.249878Z",
          fill: "var(--border-strong)"
        }
      )
    }
  );
};
var Arrow = ({ depth }) => {
  if (depth === 1) {
    return (0, import_jsx_runtime5.jsx)(HorizontalArrow, {});
  }
  if (depth === 2) {
    return (0, import_jsx_runtime5.jsxs)("div", { className: "flex flex-col items-end", children: [
      (0, import_jsx_runtime5.jsx)(HorizontalArrow, {}),
      (0, import_jsx_runtime5.jsx)(EndArrow, {})
    ] });
  }
  const inbetween = Array.from({ length: depth - 2 }).map((_, index) => (0, import_jsx_runtime5.jsx)(MiddleArrow, {}, index));
  return (0, import_jsx_runtime5.jsxs)("div", { className: "flex flex-col items-end", children: [
    (0, import_jsx_runtime5.jsx)(HorizontalArrow, {}),
    inbetween,
    (0, import_jsx_runtime5.jsx)(EndArrow, {})
  ] });
};
var Line = ({ next }) => {
  if (!next) {
    return null;
  }
  return (0, import_jsx_runtime5.jsx)("div", { className: "-ml-[5px] -mr-[7px] w-[60px] pr-[7px]", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex min-h-[24px] w-full items-start", children: [
    (0, import_jsx_runtime5.jsx)("div", { className: "flex h-6 w-2.5 items-center justify-center", children: (0, import_jsx_runtime5.jsx)("div", { className: "bg-ui-button-neutral shadow-borders-base size-2.5 shrink-0 rounded-full" }) }),
    (0, import_jsx_runtime5.jsx)("div", { className: "pt-1.5", children: (0, import_jsx_runtime5.jsx)(Arrow, { depth: next.length }) })
  ] }) });
};
var Node = ({ step }) => {
  if (step.id === "_root") {
    return null;
  }
  const stepId = step.id.split(".").pop();
  const handleScrollTo = () => {
    if (!stepId) {
      return;
    }
    const historyItem = document.getElementById(stepId);
    if (!historyItem) {
      return;
    }
    setTimeout(() => {
      historyItem.scrollIntoView({
        behavior: "smooth",
        block: "end"
      });
    }, 100);
  };
  return (0, import_jsx_runtime5.jsx)(
    Link,
    {
      to: `#${stepId}`,
      onClick: handleScrollTo,
      className: "focus-visible:shadow-borders-focus transition-fg rounded-md outline-none",
      children: (0, import_jsx_runtime5.jsxs)(
        "div",
        {
          className: "bg-ui-bg-base shadow-borders-base flex min-w-[120px] items-center gap-x-0.5 rounded-md p-0.5",
          "data-step-id": step.id,
          children: [
            (0, import_jsx_runtime5.jsx)("div", { className: "flex size-5 items-center justify-center", children: (0, import_jsx_runtime5.jsx)(
              "div",
              {
                className: clx(
                  "size-2 rounded-sm shadow-[inset_0_0_0_1px_rgba(0,0,0,0.12)]",
                  {
                    "bg-ui-tag-neutral-bg": STEP_SKIPPED_STATES.includes(
                      step.invoke.state
                    ),
                    "bg-ui-tag-green-icon": STEP_OK_STATES.includes(
                      step.invoke.state
                    ),
                    "bg-ui-tag-orange-icon": STEP_IN_PROGRESS_STATES.includes(
                      step.invoke.state
                    ),
                    "bg-ui-tag-red-icon": STEP_ERROR_STATES.includes(
                      step.invoke.state
                    ),
                    "bg-ui-tag-neutral-icon": STEP_INACTIVE_STATES.includes(
                      step.invoke.state
                    )
                  }
                )
              }
            ) }),
            (0, import_jsx_runtime5.jsx)(
              Text,
              {
                size: "xsmall",
                leading: "compact",
                weight: "plus",
                className: "select-none",
                children: stepId
              }
            )
          ]
        }
      )
    }
  );
};
var ExecutionDetail = () => {
  const { id } = useParams();
  const { workflow_execution, isLoading, isError, error } = useWorkflowExecution(id);
  const { getWidgets } = useExtension();
  if (isLoading || !workflow_execution) {
    return (0, import_jsx_runtime6.jsx)(SingleColumnPageSkeleton, { sections: 4, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime6.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("workflow.details.after"),
        before: getWidgets("workflow.details.before")
      },
      data: workflow_execution,
      showJSON: true,
      children: [
        (0, import_jsx_runtime6.jsx)(WorkflowExecutionGeneralSection, { execution: workflow_execution }),
        (0, import_jsx_runtime6.jsx)(WorkflowExecutionTimelineSection, { execution: workflow_execution }),
        (0, import_jsx_runtime6.jsx)(WorkflowExecutionPayloadSection, { execution: workflow_execution }),
        (0, import_jsx_runtime6.jsx)(WorkflowExecutionHistorySection, { execution: workflow_execution })
      ]
    }
  );
};
export {
  WorkflowExecutionDetailBreadcrumb as Breadcrumb,
  ExecutionDetail as Component,
  workflowExecutionLoader as loader
};
//# sourceMappingURL=workflow-execution-detail-D26MDYFC-AGSCIMLE.js.map
