{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-R2O6QX4D.mjs"], "sourcesContent": ["// src/lib/shipping-options.ts\nfunction isReturnOption(shippingOption) {\n  return !!shippingOption.rules?.find(\n    (r) => r.attribute === \"is_return\" && r.value === \"true\" && r.operator === \"eq\"\n  );\n}\nfunction isOptionEnabledInStore(shippingOption) {\n  return !!shippingOption.rules?.find(\n    (r) => r.attribute === \"enabled_in_store\" && r.value === \"true\" && r.operator === \"eq\"\n  );\n}\n\nexport {\n  isReturnOption,\n  isOptionEnabledInStore\n};\n"], "mappings": ";AACA,SAAS,eAAe,gBAAgB;AADxC;AAEE,SAAO,CAAC,GAAC,oBAAe,UAAf,mBAAsB;AAAA,IAC7B,CAAC,MAAM,EAAE,cAAc,eAAe,EAAE,UAAU,UAAU,EAAE,aAAa;AAAA;AAE/E;AACA,SAAS,uBAAuB,gBAAgB;AANhD;AAOE,SAAO,CAAC,GAAC,oBAAe,UAAf,mBAAsB;AAAA,IAC7B,CAAC,MAAM,EAAE,cAAc,sBAAsB,EAAE,UAAU,UAAU,EAAE,aAAa;AAAA;AAEtF;", "names": []}