{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-type-list-EKTGZP6P.mjs"], "sourcesContent": ["import {\n  useDeleteProductTypeAction\n} from \"./chunk-S22NYSST.mjs\";\nimport {\n  useProductTypeTableColumns\n} from \"./chunk-SYQ6IA6C.mjs\";\nimport {\n  useProductTypeTableQuery\n} from \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-3OHUAQUF.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTypeTableFilters\n} from \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  useProductTypes\n} from \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/product-types/product-type-list/components/product-type-list-table/product-type-list-table.tsx\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/product-types/product-type-list/components/product-type-list-table/product-table-row-actions.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductTypeRowActions = ({\n  productType\n}) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeleteProductTypeAction(\n    productType.id,\n    productType.value\n  );\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              to: `/settings/product-types/${productType.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/product-types/product-type-list/components/product-type-list-table/product-type-list-table.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ProductTypeListTable = () => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useProductTypeTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { product_types, count, isLoading, isError, error } = useProductTypes(\n    searchParams,\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useProductTypeTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    columns,\n    data: product_types,\n    count,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx2(Heading, { children: t(\"productTypes.domain\") }),\n        /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"productTypes.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx2(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        filters,\n        isLoading,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        orderBy: [\n          { key: \"value\", label: t(\"fields.value\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        navigateTo: ({ original }) => original.id,\n        queryObject: raw,\n        pagination: true,\n        search: true\n      }\n    )\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTypeTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx2(ProductTypeRowActions, { productType: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/product-types/product-type-list/product-type-list.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar ProductTypeList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_type.list.after\"),\n        before: getWidgets(\"product_type.list.before\")\n      },\n      children: /* @__PURE__ */ jsx3(ProductTypeListTable, {})\n    }\n  );\n};\nexport {\n  ProductTypeList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,mBAAwB;AAOxB,yBAAoB;AAqCpB,IAAAA,sBAAkC;AAyElC,IAAAA,sBAA4B;AA7G5B,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe;AAAA,IACnB,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,IAAI,2BAA2B,YAAY,EAAE;AAAA,YAC/C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,yBAAyB;AAAA,IACrD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,eAAe,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC1D;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,2BAA2B;AAC3C,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,YACpD,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,MACpH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC5K,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,YAAY,CAAC,EAAE,SAAS,MAAM,SAAS;AAAA,QACvC,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,2BAA2B;AACxC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA,KAAK,uBAAuB,EAAE,aAAa,IAAI,SAAS,CAAC;AAAA,QAClF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,yBAAyB;AAAA,QAC3C,QAAQ,WAAW,0BAA0B;AAAA,MAC/C;AAAA,MACA,cAA0B,oBAAAA,KAAK,sBAAsB,CAAC,CAAC;AAAA,IACzD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}