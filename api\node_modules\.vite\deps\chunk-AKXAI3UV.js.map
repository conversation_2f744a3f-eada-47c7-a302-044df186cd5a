{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-QZ6PT4QV.mjs"], "sourcesContent": ["import {\n  pricePreferencesQueryKeys\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/regions.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar REGIONS_QUERY_KEY = \"regions\";\nvar regionsQueryKeys = queryKeysFactory(REGIONS_QUERY_KEY);\nvar useRegion = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: regionsQueryKeys.detail(id, query),\n    queryFn: async () => sdk.admin.region.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useRegions = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.region.list(query),\n    queryKey: regionsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateRegion = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.region.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.list()\n      });\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateRegion = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.region.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.details() });\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.list()\n      });\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteRegion = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.region.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  regionsQueryKeys,\n  useRegion,\n  useRegions,\n  useCreateRegion,\n  useUpdateRegion,\n  useDeleteRegion\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,oBAAoB;AACxB,IAAI,mBAAmB,iBAAiB,iBAAiB;AACzD,IAAI,YAAY,CAAC,IAAI,OAAO,YAAY;AACtC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,iBAAiB,OAAO,IAAI,KAAK;AAAA,IAC3C,SAAS,YAAY,IAAI,MAAM,OAAO,SAAS,IAAI,KAAK;AAAA,IACxD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,aAAa,CAAC,OAAO,YAAY;AACnC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK;AAAA,IAC1C,UAAU,iBAAiB,KAAK,KAAK;AAAA,IACrC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,YAAY;AACjC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,IACxD,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,KAAK;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,QAAQ;AAAA,MAC9C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,OAAO,IAAI,OAAO;AAAA,IAC5D,WAAW,CAAC,MAAM,WAAW,YAAY;AAvD7C;AAwDM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,QAAQ,EAAE,CAAC;AACtE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,KAAK;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,QAAQ;AAAA,MAC9C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE;AAAA,IAC5C,WAAW,CAAC,MAAM,WAAW,YAAY;AAxE7C;AAyEM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}