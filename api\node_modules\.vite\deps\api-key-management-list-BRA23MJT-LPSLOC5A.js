import {
  getApi<PERSON>eyStatusProps,
  getApiKeyTypeFromPathname,
  getApiKeyTypeProps,
  prettifyRedactedToken
} from "./chunk-R3MT5J4J.js";
import {
  DateCell
} from "./chunk-OW6OIDUA.js";
import {
  TextCell
} from "./chunk-7HUCBNCQ.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useApiKeys,
  useDeleteApiKey,
  useRevokeApiKey
} from "./chunk-RX237AWS.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLocation
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Button,
  Container,
  Heading,
  PencilSquare,
  SquareTwoStack,
  Text,
  Trash,
  XCircle,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/api-key-management-list-BRA23MJT.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ApiKeyRowActions = ({
  apiKey
}) => {
  const { mutateAsync: revokeAsync } = useRevokeApiKey(apiKey.id);
  const { mutateAsync: deleteAsync } = useDeleteApiKey(apiKey.id);
  const { t } = useTranslation();
  const prompt = usePrompt();
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("apiKeyManagement.delete.warning", {
        title: apiKey.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await deleteAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("apiKeyManagement.delete.successToast", {
            title: apiKey.title
          })
        );
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const handleRevoke = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("apiKeyManagement.revoke.warning", {
        title: apiKey.title
      }),
      confirmText: t("apiKeyManagement.actions.revoke"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await revokeAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("apiKeyManagement.revoke.successToast", {
            title: apiKey.title
          })
        );
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const handleCopyToken = () => {
    navigator.clipboard.writeText(apiKey.token);
    toast.success(t("apiKeyManagement.actions.copySuccessToast"));
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `${apiKey.id}/edit`
            },
            ...apiKey.type !== "secret" ? [
              {
                label: t("apiKeyManagement.actions.copy"),
                onClick: handleCopyToken,
                icon: (0, import_jsx_runtime.jsx)(SquareTwoStack, {})
              }
            ] : []
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(XCircle, {}),
              label: t("apiKeyManagement.actions.revoke"),
              onClick: handleRevoke,
              disabled: !!apiKey.revoked_at
            },
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete,
              disabled: !apiKey.revoked_at
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useApiKeyManagementTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("title", {
        header: t("fields.title"),
        cell: ({ getValue }) => (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: getValue() }) })
      }),
      columnHelper.accessor("redacted", {
        header: "Token",
        cell: ({ getValue }) => {
          const token = getValue();
          return (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: prettifyRedactedToken(token) });
        }
      }),
      columnHelper.accessor("type", {
        header: t("fields.type"),
        cell: ({ getValue }) => {
          const { label } = getApiKeyTypeProps(getValue(), t);
          return (0, import_jsx_runtime2.jsx)(TextCell, { text: label });
        }
      }),
      columnHelper.accessor("revoked_at", {
        header: t("fields.status"),
        cell: ({ getValue }) => {
          const { color, label } = getApiKeyStatusProps(getValue(), t);
          return (0, import_jsx_runtime2.jsx)(StatusCell, { color, children: label });
        }
      }),
      columnHelper.accessor("last_used_at", {
        header: t("apiKeyManagement.table.lastUsedAtHeader"),
        cell: ({ getValue }) => {
          const date = getValue();
          return (0, import_jsx_runtime2.jsx)(DateCell, { date });
        }
      }),
      columnHelper.accessor("created_at", {
        header: t("fields.created"),
        cell: ({ getValue }) => {
          const date = getValue();
          return (0, import_jsx_runtime2.jsx)(DateCell, { date });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(ApiKeyRowActions, { apiKey: row.original });
        }
      })
    ],
    [t]
  );
};
var useApiKeyManagementTableFilters = () => {
  const { t } = useTranslation();
  let filters = [];
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at", type: "date" },
    { label: t("fields.updatedAt"), key: "updated_at", type: "date" },
    { label: t("fields.revokedAt"), key: "revoked_at", type: "date" }
  ];
  filters = [...filters, ...dateFilters];
  return filters;
};
var useApiKeyManagementTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "created_at", "updated_at", "revoked_at", "order"],
    prefix
  );
  const { offset, created_at, updated_at, revoked_at, q, order } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    revoked_at: revoked_at ? JSON.parse(revoked_at) : void 0,
    order,
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};
var PAGE_SIZE = 20;
var ApiKeyManagementListTable = ({
  keyType
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useApiKeyManagementTableQuery({
    pageSize: PAGE_SIZE
  });
  const query = {
    ...searchParams,
    type: keyType,
    fields: "id,title,redacted,token,type,created_at,updated_at,revoked_at,last_used_at,created_by,revoked_by"
  };
  const { api_keys, count, isLoading, isError, error } = useApiKeys(query, {
    placeholderData: keepPreviousData
  });
  const filters = useApiKeyManagementTableFilters();
  const columns = useApiKeyManagementTableColumns();
  const { table } = useDataTable({
    data: api_keys || [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsxs)("div", { children: [
        (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: keyType === "publishable" ? t(`apiKeyManagement.domain.publishable`) : t("apiKeyManagement.domain.secret") }),
        (0, import_jsx_runtime3.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: keyType === "publishable" ? t(`apiKeyManagement.subtitle.publishable`) : t("apiKeyManagement.subtitle.secret") })
      ] }),
      (0, import_jsx_runtime3.jsx)(Link, { to: "create", children: (0, import_jsx_runtime3.jsx)(Button, { variant: "secondary", size: "small", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        filters,
        columns,
        count,
        pageSize: PAGE_SIZE,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") },
          { key: "revoked_at", label: t("fields.revokedAt") }
        ],
        navigateTo: (row) => row.id,
        pagination: true,
        search: true,
        queryObject: raw,
        isLoading
      }
    )
  ] });
};
var ApiKeyManagementList = () => {
  const { pathname } = useLocation();
  const { getWidgets } = useExtension();
  const keyType = getApiKeyTypeFromPathname(pathname);
  return (0, import_jsx_runtime4.jsx)(
    SingleColumnPage,
    {
      hasOutlet: true,
      widgets: {
        before: getWidgets("api_key.list.before"),
        after: getWidgets("api_key.list.after")
      },
      children: (0, import_jsx_runtime4.jsx)(ApiKeyManagementListTable, { keyType })
    }
  );
};
export {
  ApiKeyManagementList as Component
};
//# sourceMappingURL=api-key-management-list-BRA23MJT-LPSLOC5A.js.map
