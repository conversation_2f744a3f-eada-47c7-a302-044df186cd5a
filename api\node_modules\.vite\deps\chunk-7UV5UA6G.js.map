{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-2ZKVRTBW.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/users.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar USERS_QUERY_KEY = \"users\";\nvar usersQueryKeys = {\n  ...queryKeysFactory(USERS_QUERY_KEY),\n  me: () => [USERS_QUERY_KEY, \"me\"]\n};\nvar useMe = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.user.me(query),\n    queryKey: usersQueryKeys.me(),\n    ...options\n  });\n  return {\n    ...data,\n    ...rest\n  };\n};\nvar useUser = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.user.retrieve(id, query),\n    queryKey: usersQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUsers = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.user.list(query),\n    queryKey: usersQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateUser = (id, query, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.user.update(id, payload, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.me() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteUser = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.user.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: usersQueryKeys.me() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useMe,\n  useUser,\n  useUsers,\n  useUpdateUser,\n  useDeleteUser\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAAA,EACnB,GAAG,iBAAiB,eAAe;AAAA,EACnC,IAAI,MAAM,CAAC,iBAAiB,IAAI;AAClC;AACA,IAAI,QAAQ,CAAC,OAAO,YAAY;AAC9B,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG,KAAK;AAAA,IACtC,UAAU,eAAe,GAAG;AAAA,IAC5B,GAAG;AAAA,EACL,CAAC;AACD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,IAAI,UAAU,CAAC,IAAI,OAAO,YAAY;AACpC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI,KAAK;AAAA,IAChD,UAAU,eAAe,OAAO,EAAE;AAAA,IAClC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,WAAW,CAAC,OAAO,YAAY;AACjC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,KAAK,KAAK,KAAK;AAAA,IACxC,UAAU,eAAe,KAAK,KAAK;AAAA,IACnC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,gBAAgB,CAAC,IAAI,OAAO,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,KAAK,OAAO,IAAI,SAAS,KAAK;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AAlD7C;AAmDM,kBAAY,kBAAkB,EAAE,UAAU,eAAe,OAAO,EAAE,EAAE,CAAC;AACrE,kBAAY,kBAAkB,EAAE,UAAU,eAAe,MAAM,EAAE,CAAC;AAClE,kBAAY,kBAAkB,EAAE,UAAU,eAAe,GAAG,EAAE,CAAC;AAC/D,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,gBAAgB,CAAC,IAAI,YAAY;AACnC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,KAAK,OAAO,EAAE;AAAA,IAC1C,WAAW,CAAC,MAAM,WAAW,YAAY;AA9D7C;AA+DM,kBAAY,kBAAkB,EAAE,UAAU,eAAe,OAAO,EAAE,EAAE,CAAC;AACrE,kBAAY,kBAAkB,EAAE,UAAU,eAAe,MAAM,EAAE,CAAC;AAClE,kBAAY,kBAAkB,EAAE,UAAU,eAAe,GAAG,EAAE,CAAC;AAC/D,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}