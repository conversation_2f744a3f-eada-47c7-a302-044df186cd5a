import {
  useDeleteProductType
} from "./chunk-HREJMEGI.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";

// node_modules/@medusajs/dashboard/dist/chunk-S22NYSST.mjs
var useDeleteProductTypeAction = (id, value) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteProductType(id);
  const handleDelete = async () => {
    const result = await prompt({
      title: t("general.areYouSure"),
      description: t("productTypes.delete.confirmation", { value }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!result) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        navigate("/settings/product-types", { replace: true });
        toast.success(t("productTypes.delete.successToast", { value }));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return handleDelete;
};

export {
  useDeleteProductTypeAction
};
//# sourceMappingURL=chunk-AMEHXNKS.js.map
