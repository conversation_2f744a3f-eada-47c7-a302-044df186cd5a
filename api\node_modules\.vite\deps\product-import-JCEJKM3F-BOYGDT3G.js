import {
  FileUpload
} from "./chunk-ZE42WU6O.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  FilePreview
} from "./chunk-W5LXSWLX.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useConfirmImportProducts,
  useImportProducts
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Hint,
  Text,
  Trash,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-import-JCEJKM3F.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var SUPPORTED_FORMATS = ["text/csv"];
var SUPPORTED_FORMATS_FILE_EXTENSIONS = [".csv"];
var UploadImport = ({
  onUploaded
}) => {
  const { t } = useTranslation();
  const [error, setError] = (0, import_react2.useState)();
  const hasInvalidFiles = (fileList) => {
    const invalidFile = fileList.find(
      (f) => !SUPPORTED_FORMATS.includes(f.file.type)
    );
    if (invalidFile) {
      setError(
        t("products.media.invalidFileType", {
          name: invalidFile.file.name,
          types: SUPPORTED_FORMATS_FILE_EXTENSIONS.join(", ")
        })
      );
      return true;
    }
    return false;
  };
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
    (0, import_jsx_runtime.jsx)(
      FileUpload,
      {
        label: t("products.import.uploadLabel"),
        hint: t("products.import.uploadHint"),
        multiple: false,
        hasError: !!error,
        formats: SUPPORTED_FORMATS,
        onUploaded: (files) => {
          setError(void 0);
          if (hasInvalidFiles(files)) {
            return;
          }
          onUploaded(files[0].file);
        }
      }
    ),
    error && (0, import_jsx_runtime.jsx)("div", { children: (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: error }) })
  ] });
};
var ImportSummary = ({
  summary
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component transition-fg flex flex-row rounded-md px-3 py-2", children: [
    (0, import_jsx_runtime2.jsx)(
      Stat,
      {
        title: summary.toCreate.toLocaleString(),
        description: t("products.import.upload.productsToCreate")
      }
    ),
    (0, import_jsx_runtime2.jsx)(Divider, { orientation: "vertical", className: "h-10 px-3" }),
    (0, import_jsx_runtime2.jsx)(
      Stat,
      {
        title: summary.toUpdate.toLocaleString(),
        description: t("products.import.upload.productsToUpdate")
      }
    )
  ] });
};
var Stat = ({
  title,
  description
}) => {
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 flex-col justify-center", children: [
    (0, import_jsx_runtime2.jsx)(Text, { size: "xlarge", className: "font-sans font-medium", children: title }),
    (0, import_jsx_runtime2.jsx)(
      Text,
      {
        leading: "compact",
        size: "xsmall",
        weight: "plus",
        className: "text-ui-fg-subtle",
        children: description
      }
    )
  ] });
};
var ProductImportCSV = `data:text/csv;charset=utf-8,Product Id;Product Handle;Product Title;Product Subtitle;Product Description;Product Status;Product Thumbnail;Product Weight;Product Length;Product Width;Product Height;Product HS Code;Product Origin Country;Product MID Code;Product Material;Product Collection Title;Product Collection Handle;Product Type;Product Tags;Product Discountable;Product External Id;Product Profile Name;Product Profile Type;Variant Id;Variant Title;Variant SKU;Variant Barcode;Variant Inventory Quantity;Variant Allow Backorder;Variant Manage Inventory;Variant Weight;Variant Length;Variant Width;Variant Height;Variant HS Code;Variant Origin Country;Variant MID Code;Variant Material;Price EUR;Price USD;Option 1 Name;Option 1 Value;Image 1 Url;Image 2 Url
;coffee-mug-v2;Medusa Coffee Mug;;Every programmer's best friend.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/coffee-mug.png;400;;;;;;;;;;;;true;;;;;One Size;;;100;false;true;;;;;;;;;1000;1200;Size;One Size;https://medusa-public-images.s3.eu-west-1.amazonaws.com/coffee-mug.png;
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;S;;;100;false;true;;;;;;;;;2950;3350;Size;S;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;M;;;100;false;true;;;;;;;;;2950;3350;Size;M;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;L;;;100;false;true;;;;;;;;;2950;3350;Size;L;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;XL;;;100;false;true;;;;;;;;;2950;3350;Size;XL;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
`;
var getProductImportCsvTemplate = () => {
  return encodeURI(ProductImportCSV);
};
var ProductImport = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Heading, { children: t("products.import.header") }) }),
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Description, { className: "sr-only", children: t("products.import.description") })
    ] }),
    (0, import_jsx_runtime3.jsx)(ProductImportContent, {})
  ] });
};
var ProductImportContent = () => {
  const { t } = useTranslation();
  const [filename, setFilename] = (0, import_react.useState)();
  const { mutateAsync: importProducts, isPending, data } = useImportProducts();
  const { mutateAsync: confirm } = useConfirmImportProducts();
  const { handleSuccess } = useRouteModal();
  const productImportTemplateContent = (0, import_react.useMemo)(() => {
    return getProductImportCsvTemplate();
  }, []);
  const handleUploaded = async (file) => {
    setFilename(file.name);
    await importProducts(
      { file },
      {
        onError: (err) => {
          toast.error(err.message);
          setFilename(void 0);
        }
      }
    );
  };
  const handleConfirm = async () => {
    if (!(data == null ? void 0 : data.transaction_id)) {
      return;
    }
    await confirm(data.transaction_id, {
      onSuccess: () => {
        toast.info(t("products.import.success.title"), {
          description: t("products.import.success.description")
        });
        handleSuccess();
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const uploadedFileActions = [
    {
      actions: [
        {
          label: t("actions.delete"),
          icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
          onClick: () => setFilename(void 0)
        }
      ]
    }
  ];
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [
    (0, import_jsx_runtime3.jsxs)(RouteDrawer.Body, { children: [
      (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("products.import.upload.title") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("products.import.upload.description") }),
      (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: filename ? (0, import_jsx_runtime3.jsx)(
        FilePreview,
        {
          filename,
          loading: isPending,
          activity: t("products.import.upload.preprocessing"),
          actions: uploadedFileActions
        }
      ) : (0, import_jsx_runtime3.jsx)(UploadImport, { onUploaded: handleUploaded }) }),
      (data == null ? void 0 : data.summary) && !!filename && (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: (0, import_jsx_runtime3.jsx)(ImportSummary, { summary: data == null ? void 0 : data.summary }) }),
      (0, import_jsx_runtime3.jsx)(Heading, { className: "mt-6", level: "h2", children: t("products.import.template.title") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("products.import.template.description") }),
      (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: (0, import_jsx_runtime3.jsx)(
        FilePreview,
        {
          filename: "product-import-template.csv",
          url: productImportTemplateContent
        }
      ) })
    ] }),
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime3.jsx)(
        Button,
        {
          onClick: handleConfirm,
          size: "small",
          disabled: !(data == null ? void 0 : data.transaction_id) || !filename,
          children: t("actions.import")
        }
      )
    ] }) })
  ] });
};
export {
  ProductImport as Component
};
//# sourceMappingURL=product-import-JCEJKM3F-BOYGDT3G.js.map
