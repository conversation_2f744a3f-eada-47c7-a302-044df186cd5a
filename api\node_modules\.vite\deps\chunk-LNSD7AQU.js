import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Skeleton
} from "./chunk-TP2BI5T3.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  ArrowDownMini,
  ArrowUpMini,
  Button,
  DropdownMenu,
  EllipsisVertical,
  Heading,
  IconButton,
  InlineTip,
  Trash,
  clx,
  toast
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-AL4WDQTN.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MetadataFieldSchema = z.object({
  key: z.string(),
  disabled: z.boolean().optional(),
  value: z.any()
});
var MetadataSchema = z.object({
  metadata: z.array(MetadataFieldSchema)
});
var MetadataForm = (props) => {
  const { t: t2 } = useTranslation();
  const { isPending, ...innerProps } = props;
  return (0, import_jsx_runtime.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: t2("metadata.edit.header") }) }),
      (0, import_jsx_runtime.jsx)(RouteDrawer.Description, { className: "sr-only", children: t2("metadata.edit.description") })
    ] }),
    isPending ? (0, import_jsx_runtime.jsx)(PlaceholderInner, {}) : (0, import_jsx_runtime.jsx)(InnerForm, { ...innerProps })
  ] });
};
var METADATA_KEY_LABEL_ID = "metadata-form-key-label";
var METADATA_VALUE_LABEL_ID = "metadata-form-value-label";
var InnerForm = ({
  metadata,
  hook,
  isMutating
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const hasUneditableRows = getHasUneditableRows(metadata);
  const form = useForm({
    defaultValues: {
      metadata: getDefaultValues(metadata)
    },
    resolver: t(MetadataSchema)
  });
  const handleSubmit = form.handleSubmit(async (data) => {
    const parsedData = parseValues(data, metadata);
    await hook(
      {
        metadata: parsedData
      },
      {
        onSuccess: () => {
          toast.success(t2("metadata.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const { fields, insert, remove } = useFieldArray({
    control: form.control,
    name: "metadata"
  });
  function deleteRow(index) {
    remove(index);
    if (fields.length === 1) {
      insert(0, {
        key: "",
        value: "",
        disabled: false
      });
    }
  }
  function insertRow(index, position) {
    insert(index + (position === "above" ? 0 : 1), {
      key: "",
      value: "",
      disabled: false
    });
  }
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-y-auto", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-base shadow-elevation-card-rest grid grid-cols-1 divide-y rounded-lg", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-subtle grid grid-cols-2 divide-x rounded-t-lg", children: [
              (0, import_jsx_runtime.jsx)("div", { className: "txt-compact-small-plus text-ui-fg-subtle px-2 py-1.5", children: (0, import_jsx_runtime.jsx)("label", { id: METADATA_KEY_LABEL_ID, children: t2("metadata.edit.labels.key") }) }),
              (0, import_jsx_runtime.jsx)("div", { className: "txt-compact-small-plus text-ui-fg-subtle px-2 py-1.5", children: (0, import_jsx_runtime.jsx)("label", { id: METADATA_VALUE_LABEL_ID, children: t2("metadata.edit.labels.value") }) })
            ] }),
            fields.map((field, index) => {
              const isDisabled = field.disabled || false;
              let placeholder = "-";
              if (typeof field.value === "object") {
                placeholder = "{ ... }";
              }
              if (Array.isArray(field.value)) {
                placeholder = "[ ... ]";
              }
              return (0, import_jsx_runtime.jsx)(
                ConditionalTooltip,
                {
                  showTooltip: isDisabled,
                  content: t2("metadata.edit.complexRow.tooltip"),
                  children: (0, import_jsx_runtime.jsxs)("div", { className: "group/table relative", children: [
                    (0, import_jsx_runtime.jsxs)(
                      "div",
                      {
                        className: clx("grid grid-cols-2 divide-x", {
                          "overflow-hidden rounded-b-lg": index === fields.length - 1
                        }),
                        children: [
                          (0, import_jsx_runtime.jsx)(
                            Form.Field,
                            {
                              control: form.control,
                              name: `metadata.${index}.key`,
                              render: ({ field: field2 }) => {
                                return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                                  GridInput,
                                  {
                                    "aria-labelledby": METADATA_KEY_LABEL_ID,
                                    ...field2,
                                    disabled: isDisabled,
                                    placeholder: "Key"
                                  }
                                ) }) });
                              }
                            }
                          ),
                          (0, import_jsx_runtime.jsx)(
                            Form.Field,
                            {
                              control: form.control,
                              name: `metadata.${index}.value`,
                              render: ({ field: { value, ...field2 } }) => {
                                return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                                  GridInput,
                                  {
                                    "aria-labelledby": METADATA_VALUE_LABEL_ID,
                                    ...field2,
                                    value: isDisabled ? placeholder : value,
                                    disabled: isDisabled,
                                    placeholder: "Value"
                                  }
                                ) }) });
                              }
                            }
                          )
                        ]
                      }
                    ),
                    (0, import_jsx_runtime.jsxs)(DropdownMenu, { children: [
                      (0, import_jsx_runtime.jsx)(
                        DropdownMenu.Trigger,
                        {
                          className: clx(
                            "invisible absolute inset-y-0 -right-2.5 my-auto group-hover/table:visible data-[state='open']:visible",
                            {
                              hidden: isDisabled
                            }
                          ),
                          disabled: isDisabled,
                          asChild: true,
                          children: (0, import_jsx_runtime.jsx)(IconButton, { size: "2xsmall", children: (0, import_jsx_runtime.jsx)(EllipsisVertical, {}) })
                        }
                      ),
                      (0, import_jsx_runtime.jsxs)(DropdownMenu.Content, { children: [
                        (0, import_jsx_runtime.jsxs)(
                          DropdownMenu.Item,
                          {
                            className: "gap-x-2",
                            onClick: () => insertRow(index, "above"),
                            children: [
                              (0, import_jsx_runtime.jsx)(ArrowUpMini, { className: "text-ui-fg-subtle" }),
                              t2("metadata.edit.actions.insertRowAbove")
                            ]
                          }
                        ),
                        (0, import_jsx_runtime.jsxs)(
                          DropdownMenu.Item,
                          {
                            className: "gap-x-2",
                            onClick: () => insertRow(index, "below"),
                            children: [
                              (0, import_jsx_runtime.jsx)(ArrowDownMini, { className: "text-ui-fg-subtle" }),
                              t2("metadata.edit.actions.insertRowBelow")
                            ]
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(DropdownMenu.Separator, {}),
                        (0, import_jsx_runtime.jsxs)(
                          DropdownMenu.Item,
                          {
                            className: "gap-x-2",
                            onClick: () => deleteRow(index),
                            children: [
                              (0, import_jsx_runtime.jsx)(Trash, { className: "text-ui-fg-subtle" }),
                              t2("metadata.edit.actions.deleteRow")
                            ]
                          }
                        )
                      ] })
                    ] })
                  ] })
                },
                field.id
              );
            })
          ] }),
          hasUneditableRows && (0, import_jsx_runtime.jsx)(
            InlineTip,
            {
              variant: "warning",
              label: t2("metadata.edit.complexRow.label"),
              children: t2("metadata.edit.complexRow.description")
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(
            Button,
            {
              size: "small",
              variant: "secondary",
              type: "button",
              disabled: isMutating,
              children: t2("actions.cancel")
            }
          ) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isMutating, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var GridInput = (0, import_react.forwardRef)(({ className, ...props }, ref) => {
  return (0, import_jsx_runtime.jsx)(
    "input",
    {
      ref,
      ...props,
      autoComplete: "off",
      className: clx(
        "txt-compact-small text-ui-fg-base placeholder:text-ui-fg-muted disabled:text-ui-fg-disabled disabled:bg-ui-bg-base bg-transparent px-2 py-1.5 outline-none",
        className
      )
    }
  );
});
GridInput.displayName = "MetadataForm.GridInput";
var PlaceholderInner = () => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-[148ox] w-full rounded-lg" }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-7 w-12 rounded-md" }),
      (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-7 w-12 rounded-md" })
    ] }) })
  ] });
};
var EDITABLE_TYPES = ["string", "number", "boolean"];
function getDefaultValues(metadata) {
  if (!metadata || !Object.keys(metadata).length) {
    return [
      {
        key: "",
        value: "",
        disabled: false
      }
    ];
  }
  return Object.entries(metadata).map(([key, value]) => {
    if (!EDITABLE_TYPES.includes(typeof value)) {
      return {
        key,
        value,
        disabled: true
      };
    }
    let stringValue = value;
    if (typeof value !== "string") {
      stringValue = JSON.stringify(value);
    }
    return {
      key,
      value: stringValue,
      original_key: key
    };
  });
}
function parseValues(values, original) {
  const metadata = values.metadata;
  const isEmpty = !metadata.length || metadata.length === 1 && !metadata[0].key && !metadata[0].value;
  if (isEmpty) {
    return null;
  }
  const update = {};
  if (original) {
    Object.keys(original).forEach((originalKey) => {
      const exists = metadata.some((field) => field.key === originalKey);
      if (!exists) {
        update[originalKey] = "";
      }
    });
  }
  metadata.forEach((field) => {
    let key = field.key;
    let value = field.value;
    const disabled = field.disabled;
    if (!key) {
      return;
    }
    if (disabled) {
      update[key] = value;
      return;
    }
    key = key.trim();
    value = (value == null ? void 0 : value.trim()) ?? "";
    if (value === "true") {
      update[key] = true;
    } else if (value === "false") {
      update[key] = false;
    } else {
      const isNumeric = /^-?\d*\.?\d+$/.test(value);
      if (isNumeric) {
        update[key] = parseFloat(value);
      } else {
        update[key] = value;
      }
    }
  });
  return update;
}
function getHasUneditableRows(metadata) {
  if (!metadata) {
    return false;
  }
  return Object.values(metadata).some(
    (value) => !EDITABLE_TYPES.includes(typeof value)
  );
}

export {
  MetadataForm
};
//# sourceMappingURL=chunk-LNSD7AQU.js.map
