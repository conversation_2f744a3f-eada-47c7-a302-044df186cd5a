{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-list-5UJ6CSA7.mjs"], "sourcesContent": ["import {\n  BadgeListSummary\n} from \"./chunk-BKJC5BGQ.mjs\";\nimport \"./chunk-PYIO3TDQ.mjs\";\nimport {\n  LinkButton\n} from \"./chunk-6WKBBTKM.mjs\";\nimport {\n  SidebarLink\n} from \"./chunk-LBIOZZPA.mjs\";\nimport \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  getFormattedAddress\n} from \"./chunk-OIAPXGI2.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  stockLocationsQueryKeys,\n  useDeleteStockLocation,\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/locations/location-list/loader.ts\nimport { redirect } from \"react-router-dom\";\n\n// src/routes/locations/location-list/constants.ts\nvar LOCATION_LIST_FIELDS = \"name,*sales_channels,*address,*fulfillment_sets,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options,*fulfillment_sets.service_zones.shipping_options.shipping_profile\";\n\n// src/routes/locations/location-list/loader.ts\nvar shippingListQuery = () => ({\n  queryKey: stockLocationsQueryKeys.lists(),\n  queryFn: async () => {\n    return await sdk.admin.stockLocation.list({\n      // TODO: change this when RQ is fixed\n      fields: LOCATION_LIST_FIELDS\n    }).catch((error) => {\n      if (error.status === 401) {\n        throw redirect(\"/login\");\n      }\n      throw error;\n    });\n  }\n});\nvar shippingListLoader = async (_) => {\n  const query = shippingListQuery();\n  return queryClient.getQueryData(\n    query.queryKey\n  ) ?? await queryClient.fetchQuery(query);\n};\n\n// src/routes/locations/location-list/location-list.tsx\nimport { ShoppingBag } from \"@medusajs/icons\";\nimport { Container as Container3, Heading as Heading2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { useLoaderData } from \"react-router-dom\";\n\n// src/routes/locations/location-list/components/location-list-item/location-list-item.tsx\nimport { Buildings, PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, StatusBadge, Text, toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction SalesChannels(props) {\n  const { t } = useTranslation();\n  const { salesChannels } = props;\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col px-6 py-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n    /* @__PURE__ */ jsx(\n      Text,\n      {\n        size: \"small\",\n        weight: \"plus\",\n        className: \"text-ui-fg-subtle flex-1\",\n        as: \"div\",\n        children: t(`stockLocations.salesChannels.label`)\n      }\n    ),\n    /* @__PURE__ */ jsx(\"div\", { className: \"flex-1 text-left\", children: salesChannels?.length ? /* @__PURE__ */ jsx(\n      BadgeListSummary,\n      {\n        rounded: true,\n        inline: true,\n        n: 3,\n        list: salesChannels.map((s) => s.name)\n      }\n    ) : \"-\" })\n  ] }) });\n}\nfunction FulfillmentSet(props) {\n  const { t } = useTranslation();\n  const { fulfillmentSet, type } = props;\n  const fulfillmentSetExists = !!fulfillmentSet;\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col px-6 py-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n    /* @__PURE__ */ jsx(\n      Text,\n      {\n        size: \"small\",\n        weight: \"plus\",\n        className: \"text-ui-fg-subtle flex-1\",\n        as: \"div\",\n        children: t(`stockLocations.fulfillmentSets.${type}.header`)\n      }\n    ),\n    /* @__PURE__ */ jsx(\"div\", { className: \"flex-1 text-left\", children: /* @__PURE__ */ jsx(StatusBadge, { color: fulfillmentSetExists ? \"green\" : \"grey\", children: t(fulfillmentSetExists ? \"statuses.enabled\" : \"statuses.disabled\") }) })\n  ] }) });\n}\nfunction LocationListItem(props) {\n  const { location } = props;\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync: deleteLocation } = useDeleteStockLocation(location.id);\n  const handleDelete = async () => {\n    const result = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"stockLocations.delete.confirmation\", {\n        name: location.name\n      }),\n      confirmText: t(\"actions.remove\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!result) {\n      return;\n    }\n    await deleteLocation(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"shippingProfile.delete.successToast\", {\n            name: location.name\n          })\n        );\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"flex flex-col divide-y p-0\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"px-6 py-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-row items-center justify-between gap-x-4\", children: [\n      /* @__PURE__ */ jsx(\"div\", { className: \"shadow-borders-base flex size-7 items-center justify-center rounded-md\", children: /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-field flex size-6 items-center justify-center rounded-[4px]\", children: /* @__PURE__ */ jsx(Buildings, { className: \"text-ui-fg-subtle\" }) }) }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grow-1 flex flex-1 flex-col\", children: [\n        /* @__PURE__ */ jsx(Text, { weight: \"plus\", children: location.name }),\n        /* @__PURE__ */ jsx(Text, { className: \"text-ui-fg-subtle txt-small\", children: getFormattedAddress({ address: location.address }).join(\", \") })\n      ] }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex grow-0 items-center gap-4\", children: [\n        /* @__PURE__ */ jsx(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t(\"actions.edit\"),\n                    icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n                    to: `/settings/locations/${location.id}/edit`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t(\"actions.delete\"),\n                    icon: /* @__PURE__ */ jsx(Trash, {}),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        ),\n        /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong h-[12px] w-[1px]\" }),\n        /* @__PURE__ */ jsx(LinkButton, { to: `/settings/locations/${location.id}`, children: t(\"actions.viewDetails\") })\n      ] })\n    ] }) }),\n    /* @__PURE__ */ jsx(SalesChannels, { salesChannels: location.sales_channels }),\n    /* @__PURE__ */ jsx(\n      FulfillmentSet,\n      {\n        type: \"pickup\" /* Pickup */,\n        fulfillmentSet: location.fulfillment_sets?.find(\n          (f) => f.type === \"pickup\" /* Pickup */\n        )\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      FulfillmentSet,\n      {\n        type: \"shipping\" /* Shipping */,\n        fulfillmentSet: location.fulfillment_sets?.find(\n          (f) => f.type === \"shipping\" /* Shipping */\n        )\n      }\n    )\n  ] });\n}\nvar location_list_item_default = LocationListItem;\n\n// src/routes/locations/location-list/components/location-list-header/location-list-header.tsx\nimport { Button, Container as Container2, Heading, Text as Text2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar LocationListHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"flex h-fit items-center justify-between gap-x-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { children: [\n      /* @__PURE__ */ jsx2(Heading, { children: t(\"stockLocations.domain\") }),\n      /* @__PURE__ */ jsx2(Text2, { className: \"text-ui-fg-subtle txt-small\", children: t(\"stockLocations.list.description\") })\n    ] }),\n    /* @__PURE__ */ jsx2(Button, { size: \"small\", className: \"shrink-0\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx2(Link, { to: \"create\", children: t(\"actions.create\") }) })\n  ] });\n};\n\n// src/routes/locations/location-list/location-list.tsx\nimport { jsx as jsx3, jsxs as jsxs3 } from \"react/jsx-runtime\";\nfunction LocationList() {\n  const initialData = useLoaderData();\n  const {\n    stock_locations: stockLocations = [],\n    isError,\n    error\n  } = useStockLocations(\n    {\n      fields: LOCATION_LIST_FIELDS\n    },\n    { initialData }\n  );\n  const { getWidgets } = useExtension();\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(\n    TwoColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"location.list.after\"),\n        before: getWidgets(\"location.list.before\"),\n        sideAfter: getWidgets(\"location.list.side.after\"),\n        sideBefore: getWidgets(\"location.list.side.before\")\n      },\n      showJSON: true,\n      children: [\n        /* @__PURE__ */ jsxs3(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx3(LocationListHeader, {}),\n          /* @__PURE__ */ jsx3(\"div\", { className: \"flex flex-col gap-3 lg:col-span-2\", children: stockLocations.map((location) => /* @__PURE__ */ jsx3(location_list_item_default, { location }, location.id)) })\n        ] }),\n        /* @__PURE__ */ jsx3(TwoColumnPage.Sidebar, { children: /* @__PURE__ */ jsx3(LinksSection, {}) })\n      ]\n    }\n  );\n}\nvar LinksSection = () => {\n  const { t } = useTranslation3();\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"p-0\", children: [\n    /* @__PURE__ */ jsx3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"stockLocations.sidebar.header\") }) }),\n    /* @__PURE__ */ jsx3(\n      SidebarLink,\n      {\n        to: \"/settings/locations/shipping-profiles\",\n        labelKey: t(\"stockLocations.sidebar.shippingProfiles.label\"),\n        descriptionKey: t(\n          \"stockLocations.sidebar.shippingProfiles.description\"\n        ),\n        icon: /* @__PURE__ */ jsx3(ShoppingBag, {})\n      }\n    )\n  ] });\n};\nexport {\n  LocationList as Component,\n  shippingListLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,yBAA0B;AA0I1B,IAAAA,sBAA2C;AAa3C,IAAAA,sBAA2C;AAzL3C,IAAI,uBAAuB;AAG3B,IAAI,oBAAoB,OAAO;AAAA,EAC7B,UAAU,wBAAwB,MAAM;AAAA,EACxC,SAAS,YAAY;AACnB,WAAO,MAAM,IAAI,MAAM,cAAc,KAAK;AAAA;AAAA,MAExC,QAAQ;AAAA,IACV,CAAC,EAAE,MAAM,CAAC,UAAU;AAClB,UAAI,MAAM,WAAW,KAAK;AACxB,cAAM,SAAS,QAAQ;AAAA,MACzB;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AACA,IAAI,qBAAqB,OAAO,MAAM;AACpC,QAAM,QAAQ,kBAAkB;AAChC,SAAO,YAAY;AAAA,IACjB,MAAM;AAAA,EACR,KAAK,MAAM,YAAY,WAAW,KAAK;AACzC;AAaA,SAAS,cAAc,OAAO;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI;AAC1B,aAAuB,wBAAI,OAAO,EAAE,WAAW,2BAA2B,cAA0B,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,QAC1J;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,UAAU,EAAE,oCAAoC;AAAA,MAClD;AAAA,IACF;AAAA,QACgB,wBAAI,OAAO,EAAE,WAAW,oBAAoB,WAAU,+CAAe,cAAyB;AAAA,MAC5G;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,GAAG;AAAA,QACH,MAAM,cAAc,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,MACvC;AAAA,IACF,IAAI,IAAI,CAAC;AAAA,EACX,EAAE,CAAC,EAAE,CAAC;AACR;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,gBAAgB,KAAK,IAAI;AACjC,QAAM,uBAAuB,CAAC,CAAC;AAC/B,aAAuB,wBAAI,OAAO,EAAE,WAAW,2BAA2B,cAA0B,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,QAC1J;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,UAAU,EAAE,kCAAkC,IAAI,SAAS;AAAA,MAC7D;AAAA,IACF;AAAA,QACgB,wBAAI,OAAO,EAAE,WAAW,oBAAoB,cAA0B,wBAAI,aAAa,EAAE,OAAO,uBAAuB,UAAU,QAAQ,UAAU,EAAE,uBAAuB,qBAAqB,mBAAmB,EAAE,CAAC,EAAE,CAAC;AAAA,EAC5O,EAAE,CAAC,EAAE,CAAC;AACR;AACA,SAAS,iBAAiB,OAAO;AAzHjC;AA0HE,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,aAAa,eAAe,IAAI,uBAAuB,SAAS,EAAE;AAC1E,QAAM,eAAe,YAAY;AAC/B,UAAM,SAAS,MAAM,OAAO;AAAA,MAC1B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,sCAAsC;AAAA,QACnD,MAAM,SAAS;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,eAAe,QAAQ;AAAA,MAC3B,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,uCAAuC;AAAA,YACvC,MAAM,SAAS;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,8BAA8B,UAAU;AAAA,QAC1E,wBAAI,OAAO,EAAE,WAAW,aAAa,cAA0B,yBAAK,OAAO,EAAE,WAAW,sDAAsD,UAAU;AAAA,UACtJ,wBAAI,OAAO,EAAE,WAAW,0EAA0E,cAA0B,wBAAI,OAAO,EAAE,WAAW,wEAAwE,cAA0B,wBAAI,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7S,yBAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,YAChE,wBAAI,MAAM,EAAE,QAAQ,QAAQ,UAAU,SAAS,KAAK,CAAC;AAAA,YACrD,wBAAI,MAAM,EAAE,WAAW,+BAA+B,UAAU,oBAAoB,EAAE,SAAS,SAAS,QAAQ,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,MACjJ,EAAE,CAAC;AAAA,UACa,yBAAK,OAAO,EAAE,WAAW,kCAAkC,UAAU;AAAA,YACnE;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,cAAc;AAAA,oBACvB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,oBAC1C,IAAI,uBAAuB,SAAS,EAAE;AAAA,kBACxC;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,gBAAgB;AAAA,oBACzB,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,oBACnC,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,YACgB,wBAAI,OAAO,EAAE,WAAW,uCAAuC,CAAC;AAAA,YAChE,wBAAI,YAAY,EAAE,IAAI,uBAAuB,SAAS,EAAE,IAAI,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,MAClH,EAAE,CAAC;AAAA,IACL,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,eAAe,EAAE,eAAe,SAAS,eAAe,CAAC;AAAA,QAC7D;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,iBAAgB,cAAS,qBAAT,mBAA2B;AAAA,UACzC,CAAC,MAAM,EAAE,SAAS;AAAA;AAAA,MAEtB;AAAA,IACF;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,iBAAgB,cAAS,qBAAT,mBAA2B;AAAA,UACzC,CAAC,MAAM,EAAE,SAAS;AAAA;AAAA,MAEtB;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,6BAA6B;AAOjC,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,6DAA6D,UAAU;AAAA,QAC3G,oBAAAA,MAAM,OAAO,EAAE,UAAU;AAAA,UACvB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,UACtD,oBAAAA,KAAK,MAAO,EAAE,WAAW,+BAA+B,UAAU,EAAE,iCAAiC,EAAE,CAAC;AAAA,IAC1H,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,WAAW,YAAY,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,EACnM,EAAE,CAAC;AACL;AAIA,SAAS,eAAe;AACtB,QAAM,cAAc,cAAc;AAClC,QAAM;AAAA,IACJ,iBAAiB,iBAAiB,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA,EAAE,YAAY;AAAA,EAChB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,qBAAqB;AAAA,QACvC,QAAQ,WAAW,sBAAsB;AAAA,QACzC,WAAW,WAAW,0BAA0B;AAAA,QAChD,YAAY,WAAW,2BAA2B;AAAA,MACpD;AAAA,MACA,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAC,KAAK,oBAAoB,CAAC,CAAC;AAAA,cAC3B,oBAAAA,KAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU,eAAe,IAAI,CAAC,iBAA6B,oBAAAA,KAAK,4BAA4B,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAAA,QACzM,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,cAAc,SAAS,EAAE,cAA0B,oBAAAA,KAAK,cAAc,CAAC,CAAC,EAAE,CAAC;AAAA,MAClG;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAD,MAAM,WAAY,EAAE,WAAW,OAAO,UAAU;AAAA,QACrD,oBAAAC,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,+BAA+B,EAAE,CAAC,EAAE,CAAC;AAAA,QACjL,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,UAAU,EAAE,+CAA+C;AAAA,QAC3D,gBAAgB;AAAA,UACd;AAAA,QACF;AAAA,QACA,UAAsB,oBAAAA,KAAK,aAAa,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "jsxs2", "jsx2", "jsxs3", "jsx3"]}