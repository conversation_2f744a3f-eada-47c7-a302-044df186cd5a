{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-DFA6WGYO.mjs"], "sourcesContent": ["import {\n  TextCell\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/columns/use-collection-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCollectionTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"title\", {\n        header: t(\"fields.title\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue() })\n      }),\n      columnHelper.accessor(\"handle\", {\n        header: t(\"fields.handle\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: `/${getValue()}` })\n      }),\n      columnHelper.accessor(\"products\", {\n        header: t(\"fields.products\"),\n        cell: ({ getValue }) => {\n          const count = getValue()?.length || void 0;\n          return /* @__PURE__ */ jsx(TextCell, { text: count });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\n// src/hooks/table/query/use-collection-table-query.tsx\nvar useCollectionTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, created_at, updated_at, q, order } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\n// src/hooks/table/query/use-product-tag-table-query.tsx\nvar useProductTagTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, q, order, created_at, updated_at } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\n// src/hooks/table/query/use-return-reason-table-query.tsx\nvar useReturnReasonTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, q, order, created_at, updated_at } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useCollectionTableColumns,\n  useCollectionTableQuery,\n  useProductTagTableQuery,\n  useReturnReasonTableQuery\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,mBAAwB;AAExB,yBAAoB;AACpB,IAAI,eAAe,mBAAmB;AACtC,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,cAAc;AAAA,QACxB,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC5E,CAAC;AAAA,MACD,aAAa,SAAS,UAAU;AAAA,QAC9B,QAAQ,EAAE,eAAe;AAAA,QACzB,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC;AAAA,MAClF,CAAC;AAAA,MACD,aAAa,SAAS,YAAY;AAAA,QAChC,QAAQ,EAAE,iBAAiB;AAAA,QAC3B,MAAM,CAAC,EAAE,SAAS,MAAM;AA3BhC;AA4BU,gBAAM,UAAQ,cAAS,MAAT,mBAAY,WAAU;AACpC,qBAAuB,wBAAI,UAAU,EAAE,MAAM,MAAM,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,SAAS,cAAc,YAAY;AAAA,IACnD;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,YAAY,YAAY,GAAG,MAAM,IAAI;AACrD,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;AAGA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,SAAS,cAAc,YAAY;AAAA,IACnD;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,OAAO,YAAY,WAAW,IAAI;AACrD,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;AAGA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,SAAS,cAAc,YAAY;AAAA,IACnD;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,OAAO,YAAY,WAAW,IAAI;AACrD,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}