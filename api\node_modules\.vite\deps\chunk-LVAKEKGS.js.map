{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-IR5DHEKS.mjs"], "sourcesContent": ["// src/lib/format-provider.ts\nvar formatProvider = (id) => {\n  const [_, name, type] = id.split(\"_\");\n  return name.split(\"-\").map((s) => s.charAt(0).toUpperCase() + s.slice(1)).join(\" \") + (type ? ` (${type.toUpperCase()})` : \"\");\n};\n\nexport {\n  formatProvider\n};\n"], "mappings": ";AACA,IAAI,iBAAiB,CAAC,OAAO;AAC3B,QAAM,CAAC,GAAG,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG;AACpC,SAAO,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,YAAY,CAAC,MAAM;AAC7H;", "names": []}