{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3WXBLS2P.mjs"], "sourcesContent": ["// src/lib/percentage-helpers.ts\nvar formatter = new Intl.NumberFormat([], {\n  style: \"percent\",\n  minimumFractionDigits: 2\n});\nvar formatPercentage = (value, isPercentageValue = false) => {\n  let val = value || 0;\n  if (!isPercentageValue) {\n    val = val / 100;\n  }\n  return formatter.format(val);\n};\n\nexport {\n  formatPercentage\n};\n"], "mappings": ";AACA,IAAI,YAAY,IAAI,KAAK,aAAa,CAAC,GAAG;AAAA,EACxC,OAAO;AAAA,EACP,uBAAuB;AACzB,CAAC;AACD,IAAI,mBAAmB,CAAC,OAAO,oBAAoB,UAAU;AAC3D,MAAI,MAAM,SAAS;AACnB,MAAI,CAAC,mBAAmB;AACtB,UAAM,MAAM;AAAA,EACd;AACA,SAAO,UAAU,OAAO,GAAG;AAC7B;", "names": []}