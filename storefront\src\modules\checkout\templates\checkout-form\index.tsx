import { listCartShippingMethods } from "@lib/data/fulfillment"
import { listCartPaymentMethods } from "@lib/data/payment"

import { HttpTypes } from "@medusajs/types"
import { TCustomLocation } from "types/custom-locations"

import Addresses from "@modules/checkout/components/addresses"
import Payment from "@modules/checkout/components/payment"
import Review from "@modules/checkout/components/review"
import Shipping from "@modules/checkout/components/shipping"

export default async function CheckoutForm({
  cart,
  customer,
  locationList,
}: {
  cart: HttpTypes.StoreCart | null
  customer: HttpTypes.StoreCustomer | null
  locationList: TCustomLocation[]
}) {
  if (!cart) {
    return null
  }

  const shippingMethods = await listCartShippingMethods(cart.id)
  const paymentMethods = await listCartPaymentMethods(cart.region?.id ?? "")

  console.log("🚀 ~ CheckoutForm ~ paymentMethods:", paymentMethods)

  if (!shippingMethods) {
    return null
  }

  // Don't block rendering if paymentMethods is null, pass empty array instead
  const safePaymentMethods = paymentMethods || []

  return (
    <div className="grid w-full space-y-10">
      <Addresses cart={cart} customer={customer} locationList={locationList} />

      <Shipping cart={cart} availableShippingMethods={shippingMethods} />

      <Payment cart={cart} availablePaymentMethods={safePaymentMethods} />

      <Review cart={cart} />
    </div>
  )
}
