import {
  IconAvatar
} from "./chunk-73I4NEMG.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Text,
  TriangleRightMini
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-LBIOZZPA.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var SidebarLink = ({
  to,
  labelKey,
  descriptionKey,
  icon
}) => {
  return (0, import_jsx_runtime.jsx)(Link, { to, className: "group outline-none", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-2 px-2 pb-2", children: (0, import_jsx_runtime.jsx)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component transition-fg hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed group-focus-visible:shadow-borders-interactive-with-active rounded-md px-4 py-2", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-4", children: [
    (0, import_jsx_runtime.jsx)(IconAvatar, { children: icon }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 flex-col", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: labelKey }),
      (0, import_jsx_runtime.jsx)(
        Text,
        {
          size: "small",
          leading: "compact",
          className: "text-ui-fg-subtle",
          children: descriptionKey
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)("div", { className: "flex size-7 items-center justify-center", children: (0, import_jsx_runtime.jsx)(TriangleRightMini, { className: "text-ui-fg-muted" }) })
  ] }) }) }) });
};

export {
  SidebarLink
};
//# sourceMappingURL=chunk-TDOJLFIZ.js.map
