import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  useStockLocation
} from "./chunk-CXC4I63N.js";
import {
  useInventoryItem,
  useUpdateInventoryLevel
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/adjust-inventory-WYNH75F7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AttributeGridRow = ({
  title,
  value
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 divide-x", children: [
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: title }),
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: value })
  ] });
};
var AdjustInventoryForm = ({
  item,
  level,
  location
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const AdjustInventorySchema = z.object({
    stocked_quantity: z.union([z.number(), z.string()])
  }).superRefine((data, ctx) => {
    const quantity = data.stocked_quantity ? castNumber(data.stocked_quantity) : null;
    if (quantity === null) {
      ctx.addIssue({
        code: z.ZodIssueCode.invalid_type,
        expected: "number",
        received: "undefined",
        path: ["stocked_quantity"]
      });
      return;
    }
    if (quantity < level.reserved_quantity) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: t2("inventory.adjustInventory.errors.stockedQuantity", {
          quantity: level.reserved_quantity
        }),
        path: ["stocked_quantity"]
      });
    }
  });
  const form = useForm({
    defaultValues: {
      stocked_quantity: level.stocked_quantity
    },
    resolver: t(AdjustInventorySchema)
  });
  const stockedQuantityUpdate = useWatch({
    control: form.control,
    name: "stocked_quantity"
  });
  const availableQuantity = stockedQuantityUpdate ? castNumber(stockedQuantityUpdate) - level.reserved_quantity : 0 - level.reserved_quantity;
  const { mutateAsync, isPending: isLoading } = useUpdateInventoryLevel(
    item.id,
    level.location_id
  );
  const handleSubmit = form.handleSubmit(async (value) => {
    await mutateAsync(
      {
        stocked_quantity: castNumber(value.stocked_quantity)
      },
      {
        onSuccess: () => {
          toast.success(t2("inventory.toast.updateLevel"));
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-auto", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-4 divide-y rounded-lg border", children: [
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.title"),
                value: item.title ?? "-"
              }
            ),
            (0, import_jsx_runtime.jsx)(AttributeGridRow, { title: t2("fields.sku"), value: item.sku }),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("locations.domain"),
                value: location.name
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("inventory.reserved"),
                value: level.reserved_quantity
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("inventory.available"),
                value: availableQuantity
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "stocked_quantity",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.inStock") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      value,
                      onChange,
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var AdjustInventoryDrawer = () => {
  const { id, location_id } = useParams();
  const { t: t2 } = useTranslation();
  const {
    inventory_item: inventoryItem,
    isPending: isLoading,
    isError,
    error
  } = useInventoryItem(id);
  const inventoryLevel = inventoryItem == null ? void 0 : inventoryItem.location_levels.find(
    (level) => level.location_id === location_id
  );
  const { stock_location, isLoading: isLoadingLocation } = useStockLocation(
    location_id
  );
  const ready = !isLoading && inventoryItem && inventoryLevel && !isLoadingLocation && stock_location;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("inventory.manageLocationQuantity") }) }),
    ready && (0, import_jsx_runtime2.jsx)(
      AdjustInventoryForm,
      {
        item: inventoryItem,
        level: inventoryLevel,
        location: stock_location
      }
    )
  ] });
};
export {
  AdjustInventoryDrawer as Component
};
//# sourceMappingURL=adjust-inventory-WYNH75F7-DCRXPOQC.js.map
