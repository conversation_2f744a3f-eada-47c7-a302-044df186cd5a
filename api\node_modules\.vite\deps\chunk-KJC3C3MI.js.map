{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-OC7BQLYI.mjs"], "sourcesContent": ["// src/components/common/conditional-tooltip/conditional-tooltip.tsx\nimport { Tooltip } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ConditionalTooltip = ({\n  children,\n  showTooltip = false,\n  ...props\n}) => {\n  if (showTooltip) {\n    return /* @__PURE__ */ jsx(Tooltip, { ...props, children });\n  }\n  return children;\n};\n\nexport {\n  ConditionalTooltip\n};\n"], "mappings": ";;;;;;;;;;;AAEA,yBAAoB;AACpB,IAAI,qBAAqB,CAAC;AAAA,EACxB;AAAA,EACA,cAAc;AAAA,EACd,GAAG;AACL,MAAM;AACJ,MAAI,aAAa;AACf,eAAuB,wBAAI,SAAS,EAAE,GAAG,OAAO,SAAS,CAAC;AAAA,EAC5D;AACA,SAAO;AACT;", "names": []}