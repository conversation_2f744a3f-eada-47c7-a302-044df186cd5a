{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-YRY2CZ6I.mjs"], "sourcesContent": ["// src/components/inputs/percentage-input/percentage-input.tsx\nimport { Input, Text, clx } from \"@medusajs/ui\";\nimport { forwardRef } from \"react\";\nimport Primitive from \"react-currency-input-field\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar DeprecatedPercentageInput = forwardRef(({ min = 0, max = 100, step = 1e-4, ...props }, ref) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"relative\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r\", children: /* @__PURE__ */ jsx(\n      Text,\n      {\n        className: \"text-ui-fg-muted\",\n        size: \"small\",\n        leading: \"compact\",\n        weight: \"plus\",\n        children: \"%\"\n      }\n    ) }),\n    /* @__PURE__ */ jsx(\n      Input,\n      {\n        ref,\n        type: \"number\",\n        min,\n        max,\n        step,\n        ...props,\n        className: \"pl-10\"\n      }\n    )\n  ] });\n});\nDeprecatedPercentageInput.displayName = \"PercentageInput\";\nvar PercentageInput = forwardRef(({ min = 0, decimalScale = 2, className, ...props }, ref) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"relative\", children: [\n    /* @__PURE__ */ jsx(\n      Primitive,\n      {\n        ref,\n        min,\n        autoComplete: \"off\",\n        decimalScale,\n        decimalsLimit: decimalScale,\n        ...props,\n        className: clx(\n          \"caret-ui-fg-base bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 pr-10 text-right outline-none\",\n          \"placeholder:text-ui-fg-muted text-ui-fg-base\",\n          \"hover:bg-ui-bg-field-hover\",\n          \"focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active\",\n          \"aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error\",\n          \"invalid::border-ui-border-error invalid:shadow-borders-error\",\n          \"disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled\",\n          className\n        )\n      }\n    ),\n    /* @__PURE__ */ jsx(\"div\", { className: \"absolute inset-y-0 right-0 z-10 flex w-8 items-center justify-center border-l\", children: /* @__PURE__ */ jsx(\n      Text,\n      {\n        className: \"text-ui-fg-muted\",\n        size: \"small\",\n        leading: \"compact\",\n        weight: \"plus\",\n        children: \"%\"\n      }\n    ) })\n  ] });\n});\nPercentageInput.displayName = \"PercentageInput\";\n\nexport {\n  DeprecatedPercentageInput,\n  PercentageInput\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,mBAA2B;AAE3B,yBAA0B;AAC1B,IAAI,gCAA4B,yBAAW,CAAC,EAAE,MAAM,GAAG,MAAM,KAAK,OAAO,MAAM,GAAG,MAAM,GAAG,QAAQ;AACjG,aAAuB,yBAAK,OAAO,EAAE,WAAW,YAAY,UAAU;AAAA,QACpD,wBAAI,OAAO,EAAE,WAAW,gFAAgF,cAA0B;AAAA,MAChJ;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG;AAAA,QACH,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL,CAAC;AACD,0BAA0B,cAAc;AACxC,IAAI,sBAAkB,yBAAW,CAAC,EAAE,MAAM,GAAG,eAAe,GAAG,WAAW,GAAG,MAAM,GAAG,QAAQ;AAC5F,aAAuB,yBAAK,OAAO,EAAE,WAAW,YAAY,UAAU;AAAA,QACpD;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA,eAAe;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,OAAO,EAAE,WAAW,iFAAiF,cAA0B;AAAA,MACjJ;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL,CAAC;AACD,gBAAgB,cAAc;", "names": []}