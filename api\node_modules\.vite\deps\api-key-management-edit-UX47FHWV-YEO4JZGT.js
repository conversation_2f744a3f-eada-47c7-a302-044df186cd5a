import {
  VisuallyH<PERSON>den
} from "./chunk-TAVTGMIK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useApiKey,
  useUpdateApiKey
} from "./chunk-RX237AWS.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  Heading,
  Input,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/api-key-management-edit-UX47FHWV.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditApiKeySchema = objectType({
  title: stringType().min(1)
});
var EditApiKeyForm = ({ apiKey }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      title: apiKey.title
    },
    resolver: t(EditApiKeySchema)
  });
  const { mutateAsync, isPending } = useUpdateApiKey(apiKey.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(data, {
      onSuccess: ({ api_key }) => {
        toast.success(
          t2("apiKeyManagement.edit.successToast", {
            title: api_key.title
          })
        );
        handleSuccess();
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "title",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ) }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var ApiKeyManagementEdit = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { api_key, isLoading, isError, error } = useApiKey(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("apiKeyManagement.edit.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { asChild: true, children: (0, import_jsx_runtime2.jsx)(VisuallyHidden, { children: t2("apiKeyManagement.edit.description") }) })
    ] }),
    !isLoading && !!api_key && (0, import_jsx_runtime2.jsx)(EditApiKeyForm, { apiKey: api_key })
  ] });
};
export {
  ApiKeyManagementEdit as Component
};
//# sourceMappingURL=api-key-management-edit-UX47FHWV-YEO4JZGT.js.map
