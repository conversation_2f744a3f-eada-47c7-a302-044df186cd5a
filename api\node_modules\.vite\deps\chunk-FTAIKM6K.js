import {
  react_country_flag_esm_default
} from "./chunk-QF476XOZ.js";
import {
  Date<PERSON><PERSON>,
  DateHeader
} from "./chunk-OW6OIDUA.js";
import {
  getOrderFulfillmentStatus,
  getOrderPaymentStatus
} from "./chunk-5ZQBU3TD.js";
import {
  MoneyAmountCell
} from "./chunk-NO4BKKGC.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Tooltip,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-3IRPEKIV.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var CountryCell = ({
  country
}) => {
  if (!country) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-5 items-center justify-center", children: (0, import_jsx_runtime.jsx)(Tooltip, { content: country.display_name, children: (0, import_jsx_runtime.jsx)("div", { className: "flex size-4 items-center justify-center overflow-hidden rounded-sm", children: (0, import_jsx_runtime.jsx)(
    react_country_flag_esm_default,
    {
      countryCode: country.iso_2.toUpperCase(),
      svg: true,
      style: {
        width: "16px",
        height: "16px"
      },
      "aria-label": country.display_name
    }
  ) }) }) });
};
var CustomerCell = ({
  customer
}) => {
  if (!customer) {
    return (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-muted", children: "-" });
  }
  const { first_name, last_name, email } = customer;
  const name = [first_name, last_name].filter(Boolean).join(" ");
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: name || email }) });
};
var CustomerHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: t("fields.customer") }) });
};
var DisplayIdCell = ({ displayId }) => {
  if (!displayId) {
    return (0, import_jsx_runtime3.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime3.jsx)("div", { className: "text-ui-fg-subtle txt-compact-small flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime3.jsxs)("span", { className: "truncate", children: [
    "#",
    displayId
  ] }) });
};
var DisplayIdHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: t("fields.order") }) });
};
var FulfillmentStatusCell = ({
  status
}) => {
  const { t } = useTranslation();
  if (!status) {
    return "-";
  }
  const { label, color } = getOrderFulfillmentStatus(t, status);
  return (0, import_jsx_runtime4.jsx)(StatusCell, { color, children: label });
};
var FulfillmentStatusHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime4.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: t("fields.fulfillment") }) });
};
var PaymentStatusCell = ({ status }) => {
  const { t } = useTranslation();
  const { label, color } = getOrderPaymentStatus(t, status);
  return (0, import_jsx_runtime5.jsx)(StatusCell, { color, children: label });
};
var PaymentStatusHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime5.jsx)("span", { className: "truncate", children: t("fields.payment") }) });
};
var SalesChannelCell = ({
  channel
}) => {
  if (!channel) {
    return (0, import_jsx_runtime6.jsx)("span", { className: "text-ui-fg-muted", children: "-" });
  }
  const { name } = channel;
  return (0, import_jsx_runtime6.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime6.jsx)("span", { className: "truncate", children: name }) });
};
var SalesChannelHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime6.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime6.jsx)("span", { className: "truncate", children: t("fields.salesChannel") }) });
};
var TotalCell = ({ currencyCode, total }) => {
  if (!total) {
    return (0, import_jsx_runtime7.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime7.jsx)(MoneyAmountCell, { currencyCode, amount: total, align: "right" });
};
var TotalHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime7.jsx)("div", { className: "flex h-full w-full items-center justify-end", children: (0, import_jsx_runtime7.jsx)("span", { className: "truncate", children: t("fields.total") }) });
};
var columnHelper = createColumnHelper();
var useOrderTableColumns = (props) => {
  const { exclude = [] } = props ?? {};
  const columns = (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("display_id", {
        header: () => (0, import_jsx_runtime8.jsx)(DisplayIdHeader, {}),
        cell: ({ getValue }) => {
          const id = getValue();
          return (0, import_jsx_runtime8.jsx)(DisplayIdCell, { displayId: id });
        }
      }),
      columnHelper.accessor("created_at", {
        header: () => (0, import_jsx_runtime8.jsx)(DateHeader, {}),
        cell: ({ getValue }) => {
          const date = new Date(getValue());
          return (0, import_jsx_runtime8.jsx)(DateCell, { date });
        }
      }),
      columnHelper.accessor("customer", {
        header: () => (0, import_jsx_runtime8.jsx)(CustomerHeader, {}),
        cell: ({ getValue }) => {
          const customer = getValue();
          return (0, import_jsx_runtime8.jsx)(CustomerCell, { customer });
        }
      }),
      columnHelper.accessor("sales_channel", {
        header: () => (0, import_jsx_runtime8.jsx)(SalesChannelHeader, {}),
        cell: ({ getValue }) => {
          const channel = getValue();
          return (0, import_jsx_runtime8.jsx)(SalesChannelCell, { channel });
        }
      }),
      columnHelper.accessor("payment_status", {
        header: () => (0, import_jsx_runtime8.jsx)(PaymentStatusHeader, {}),
        cell: ({ getValue }) => {
          const status = getValue();
          return (0, import_jsx_runtime8.jsx)(PaymentStatusCell, { status });
        }
      }),
      columnHelper.accessor("fulfillment_status", {
        header: () => (0, import_jsx_runtime8.jsx)(FulfillmentStatusHeader, {}),
        cell: ({ getValue }) => {
          const status = getValue();
          return (0, import_jsx_runtime8.jsx)(FulfillmentStatusCell, { status });
        }
      }),
      columnHelper.accessor("total", {
        header: () => (0, import_jsx_runtime8.jsx)(TotalHeader, {}),
        cell: ({ getValue, row }) => {
          const total = getValue();
          const currencyCode = row.original.currency_code;
          return (0, import_jsx_runtime8.jsx)(TotalCell, { currencyCode, total });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          var _a;
          const country = (_a = row.original.shipping_address) == null ? void 0 : _a.country;
          return (0, import_jsx_runtime8.jsx)(CountryCell, { country });
        }
      })
    ],
    []
  );
  const isAccessorColumnDef = (c) => {
    return c.accessorKey !== void 0;
  };
  const isDisplayColumnDef = (c) => {
    return c.id !== void 0;
  };
  const shouldExclude = (c) => {
    if (isAccessorColumnDef(c)) {
      return exclude.includes(c.accessorKey);
    } else if (isDisplayColumnDef(c)) {
      return exclude.includes(c.id);
    }
    return false;
  };
  return columns.filter(
    (c) => !shouldExclude(c)
  );
};

export {
  useOrderTableColumns
};
//# sourceMappingURL=chunk-FTAIKM6K.js.map
