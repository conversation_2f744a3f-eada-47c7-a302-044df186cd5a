{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-detail-GHY3DXRP.mjs"], "sourcesContent": ["import {\n  isOptionEnabledInStore,\n  isReturnOption\n} from \"./chunk-R2O6QX4D.mjs\";\nimport {\n  ListSummary\n} from \"./chunk-I3VB6NM2.mjs\";\nimport \"./chunk-PYIO3TDQ.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport {\n  LinkButton\n} from \"./chunk-6WKBBTKM.mjs\";\nimport {\n  NoRecords\n} from \"./chunk-EMIHDNB7.mjs\";\nimport {\n  IconAvatar\n} from \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  getFormattedAddress\n} from \"./chunk-OIAPXGI2.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  countries\n} from \"./chunk-VDBOSWVE.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport {\n  useDeleteFulfillmentServiceZone,\n  useDeleteFulfillmentSet\n} from \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport {\n  useDeleteShippingOption\n} from \"./chunk-GRT22PE5.mjs\";\nimport {\n  stockLocationsQueryKeys,\n  useCreateStockLocationFulfillmentSet,\n  useDeleteStockLocation,\n  useFulfillmentProviders,\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/locations/location-detail/constants.ts\nvar LOCATION_DETAILS_FIELD = \"name,*sales_channels,*address,fulfillment_sets.type,fulfillment_sets.name,*fulfillment_sets.service_zones.geo_zones,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options,*fulfillment_sets.service_zones.shipping_options.rules,*fulfillment_sets.service_zones.shipping_options.shipping_profile,*fulfillment_providers\";\n\n// src/routes/locations/location-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar LocationDetailBreadcrumb = (props) => {\n  const { location_id } = props.params || {};\n  const { stock_location } = useStockLocation(\n    location_id,\n    {\n      fields: LOCATION_DETAILS_FIELD\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(location_id)\n    }\n  );\n  if (!stock_location) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: stock_location.name });\n};\n\n// src/routes/locations/location-detail/loader.ts\nvar locationQuery = (id) => ({\n  queryKey: stockLocationsQueryKeys.detail(id, {\n    fields: LOCATION_DETAILS_FIELD\n  }),\n  queryFn: async () => sdk.admin.stockLocation.retrieve(id, {\n    fields: LOCATION_DETAILS_FIELD\n  })\n});\nvar locationLoader = async ({ params }) => {\n  const id = params.location_id;\n  const query = locationQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/locations/location-detail/location-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-detail/components/location-general-section/location-general-section.tsx\nimport {\n  ArchiveBox,\n  CurrencyDollar,\n  Map,\n  PencilSquare,\n  Plus,\n  Trash,\n  TriangleDownMini\n} from \"@medusajs/icons\";\nimport {\n  Badge,\n  Container,\n  Divider,\n  Heading,\n  IconButton,\n  StatusBadge,\n  Text,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Fragment, jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar LocationGeneralSection = ({\n  location\n}) => {\n  return /* @__PURE__ */ jsxs(Fragment, { children: [\n    /* @__PURE__ */ jsx2(Container, { className: \"p-0\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx2(Heading, { children: location.name }),\n        /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle txt-small\", children: getFormattedAddress({ address: location.address }).join(\", \") })\n      ] }),\n      /* @__PURE__ */ jsx2(Actions, { location })\n    ] }) }),\n    /* @__PURE__ */ jsx2(\n      FulfillmentSet,\n      {\n        locationId: location.id,\n        locationName: location.name,\n        type: \"pickup\" /* Pickup */,\n        fulfillmentSet: location.fulfillment_sets?.find(\n          (f) => f.type === \"pickup\" /* Pickup */\n        )\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      FulfillmentSet,\n      {\n        locationId: location.id,\n        locationName: location.name,\n        type: \"shipping\" /* Shipping */,\n        fulfillmentSet: location.fulfillment_sets?.find(\n          (f) => f.type === \"shipping\" /* Shipping */\n        )\n      }\n    )\n  ] });\n};\nfunction ShippingOption({\n  option,\n  fulfillmentSetId,\n  locationId\n}) {\n  const prompt = usePrompt();\n  const { t } = useTranslation();\n  const isStoreOption = isOptionEnabledInStore(option);\n  const { mutateAsync } = useDeleteShippingOption(option.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"stockLocations.shippingOptions.delete.confirmation\", {\n        name: option.name\n      }),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      verificationText: option.name,\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"stockLocations.shippingOptions.delete.successToast\", {\n            name: option.name\n          })\n        );\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-3 py-2\", children: [\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex-1\", children: /* @__PURE__ */ jsxs(Text, { size: \"small\", weight: \"plus\", children: [\n      option.name,\n      \" - \",\n      option.shipping_profile.name,\n      \" (\",\n      formatProvider(option.provider_id),\n      \")\"\n    ] }) }),\n    /* @__PURE__ */ jsx2(\n      Badge,\n      {\n        className: \"mr-4\",\n        color: isStoreOption ? \"grey\" : \"purple\",\n        size: \"2xsmall\",\n        rounded: \"full\",\n        children: isStoreOption ? t(\"general.store\") : t(\"general.admin\")\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      ActionMenu,\n      {\n        groups: [\n          {\n            actions: [\n              {\n                icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                label: t(\"stockLocations.shippingOptions.edit.action\"),\n                to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${option.service_zone_id}/shipping-option/${option.id}/edit`\n              },\n              {\n                label: t(\"stockLocations.shippingOptions.pricing.action\"),\n                icon: /* @__PURE__ */ jsx2(CurrencyDollar, {}),\n                disabled: option.price_type === \"calculated\" /* Calculated */,\n                to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${option.service_zone_id}/shipping-option/${option.id}/pricing`\n              }\n            ]\n          },\n          {\n            actions: [\n              {\n                label: t(\"actions.delete\"),\n                icon: /* @__PURE__ */ jsx2(Trash, {}),\n                onClick: handleDelete\n              }\n            ]\n          }\n        ]\n      }\n    )\n  ] });\n}\nfunction ServiceZoneOptions({\n  zone,\n  locationId,\n  fulfillmentSetId,\n  type\n}) {\n  const { t } = useTranslation();\n  const shippingOptions = zone.shipping_options.filter(\n    (o) => !isReturnOption(o)\n  );\n  const returnOptions = zone.shipping_options.filter((o) => isReturnOption(o));\n  return /* @__PURE__ */ jsxs(\"div\", { children: [\n    /* @__PURE__ */ jsx2(Divider, { variant: \"dashed\" }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4 px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { className: \"item-center flex justify-between\", children: [\n        /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-subtle txt-small self-center font-medium\", children: t(`stockLocations.shippingOptions.create.${type}.label`) }),\n        /* @__PURE__ */ jsx2(\n          LinkButton,\n          {\n            to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/shipping-option/create`,\n            children: t(\"stockLocations.shippingOptions.create.action\")\n          }\n        )\n      ] }),\n      !!shippingOptions.length && /* @__PURE__ */ jsx2(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md\", children: shippingOptions.map((o) => /* @__PURE__ */ jsx2(\n        ShippingOption,\n        {\n          option: o,\n          locationId,\n          fulfillmentSetId\n        },\n        o.id\n      )) })\n    ] }),\n    /* @__PURE__ */ jsx2(Divider, { variant: \"dashed\" }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4 px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { className: \"item-center flex justify-between\", children: [\n        /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-subtle txt-small self-center font-medium\", children: t(\"stockLocations.shippingOptions.create.returns.label\") }),\n        /* @__PURE__ */ jsx2(\n          LinkButton,\n          {\n            to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/shipping-option/create?is_return`,\n            children: t(\"stockLocations.shippingOptions.create.action\")\n          }\n        )\n      ] }),\n      !!returnOptions.length && /* @__PURE__ */ jsx2(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md\", children: returnOptions.map((o) => /* @__PURE__ */ jsx2(\n        ShippingOption,\n        {\n          option: o,\n          locationId,\n          fulfillmentSetId\n        },\n        o.id\n      )) })\n    ] })\n  ] });\n}\nfunction ServiceZone({\n  zone,\n  locationId,\n  fulfillmentSetId,\n  type\n}) {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const [open, setOpen] = useState(true);\n  const { mutateAsync: deleteZone } = useDeleteFulfillmentServiceZone(\n    fulfillmentSetId,\n    zone.id\n  );\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"stockLocations.serviceZones.delete.confirmation\", {\n        name: zone.name\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await deleteZone(void 0, {\n      onError: (e) => {\n        toast.error(e.message);\n      },\n      onSuccess: () => {\n        toast.success(\n          t(\"stockLocations.serviceZones.delete.successToast\", {\n            name: zone.name\n          })\n        );\n      }\n    });\n  };\n  const countries2 = useMemo(() => {\n    const countryGeoZones = zone.geo_zones.filter((g) => g.type === \"country\");\n    const countries3 = countryGeoZones.map(\n      ({ country_code }) => countries.find((c) => c.iso_2 === country_code)\n    ).filter((c) => !!c);\n    if (process.env.NODE_ENV === \"development\" && countryGeoZones.length !== countries3.length) {\n      console.warn(\n        \"Some countries are missing in the static countries list\",\n        countryGeoZones.filter((g) => !countries3.find((c) => c.iso_2 === g.country_code)).map((g) => g.country_code)\n      );\n    }\n    return countries3.sort((c1, c2) => c1.name.localeCompare(c2.name));\n  }, [zone.geo_zones]);\n  const [shippingOptionsCount, returnOptionsCount] = useMemo(() => {\n    const options = zone.shipping_options;\n    const optionsCount = options.filter((o) => !isReturnOption(o)).length;\n    const returnOptionsCount2 = options.filter(isReturnOption).length;\n    return [optionsCount, returnOptionsCount2];\n  }, [zone.shipping_options]);\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-row items-center justify-between gap-x-4 px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(IconAvatar, { children: /* @__PURE__ */ jsx2(Map, {}) }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grow-1 flex flex-1 flex-col\", children: [\n        /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: zone.name }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-2\", children: [\n          /* @__PURE__ */ jsx2(\n            ListSummary,\n            {\n              variant: \"base\",\n              list: countries2.map((c) => c.display_name),\n              inline: true,\n              n: 1\n            }\n          ),\n          /* @__PURE__ */ jsx2(\"span\", { children: \"\\xB7\" }),\n          /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle txt-small\", children: t(`stockLocations.shippingOptions.fields.count.${type}`, {\n            count: shippingOptionsCount\n          }) }),\n          /* @__PURE__ */ jsx2(\"span\", { children: \"\\xB7\" }),\n          /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle txt-small\", children: t(\"stockLocations.shippingOptions.fields.count.returns\", {\n            count: returnOptionsCount\n          }) })\n        ] })\n      ] }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex grow-0 items-center gap-4\", children: [\n        /* @__PURE__ */ jsx2(\n          IconButton,\n          {\n            size: \"small\",\n            onClick: () => setOpen((s) => !s),\n            variant: \"transparent\",\n            children: /* @__PURE__ */ jsx2(\n              TriangleDownMini,\n              {\n                style: {\n                  transform: `rotate(${!open ? 0 : 180}deg)`,\n                  transition: \".2s transform ease-in-out\"\n                }\n              }\n            )\n          }\n        ),\n        /* @__PURE__ */ jsx2(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t(\"actions.edit\"),\n                    icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                    to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/edit`\n                  },\n                  {\n                    label: t(\"stockLocations.serviceZones.manageAreas.action\"),\n                    icon: /* @__PURE__ */ jsx2(Map, {}),\n                    to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/areas`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t(\"actions.delete\"),\n                    icon: /* @__PURE__ */ jsx2(Trash, {}),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    open && /* @__PURE__ */ jsx2(\n      ServiceZoneOptions,\n      {\n        fulfillmentSetId,\n        locationId,\n        type,\n        zone\n      }\n    )\n  ] });\n}\nfunction FulfillmentSet(props) {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { fulfillmentSet, locationName, locationId, type } = props;\n  const fulfillmentSetExists = !!fulfillmentSet;\n  const hasServiceZones = !!fulfillmentSet?.service_zones.length;\n  const { mutateAsync: createFulfillmentSet } = useCreateStockLocationFulfillmentSet(locationId);\n  const { mutateAsync: deleteFulfillmentSet } = useDeleteFulfillmentSet(\n    fulfillmentSet?.id\n  );\n  const handleCreate = async () => {\n    await createFulfillmentSet(\n      {\n        name: `${locationName} ${type === \"pickup\" /* Pickup */ ? \"pick up\" : type}`,\n        type\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(`stockLocations.fulfillmentSets.enable.${type}`));\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  };\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(`stockLocations.fulfillmentSets.disable.confirmation`, {\n        name: fulfillmentSet?.name\n      }),\n      confirmText: t(\"actions.disable\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await deleteFulfillmentSet(void 0, {\n      onSuccess: () => {\n        toast.success(t(`stockLocations.fulfillmentSets.disable.${type}`));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  const groups = fulfillmentSet ? [\n    {\n      actions: [\n        {\n          icon: /* @__PURE__ */ jsx2(Plus, {}),\n          label: t(\"stockLocations.serviceZones.create.action\"),\n          to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSet.id}/service-zones/create`\n        }\n      ]\n    },\n    {\n      actions: [\n        {\n          icon: /* @__PURE__ */ jsx2(Trash, {}),\n          label: t(\"actions.disable\"),\n          onClick: handleDelete\n        }\n      ]\n    }\n  ] : [\n    {\n      actions: [\n        {\n          icon: /* @__PURE__ */ jsx2(Plus, {}),\n          label: t(\"actions.enable\"),\n          onClick: handleCreate\n        }\n      ]\n    }\n  ];\n  return /* @__PURE__ */ jsx2(Container, { className: \"p-0\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col divide-y\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(`stockLocations.fulfillmentSets.${type}.header`) }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-4\", children: [\n        /* @__PURE__ */ jsx2(StatusBadge, { color: fulfillmentSetExists ? \"green\" : \"grey\", children: t(\n          fulfillmentSetExists ? \"statuses.enabled\" : \"statuses.disabled\"\n        ) }),\n        /* @__PURE__ */ jsx2(ActionMenu, { groups })\n      ] })\n    ] }),\n    fulfillmentSetExists && !hasServiceZones && /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-center py-8 pt-6\", children: /* @__PURE__ */ jsx2(\n      NoRecords,\n      {\n        message: t(\"stockLocations.serviceZones.fields.noRecords\"),\n        className: \"h-fit\",\n        action: {\n          to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSet.id}/service-zones/create`,\n          label: t(\"stockLocations.serviceZones.create.action\")\n        }\n      }\n    ) }),\n    hasServiceZones && /* @__PURE__ */ jsx2(\"div\", { className: \"flex flex-col divide-y\", children: fulfillmentSet?.service_zones.map((zone) => /* @__PURE__ */ jsx2(\n      ServiceZone,\n      {\n        zone,\n        type,\n        locationId,\n        fulfillmentSetId: fulfillmentSet.id\n      },\n      zone.id\n    )) })\n  ] }) });\n}\nvar Actions = ({ location }) => {\n  const navigate = useNavigate();\n  const { t } = useTranslation();\n  const { mutateAsync } = useDeleteStockLocation(location.id);\n  const prompt = usePrompt();\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"stockLocations.delete.confirmation\", {\n        name: location.name\n      }),\n      verificationText: location.name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"stockLocations.create.successToast\", {\n            name: location.name\n          })\n        );\n        navigate(\"/settings/locations\", { replace: true });\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx2(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `edit`\n            },\n            {\n              icon: /* @__PURE__ */ jsx2(ArchiveBox, {}),\n              label: t(\"stockLocations.edit.viewInventory\"),\n              to: `/inventory?location_id=${location.id}`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx2(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/locations/location-detail/components/location-sales-channels-section/locations-sales-channels-section.tsx\nimport { Channels, PencilSquare as PencilSquare2 } from \"@medusajs/icons\";\nimport { Container as Container2, Heading as Heading2, Text as Text2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nfunction LocationsSalesChannelsSection({\n  location\n}) {\n  const { t } = useTranslation2();\n  const { count } = useSalesChannels({ limit: 1, fields: \"id\" });\n  const hasConnectedChannels = !!location.sales_channels?.length;\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"flex flex-col px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"stockLocations.salesChannels.header\") }),\n      /* @__PURE__ */ jsx3(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"sales-channels\",\n                  icon: /* @__PURE__ */ jsx3(PencilSquare2, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    hasConnectedChannels ? /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col gap-y-4 pt-4\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-[28px_1fr] items-center gap-x-3\", children: [\n        /* @__PURE__ */ jsx3(IconAvatar, { children: /* @__PURE__ */ jsx3(Channels, { className: \"text-ui-fg-subtle\" }) }),\n        /* @__PURE__ */ jsx3(\n          ListSummary,\n          {\n            n: 3,\n            className: \"text-ui-fg-base\",\n            inline: true,\n            list: location.sales_channels?.map((sc) => sc.name) ?? []\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx3(Text2, { className: \"text-ui-fg-subtle\", size: \"small\", leading: \"compact\", children: t(\"stockLocations.salesChannels.connectedTo\", {\n        count: location.sales_channels?.length,\n        total: count\n      }) })\n    ] }) : /* @__PURE__ */ jsx3(\n      NoRecords,\n      {\n        className: \"h-fit pb-2 pt-6\",\n        action: {\n          label: t(\"stockLocations.salesChannels.action\"),\n          to: \"sales-channels\"\n        },\n        message: t(\"stockLocations.salesChannels.noChannels\")\n      }\n    )\n  ] });\n}\nvar locations_sales_channels_section_default = LocationsSalesChannelsSection;\n\n// src/routes/locations/location-detail/components/location-fulfillment-providers-section/location-fulfillment-providers-section.tsx\nimport { HandTruck, PencilSquare as PencilSquare3 } from \"@medusajs/icons\";\nimport { Container as Container3, Heading as Heading3 } from \"@medusajs/ui\";\nimport { Fragment as Fragment2 } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nfunction LocationsFulfillmentProvidersSection({\n  location\n}) {\n  const { t } = useTranslation3();\n  const { fulfillment_providers } = useFulfillmentProviders({\n    stock_location_id: location.id,\n    fields: \"id\",\n    is_enabled: true\n  });\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"flex flex-col px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between\", children: [\n      /* @__PURE__ */ jsx4(Heading3, { level: \"h2\", children: t(\"stockLocations.fulfillmentProviders.header\") }),\n      /* @__PURE__ */ jsx4(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"fulfillment-providers\",\n                  icon: /* @__PURE__ */ jsx4(PencilSquare3, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    fulfillment_providers?.length ? /* @__PURE__ */ jsx4(\"div\", { className: \"flex flex-col gap-y-4 pt-4\", children: /* @__PURE__ */ jsx4(\"div\", { className: \"grid grid-cols-[28px_1fr] items-center gap-x-3 gap-y-3\", children: fulfillment_providers?.map((fulfillmentProvider) => {\n      return /* @__PURE__ */ jsxs3(Fragment2, { children: [\n        /* @__PURE__ */ jsx4(IconAvatar, { children: /* @__PURE__ */ jsx4(HandTruck, { className: \"text-ui-fg-subtle\" }) }),\n        /* @__PURE__ */ jsx4(\"div\", { className: \"txt-compact-small\", children: formatProvider(fulfillmentProvider.id) })\n      ] }, fulfillmentProvider.id);\n    }) }) }) : /* @__PURE__ */ jsx4(\n      NoRecords,\n      {\n        className: \"h-fit pb-2 pt-6 text-center\",\n        action: {\n          label: t(\"stockLocations.fulfillmentProviders.action\"),\n          to: \"fulfillment-providers\"\n        },\n        message: t(\"stockLocations.fulfillmentProviders.noProviders\")\n      }\n    )\n  ] });\n}\nvar location_fulfillment_providers_section_default = LocationsFulfillmentProvidersSection;\n\n// src/routes/locations/location-detail/location-detail.tsx\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar LocationDetail = () => {\n  const initialData = useLoaderData();\n  const { location_id } = useParams();\n  const {\n    stock_location: location,\n    isPending: isLoading,\n    isError,\n    error\n  } = useStockLocation(\n    location_id,\n    { fields: LOCATION_DETAILS_FIELD },\n    { initialData }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !location) {\n    return /* @__PURE__ */ jsx5(TwoColumnPageSkeleton, { mainSections: 3, sidebarSections: 2, showJSON: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs4(\n    TwoColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"location.details.after\"),\n        before: getWidgets(\"location.details.before\"),\n        sideAfter: getWidgets(\"location.details.side.after\"),\n        sideBefore: getWidgets(\"location.details.side.before\")\n      },\n      data: location,\n      showJSON: true,\n      hasOutlet: true,\n      children: [\n        /* @__PURE__ */ jsx5(TwoColumnPage.Main, { children: /* @__PURE__ */ jsx5(LocationGeneralSection, { location }) }),\n        /* @__PURE__ */ jsxs4(TwoColumnPage.Sidebar, { children: [\n          /* @__PURE__ */ jsx5(locations_sales_channels_section_default, { location }),\n          /* @__PURE__ */ jsx5(location_fulfillment_providers_section_default, { location })\n        ] })\n      ]\n    }\n  );\n};\nexport {\n  LocationDetailBreadcrumb as Breadcrumb,\n  LocationDetail as Component,\n  locationLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA,yBAAoB;AA0DpB,mBAAkC;AAGlC,IAAAA,sBAA4C;AAwf5C,IAAAC,sBAA2C;AA8D3C,IAAAC,gBAAsC;AAEtC,IAAAC,sBAA2C;AAmD3C,IAAAA,sBAA2C;AA3qB3C,IAAI,yBAAyB;AAI7B,IAAI,2BAA2B,CAAC,UAAU;AACxC,QAAM,EAAE,YAAY,IAAI,MAAM,UAAU,CAAC;AACzC,QAAM,EAAE,eAAe,IAAI;AAAA,IACzB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,WAAW;AAAA,IAC9B;AAAA,EACF;AACA,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,eAAe,KAAK,CAAC;AACtE;AAGA,IAAI,gBAAgB,CAAC,QAAQ;AAAA,EAC3B,UAAU,wBAAwB,OAAO,IAAI;AAAA,IAC3C,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,SAAS,YAAY,IAAI,MAAM,cAAc,SAAS,IAAI;AAAA,IACxD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,iBAAiB,OAAO,EAAE,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,cAAc,EAAE;AAC9B,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AA8BA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AA5JN;AA6JE,aAAuB,0BAAK,8BAAU,EAAE,UAAU;AAAA,QAChC,oBAAAC,KAAK,WAAW,EAAE,WAAW,OAAO,cAA0B,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAC9I,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAA,KAAK,SAAS,EAAE,UAAU,SAAS,KAAK,CAAC;AAAA,YACzC,oBAAAA,KAAK,MAAM,EAAE,WAAW,+BAA+B,UAAU,oBAAoB,EAAE,SAAS,SAAS,QAAQ,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,MAClJ,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,SAAS,EAAE,SAAS,CAAC;AAAA,IAC5C,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,YAAY,SAAS;AAAA,QACrB,cAAc,SAAS;AAAA,QACvB,MAAM;AAAA,QACN,iBAAgB,cAAS,qBAAT,mBAA2B;AAAA,UACzC,CAAC,MAAM,EAAE,SAAS;AAAA;AAAA,MAEtB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,YAAY,SAAS;AAAA,QACrB,cAAc,SAAS;AAAA,QACvB,MAAM;AAAA,QACN,iBAAgB,cAAS,qBAAT,mBAA2B;AAAA,UACzC,CAAC,MAAM,EAAE,SAAS;AAAA;AAAA,MAEtB;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,gBAAgB,uBAAuB,MAAM;AACnD,QAAM,EAAE,YAAY,IAAI,wBAAwB,OAAO,EAAE;AACzD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,sDAAsD;AAAA,QACnE,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,MACD,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,kBAAkB,OAAO;AAAA,MACzB,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,sDAAsD;AAAA,YACtD,MAAM,OAAO;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,QACvF,oBAAAA,KAAK,OAAO,EAAE,WAAW,UAAU,cAA0B,0BAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,UAAU;AAAA,MACjI,OAAO;AAAA,MACP;AAAA,MACA,OAAO,iBAAiB;AAAA,MACxB;AAAA,MACA,eAAe,OAAO,WAAW;AAAA,MACjC;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,OAAO,gBAAgB,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU,gBAAgB,EAAE,eAAe,IAAI,EAAE,eAAe;AAAA,MAClE;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,OAAO,EAAE,4CAA4C;AAAA,gBACrD,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,OAAO,eAAe,oBAAoB,OAAO,EAAE;AAAA,cAC/I;AAAA,cACA;AAAA,gBACE,OAAO,EAAE,+CAA+C;AAAA,gBACxD,UAAsB,oBAAAA,KAAK,gBAAgB,CAAC,CAAC;AAAA,gBAC7C,UAAU,OAAO,eAAe;AAAA,gBAChC,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,OAAO,eAAe,oBAAoB,OAAO,EAAE;AAAA,cAC/I;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,OAAO,EAAE,gBAAgB;AAAA,gBACzB,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,gBACpC,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,SAAS,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,kBAAkB,KAAK,iBAAiB;AAAA,IAC5C,CAAC,MAAM,CAAC,eAAe,CAAC;AAAA,EAC1B;AACA,QAAM,gBAAgB,KAAK,iBAAiB,OAAO,CAAC,MAAM,eAAe,CAAC,CAAC;AAC3E,aAAuB,0BAAK,OAAO,EAAE,UAAU;AAAA,QAC7B,oBAAAA,KAAK,SAAS,EAAE,SAAS,SAAS,CAAC;AAAA,QACnC,0BAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,UACpE,0BAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,YACrE,oBAAAA,KAAK,QAAQ,EAAE,WAAW,uDAAuD,UAAU,EAAE,yCAAyC,IAAI,QAAQ,EAAE,CAAC;AAAA,YACrJ,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,KAAK,EAAE;AAAA,YACjG,UAAU,EAAE,8CAA8C;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,MACH,CAAC,CAAC,gBAAgB,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,uEAAuE,UAAU,gBAAgB,IAAI,CAAC,UAAsB,oBAAAA;AAAA,QAC/L;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,QACA,EAAE;AAAA,MACJ,CAAC,EAAE,CAAC;AAAA,IACN,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,SAAS,EAAE,SAAS,SAAS,CAAC;AAAA,QACnC,0BAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,UACpE,0BAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,YACrE,oBAAAA,KAAK,QAAQ,EAAE,WAAW,uDAAuD,UAAU,EAAE,qDAAqD,EAAE,CAAC;AAAA,YACrJ,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,KAAK,EAAE;AAAA,YACjG,UAAU,EAAE,8CAA8C;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,MACH,CAAC,CAAC,cAAc,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,uEAAuE,UAAU,cAAc,IAAI,CAAC,UAAsB,oBAAAA;AAAA,QAC3L;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,QACA,EAAE;AAAA,MACJ,CAAC,EAAE,CAAC;AAAA,IACN,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAS,IAAI;AACrC,QAAM,EAAE,aAAa,WAAW,IAAI;AAAA,IAClC;AAAA,IACA,KAAK;AAAA,EACP;AACA,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,mDAAmD;AAAA,QAChE,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,WAAW,QAAQ;AAAA,MACvB,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,MACA,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,mDAAmD;AAAA,YACnD,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,iBAAa,sBAAQ,MAAM;AAC/B,UAAM,kBAAkB,KAAK,UAAU,OAAO,CAAC,MAAM,EAAE,SAAS,SAAS;AACzE,UAAM,aAAa,gBAAgB;AAAA,MACjC,CAAC,EAAE,aAAa,MAAM,UAAU,KAAK,CAAC,MAAM,EAAE,UAAU,YAAY;AAAA,IACtE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACnB,QAA8C,gBAAgB,WAAW,WAAW,QAAQ;AAC1F,cAAQ;AAAA,QACN;AAAA,QACA,gBAAgB,OAAO,CAAC,MAAM,CAAC,WAAW,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY;AAAA,MAC9G;AAAA,IACF;AACA,WAAO,WAAW,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,cAAc,GAAG,IAAI,CAAC;AAAA,EACnE,GAAG,CAAC,KAAK,SAAS,CAAC;AACnB,QAAM,CAAC,sBAAsB,kBAAkB,QAAI,sBAAQ,MAAM;AAC/D,UAAM,UAAU,KAAK;AACrB,UAAM,eAAe,QAAQ,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,EAAE;AAC/D,UAAM,sBAAsB,QAAQ,OAAO,cAAc,EAAE;AAC3D,WAAO,CAAC,cAAc,mBAAmB;AAAA,EAC3C,GAAG,CAAC,KAAK,gBAAgB,CAAC;AAC1B,aAAuB,0BAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,QACzD,0BAAK,OAAO,EAAE,WAAW,gEAAgE,UAAU;AAAA,UACjG,oBAAAA,KAAK,YAAY,EAAE,cAA0B,oBAAAA,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;AAAA,UAC5D,0BAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,YAChE,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,KAAK,KAAK,CAAC;AAAA,YACrF,0BAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,cAC5D,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,MAAM,WAAW,IAAI,CAAC,MAAM,EAAE,YAAY;AAAA,cAC1C,QAAQ;AAAA,cACR,GAAG;AAAA,YACL;AAAA,UACF;AAAA,cACgB,oBAAAA,KAAK,QAAQ,EAAE,UAAU,IAAO,CAAC;AAAA,cACjC,oBAAAA,KAAK,MAAM,EAAE,WAAW,+BAA+B,UAAU,EAAE,+CAA+C,IAAI,IAAI;AAAA,YACxI,OAAO;AAAA,UACT,CAAC,EAAE,CAAC;AAAA,cACY,oBAAAA,KAAK,QAAQ,EAAE,UAAU,IAAO,CAAC;AAAA,cACjC,oBAAAA,KAAK,MAAM,EAAE,WAAW,+BAA+B,UAAU,EAAE,uDAAuD;AAAA,YACxI,OAAO;AAAA,UACT,CAAC,EAAE,CAAC;AAAA,QACN,EAAE,CAAC;AAAA,MACL,EAAE,CAAC;AAAA,UACa,0BAAK,OAAO,EAAE,WAAW,kCAAkC,UAAU;AAAA,YACnE,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC;AAAA,YAChC,SAAS;AAAA,YACT,cAA0B,oBAAAA;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,WAAW,UAAU,CAAC,OAAO,IAAI,GAAG;AAAA,kBACpC,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,YACgB,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,cAAc;AAAA,oBACvB,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,KAAK,EAAE;AAAA,kBACnG;AAAA,kBACA;AAAA,oBACE,OAAO,EAAE,gDAAgD;AAAA,oBACzD,UAAsB,oBAAAA,KAAK,KAAK,CAAC,CAAC;AAAA,oBAClC,IAAI,uBAAuB,UAAU,oBAAoB,gBAAgB,iBAAiB,KAAK,EAAE;AAAA,kBACnG;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,gBAAgB;AAAA,oBACzB,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,oBACpC,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,YAAwB,oBAAAA;AAAA,MACtB;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,gBAAgB,cAAc,YAAY,KAAK,IAAI;AAC3D,QAAM,uBAAuB,CAAC,CAAC;AAC/B,QAAM,kBAAkB,CAAC,EAAC,iDAAgB,cAAc;AACxD,QAAM,EAAE,aAAa,qBAAqB,IAAI,qCAAqC,UAAU;AAC7F,QAAM,EAAE,aAAa,qBAAqB,IAAI;AAAA,IAC5C,iDAAgB;AAAA,EAClB;AACA,QAAM,eAAe,YAAY;AAC/B,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,GAAG,YAAY,IAAI,SAAS,WAAwB,YAAY,IAAI;AAAA,QAC1E;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQ,EAAE,yCAAyC,IAAI,EAAE,CAAC;AAAA,QAClE;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,uDAAuD;AAAA,QACpE,MAAM,iDAAgB;AAAA,MACxB,CAAC;AAAA,MACD,aAAa,EAAE,iBAAiB;AAAA,MAChC,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,qBAAqB,QAAQ;AAAA,MACjC,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,0CAA0C,IAAI,EAAE,CAAC;AAAA,MACnE;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,SAAS,iBAAiB;AAAA,IAC9B;AAAA,MACE,SAAS;AAAA,QACP;AAAA,UACE,UAAsB,oBAAAA,KAAK,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO,EAAE,2CAA2C;AAAA,UACpD,IAAI,uBAAuB,UAAU,oBAAoB,eAAe,EAAE;AAAA,QAC5E;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,UACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,UACpC,OAAO,EAAE,iBAAiB;AAAA,UAC1B,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,SAAS;AAAA,QACP;AAAA,UACE,UAAsB,oBAAAA,KAAK,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO,EAAE,gBAAgB;AAAA,UACzB,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAA,KAAK,WAAW,EAAE,WAAW,OAAO,cAA0B,0BAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,QAChI,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAA,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,kCAAkC,IAAI,SAAS,EAAE,CAAC;AAAA,UAC3F,0BAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,YAC5D,oBAAAA,KAAK,aAAa,EAAE,OAAO,uBAAuB,UAAU,QAAQ,UAAU;AAAA,UAC5F,uBAAuB,qBAAqB;AAAA,QAC9C,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,CAAC;AAAA,MAC7C,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,wBAAwB,CAAC,uBAAmC,oBAAAA,KAAK,OAAO,EAAE,WAAW,8CAA8C,cAA0B,oBAAAA;AAAA,MAC3J;AAAA,MACA;AAAA,QACE,SAAS,EAAE,8CAA8C;AAAA,QACzD,WAAW;AAAA,QACX,QAAQ;AAAA,UACN,IAAI,uBAAuB,UAAU,oBAAoB,eAAe,EAAE;AAAA,UAC1E,OAAO,EAAE,2CAA2C;AAAA,QACtD;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,uBAAmC,oBAAAA,KAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU,iDAAgB,cAAc,IAAI,CAAC,aAAyB,oBAAAA;AAAA,MAC1J;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB,eAAe;AAAA,MACnC;AAAA,MACA,KAAK;AAAA,IACP,GAAG,CAAC;AAAA,EACN,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,UAAU,CAAC,EAAE,SAAS,MAAM;AAC9B,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,YAAY,IAAI,uBAAuB,SAAS,EAAE;AAC1D,QAAM,SAAS,UAAU;AACzB,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,sCAAsC;AAAA,QACnD,MAAM,SAAS;AAAA,MACjB,CAAC;AAAA,MACD,kBAAkB,SAAS;AAAA,MAC3B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,sCAAsC;AAAA,YACtC,MAAM,SAAS;AAAA,UACjB,CAAC;AAAA,QACH;AACA,iBAAS,uBAAuB,EAAE,SAAS,KAAK,CAAC;AAAA,MACnD;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI;AAAA,YACN;AAAA,YACA;AAAA,cACE,UAAsB,oBAAAA,KAAK,YAAY,CAAC,CAAC;AAAA,cACzC,OAAO,EAAE,mCAAmC;AAAA,cAC5C,IAAI,0BAA0B,SAAS,EAAE;AAAA,YAC3C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,cACpC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,8BAA8B;AAAA,EACrC;AACF,GAAG;AAppBH;AAqpBE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,MAAM,IAAI,iBAAiB,EAAE,OAAO,GAAG,QAAQ,KAAK,CAAC;AAC7D,QAAM,uBAAuB,CAAC,GAAC,cAAS,mBAAT,mBAAyB;AACxD,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,2BAA2B,UAAU;AAAA,QACzE,oBAAAA,MAAM,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,UACvE,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,qCAAqC,EAAE,CAAC;AAAA,UAClF,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,2BAAuC,oBAAAD,MAAM,OAAO,EAAE,WAAW,8BAA8B,UAAU;AAAA,UACvF,oBAAAA,MAAM,OAAO,EAAE,WAAW,kDAAkD,UAAU;AAAA,YACpF,oBAAAC,KAAK,YAAY,EAAE,cAA0B,oBAAAA,KAAK,UAAU,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC;AAAA,YACjG,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,QAAM,cAAS,mBAAT,mBAAyB,IAAI,CAAC,OAAO,GAAG,UAAS,CAAC;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,MAAO,EAAE,WAAW,qBAAqB,MAAM,SAAS,SAAS,WAAW,UAAU,EAAE,4CAA4C;AAAA,QACvJ,QAAO,cAAS,mBAAT,mBAAyB;AAAA,QAChC,OAAO;AAAA,MACT,CAAC,EAAE,CAAC;AAAA,IACN,EAAE,CAAC,QAAoB,oBAAAA;AAAA,MACrB;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,QAAQ;AAAA,UACN,OAAO,EAAE,qCAAqC;AAAA,UAC9C,IAAI;AAAA,QACN;AAAA,QACA,SAAS,EAAE,yCAAyC;AAAA,MACtD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,2CAA2C;AAQ/C,SAAS,qCAAqC;AAAA,EAC5C;AACF,GAAG;AACD,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,sBAAsB,IAAI,wBAAwB;AAAA,IACxD,mBAAmB,SAAS;AAAA,IAC5B,QAAQ;AAAA,IACR,YAAY;AAAA,EACd,CAAC;AACD,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,2BAA2B,UAAU;AAAA,QACzE,oBAAAA,MAAM,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,UACvE,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,4CAA4C,EAAE,CAAC;AAAA,UACzF,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,KACH,+DAAuB,cAAyB,oBAAAA,KAAK,OAAO,EAAE,WAAW,8BAA8B,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,0DAA0D,UAAU,+DAAuB,IAAI,CAAC,wBAAwB;AAChR,iBAAuB,oBAAAD,MAAM,cAAAE,UAAW,EAAE,UAAU;AAAA,YAClC,oBAAAD,KAAK,YAAY,EAAE,cAA0B,oBAAAA,KAAK,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC;AAAA,YAClG,oBAAAA,KAAK,OAAO,EAAE,WAAW,qBAAqB,UAAU,eAAe,oBAAoB,EAAE,EAAE,CAAC;AAAA,MAClH,EAAE,GAAG,oBAAoB,EAAE;AAAA,IAC7B,GAAG,CAAC,EAAE,CAAC,QAAoB,oBAAAA;AAAA,MACzB;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,QAAQ;AAAA,UACN,OAAO,EAAE,4CAA4C;AAAA,UACrD,IAAI;AAAA,QACN;AAAA,QACA,SAAS,EAAE,iDAAiD;AAAA,MAC9D;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,iDAAiD;AAIrD,IAAI,iBAAiB,MAAM;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,YAAY,IAAI,UAAU;AAClC,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,IACA,EAAE,QAAQ,uBAAuB;AAAA,IACjC,EAAE,YAAY;AAAA,EAChB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,UAAU;AAC1B,eAAuB,oBAAAE,KAAK,uBAAuB,EAAE,cAAc,GAAG,iBAAiB,GAAG,UAAU,KAAK,CAAC;AAAA,EAC5G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,wBAAwB;AAAA,QAC1C,QAAQ,WAAW,yBAAyB;AAAA,QAC5C,WAAW,WAAW,6BAA6B;AAAA,QACnD,YAAY,WAAW,8BAA8B;AAAA,MACvD;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,oBAAAD,KAAK,cAAc,MAAM,EAAE,cAA0B,oBAAAA,KAAK,wBAAwB,EAAE,SAAS,CAAC,EAAE,CAAC;AAAA,YACjG,oBAAAC,MAAM,cAAc,SAAS,EAAE,UAAU;AAAA,cACvC,oBAAAD,KAAK,0CAA0C,EAAE,SAAS,CAAC;AAAA,cAC3D,oBAAAA,KAAK,gDAAgD,EAAE,SAAS,CAAC;AAAA,QACnF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsxs3", "jsx4", "Fragment2", "jsx5", "jsxs4"]}