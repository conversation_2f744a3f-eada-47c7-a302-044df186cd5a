"use client"

import Image from "next/image"
import { useState } from "react"
import { useTranslation } from "react-i18next"

import { TCartItemVariant } from "types/cart"

import { updateLineItem } from "@lib/data/cart"
import { PAGE_PATH } from "utils/path"

import Typography from "components/ui/typography"

import { formatDescription, formatTitle } from "@lib/util/format-description"
import { convertCurrencyToLocale } from "@lib/util/money"
import DeleteButton from "@modules/common/components/delete-button"
import LineItemPrice from "@modules/common/components/line-item-price"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import { ProductQuantity } from "@modules/products/components/product-quantity"
import SafeHTML from "components/ui/safe-html"
import { TableCell, TableRow } from "components/ui/table"

type ItemProps = {
  item: TCartItemVariant
  currencyCode?: string
  isCard?: boolean
}

const Item = ({ item, currencyCode, isCard = false }: ItemProps) => {
  const [updating, setUpdating] = useState(false)
  const { t, i18n } = useTranslation("cart")
  const { t: tProduct } = useTranslation("product_detail")
  const { handle } = item.variant?.product ?? {}

  const changeQuantity = async (quantity: number) => {
    setUpdating(true)

    await updateLineItem({
      lineId: item.id,
      quantity,
    })
      .catch((err) => {
        console.error("Error updating quantity:", err.message)
      })
      .finally(() => {
        setUpdating(false)
      })
  }

  // TODO: Update this to grab the actual max inventory
  const maxQtyFromInventory = 10
  const maxQuantity = item.variant?.manage_inventory ? 10 : maxQtyFromInventory

  const variantImage = item.product_variant_images?.length
    ? item.product_variant_images[0]?.image_url
    : item.variant?.product?.thumbnail || ""

  return isCard ? (
    <div className="flex gap-x-4">
      {/* Thumbnail */}
      <div className="h-28 w-28 flex-shrink-0 md:h-20 md:w-20 lg:h-28 lg:w-28">
        <Image
          src={item.variant?.product?.thumbnail || "/images/no-image.svg"}
          alt={item.variant?.product?.title || ""}
          width={112}
          height={112}
          className="h-full w-full rounded-lg object-cover"
          sizes="100vw"
        />
      </div>

      {/* Content */}
      <div className="flex flex-grow flex-col justify-between">
        <div className="flex items-start justify-between">
          <Typography
            variant="p"
            size="base"
            className="line-clamp-2 font-semibold text-gray-800"
          >
            {formatTitle(
              item.variant?.product?.title || item.product_title,
              i18n.language
            )}
          </Typography>
          <Typography
            variant="p"
            size="base"
            className="font-semibold text-primary-main"
          >
            {convertCurrencyToLocale({
              amount: item.unit_price ?? 0,
              currency_code: currencyCode ?? "",
            })}
          </Typography>
        </div>

        {/* Row 2: Variant Info */}
        <div className="">
          <Typography variant="p" className="text-sm font-normal text-gray-700">
            {item?.product?.variants?.length &&
            item?.product?.variants?.length > 1
              ? item.variant?.title
              : ""}
          </Typography>
        </div>

        {/* Row 3: Note & Description */}
        <Typography variant="p" size="sm" className="font-medium text-gray-700">
          <strong className="text-gray-800">{t("note")}: </strong>{" "}
          {(item as any).metadata.note || t("no")}
        </Typography>

        {/* <SafeHTML
          html={formatDescription(
            item.product_description,
            i18n.language,
            tProduct("product_action.default_description")
          )}
          className="line-clamp-2 max-w-[400px] text-sm font-normal italic leading-6 text-gray-800"
        /> */}

        {/* Row 4: Quantity và Delete */}
        <div className="flex items-center justify-between">
          <ProductQuantity
            quantity={item.quantity}
            setQuantity={changeQuantity}
            disabled={updating}
            maxQuantity={maxQuantity}
          />
          <DeleteButton
            id={item.id}
            data-testid="product-delete-button"
            type={"icon"}
          />
        </div>
      </div>
    </div>
  ) : (
    <TableRow>
      <TableCell>
        <div className="mt-4 flex w-full items-center gap-x-4">
          <LocalizedClientLink
            href={PAGE_PATH.PRODUCT.detail(
              handle ?? PAGE_PATH.COLLECTIONS.root
            )}
            className={"relative flex aspect-square"}
          >
            <Image
              src={variantImage || "/images/no-image.svg"}
              alt={item.product_title ?? "Product image"}
              width={70}
              height={70}
              className="max-h-[70px] rounded-md object-cover"
            />
          </LocalizedClientLink>
          <div className="flex flex-col gap-y-2">
            <Typography
              variant="p"
              size="base"
              className="font-semibold text-gray-800"
            >
              {formatTitle(
                item.product_title || item.variant?.product?.title,
                i18n.language
              )}
            </Typography>
            <Typography
              variant="p"
              size="sm"
              className="font-medium text-gray-700"
            >
              <strong className="text-gray-800">{t("note")}: </strong>{" "}
              {(item as any).metadata.note || t("no")}
            </Typography>

            <SafeHTML
              html={formatDescription(
                item.product_description,
                i18n.language,
                tProduct("product_action.default_description")
              )}
              className="line-clamp-2 max-w-[400px] text-sm font-normal italic leading-6 text-gray-800"
            />
          </div>
        </div>
      </TableCell>
      <TableCell>
        <Typography variant="p" className="font-normal text-gray-700">
          {item?.product?.variants?.length &&
          item?.product?.variants?.length > 1
            ? item.variant?.title
            : t("no")}
        </Typography>
      </TableCell>
      <TableCell>
        <ProductQuantity
          quantity={item.quantity}
          setQuantity={changeQuantity}
          disabled={updating}
          maxQuantity={maxQuantity}
        />
      </TableCell>
      <TableCell>
        <Typography variant="p" className="font-normal text-gray-800">
          {convertCurrencyToLocale({
            amount: item.unit_price ?? 0,
            currency_code: currencyCode ?? "",
          })}
        </Typography>
      </TableCell>
      <TableCell>
        <LineItemPrice item={item} />
      </TableCell>
      <TableCell>
        <DeleteButton
          id={item.id}
          data-testid="product-delete-button"
          type={"icon"}
        />
      </TableCell>
    </TableRow>
  )
}

export default Item
