import {
  require_lodash,
  useSelectedParams
} from "./chunk-QX6SXRUW.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Input
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-YEDAFXMB.mjs
var import_react = __toESM(require_react(), 1);
var import_lodash = __toESM(require_lodash(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DataTableSearch = ({
  placeholder,
  prefix,
  autofocus
}) => {
  const { t } = useTranslation();
  const placeholderText = placeholder || t("general.search");
  const selectedParams = useSelectedParams({
    param: "q",
    prefix,
    multiple: false
  });
  const query = selectedParams.get();
  const debouncedOnChange = (0, import_react.useCallback)(
    (0, import_lodash.debounce)((e) => {
      const value = e.target.value;
      if (!value) {
        selectedParams.delete();
      } else {
        selectedParams.add(value);
      }
    }, 500),
    [selectedParams]
  );
  (0, import_react.useEffect)(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);
  return (0, import_jsx_runtime.jsx)(
    Input,
    {
      autoComplete: "off",
      name: "q",
      type: "search",
      size: "small",
      autoFocus: autofocus,
      defaultValue: (query == null ? void 0 : query[0]) || void 0,
      onChange: debouncedOnChange,
      placeholder: placeholderText
    }
  );
};

export {
  DataTableSearch
};
//# sourceMappingURL=chunk-733UOLIB.js.map
