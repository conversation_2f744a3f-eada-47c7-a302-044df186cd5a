{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4380d7._.js", "server/edge/chunks/[root of the server]__a96925._.js", "server/edge/chunks/edge-wrapper_882b0c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(|\\\\.json|\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|assets|_next\\/static|favicon.ico|_next\\/image|images|robots.txt|public|static).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|assets|_next/static|favicon.ico|_next/image|images|robots.txt|public|static).*)"}], "wasm": [], "assets": [{"name": "server/edge/chunks/_4380d7._.js.map", "filePath": "server/edge/chunks/_4380d7._.js.map"}, {"name": "server/edge/chunks/_4380d7._.js", "filePath": "server/edge/chunks/_4380d7._.js"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js.map", "filePath": "server/edge/chunks/[root of the server]__a96925._.js.map"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js", "filePath": "server/edge/chunks/[root of the server]__a96925._.js"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js.map", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js.map"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "P05OuEt5furfyEF66ygpaKH/w9vOxc9kXCOUgMXeQGw=", "__NEXT_PREVIEW_MODE_ID": "b74d009e2942eef62e93aba1e3482271", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fca125301c0e77f9dbebc380b2a39f1abd87293b22e1b76a22e62e479aaf07da", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b17602c0e196d084d13fce80a20ec53f3d6dd921bf9fe46c890580d5945e9ba7"}}}, "sortedMiddleware": ["/"], "functions": {}}