import {
  useDeleteProductTypeAction
} from "./chunk-AMEHXNKS.js";
import {
  useProductTypeTableColumns
} from "./chunk-DGPYW6Y5.js";
import {
  useProductTypeTableQuery
} from "./chunk-ITWRYKT3.js";
import "./chunk-OW6OIDUA.js";
import "./chunk-7HUCBNCQ.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-32T72GVU.js";
import {
  useProductTypeTableFilters
} from "./chunk-P5T2IZP5.js";
import "./chunk-IONS3C54.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useProductTypes
} from "./chunk-HREJMEGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-type-list-EKTGZP6P.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ProductTypeRowActions = ({
  productType
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTypeAction(
    productType.id,
    productType.value
  );
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              to: `/settings/product-types/${productType.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var PAGE_SIZE = 20;
var ProductTypeListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTypeTableQuery({
    pageSize: PAGE_SIZE
  });
  const { product_types, count, isLoading, isError, error } = useProductTypes(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useProductTypeTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    columns,
    data: product_types,
    count,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: t("productTypes.domain") }),
        (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("productTypes.subtitle") })
      ] }),
      (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        filters,
        isLoading,
        columns,
        pageSize: PAGE_SIZE,
        count,
        orderBy: [
          { key: "value", label: t("fields.value") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        navigateTo: ({ original }) => original.id,
        queryObject: raw,
        pagination: true,
        search: true
      }
    )
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTypeTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(ProductTypeRowActions, { productType: row.original });
        }
      })
    ],
    [base]
  );
};
var ProductTypeList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_type.list.after"),
        before: getWidgets("product_type.list.before")
      },
      children: (0, import_jsx_runtime3.jsx)(ProductTypeListTable, {})
    }
  );
};
export {
  ProductTypeList as Component
};
//# sourceMappingURL=product-type-list-EKTGZP6P-SOYDA2LO.js.map
