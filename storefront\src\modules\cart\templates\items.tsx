"use client"

import { useTranslation } from "react-i18next"

import { TCartCustomField } from "types/cart"

import { convertCurrencyToLocale } from "@lib/util/money"
import Item from "@modules/cart/components/item"
import Divider from "@modules/common/components/divider"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import { Button } from "components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card"
import { Input } from "components/ui/input"
import { ScrollArea } from "components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "components/ui/table"
import Typography from "components/ui/typography"
import { PAGE_PATH } from "utils/path"
import EmptyCartMessage from "../components/empty-cart-message"
import { getCheckoutStep } from "../templates/summary"
type ItemsTemplateProps = {
  cart: TCartCustomField
  isCard?: boolean
}

const ItemsTemplate = ({ cart, isCard = false }: ItemsTemplateProps) => {
  const { t } = useTranslation("cart")

  const TABLE_HEAD = [
    { id: "name", label: t("table_headers.name") },
    { id: "option", label: t("table_headers.options") },
    { id: "quantity", label: t("table_headers.quantity") },
    { id: "price", label: t("table_headers.price"), align: "right" },
    { id: "total", label: t("table_headers.total_amount"), align: "right" },
    { id: "action", label: "" },
  ]
  const step = getCheckoutStep(cart!)

  // console.log("cart length", cart?.items?.length)

  return isCard ? (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          <Typography
            variant="p"
            size="md"
            className="mt-3 font-bold text-gray-950"
          >
            {t("cart")}
            <span className="ml-2 text-base font-normal lowercase text-gray-500">
              ({cart?.items?.length} {t("items")})
            </span>
          </Typography>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-y-8 !pt-0">
        <div className="flex flex-col gap-y-4">
          {cart?.items && cart?.items?.length > 0 ? (
            cart?.items
              .sort((a, b) => {
                return (a.created_at ?? "") > (b.created_at ?? "") ? -1 : 1
              })
              .map((item, index) => {
                return (
                  <Item
                    key={item.id}
                    item={item}
                    currencyCode={cart?.currency_code}
                    isCard={true}
                  />
                )
              })
          ) : (
            <EmptyCartMessage />
          )}
        </div>
        <Divider />
        {cart?.items && cart?.items?.length > 0 && (
          <Input className="w-full sm:h-18" placeholder={t("brewing_note")} />
        )}
        <div className="flex items-center justify-between">
          <Typography variant="p" size="md" className="font-bold text-gray-950">
            {t("subtotal")}:
          </Typography>
          <Typography
            variant="p"
            size="lg"
            className="font-semibold text-primary-main"
          >
            {convertCurrencyToLocale({
              amount: cart?.item_subtotal ?? 0,
              currency_code: cart?.currency_code ?? "",
            })}
          </Typography>
        </div>
        
      </CardContent>
    </Card>
  ) : (
    <ScrollArea className="h-[360px] overflow-y-auto">
      <Table>
        <TableHeader className="sticky top-0 z-50 bg-white">
          <TableRow className="bg-gray-100">
            {TABLE_HEAD.map((head) => (
              <TableHead key={head.id} className="font-semibold text-gray-700">
                {head.label}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {cart?.items && cart?.items?.length > 0 ? (
            cart?.items
              .sort((a, b) => {
                return (a.created_at ?? "") > (b.created_at ?? "") ? -1 : 1
              })
              .map((item, index) => {
                return (
                  <Item
                    key={item.id}
                    item={item}
                    currencyCode={cart?.currency_code}
                  />
                )
              })
          ) : (
            <TableRow className="h-[300px]">
              <TableCell
                colSpan={TABLE_HEAD.length}
                className="text-center align-middle"
              >
                <EmptyCartMessage />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </ScrollArea>
  )
}

export default ItemsTemplate
