{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-ZQRKUG6J.mjs"], "sourcesContent": ["import {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\n\n// src/lib/validation.ts\nimport i18next from \"i18next\";\nimport { z } from \"zod\";\nvar optionalInt = z.union([z.string(), z.number()]).optional().refine(\n  (value) => {\n    if (value === \"\" || value === void 0) {\n      return true;\n    }\n    return Number.isInteger(castNumber(value));\n  },\n  {\n    message: i18next.t(\"validation.mustBeInt\")\n  }\n).refine(\n  (value) => {\n    if (value === \"\" || value === void 0) {\n      return true;\n    }\n    return castNumber(value) >= 0;\n  },\n  {\n    message: i18next.t(\"validation.mustBePositive\")\n  }\n);\nvar optionalFloat = z.union([z.string(), z.number()]).optional().refine(\n  (value) => {\n    if (value === \"\" || value === void 0) {\n      return true;\n    }\n    return castNumber(value) >= 0;\n  },\n  {\n    message: i18next.t(\"validation.mustBePositive\")\n  }\n);\nvar metadataFormSchema = z.array(\n  z.object({\n    key: z.string(),\n    value: z.unknown(),\n    isInitial: z.boolean().optional(),\n    isDeleted: z.boolean().optional(),\n    isIgnored: z.boolean().optional()\n  })\n);\nfunction partialFormValidation(form, fields, schema) {\n  form.clearErrors(fields);\n  const values = fields.reduce((acc, key) => {\n    acc[key] = form.getValues(key);\n    return acc;\n  }, {});\n  const validationResult = schema.safeParse(values);\n  if (!validationResult.success) {\n    validationResult.error.errors.forEach(({ path, message, code }) => {\n      form.setError(path.join(\".\"), {\n        type: code,\n        message\n      });\n    });\n    return false;\n  }\n  return true;\n}\n\nexport {\n  optionalInt,\n  optionalFloat,\n  partialFormValidation\n};\n"], "mappings": ";;;;;;;;;;;AAOA,IAAI,cAAc,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE;AAAA,EAC7D,CAAC,UAAU;AACT,QAAI,UAAU,MAAM,UAAU,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,WAAO,OAAO,UAAU,WAAW,KAAK,CAAC;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,SAAS,SAAQ,EAAE,sBAAsB;AAAA,EAC3C;AACF,EAAE;AAAA,EACA,CAAC,UAAU;AACT,QAAI,UAAU,MAAM,UAAU,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,WAAO,WAAW,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,SAAS,SAAQ,EAAE,2BAA2B;AAAA,EAChD;AACF;AACA,IAAI,gBAAgB,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE;AAAA,EAC/D,CAAC,UAAU;AACT,QAAI,UAAU,MAAM,UAAU,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,WAAO,WAAW,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,SAAS,SAAQ,EAAE,2BAA2B;AAAA,EAChD;AACF;AACA,IAAI,qBAAqB,EAAE;AAAA,EACzB,EAAE,OAAO;AAAA,IACP,KAAK,EAAE,OAAO;AAAA,IACd,OAAO,EAAE,QAAQ;AAAA,IACjB,WAAW,EAAE,QAAQ,EAAE,SAAS;AAAA,IAChC,WAAW,EAAE,QAAQ,EAAE,SAAS;AAAA,IAChC,WAAW,EAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,CAAC;AACH;AACA,SAAS,sBAAsB,MAAM,QAAQ,QAAQ;AACnD,OAAK,YAAY,MAAM;AACvB,QAAM,SAAS,OAAO,OAAO,CAAC,KAAK,QAAQ;AACzC,QAAI,GAAG,IAAI,KAAK,UAAU,GAAG;AAC7B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,mBAAmB,OAAO,UAAU,MAAM;AAChD,MAAI,CAAC,iBAAiB,SAAS;AAC7B,qBAAiB,MAAM,OAAO,QAAQ,CAAC,EAAE,MAAM,SAAS,KAAK,MAAM;AACjE,WAAK,SAAS,KAAK,KAAK,GAAG,GAAG;AAAA,QAC5B,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": []}