{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-4GJJIXM6.mjs"], "sourcesContent": ["import {\n  ordersQueryKeys\n} from \"./chunk-RWU2ZKWZ.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/payments.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar PAYMENT_QUERY_KEY = \"payment\";\nvar paymentQueryKeys = queryKeysFactory(PAYMENT_QUERY_KEY);\nvar usePaymentProviders = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.payment.listPaymentProviders(query),\n    queryKey: [],\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCapturePayment = (orderId, paymentId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.payment.capture(paymentId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRefundPayment = (orderId, paymentId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.payment.refund(paymentId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  usePaymentProviders,\n  useCapturePayment,\n  useRefundPayment\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,oBAAoB;AACxB,IAAI,mBAAmB,iBAAiB,iBAAiB;AACzD,IAAI,sBAAsB,CAAC,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,QAAQ,qBAAqB,KAAK;AAAA,IACjE,UAAU,CAAC;AAAA,IACX,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,SAAS,WAAW,YAAY;AACvD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,QAAQ,QAAQ,WAAW,OAAO;AAAA,IACrE,WAAW,CAAC,MAAM,WAAW,YAAY;AA/B7C;AAgCM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mBAAmB,CAAC,SAAS,WAAW,YAAY;AACtD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,QAAQ,OAAO,WAAW,OAAO;AAAA,IACpE,WAAW,CAAC,MAAM,WAAW,YAAY;AA9C7C;AA+CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}