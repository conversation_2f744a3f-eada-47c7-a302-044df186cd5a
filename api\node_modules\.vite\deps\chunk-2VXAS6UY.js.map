{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-NOAFLTPV.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/routes/regions/common/hooks/use-countries.tsx\nimport { json } from \"react-router-dom\";\nvar acceptedOrderKeys = [\"name\", \"code\"];\nvar useCountries = ({\n  countries,\n  q,\n  order = \"name\",\n  limit,\n  offset = 0\n}) => {\n  const data = countries.slice(offset, offset + limit);\n  if (order) {\n    const direction = order.startsWith(\"-\") ? -1 : 1;\n    const key = order.replace(\"-\", \"\");\n    if (!acceptedOrderKeys.includes(key)) {\n      console.log(\"The key ${key} is not a valid order key\");\n      throw json(`The key ${key} is not a valid order key`, 500);\n    }\n    const sortKey = key === \"code\" ? \"iso_2\" : \"name\";\n    data.sort((a, b) => {\n      if (a[sortKey] === null && b[sortKey] === null) {\n        return 0;\n      }\n      if (a[sortKey] === null) {\n        return direction;\n      }\n      if (b[sortKey] === null) {\n        return -direction;\n      }\n      return a[sortKey] > b[sortKey] ? direction : -direction;\n    });\n  }\n  if (q) {\n    const query = q.toLowerCase();\n    const results = countries.filter(\n      (c) => c.name.toLowerCase().includes(query) || c.iso_2.toLowerCase().includes(query)\n    );\n    return {\n      countries: results,\n      count: results.length\n    };\n  }\n  return {\n    countries: data,\n    count: countries.length\n  };\n};\n\n// src/routes/regions/common/hooks/use-country-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCountryTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"display_name\", {\n        header: t(\"fields.name\"),\n        cell: ({ getValue }) => getValue()\n      }),\n      columnHelper.accessor(\"iso_2\", {\n        header: t(\"fields.code\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(\"span\", { className: \"uppercase\", children: getValue() })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/regions/common/hooks/use-country-table-query.tsx\nvar useCountryTableQuery = ({\n  pageSize,\n  prefix\n}) => {\n  const raw = useQueryParams([\"order\", \"q\", \"offset\"], prefix);\n  const { offset, order, q } = raw;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? parseInt(offset, 10) : 0,\n    order,\n    q\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\nexport {\n  useCountries,\n  useCountryTableColumns,\n  useCountryTableQuery\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAsDA,mBAAwB;AAExB,yBAAoB;AAlDpB,IAAI,oBAAoB,CAAC,QAAQ,MAAM;AACvC,IAAI,eAAe,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,SAAS;AACX,MAAM;AACJ,QAAM,OAAO,UAAU,MAAM,QAAQ,SAAS,KAAK;AACnD,MAAI,OAAO;AACT,UAAM,YAAY,MAAM,WAAW,GAAG,IAAI,KAAK;AAC/C,UAAM,MAAM,MAAM,QAAQ,KAAK,EAAE;AACjC,QAAI,CAAC,kBAAkB,SAAS,GAAG,GAAG;AACpC,cAAQ,IAAI,yCAAyC;AACrD,YAAM,KAAK,WAAW,GAAG,6BAA6B,GAAG;AAAA,IAC3D;AACA,UAAM,UAAU,QAAQ,SAAS,UAAU;AAC3C,SAAK,KAAK,CAAC,GAAG,MAAM;AAClB,UAAI,EAAE,OAAO,MAAM,QAAQ,EAAE,OAAO,MAAM,MAAM;AAC9C,eAAO;AAAA,MACT;AACA,UAAI,EAAE,OAAO,MAAM,MAAM;AACvB,eAAO;AAAA,MACT;AACA,UAAI,EAAE,OAAO,MAAM,MAAM;AACvB,eAAO,CAAC;AAAA,MACV;AACA,aAAO,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,YAAY,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACA,MAAI,GAAG;AACL,UAAM,QAAQ,EAAE,YAAY;AAC5B,UAAM,UAAU,UAAU;AAAA,MACxB,CAAC,MAAM,EAAE,KAAK,YAAY,EAAE,SAAS,KAAK,KAAK,EAAE,MAAM,YAAY,EAAE,SAAS,KAAK;AAAA,IACrF;AACA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,OAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AACF;AAOA,IAAI,eAAe,mBAAmB;AACtC,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,gBAAgB;AAAA,QACpC,QAAQ,EAAE,aAAa;AAAA,QACvB,MAAM,CAAC,EAAE,SAAS,MAAM,SAAS;AAAA,MACnC,CAAC;AAAA,MACD,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,aAAa;AAAA,QACvB,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,QAAQ,EAAE,WAAW,aAAa,UAAU,SAAS,EAAE,CAAC;AAAA,MACtG,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AACJ,QAAM,MAAM,eAAe,CAAC,SAAS,KAAK,QAAQ,GAAG,MAAM;AAC3D,QAAM,EAAE,QAAQ,OAAO,EAAE,IAAI;AAC7B,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,SAAS,QAAQ,EAAE,IAAI;AAAA,IACxC;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": []}