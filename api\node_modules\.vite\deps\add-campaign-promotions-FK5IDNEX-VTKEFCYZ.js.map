{"version": 3, "sources": ["../../@medusajs/dashboard/dist/add-campaign-promotions-FK5IDNEX.mjs"], "sourcesContent": ["import {\n  usePromotionTableColumns,\n  usePromotionTableQuery\n} from \"./chunk-LADMQDYD.mjs\";\nimport \"./chunk-KD3INMVA.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  usePromotionTableFilters\n} from \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useAddOrRemoveCampaignPromotions,\n  useCampaign,\n  usePromotions\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/add-campaign-promotions/add-campaign-promotions.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/campaigns/add-campaign-promotions/components/add-campaign-promotions-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddCampaignPromotionsSchema = zod.object({\n  promotion_ids: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 50;\nvar AddCampaignPromotionsForm = ({\n  campaign\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: { promotion_ids: [] },\n    resolver: zodResolver(AddCampaignPromotionsSchema)\n  });\n  const { setValue } = form;\n  const { mutateAsync, isPending } = useAddOrRemoveCampaignPromotions(\n    campaign.id\n  );\n  const [rowSelection, setRowSelection] = useState({});\n  const updater = (fn) => {\n    const state = typeof fn === \"function\" ? fn(rowSelection) : fn;\n    const ids = Object.keys(state);\n    setValue(\"promotion_ids\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(state);\n  };\n  const { searchParams, raw } = usePromotionTableQuery({ pageSize: PAGE_SIZE });\n  const {\n    promotions,\n    count,\n    isPending: isLoading\n  } = usePromotions({ ...searchParams }, { placeholderData: keepPreviousData });\n  const columns = useColumns();\n  const filters = usePromotionTableFilters();\n  const { table } = useDataTable({\n    data: promotions ?? [],\n    columns,\n    enableRowSelection: (row) => {\n      return row.original.campaign_id !== campaign.id;\n    },\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE,\n    count,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    },\n    meta: {\n      campaignId: campaign.id,\n      currencyCode: campaign?.budget?.currency_code,\n      budgetType: campaign?.budget?.type\n    }\n  });\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      { add: values.promotion_ids },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"campaigns.promotions.toast.success\", {\n              count: values.promotion_ids.length\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => toast.error(error.message)\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: form.formState.errors.promotion_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.promotion_ids.message }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex size-full flex-col overflow-y-auto\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            count,\n            columns,\n            pageSize: PAGE_SIZE,\n            isLoading,\n            filters,\n            orderBy: [\n              { key: \"code\", label: t(\"fields.code\") },\n              { key: \"type\", label: t(\"fields.type\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            queryObject: raw,\n            layout: \"fill\",\n            pagination: true,\n            search: \"autofocus\",\n            noRecords: {\n              message: t(\"campaigns.promotions.add.list.noRecordsMessage\")\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = usePromotionTableColumns();\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row, table }) => {\n          const { campaignId, currencyCode, budgetType } = table.options.meta;\n          const isTypeSpend = budgetType === \"spend\";\n          const isAdded = row.original.campaign_id === campaignId;\n          const isAddedToADiffCampaign = !!row.original.campaign_id && row.original.campaign_id !== campaignId;\n          const currencyMismatch = isTypeSpend && row.original.application_method?.currency_code !== currencyCode;\n          const isSelected = row.getIsSelected() || isAdded;\n          const isIndeterminate = currencyMismatch || isAddedToADiffCampaign;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isIndeterminate ? \"indeterminate\" : isSelected,\n              disabled: isAdded || isAddedToADiffCampaign || currencyMismatch,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isAddedToADiffCampaign) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"campaigns.promotions.alreadyAddedDiffCampaign\", {\n                  name: row.original?.campaign?.name\n                }),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          if (currencyMismatch) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"campaigns.promotions.currencyMismatch\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          if (isAdded) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"campaigns.promotions.alreadyAdded\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n};\n\n// src/routes/campaigns/add-campaign-promotions/add-campaign-promotions.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar AddCampaignPromotions = () => {\n  const { id } = useParams();\n  const { campaign, isError, error } = useCampaign(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: campaign && /* @__PURE__ */ jsx2(AddCampaignPromotionsForm, { campaign }) });\n};\nexport {\n  AddCampaignPromotions as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,mBAAkC;AAIlC,yBAA0B;AA6L1B,IAAAA,sBAA4B;AA5L5B,IAAI,8BAAkC,WAAO;AAAA,EAC3C,eAAmB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAC9C,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AApEN;AAqEE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,EAAE,eAAe,CAAC,EAAE;AAAA,IACnC,UAAU,EAAY,2BAA2B;AAAA,EACnD,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,aAAa,UAAU,IAAI;AAAA,IACjC,SAAS;AAAA,EACX;AACA,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,QAAQ,OAAO,OAAO,aAAa,GAAG,YAAY,IAAI;AAC5D,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,aAAS,iBAAiB,KAAK;AAAA,MAC7B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,KAAK;AAAA,EACvB;AACA,QAAM,EAAE,cAAc,IAAI,IAAI,uBAAuB,EAAE,UAAU,UAAU,CAAC;AAC5E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,IAAI,cAAc,EAAE,GAAG,aAAa,GAAG,EAAE,iBAAiB,iBAAiB,CAAC;AAC5E,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,yBAAyB;AACzC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA,oBAAoB,CAAC,QAAQ;AAC3B,aAAO,IAAI,SAAS,gBAAgB,SAAS;AAAA,IAC/C;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,YAAY,SAAS;AAAA,MACrB,eAAc,0CAAU,WAAV,mBAAkB;AAAA,MAChC,aAAY,0CAAU,WAAV,mBAAkB;AAAA,IAChC;AAAA,EACF,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ,EAAE,KAAK,OAAO,cAAc;AAAA,MAC5B;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,sCAAsC;AAAA,cACtC,OAAO,OAAO,cAAc;AAAA,YAC9B,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU,MAAM,MAAM,MAAM,OAAO;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yCAAyC,UAAU,KAAK,UAAU,OAAO,qBAAiC,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,cAAc,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACrR,wBAAI,gBAAgB,MAAM,EAAE,WAAW,2CAA2C,cAA0B;AAAA,UAC1H;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,QAAQ,OAAOA,GAAE,aAAa,EAAE;AAAA,cACvC,EAAE,KAAK,QAAQ,OAAOA,GAAE,aAAa,EAAE;AAAA,cACvC,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,WAAW;AAAA,cACT,SAASA,GAAE,gDAAgD;AAAA,YAC7D;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,yBAAyB;AACtC,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,sBAAsB,CAAC,CAAC,KAAK;AAAA,YACjE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AA7LlC;AA8LU,gBAAM,EAAE,YAAY,cAAc,WAAW,IAAI,MAAM,QAAQ;AAC/D,gBAAM,cAAc,eAAe;AACnC,gBAAM,UAAU,IAAI,SAAS,gBAAgB;AAC7C,gBAAM,yBAAyB,CAAC,CAAC,IAAI,SAAS,eAAe,IAAI,SAAS,gBAAgB;AAC1F,gBAAM,mBAAmB,iBAAe,SAAI,SAAS,uBAAb,mBAAiC,mBAAkB;AAC3F,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,kBAAkB,oBAAoB;AAC5C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS,kBAAkB,kBAAkB;AAAA,cAC7C,UAAU,WAAW,0BAA0B;AAAA,cAC/C,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,wBAAwB;AAC1B,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,iDAAiD;AAAA,kBAC1D,OAAM,eAAI,aAAJ,mBAAc,aAAd,mBAAwB;AAAA,gBAChC,CAAC;AAAA,gBACD,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,cAAI,kBAAkB;AACpB,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,uCAAuC;AAAA,gBAClD,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,cAAI,SAAS;AACX,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,mCAAmC;AAAA,gBAC9C,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACF;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,SAAS,MAAM,IAAI,YAAY,EAAE;AACnD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,gBAA4B,oBAAAA,KAAK,2BAA2B,EAAE,SAAS,CAAC,EAAE,CAAC;AACtI;", "names": ["import_jsx_runtime", "t", "jsx2"]}