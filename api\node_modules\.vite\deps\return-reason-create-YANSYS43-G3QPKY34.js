import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  useCreateReturnReason
} from "./chunk-GE5OJZGS.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/return-reason-create-YANSYS43.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ReturnReasonCreateSchema = z.object({
  value: z.string().min(1),
  label: z.string().min(1),
  description: z.string().optional()
});
var ReturnReasonCreateForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      value: "",
      label: "",
      description: ""
    },
    resolver: t(ReturnReasonCreateSchema)
  });
  const { mutateAsync, isPending } = useCreateReturnReason();
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(data, {
      onSuccess: ({ return_reason }) => {
        toast.success(
          t2("returnReasons.create.successToast", {
            label: return_reason.label
          })
        );
        handleSuccess(`../`);
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex size-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 justify-center overflow-auto px-6 py-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: t2("returnReasons.create.header") }) }),
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("returnReasons.create.subtitle") }) })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid gap-4 md:grid-cols-2", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "value",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(
                      Form.Label,
                      {
                        tooltip: t2("returnReasons.fields.value.tooltip"),
                        children: t2("returnReasons.fields.value.label")
                      }
                    ),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        ...field,
                        placeholder: t2(
                          "returnReasons.fields.value.placeholder"
                        )
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "label",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("returnReasons.fields.label.label") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        ...field,
                        placeholder: t2(
                          "returnReasons.fields.label.placeholder"
                        )
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "description",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("returnReasons.fields.description.label") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Textarea,
                    {
                      ...field,
                      placeholder: t2(
                        "returnReasons.fields.description.placeholder"
                      )
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ReturnReasonCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(ReturnReasonCreateForm, {}) });
};
export {
  ReturnReasonCreate as Component
};
//# sourceMappingURL=return-reason-create-YANSYS43-G3QPKY34.js.map
