{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-variant-manage-inventory-items-SNLRM4XR.mjs"], "sourcesContent": ["import {\n  VARIANT_DETAIL_FIELDS\n} from \"./chunk-EUTK2A3J.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useProductVariant,\n  useProductVariantsInventoryItemsBatch\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/product-variants/product-variant-manage-inventory-items/product-variant-manage-inventory-items.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/product-variants/product-variant-manage-inventory-items/components/manage-variant-inventory-items-form/manage-variant-inventory-items-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { XMarkMini } from \"@medusajs/icons\";\nimport { Button, Heading, IconButton, Input, Label, toast } from \"@medusajs/ui\";\nimport i18next from \"i18next\";\nimport { useFieldArray, useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ManageVariantInventoryItemsSchema = zod.object({\n  inventory: zod.array(\n    zod.object({\n      inventory_item_id: zod.string().min(1, i18next.t(\"products.variant.inventory.validation.itemId\")),\n      required_quantity: zod.union([zod.number(), zod.string()])\n    }).superRefine((data, ctx) => {\n      const quantity = data.required_quantity ? castNumber(data.required_quantity) : 0;\n      if (quantity < 1) {\n        ctx.addIssue({\n          code: zod.ZodIssueCode.custom,\n          message: i18next.t(\n            \"products.variant.inventory.validation.quantity\"\n          ),\n          path: [\"required_quantity\"]\n        });\n      }\n    })\n  )\n});\nfunction ManageVariantInventoryItemsForm({\n  variant\n}) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      inventory: variant.inventory_items.length ? variant.inventory_items.map((i) => ({\n        required_quantity: i.required_quantity,\n        inventory_item_id: i.inventory.id\n      })) : [\n        {\n          inventory_item_id: \"\",\n          required_quantity: \"\"\n        }\n      ]\n    },\n    resolver: zodResolver(ManageVariantInventoryItemsSchema)\n  });\n  const inventory = useFieldArray({\n    control: form.control,\n    name: `inventory`\n  });\n  const hasKit = inventory.fields.length > 1;\n  const items = useComboboxData({\n    queryKey: [\"inventory_items\"],\n    queryFn: (params) => sdk.admin.inventoryItem.list(params),\n    getOptions: (data) => data.inventory_items.map((item) => ({\n      label: `${item.title} ${item.sku ? `(${item.sku})` : \"\"}`,\n      value: item.id\n    })),\n    defaultValue: variant.inventory_items?.[0]?.inventory_item_id\n  });\n  const { mutateAsync, isPending } = useProductVariantsInventoryItemsBatch(\n    variant?.product_id\n  );\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const existingItems = {};\n    const selectedItems = {};\n    variant.inventory_items.forEach(\n      (i) => existingItems[i.inventory.id] = i.required_quantity\n    );\n    values.inventory.forEach((i) => selectedItems[i.inventory_item_id] = true);\n    const payload = {};\n    values.inventory.forEach((v) => {\n      if (v.inventory_item_id in existingItems) {\n        if (v.required_quantity !== existingItems[v.inventory_item_id]) {\n          payload.update = payload.update || [];\n          payload.update.push({\n            required_quantity: castNumber(v.required_quantity),\n            inventory_item_id: v.inventory_item_id,\n            variant_id: variant.id\n          });\n        }\n      } else {\n        payload.create = payload.create || [];\n        payload.create.push({\n          required_quantity: castNumber(v.required_quantity),\n          inventory_item_id: v.inventory_item_id,\n          variant_id: variant.id\n        });\n      }\n    });\n    variant.inventory_items.forEach((i) => {\n      if (!(i.inventory.id in selectedItems)) {\n        payload.delete = payload.delete || [];\n        payload.delete.push({\n          inventory_item_id: i.inventory.id,\n          variant_id: variant.id\n        });\n      }\n    });\n    await mutateAsync(payload, {\n      onSuccess: () => {\n        toast.success(t(\"products.variant.inventory.toast.itemsManageSuccess\"));\n        handleSuccess();\n      },\n      onError: (err) => {\n        toast.error(err.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex justify-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full flex-col gap-y-8 px-6 pt-12 md:w-[720px] md:pt-24\", children: [\n          /* @__PURE__ */ jsx(Heading, { children: t(\n            hasKit ? \"products.create.inventory.heading\" : \"fields.inventoryItems\"\n          ) }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"grid gap-y-4\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-start justify-between gap-x-4\", children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: variant.title }),\n                /* @__PURE__ */ jsx(Form.Hint, { children: t(\n                  hasKit ? \"products.create.inventory.label\" : \"fields.inventoryItem\"\n                ) })\n              ] }),\n              /* @__PURE__ */ jsx(\n                Button,\n                {\n                  size: \"small\",\n                  variant: \"secondary\",\n                  type: \"button\",\n                  onClick: () => {\n                    inventory.append({\n                      inventory_item_id: \"\",\n                      required_quantity: \"\"\n                    });\n                  },\n                  children: t(\"actions.add\")\n                }\n              )\n            ] }),\n            inventory.fields.map((inventoryItem, inventoryIndex) => /* @__PURE__ */ jsxs(\n              \"li\",\n              {\n                className: \"bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5\",\n                children: [\n                  /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-[min-content,1fr] items-center gap-1.5\", children: [\n                    /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center px-2 py-1.5\", children: /* @__PURE__ */ jsx(\n                      Label,\n                      {\n                        size: \"xsmall\",\n                        weight: \"plus\",\n                        className: \"text-ui-fg-subtle\",\n                        htmlFor: `inventory.${inventoryIndex}.inventory_item_id`,\n                        children: t(\"fields.item\")\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(\n                      Form.Field,\n                      {\n                        control: form.control,\n                        name: `inventory.${inventoryIndex}.inventory_item_id`,\n                        render: ({ field }) => {\n                          return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                              Combobox,\n                              {\n                                ...field,\n                                options: items.options,\n                                searchValue: items.searchValue,\n                                onSearchValueChange: items.onSearchValueChange,\n                                fetchNextPage: items.fetchNextPage,\n                                className: \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover\",\n                                placeholder: t(\n                                  \"products.create.inventory.itemPlaceholder\"\n                                )\n                              }\n                            ) }),\n                            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                          ] });\n                        }\n                      }\n                    ),\n                    /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center px-2 py-1.5\", children: /* @__PURE__ */ jsx(\n                      Label,\n                      {\n                        size: \"xsmall\",\n                        weight: \"plus\",\n                        className: \"text-ui-fg-subtle\",\n                        htmlFor: `inventory.${inventoryIndex}.required_quantity`,\n                        children: t(\"fields.quantity\")\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(\n                      Form.Field,\n                      {\n                        control: form.control,\n                        name: `inventory.${inventoryIndex}.required_quantity`,\n                        render: ({ field: { onChange, value, ...field } }) => {\n                          return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                              Input,\n                              {\n                                type: \"number\",\n                                className: \"bg-ui-bg-field-component\",\n                                min: 0,\n                                value,\n                                onChange,\n                                ...field,\n                                placeholder: t(\n                                  \"products.create.inventory.quantityPlaceholder\"\n                                )\n                              }\n                            ) }),\n                            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                          ] });\n                        }\n                      }\n                    )\n                  ] }),\n                  /* @__PURE__ */ jsx(\n                    IconButton,\n                    {\n                      type: \"button\",\n                      size: \"small\",\n                      variant: \"transparent\",\n                      className: \"text-ui-fg-muted\",\n                      onClick: () => inventory.remove(inventoryIndex),\n                      children: /* @__PURE__ */ jsx(XMarkMini, {})\n                    }\n                  )\n                ]\n              },\n              inventoryItem.id\n            ))\n          ] })\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/product-variants/product-variant-manage-inventory-items/product-variant-manage-inventory-items.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction ProductVariantManageInventoryItems() {\n  const { id, variant_id } = useParams();\n  const {\n    variant,\n    isPending: isLoading,\n    isError,\n    error\n  } = useProductVariant(id, variant_id, {\n    fields: VARIANT_DETAIL_FIELDS\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && variant && /* @__PURE__ */ jsx2(ManageVariantInventoryItemsForm, { variant }) });\n}\nexport {\n  ProductVariantManageInventoryItems as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,yBAA0B;AAkP1B,IAAAA,sBAA4B;AAjP5B,IAAI,oCAAwC,WAAO;AAAA,EACjD,WAAe;AAAA,IACT,WAAO;AAAA,MACT,mBAAuB,WAAO,EAAE,IAAI,GAAG,SAAQ,EAAE,8CAA8C,CAAC;AAAA,MAChG,mBAAuB,UAAM,CAAK,WAAO,GAAO,WAAO,CAAC,CAAC;AAAA,IAC3D,CAAC,EAAE,YAAY,CAAC,MAAM,QAAQ;AAC5B,YAAM,WAAW,KAAK,oBAAoB,WAAW,KAAK,iBAAiB,IAAI;AAC/E,UAAI,WAAW,GAAG;AAChB,YAAI,SAAS;AAAA,UACX,MAAU,aAAa;AAAA,UACvB,SAAS,SAAQ;AAAA,YACf;AAAA,UACF;AAAA,UACA,MAAM,CAAC,mBAAmB;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,gCAAgC;AAAA,EACvC;AACF,GAAG;AAnEH;AAoEE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,WAAW,QAAQ,gBAAgB,SAAS,QAAQ,gBAAgB,IAAI,CAAC,OAAO;AAAA,QAC9E,mBAAmB,EAAE;AAAA,QACrB,mBAAmB,EAAE,UAAU;AAAA,MACjC,EAAE,IAAI;AAAA,QACJ;AAAA,UACE,mBAAmB;AAAA,UACnB,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU,EAAY,iCAAiC;AAAA,EACzD,CAAC;AACD,QAAM,YAAY,cAAc;AAAA,IAC9B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,SAAS,UAAU,OAAO,SAAS;AACzC,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,UAAU,CAAC,iBAAiB;AAAA,IAC5B,SAAS,CAAC,WAAW,IAAI,MAAM,cAAc,KAAK,MAAM;AAAA,IACxD,YAAY,CAAC,SAAS,KAAK,gBAAgB,IAAI,CAAC,UAAU;AAAA,MACxD,OAAO,GAAG,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG,MAAM,EAAE;AAAA,MACvD,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,IACF,eAAc,mBAAQ,oBAAR,mBAA0B,OAA1B,mBAA8B;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI;AAAA,IACjC,mCAAS;AAAA,EACX;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,gBAAgB,CAAC;AACvB,UAAM,gBAAgB,CAAC;AACvB,YAAQ,gBAAgB;AAAA,MACtB,CAAC,MAAM,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE;AAAA,IAC3C;AACA,WAAO,UAAU,QAAQ,CAAC,MAAM,cAAc,EAAE,iBAAiB,IAAI,IAAI;AACzE,UAAM,UAAU,CAAC;AACjB,WAAO,UAAU,QAAQ,CAAC,MAAM;AAC9B,UAAI,EAAE,qBAAqB,eAAe;AACxC,YAAI,EAAE,sBAAsB,cAAc,EAAE,iBAAiB,GAAG;AAC9D,kBAAQ,SAAS,QAAQ,UAAU,CAAC;AACpC,kBAAQ,OAAO,KAAK;AAAA,YAClB,mBAAmB,WAAW,EAAE,iBAAiB;AAAA,YACjD,mBAAmB,EAAE;AAAA,YACrB,YAAY,QAAQ;AAAA,UACtB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,gBAAQ,SAAS,QAAQ,UAAU,CAAC;AACpC,gBAAQ,OAAO,KAAK;AAAA,UAClB,mBAAmB,WAAW,EAAE,iBAAiB;AAAA,UACjD,mBAAmB,EAAE;AAAA,UACrB,YAAY,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,YAAQ,gBAAgB,QAAQ,CAAC,MAAM;AACrC,UAAI,EAAE,EAAE,UAAU,MAAM,gBAAgB;AACtC,gBAAQ,SAAS,QAAQ,UAAU,CAAC;AACpC,gBAAQ,OAAO,KAAK;AAAA,UAClB,mBAAmB,EAAE,UAAU;AAAA,UAC/B,YAAY,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,YAAY,SAAS;AAAA,MACzB,WAAW,MAAM;AACf,cAAM,QAAQA,GAAE,qDAAqD,CAAC;AACtE,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,QAAQ;AAChB,cAAM,MAAM,IAAI,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,uBAAuB,cAA0B,yBAAK,OAAO,EAAE,WAAW,iEAAiE,UAAU;AAAA,cAC1L,wBAAI,SAAS,EAAE,UAAUA;AAAA,YACvC,SAAS,sCAAsC;AAAA,UACjD,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,gBAAgB,UAAU;AAAA,gBACjD,yBAAK,OAAO,EAAE,WAAW,4CAA4C,UAAU;AAAA,kBAC7E,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,oBAClD,wBAAI,KAAK,OAAO,EAAE,UAAU,QAAQ,MAAM,CAAC;AAAA,oBAC3C,wBAAI,KAAK,MAAM,EAAE,UAAUA;AAAA,kBACzC,SAAS,oCAAoC;AAAA,gBAC/C,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,MAAM;AAAA,kBACN,SAAS,MAAM;AACb,8BAAU,OAAO;AAAA,sBACf,mBAAmB;AAAA,sBACnB,mBAAmB;AAAA,oBACrB,CAAC;AAAA,kBACH;AAAA,kBACA,UAAUA,GAAE,aAAa;AAAA,gBAC3B;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,YACH,UAAU,OAAO,IAAI,CAAC,eAAe,uBAAmC;AAAA,cACtE;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,sBACQ,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,wBAC1F,wBAAI,OAAO,EAAE,WAAW,iCAAiC,cAA0B;AAAA,sBACjG;AAAA,sBACA;AAAA,wBACE,MAAM;AAAA,wBACN,QAAQ;AAAA,wBACR,WAAW;AAAA,wBACX,SAAS,aAAa,cAAc;AAAA,wBACpC,UAAUA,GAAE,aAAa;AAAA,sBAC3B;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa;AAAA,sBACd,KAAK;AAAA,sBACL;AAAA,wBACE,SAAS,KAAK;AAAA,wBACd,MAAM,aAAa,cAAc;AAAA,wBACjC,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gCACjC,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,8BAC5D;AAAA,8BACA;AAAA,gCACE,GAAG;AAAA,gCACH,SAAS,MAAM;AAAA,gCACf,aAAa,MAAM;AAAA,gCACnB,qBAAqB,MAAM;AAAA,gCAC3B,eAAe,MAAM;AAAA,gCACrB,WAAW;AAAA,gCACX,aAAaA;AAAA,kCACX;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF,EAAE,CAAC;AAAA,gCACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,0BAC3C,EAAE,CAAC;AAAA,wBACL;AAAA,sBACF;AAAA,oBACF;AAAA,wBACgB,wBAAI,OAAO,EAAE,WAAW,iCAAiC,cAA0B;AAAA,sBACjG;AAAA,sBACA;AAAA,wBACE,MAAM;AAAA,wBACN,QAAQ;AAAA,wBACR,WAAW;AAAA,wBACX,SAAS,aAAa,cAAc;AAAA,wBACpC,UAAUA,GAAE,iBAAiB;AAAA,sBAC/B;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa;AAAA,sBACd,KAAK;AAAA,sBACL;AAAA,wBACE,SAAS,KAAK;AAAA,wBACd,MAAM,aAAa,cAAc;AAAA,wBACjC,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,qCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gCACjC,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,8BAC5D;AAAA,8BACA;AAAA,gCACE,MAAM;AAAA,gCACN,WAAW;AAAA,gCACX,KAAK;AAAA,gCACL;AAAA,gCACA;AAAA,gCACA,GAAG;AAAA,gCACH,aAAaA;AAAA,kCACX;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF,EAAE,CAAC;AAAA,gCACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,0BAC3C,EAAE,CAAC;AAAA,wBACL;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,MAAM;AAAA,sBACN,SAAS;AAAA,sBACT,WAAW;AAAA,sBACX,SAAS,MAAM,UAAU,OAAO,cAAc;AAAA,sBAC9C,cAA0B,wBAAI,WAAW,CAAC,CAAC;AAAA,oBAC7C;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,cAAc;AAAA,YAChB,CAAC;AAAA,UACH,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,SAAS,qCAAqC;AAC5C,QAAM,EAAE,IAAI,WAAW,IAAI,UAAU;AACrC,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB,IAAI,YAAY;AAAA,IACpC,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,eAA2B,oBAAAA,KAAK,iCAAiC,EAAE,QAAQ,CAAC,EAAE,CAAC;AACxJ;", "names": ["import_jsx_runtime", "t", "jsx2"]}