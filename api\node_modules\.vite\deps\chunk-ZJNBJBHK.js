import {
  productsQueryKeys
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-ZJ3OFMHB.mjs
var CATEGORIES_QUERY_KEY = "categories";
var categoriesQueryKeys = queryKeysFactory(CATEGORIES_QUERY_KEY);
var useProductCategory = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: categoriesQueryKeys.detail(id, query),
    queryFn: () => sdk.admin.productCategory.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useProductCategories = (query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: categoriesQueryKeys.list(query),
    queryFn: () => sdk.admin.productCategory.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateProductCategory = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCategory.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductCategory = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCategory.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: categoriesQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteProductCategory = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.productCategory.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: categoriesQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductCategoryProducts = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCategory.updateProducts(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: categoriesQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  categoriesQueryKeys,
  useProductCategory,
  useProductCategories,
  useCreateProductCategory,
  useUpdateProductCategory,
  useDeleteProductCategory,
  useUpdateProductCategoryProducts
};
//# sourceMappingURL=chunk-ZJNBJBHK.js.map
