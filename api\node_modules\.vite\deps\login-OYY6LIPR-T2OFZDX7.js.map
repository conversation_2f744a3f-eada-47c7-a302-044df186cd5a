{"version": 3, "sources": ["../../@medusajs/dashboard/dist/login-OYY6LIPR.mjs"], "sourcesContent": ["import {\n  AvatarBox\n} from \"./chunk-Q6MSICBU.mjs\";\nimport \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  isFetchError\n} from \"./chunk-ONB3JEHR.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport {\n  useSignInWithEmailPass\n} from \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/login/login.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Alert, Button, Heading, Hint, Input, Text } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { Trans, useTranslation } from \"react-i18next\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport * as z from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar LoginSchema = z.object({\n  email: z.string().email(),\n  password: z.string()\n});\nvar Login = () => {\n  const { t } = useTranslation();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { getWidgets } = useExtension();\n  const from = location.state?.from?.pathname || \"/orders\";\n  const form = useForm({\n    resolver: zodResolver(LoginSchema),\n    defaultValues: {\n      email: \"\",\n      password: \"\"\n    }\n  });\n  const { mutateAsync, isPending } = useSignInWithEmailPass();\n  const handleSubmit = form.handleSubmit(async ({ email, password }) => {\n    await mutateAsync(\n      {\n        email,\n        password\n      },\n      {\n        onError: (error) => {\n          if (isFetchError(error)) {\n            if (error.status === 401) {\n              form.setError(\"email\", {\n                type: \"manual\",\n                message: error.message\n              });\n              return;\n            }\n          }\n          form.setError(\"root.serverError\", {\n            type: \"manual\",\n            message: error.message\n          });\n        },\n        onSuccess: () => {\n          navigate(from, { replace: true });\n        }\n      }\n    );\n  });\n  const serverError = form.formState.errors?.root?.serverError?.message;\n  const validationError = form.formState.errors.email?.message || form.formState.errors.password?.message;\n  return /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-subtle flex min-h-dvh w-dvw items-center justify-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"m-4 flex w-full max-w-[280px] flex-col items-center\", children: [\n    /* @__PURE__ */ jsx(AvatarBox, {}),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"mb-4 flex flex-col items-center\", children: [\n      /* @__PURE__ */ jsx(Heading, { children: t(\"login.title\") }),\n      /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle text-center\", children: t(\"login.hint\") })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full flex-col gap-y-3\", children: [\n      getWidgets(\"login.before\").map((Component, i) => {\n        return /* @__PURE__ */ jsx(Component, {}, i);\n      }),\n      /* @__PURE__ */ jsx(Form, { ...form, children: /* @__PURE__ */ jsxs(\n        \"form\",\n        {\n          onSubmit: handleSubmit,\n          className: \"flex w-full flex-col gap-y-6\",\n          children: [\n            /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-1\", children: [\n              /* @__PURE__ */ jsx(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: \"email\",\n                  render: ({ field }) => {\n                    return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Input,\n                      {\n                        autoComplete: \"email\",\n                        ...field,\n                        className: \"bg-ui-bg-field-component\",\n                        placeholder: t(\"fields.email\")\n                      }\n                    ) }) });\n                  }\n                }\n              ),\n              /* @__PURE__ */ jsx(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: \"password\",\n                  render: ({ field }) => {\n                    return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                      /* @__PURE__ */ jsx(Form.Label, {}),\n                      /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                        Input,\n                        {\n                          type: \"password\",\n                          autoComplete: \"current-password\",\n                          ...field,\n                          className: \"bg-ui-bg-field-component\",\n                          placeholder: t(\"fields.password\")\n                        }\n                      ) })\n                    ] });\n                  }\n                }\n              )\n            ] }),\n            validationError && /* @__PURE__ */ jsx(\"div\", { className: \"text-center\", children: /* @__PURE__ */ jsx(Hint, { className: \"inline-flex\", variant: \"error\", children: validationError }) }),\n            serverError && /* @__PURE__ */ jsx(\n              Alert,\n              {\n                className: \"bg-ui-bg-base items-center p-2\",\n                dismissible: true,\n                variant: \"error\",\n                children: serverError\n              }\n            ),\n            /* @__PURE__ */ jsx(Button, { className: \"w-full\", type: \"submit\", isLoading: isPending, children: t(\"actions.continueWithEmail\") })\n          ]\n        }\n      ) }),\n      getWidgets(\"login.after\").map((Component, i) => {\n        return /* @__PURE__ */ jsx(Component, {}, i);\n      })\n    ] }),\n    /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-muted txt-small my-6\", children: /* @__PURE__ */ jsx(\n      Trans,\n      {\n        i18nKey: \"login.forgotPassword\",\n        components: [\n          /* @__PURE__ */ jsx(\n            Link,\n            {\n              to: \"/reset-password\",\n              className: \"text-ui-fg-interactive transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover font-medium outline-none\"\n            },\n            \"reset-password-link\"\n          )\n        ]\n      }\n    ) })\n  ] }) });\n};\nexport {\n  Login as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,yBAA0B;AAC1B,IAAI,cAAgB,WAAO;AAAA,EACzB,OAAS,WAAO,EAAE,MAAM;AAAA,EACxB,UAAY,WAAO;AACrB,CAAC;AACD,IAAI,QAAQ,MAAM;AA3DlB;AA4DE,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,QAAM,SAAO,oBAAS,UAAT,mBAAgB,SAAhB,mBAAsB,aAAY;AAC/C,QAAM,OAAO,QAAQ;AAAA,IACnB,UAAU,EAAY,WAAW;AAAA,IACjC,eAAe;AAAA,MACb,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,uBAAuB;AAC1D,QAAM,eAAe,KAAK,aAAa,OAAO,EAAE,OAAO,SAAS,MAAM;AACpE,UAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,QACE,SAAS,CAAC,UAAU;AAClB,cAAI,aAAa,KAAK,GAAG;AACvB,gBAAI,MAAM,WAAW,KAAK;AACxB,mBAAK,SAAS,SAAS;AAAA,gBACrB,MAAM;AAAA,gBACN,SAAS,MAAM;AAAA,cACjB,CAAC;AACD;AAAA,YACF;AAAA,UACF;AACA,eAAK,SAAS,oBAAoB;AAAA,YAChC,MAAM;AAAA,YACN,SAAS,MAAM;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,QACA,WAAW,MAAM;AACf,mBAAS,MAAM,EAAE,SAAS,KAAK,CAAC;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,eAAc,sBAAK,UAAU,WAAf,mBAAuB,SAAvB,mBAA6B,gBAA7B,mBAA0C;AAC9D,QAAM,oBAAkB,UAAK,UAAU,OAAO,UAAtB,mBAA6B,cAAW,UAAK,UAAU,OAAO,aAAtB,mBAAgC;AAChG,aAAuB,wBAAI,OAAO,EAAE,WAAW,oEAAoE,cAA0B,yBAAK,OAAO,EAAE,WAAW,uDAAuD,UAAU;AAAA,QACrN,wBAAI,WAAW,CAAC,CAAC;AAAA,QACjB,yBAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,UACpE,wBAAI,SAAS,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,UAC3C,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,iCAAiC,UAAUA,GAAE,YAAY,EAAE,CAAC;AAAA,IACpH,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,gCAAgC,UAAU;AAAA,MACjF,WAAW,cAAc,EAAE,IAAI,CAAC,WAAW,MAAM;AAC/C,mBAAuB,wBAAI,WAAW,CAAC,GAAG,CAAC;AAAA,MAC7C,CAAC;AAAA,UACe,wBAAI,MAAM,EAAE,GAAG,MAAM,cAA0B;AAAA,QAC7D;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,gBACQ,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,kBAC1D;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM;AAAA,kBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,+BAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC9G;AAAA,sBACA;AAAA,wBACE,cAAc;AAAA,wBACd,GAAG;AAAA,wBACH,WAAW;AAAA,wBACX,aAAaA,GAAE,cAAc;AAAA,sBAC/B;AAAA,oBACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM;AAAA,kBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,+BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,0BACjC,wBAAI,KAAK,OAAO,CAAC,CAAC;AAAA,0BAClB,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,wBAC5D;AAAA,wBACA;AAAA,0BACE,MAAM;AAAA,0BACN,cAAc;AAAA,0BACd,GAAG;AAAA,0BACH,WAAW;AAAA,0BACX,aAAaA,GAAE,iBAAiB;AAAA,wBAClC;AAAA,sBACF,EAAE,CAAC;AAAA,oBACL,EAAE,CAAC;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,YACH,uBAAmC,wBAAI,OAAO,EAAE,WAAW,eAAe,cAA0B,wBAAI,MAAM,EAAE,WAAW,eAAe,SAAS,SAAS,UAAU,gBAAgB,CAAC,EAAE,CAAC;AAAA,YAC1L,mBAA+B;AAAA,cAC7B;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,aAAa;AAAA,gBACb,SAAS;AAAA,gBACT,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,gBACgB,wBAAI,QAAQ,EAAE,WAAW,UAAU,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,UACrI;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,MACH,WAAW,aAAa,EAAE,IAAI,CAAC,WAAW,MAAM;AAC9C,mBAAuB,wBAAI,WAAW,CAAC,GAAG,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH,EAAE,CAAC;AAAA,QACa,wBAAI,QAAQ,EAAE,WAAW,mCAAmC,cAA0B;AAAA,MACpG;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,cACM;AAAA,YACd;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,WAAW;AAAA,YACb;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC,EAAE,CAAC;AACR;", "names": ["t"]}