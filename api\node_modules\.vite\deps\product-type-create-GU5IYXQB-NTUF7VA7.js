import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateProductType
} from "./chunk-HREJMEGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-type-create-GU5IYXQB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateProductTypeSchema = z.object({
  value: z.string().min(1)
});
var CreateProductTypeForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      value: ""
    },
    resolver: t(CreateProductTypeSchema)
  });
  const { mutateAsync, isPending } = useCreateProductType();
  const handleSubmit = form.handleSubmit(
    async (values) => {
      await mutateAsync(values, {
        onSuccess: ({ product_type }) => {
          toast.success(
            t2("productTypes.create.successToast", {
              value: product_type.value.trim()
            })
          );
          handleSuccess(`/settings/product-types/${product_type.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    }
  );
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-col items-center overflow-auto p-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t2("productTypes.create.header") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("productTypes.create.hint") })
      ] }),
      (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "value",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("productTypes.fields.value") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ) })
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(
        Button,
        {
          size: "small",
          variant: "primary",
          type: "submit",
          isLoading: isPending,
          children: t2("actions.create")
        }
      )
    ] }) })
  ] }) });
};
var ProductTypeCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateProductTypeForm, {}) });
};
export {
  ProductTypeCreate as Component
};
//# sourceMappingURL=product-type-create-GU5IYXQB-NTUF7VA7.js.map
