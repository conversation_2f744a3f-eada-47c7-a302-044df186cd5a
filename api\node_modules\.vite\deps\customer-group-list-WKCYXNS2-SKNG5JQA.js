import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  DataTable,
  useDataTableDateFilters
} from "./chunk-EPUS4TBC.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useDate
} from "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import {
  useCustomerGroups,
  useDeleteCustomerGroupLazy
} from "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  Trash,
  createDataTableColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-list-WKCYXNS2.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 10;
var CustomerGroupListTable = () => {
  const { t } = useTranslation();
  const { getWidgets } = useExtension();
  const { q, order, offset, created_at, updated_at } = useQueryParams([
    "q",
    "order",
    "offset",
    "created_at",
    "updated_at"
  ]);
  const columns = useColumns();
  const filters = useFilters();
  const { customer_groups, count, isPending, isError, error } = useCustomerGroups(
    {
      q,
      order,
      offset: offset ? parseInt(offset) : void 0,
      limit: PAGE_SIZE,
      created_at: created_at ? JSON.parse(created_at) : void 0,
      updated_at: updated_at ? JSON.parse(updated_at) : void 0,
      fields: "id,name,created_at,updated_at,customers.id"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("customer_group.list.before"),
        after: getWidgets("customer_group.list.after")
      },
      children: (0, import_jsx_runtime.jsx)(Container, { className: "overflow-hidden p-0", children: (0, import_jsx_runtime.jsx)(
        DataTable,
        {
          data: customer_groups,
          columns,
          filters,
          heading: t("customerGroups.domain"),
          rowCount: count,
          getRowId: (row) => row.id,
          rowHref: (row) => `/customer-groups/${row.id}`,
          action: {
            label: t("actions.create"),
            to: "/customer-groups/create"
          },
          emptyState: {
            empty: {
              heading: t("customerGroups.list.empty.heading"),
              description: t("customerGroups.list.empty.description")
            },
            filtered: {
              heading: t("customerGroups.list.filtered.heading"),
              description: t("customerGroups.list.filtered.description")
            }
          },
          pageSize: PAGE_SIZE,
          isLoading: isPending
        }
      ) })
    }
  );
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const { getFullDate } = useDate();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { mutateAsync: deleteCustomerGroup } = useDeleteCustomerGroupLazy();
  const handleDeleteCustomerGroup = (0, import_react.useCallback)(
    async ({ id, name }) => {
      const res = await prompt({
        title: t("customerGroups.delete.title"),
        description: t("customerGroups.delete.description", {
          name
        }),
        verificationText: name,
        verificationInstruction: t("general.typeToConfirm"),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!res) {
        return;
      }
      await deleteCustomerGroup(
        { id },
        {
          onSuccess: () => {
            toast.success(t("customerGroups.delete.successToast", { name }));
          },
          onError: (e) => {
            toast.error(e.message);
          }
        }
      );
    },
    [t, prompt, deleteCustomerGroup]
  );
  return (0, import_react.useMemo)(() => {
    return [
      columnHelper.accessor("name", {
        header: t("fields.name"),
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("customers", {
        header: t("customers.domain"),
        cell: ({ row }) => {
          var _a;
          return (0, import_jsx_runtime.jsx)("span", { children: ((_a = row.original.customers) == null ? void 0 : _a.length) ?? 0 });
        }
      }),
      columnHelper.accessor("created_at", {
        header: t("fields.createdAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)("span", { children: getFullDate({
            date: row.original.created_at,
            includeTime: true
          }) });
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      }),
      columnHelper.accessor("updated_at", {
        header: t("fields.updatedAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)("span", { children: getFullDate({
            date: row.original.updated_at,
            includeTime: true
          }) });
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              onClick: (row) => {
                navigate(`/customer-groups/${row.row.original.id}/edit`);
              }
            }
          ],
          [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: (row) => {
                handleDeleteCustomerGroup({
                  id: row.row.original.id,
                  name: row.row.original.name ?? ""
                });
              }
            }
          ]
        ]
      })
    ];
  }, [t, navigate, getFullDate, handleDeleteCustomerGroup]);
};
var useFilters = () => {
  const dateFilters = useDataTableDateFilters();
  return (0, import_react.useMemo)(() => {
    return dateFilters;
  }, [dateFilters]);
};
var CustomerGroupsList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("customer_group.list.after"),
        before: getWidgets("customer_group.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(CustomerGroupListTable, {})
    }
  );
};
export {
  CustomerGroupsList as Component
};
//# sourceMappingURL=customer-group-list-WKCYXNS2-SKNG5JQA.js.map
