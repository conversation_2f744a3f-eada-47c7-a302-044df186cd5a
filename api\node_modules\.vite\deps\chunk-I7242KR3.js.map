{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LT4MVCA7.mjs"], "sourcesContent": ["// src/hooks/table/filters/use-region-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useRegionTableFilters = () => {\n  const { t } = useTranslation();\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  const filters = [...dateFilters];\n  return filters;\n};\n\nexport {\n  useRegionTableFilters\n};\n"], "mappings": ";;;;;AAEA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,QAAM,UAAU,CAAC,GAAG,WAAW;AAC/B,SAAO;AACT;", "names": []}