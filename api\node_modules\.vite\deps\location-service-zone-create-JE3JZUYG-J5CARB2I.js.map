{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-service-zone-create-JE3JZUYG.mjs"], "sourcesContent": ["import {\n  GeoZoneForm\n} from \"./chunk-77BAMLUK.mjs\";\nimport \"./chunk-NOAFLTPV.mjs\";\nimport {\n  GEO_ZONE_STACKED_MODAL_ID\n} from \"./chunk-PYIO3TDQ.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-X5VECN6S.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  StackedFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateFulfillmentSetServiceZone\n} from \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/locations/location-service-zone-create/location-service-zone-create.tsx\nimport { json, useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-service-zone-create/components/create-service-zone-form/create-service-zone-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, InlineTip, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateServiceZoneSchema = z.object({\n  name: z.string().min(1),\n  countries: z.array(z.object({ iso_2: z.string().min(2), display_name: z.string() })).min(1)\n});\nfunction CreateServiceZoneForm({\n  fulfillmentSet,\n  type,\n  location\n}) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      countries: []\n    },\n    resolver: zodResolver(CreateServiceZoneSchema)\n  });\n  const { mutateAsync, isPending } = useCreateFulfillmentSetServiceZone(\n    fulfillmentSet.id\n  );\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        name: data.name,\n        geo_zones: data.countries.map(({ iso_2 }) => ({\n          country_code: iso_2,\n          type: \"country\"\n        }))\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"stockLocations.serviceZones.create.successToast\", {\n              name: data.name\n            })\n          );\n          handleSuccess(`/settings/locations/${location.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col items-center overflow-auto\", children: /* @__PURE__ */ jsxs(StackedFocusModal, { id: GEO_ZONE_STACKED_MODAL_ID, children: [\n          /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n            /* @__PURE__ */ jsx(Heading, { children: type === \"pickup\" /* Pickup */ ? t(\"stockLocations.serviceZones.create.headerPickup\", {\n              location: location.name\n            }) : t(\"stockLocations.serviceZones.create.headerShipping\", {\n              location: location.name\n            }) }),\n            /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ) }),\n            /* @__PURE__ */ jsx(InlineTip, { label: t(\"general.tip\"), children: t(\"stockLocations.serviceZones.fields.tip\") }),\n            /* @__PURE__ */ jsx(GeoZoneForm, { form })\n          ] }) }),\n          /* @__PURE__ */ jsx(GeoZoneForm.AreaDrawer, { form })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/locations/location-service-zone-create/location-service-zone-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction LocationCreateServiceZone() {\n  const { fset_id, location_id } = useParams();\n  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {\n    fields: \"*fulfillment_sets\"\n  });\n  const fulfillmentSet = stock_location?.fulfillment_sets?.find(\n    (f) => f.id === fset_id\n  );\n  const type = fulfillmentSet?.type === \"pickup\" /* Pickup */ ? \"pickup\" /* Pickup */ : \"shipping\" /* Shipping */;\n  if (!isPending && !isFetching && !fulfillmentSet) {\n    throw json(\n      { message: `Fulfillment set with ID: ${fset_id} was not found.` },\n      404\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: fulfillmentSet && /* @__PURE__ */ jsx2(\n    CreateServiceZoneForm,\n    {\n      fulfillmentSet,\n      location: stock_location,\n      type\n    }\n  ) });\n}\nexport {\n  LocationCreateServiceZone as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,yBAA0B;AAyF1B,IAAAA,sBAA4B;AAxF5B,IAAI,0BAA0B,EAAE,OAAO;AAAA,EACrC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;AAC5F,CAAC;AACD,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,IACd;AAAA,IACA,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI;AAAA,IACjC,eAAe;AAAA,EACjB;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,KAAK;AAAA,QACX,WAAW,KAAK,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UAC5C,cAAc;AAAA,UACd,MAAM;AAAA,QACR,EAAE;AAAA,MACJ;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,mDAAmD;AAAA,cACnD,MAAM,KAAK;AAAA,YACb,CAAC;AAAA,UACH;AACA,wBAAc,uBAAuB,SAAS,EAAE,EAAE;AAAA,QACpD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,mDAAmD,cAA0B,yBAAK,mBAAmB,EAAE,IAAI,2BAA2B,UAAU;AAAA,cACrL,wBAAI,OAAO,EAAE,WAAW,qCAAqC,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,gBACjL,wBAAI,SAAS,EAAE,UAAU,SAAS,WAAwBA,GAAE,mDAAmD;AAAA,cAC7H,UAAU,SAAS;AAAA,YACrB,CAAC,IAAIA,GAAE,qDAAqD;AAAA,cAC1D,UAAU,SAAS;AAAA,YACrB,CAAC,EAAE,CAAC;AAAA,gBACY,wBAAI,OAAO,EAAE,WAAW,yCAAyC,cAA0B;AAAA,cACzG,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,wBAAI,WAAW,EAAE,OAAOA,GAAE,aAAa,GAAG,UAAUA,GAAE,wCAAwC,EAAE,CAAC;AAAA,gBACjG,wBAAI,aAAa,EAAE,KAAK,CAAC;AAAA,UAC3C,EAAE,CAAC,EAAE,CAAC;AAAA,cACU,wBAAI,YAAY,YAAY,EAAE,KAAK,CAAC;AAAA,QACtD,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,SAAS,4BAA4B;AA/IrC;AAgJE,QAAM,EAAE,SAAS,YAAY,IAAI,UAAU;AAC3C,QAAM,EAAE,gBAAgB,WAAW,YAAY,SAAS,MAAM,IAAI,iBAAiB,aAAa;AAAA,IAC9F,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,kBAAiB,sDAAgB,qBAAhB,mBAAkC;AAAA,IACvD,CAAC,MAAM,EAAE,OAAO;AAAA;AAElB,QAAM,QAAO,iDAAgB,UAAS,WAAwB,WAAwB;AACtF,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB;AAChD,UAAM;AAAA,MACJ,EAAE,SAAS,4BAA4B,OAAO,kBAAkB;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,MAAM,uBAAuB,WAAW,IAAI,UAAU,sBAAkC,oBAAAA;AAAA,IACrI;AAAA,IACA;AAAA,MACE;AAAA,MACA,UAAU;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}