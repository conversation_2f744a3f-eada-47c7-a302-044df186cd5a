import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-W7625H47.mjs
var useDateTableFilters = () => {
  const { t } = useTranslation();
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at" },
    { label: t("fields.updatedAt"), key: "updated_at" }
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date"
  }));
  return dateFilters;
};

export {
  useDateTableFilters
};
//# sourceMappingURL=chunk-IONS3C54.js.map
