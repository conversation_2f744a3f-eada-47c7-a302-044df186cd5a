import {
  SectionRow
} from "./chunk-KTCGZHOS.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Container,
  Heading,
  PencilSquare
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-Q5DI5VYN.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var InventoryItemGeneralSection = ({
  inventoryItem
}) => {
  const { t } = useTranslation();
  const getQuantityFormat = (quantity) => {
    var _a;
    if (quantity !== void 0 && !isNaN(quantity)) {
      return t("inventory.quantityAcrossLocations", {
        quantity,
        locations: (_a = inventoryItem.location_levels) == null ? void 0 : _a.length
      });
    }
    return "-";
  };
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsxs)(Heading, { children: [
        inventoryItem.title ?? inventoryItem.sku,
        " ",
        t("fields.details")
      ] }),
      (0, import_jsx_runtime.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: "edit"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)(SectionRow, { title: t("fields.sku"), value: inventoryItem.sku ?? "-" }),
    (0, import_jsx_runtime.jsx)(
      SectionRow,
      {
        title: t("fields.inStock"),
        value: getQuantityFormat(inventoryItem.stocked_quantity)
      }
    ),
    (0, import_jsx_runtime.jsx)(
      SectionRow,
      {
        title: t("inventory.reserved"),
        value: getQuantityFormat(inventoryItem.reserved_quantity)
      }
    ),
    (0, import_jsx_runtime.jsx)(
      SectionRow,
      {
        title: t("inventory.available"),
        value: getQuantityFormat(
          inventoryItem.stocked_quantity - inventoryItem.reserved_quantity
        )
      }
    )
  ] });
};

export {
  InventoryItemGeneralSection
};
//# sourceMappingURL=chunk-ITCUGY4F.js.map
