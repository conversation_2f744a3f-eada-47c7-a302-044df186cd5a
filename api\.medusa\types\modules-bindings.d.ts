import type Meilisearch from '@rokmohar/medusa-plugin-meilisearch/.medusa/server/src/modules/meilisearch'
import type { IStockLocationService } from '@medusajs/framework/types'
import type { IInventoryService } from '@medusajs/framework/types'
import type { IProductModuleService } from '@medusajs/framework/types'
import type { IPricingModuleService } from '@medusajs/framework/types'
import type { IPromotionModuleService } from '@medusajs/framework/types'
import type { ICustomerModuleService } from '@medusajs/framework/types'
import type { ISalesChannelModuleService } from '@medusajs/framework/types'
import type { ICartModuleService } from '@medusajs/framework/types'
import type { IRegionModuleService } from '@medusajs/framework/types'
import type { IApiKeyModuleService } from '@medusajs/framework/types'
import type { IStoreModuleService } from '@medusajs/framework/types'
import type { ITaxModuleService } from '@medusajs/framework/types'
import type { ICurrencyModuleService } from '@medusajs/framework/types'
import type { IPaymentModuleService } from '@medusajs/framework/types'
import type { IOrderModuleService } from '@medusajs/framework/types'
import type { IAuthModuleService } from '@medusajs/framework/types'
import type { IUserModuleService } from '@medusajs/framework/types'
import type { IFulfillmentModuleService } from '@medusajs/framework/types'
import type { INotificationModuleService } from '@medusajs/framework/types'
import type { ICacheService } from '@medusajs/framework/types'
import type { IEventBusModuleService } from '@medusajs/framework/types'
import type { IWorkflowEngineService } from '@medusajs/framework/types'
import type { ILockingModule } from '@medusajs/framework/types'
import type { IFileModuleService } from '@medusajs/framework/types'
import type CmsModuleService from '..\..\src\modules\cms'
import type MenuModuleService from '..\..\src\modules\navigation'
import type LayoutModuleService from '..\..\src\modules\layout'
import type ContactFormModuleService from '..\..\src\modules\contact-form'
import type ProductVariantImage from '..\..\src\modules\product-variant-image'
import type ProductOptionImage from '..\..\src\modules\product-option-image'
import type ProductReview from '..\..\src\modules\product-review'
import type ProductMixMatch from '..\..\src\modules\product-mix-match'
import type WishlistModuleService from '..\..\src\modules\wishlist'
import type LanguageModuleService from '..\..\src\modules\languages'

declare module '@medusajs/framework/types' {
  interface ModuleImplementations {
    'meilisearch': InstanceType<(typeof Meilisearch)['service']>,
    'stock_location': IStockLocationService,
    'inventory': IInventoryService,
    'product': IProductModuleService,
    'pricing': IPricingModuleService,
    'promotion': IPromotionModuleService,
    'customer': ICustomerModuleService,
    'sales_channel': ISalesChannelModuleService,
    'cart': ICartModuleService,
    'region': IRegionModuleService,
    'api_key': IApiKeyModuleService,
    'store': IStoreModuleService,
    'tax': ITaxModuleService,
    'currency': ICurrencyModuleService,
    'payment': IPaymentModuleService,
    'order': IOrderModuleService,
    'auth': IAuthModuleService,
    'user': IUserModuleService,
    'fulfillment': IFulfillmentModuleService,
    'notification': INotificationModuleService,
    'cache': ICacheService,
    'event_bus': IEventBusModuleService,
    'workflows': IWorkflowEngineService,
    'locking': ILockingModule,
    'file': IFileModuleService,
    'cmsModuleService': InstanceType<(typeof CmsModuleService)['service']>,
    'menuModuleService': InstanceType<(typeof MenuModuleService)['service']>,
    'layout_module_service': InstanceType<(typeof LayoutModuleService)['service']>,
    'contactFormModuleService': InstanceType<(typeof ContactFormModuleService)['service']>,
    'product_variant_image': InstanceType<(typeof ProductVariantImage)['service']>,
    'productOptionImage': InstanceType<(typeof ProductOptionImage)['service']>,
    'productReview': InstanceType<(typeof ProductReview)['service']>,
    'productMixMatch': InstanceType<(typeof ProductMixMatch)['service']>,
    'wishlistModuleService': InstanceType<(typeof WishlistModuleService)['service']>,
    'languageModuleService': InstanceType<(typeof LanguageModuleService)['service']>
  }
}