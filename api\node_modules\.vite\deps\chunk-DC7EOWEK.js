import {
  castNumber
} from "./chunk-EZLR4STK.js";
import {
  json
} from "./chunk-T7YBVUWZ.js";

// node_modules/@medusajs/dashboard/dist/chunk-G2J2T2QU.mjs
var getValues = (priceList) => {
  const startsAt = priceList.starts_at;
  const endsAt = priceList.ends_at;
  const isExpired = endsAt ? new Date(endsAt) < /* @__PURE__ */ new Date() : false;
  const isScheduled = startsAt ? new Date(startsAt) > /* @__PURE__ */ new Date() : false;
  const isDraft = priceList.status === "draft";
  return {
    isExpired,
    isScheduled,
    isDraft
  };
};
var getPriceListStatus = (t, priceList) => {
  const { isExpired, isScheduled, isDraft } = getValues(priceList);
  let text = t("priceLists.fields.status.options.active");
  let color = "green";
  let status = "active";
  if (isDraft) {
    color = "grey";
    text = t("priceLists.fields.status.options.draft");
    status = "draft";
  }
  if (isExpired) {
    color = "red";
    text = t("priceLists.fields.status.options.expired");
    status = "expired";
  }
  if (isScheduled) {
    color = "orange";
    text = t("priceLists.fields.status.options.scheduled");
    status = "scheduled";
  }
  return {
    color,
    text,
    status
  };
};
var isProductRow = (row) => {
  return "variants" in row;
};
var extractPricesFromVariants = (variantId, variant, regions) => {
  const extractPriceDetails = (price, priceType, id) => {
    var _a;
    const currencyCode = priceType === "currency" ? id : (_a = regions.find((r) => r.id === id)) == null ? void 0 : _a.currency_code;
    if (!currencyCode) {
      throw json({ message: "Currency code not found" }, 400);
    }
    return {
      amount: castNumber(price.amount),
      ...priceType === "region" ? { rules: { region_id: id } } : {},
      currency_code: currencyCode,
      variant_id: variantId
    };
  };
  const currencyPrices = Object.entries(variant.currency_prices || {}).flatMap(
    ([currencyCode, currencyPrice]) => {
      return (currencyPrice == null ? void 0 : currencyPrice.amount) ? [extractPriceDetails(currencyPrice, "currency", currencyCode)] : [];
    }
  );
  const regionPrices = Object.entries(variant.region_prices || {}).flatMap(
    ([regionId, regionPrice]) => {
      return (regionPrice == null ? void 0 : regionPrice.amount) ? [extractPriceDetails(regionPrice, "region", regionId)] : [];
    }
  );
  return [...currencyPrices, ...regionPrices];
};
var exctractPricesFromProducts = (products, regions) => {
  return Object.values(products).flatMap(
    ({ variants }) => Object.entries(variants).flatMap(
      ([variantId, variant]) => extractPricesFromVariants(variantId, variant, regions)
    )
  );
};

export {
  getPriceListStatus,
  isProductRow,
  exctractPricesFromProducts
};
//# sourceMappingURL=chunk-DC7EOWEK.js.map
