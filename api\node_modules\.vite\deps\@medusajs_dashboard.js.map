{"version": 3, "sources": ["../../@medusajs/dashboard/dist/app.mjs"], "sourcesContent": ["import {\n  DashboardApp\n} from \"./chunk-L4KSRFES.mjs\";\nimport \"./chunk-BC3M3N6P.mjs\";\nimport \"./chunk-ONB3JEHR.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-D3YQN7HV.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-XKXNQ2KV.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/app.tsx\nimport displayModule from \"virtual:medusa/displays\";\nimport formModule from \"virtual:medusa/forms\";\nimport menuItemModule from \"virtual:medusa/menu-items\";\nimport routeModule from \"virtual:medusa/routes\";\nimport widgetModule from \"virtual:medusa/widgets\";\nimport { jsx } from \"react/jsx-runtime\";\nvar localPlugin = {\n  widgetModule,\n  routeModule,\n  displayModule,\n  formModule,\n  menuItemModule\n};\nfunction App({ plugins = [] }) {\n  const app = new DashboardApp({\n    plugins: [localPlugin, ...plugins]\n  });\n  return /* @__PURE__ */ jsx(\"div\", { children: app.render() });\n}\nvar app_default = App;\nexport {\n  app_default as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA,yBAAoB;AALpB,OAAO,mBAAmB;AAC1B,OAAO,gBAAgB;AACvB,OAAO,oBAAoB;AAC3B,OAAO,iBAAiB;AACxB,OAAO,kBAAkB;AAEzB,IAAI,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,IAAI,EAAE,UAAU,CAAC,EAAE,GAAG;AAC7B,QAAM,MAAM,IAAI,aAAa;AAAA,IAC3B,SAAS,CAAC,aAAa,GAAG,OAAO;AAAA,EACnC,CAAC;AACD,aAAuB,wBAAI,OAAO,EAAE,UAAU,IAAI,OAAO,EAAE,CAAC;AAC9D;AACA,IAAI,cAAc;", "names": []}