{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-54IEHX46.mjs"], "sourcesContent": ["// src/routes/categories/common/utils.ts\nfunction getIsActiveProps(isActive, t) {\n  switch (isActive) {\n    case true:\n      return {\n        label: t(\"categories.fields.status.active\"),\n        color: \"green\"\n      };\n    case false:\n      return {\n        label: t(\"categories.fields.status.inactive\"),\n        color: \"red\"\n      };\n  }\n}\nfunction getIsInternalProps(isInternal, t) {\n  switch (isInternal) {\n    case true:\n      return {\n        label: t(\"categories.fields.visibility.internal\"),\n        color: \"blue\"\n      };\n    case false:\n      return {\n        label: t(\"categories.fields.visibility.public\"),\n        color: \"green\"\n      };\n  }\n}\nfunction getCategoryPath(category) {\n  if (!category) {\n    return [];\n  }\n  const path = category.parent_category ? getCategoryPath(category.parent_category) : [];\n  path.push({ id: category.id, name: category.name });\n  return path;\n}\nfunction getCategoryChildren(category) {\n  if (!category || !category.category_children) {\n    return [];\n  }\n  return category.category_children.map((child) => ({\n    id: child.id,\n    name: child.name\n  }));\n}\nvar insertCategoryTreeItem = (categories, newItem) => {\n  const seen = /* @__PURE__ */ new Set();\n  const remove = (items, id) => {\n    const stack = [...items];\n    const result = [];\n    while (stack.length > 0) {\n      const item = stack.pop();\n      if (item.id !== id) {\n        if (item.category_children) {\n          item.category_children = remove(item.category_children, id);\n        }\n        result.push(item);\n      }\n    }\n    return result;\n  };\n  const insert = (items) => {\n    const stack = [...items];\n    while (stack.length > 0) {\n      const item = stack.pop();\n      if (seen.has(item.id)) {\n        continue;\n      }\n      seen.add(item.id);\n      if (item.id === newItem.parent_category_id) {\n        if (!item.category_children) {\n          item.category_children = [];\n        }\n        if (newItem.rank === null) {\n          item.category_children.push(newItem);\n        } else {\n          item.category_children.splice(newItem.rank, 0, newItem);\n        }\n        item.category_children.forEach((child, index) => {\n          child.rank = index;\n        });\n        item.category_children.sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0));\n        return categories;\n      }\n      if (item.category_children) {\n        stack.push(...item.category_children);\n      }\n    }\n    return items;\n  };\n  categories = remove(categories, newItem.id);\n  if (newItem.parent_category_id === null && newItem.rank === null) {\n    categories.unshift(newItem);\n    categories.forEach((child, index) => {\n      child.rank = index;\n    });\n  } else if (newItem.parent_category_id === null && newItem.rank !== null) {\n    categories.splice(newItem.rank, 0, newItem);\n    categories.forEach((child, index) => {\n      child.rank = index;\n    });\n  } else {\n    categories = insert(categories);\n  }\n  categories.sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0));\n  return categories;\n};\n\nexport {\n  getIsActiveProps,\n  getIsInternalProps,\n  getCategoryPath,\n  getCategoryChildren,\n  insertCategoryTreeItem\n};\n"], "mappings": ";AACA,SAAS,iBAAiB,UAAU,GAAG;AACrC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,iCAAiC;AAAA,QAC1C,OAAO;AAAA,MACT;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,mCAAmC;AAAA,QAC5C,OAAO;AAAA,MACT;AAAA,EACJ;AACF;AACA,SAAS,mBAAmB,YAAY,GAAG;AACzC,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,uCAAuC;AAAA,QAChD,OAAO;AAAA,MACT;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,qCAAqC;AAAA,QAC9C,OAAO;AAAA,MACT;AAAA,EACJ;AACF;AACA,SAAS,gBAAgB,UAAU;AACjC,MAAI,CAAC,UAAU;AACb,WAAO,CAAC;AAAA,EACV;AACA,QAAM,OAAO,SAAS,kBAAkB,gBAAgB,SAAS,eAAe,IAAI,CAAC;AACrF,OAAK,KAAK,EAAE,IAAI,SAAS,IAAI,MAAM,SAAS,KAAK,CAAC;AAClD,SAAO;AACT;AACA,SAAS,oBAAoB,UAAU;AACrC,MAAI,CAAC,YAAY,CAAC,SAAS,mBAAmB;AAC5C,WAAO,CAAC;AAAA,EACV;AACA,SAAO,SAAS,kBAAkB,IAAI,CAAC,WAAW;AAAA,IAChD,IAAI,MAAM;AAAA,IACV,MAAM,MAAM;AAAA,EACd,EAAE;AACJ;AACA,IAAI,yBAAyB,CAAC,YAAY,YAAY;AACpD,QAAM,OAAuB,oBAAI,IAAI;AACrC,QAAM,SAAS,CAAC,OAAO,OAAO;AAC5B,UAAM,QAAQ,CAAC,GAAG,KAAK;AACvB,UAAM,SAAS,CAAC;AAChB,WAAO,MAAM,SAAS,GAAG;AACvB,YAAM,OAAO,MAAM,IAAI;AACvB,UAAI,KAAK,OAAO,IAAI;AAClB,YAAI,KAAK,mBAAmB;AAC1B,eAAK,oBAAoB,OAAO,KAAK,mBAAmB,EAAE;AAAA,QAC5D;AACA,eAAO,KAAK,IAAI;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,UAAU;AACxB,UAAM,QAAQ,CAAC,GAAG,KAAK;AACvB,WAAO,MAAM,SAAS,GAAG;AACvB,YAAM,OAAO,MAAM,IAAI;AACvB,UAAI,KAAK,IAAI,KAAK,EAAE,GAAG;AACrB;AAAA,MACF;AACA,WAAK,IAAI,KAAK,EAAE;AAChB,UAAI,KAAK,OAAO,QAAQ,oBAAoB;AAC1C,YAAI,CAAC,KAAK,mBAAmB;AAC3B,eAAK,oBAAoB,CAAC;AAAA,QAC5B;AACA,YAAI,QAAQ,SAAS,MAAM;AACzB,eAAK,kBAAkB,KAAK,OAAO;AAAA,QACrC,OAAO;AACL,eAAK,kBAAkB,OAAO,QAAQ,MAAM,GAAG,OAAO;AAAA,QACxD;AACA,aAAK,kBAAkB,QAAQ,CAAC,OAAO,UAAU;AAC/C,gBAAM,OAAO;AAAA,QACf,CAAC;AACD,aAAK,kBAAkB,KAAK,CAAC,GAAG,OAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ,EAAE;AACnE,eAAO;AAAA,MACT;AACA,UAAI,KAAK,mBAAmB;AAC1B,cAAM,KAAK,GAAG,KAAK,iBAAiB;AAAA,MACtC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,eAAa,OAAO,YAAY,QAAQ,EAAE;AAC1C,MAAI,QAAQ,uBAAuB,QAAQ,QAAQ,SAAS,MAAM;AAChE,eAAW,QAAQ,OAAO;AAC1B,eAAW,QAAQ,CAAC,OAAO,UAAU;AACnC,YAAM,OAAO;AAAA,IACf,CAAC;AAAA,EACH,WAAW,QAAQ,uBAAuB,QAAQ,QAAQ,SAAS,MAAM;AACvE,eAAW,OAAO,QAAQ,MAAM,GAAG,OAAO;AAC1C,eAAW,QAAQ,CAAC,OAAO,UAAU;AACnC,YAAM,OAAO;AAAA,IACf,CAAC;AAAA,EACH,OAAO;AACL,iBAAa,OAAO,UAAU;AAAA,EAChC;AACA,aAAW,KAAK,CAAC,GAAG,OAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ,EAAE;AACvD,SAAO;AACT;", "names": []}