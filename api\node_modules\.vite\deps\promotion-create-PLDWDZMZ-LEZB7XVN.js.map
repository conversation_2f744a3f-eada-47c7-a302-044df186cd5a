{"version": 3, "sources": ["../../@medusajs/dashboard/dist/promotion-create-PLDWDZMZ.mjs"], "sourcesContent": ["import {\n  DeprecatedPercentageInput\n} from \"./chunk-YRY2CZ6I.mjs\";\nimport {\n  AddCampaignPromotionFields\n} from \"./chunk-DRAV7IQC.mjs\";\nimport {\n  RulesFormField\n} from \"./chunk-NGEWNLV7.mjs\";\nimport {\n  CreateCampaignSchema,\n  DEFAULT_CAMPAIGN_VALUES\n} from \"./chunk-DXOEDXLQ.mjs\";\nimport \"./chunk-TDDYKNA2.mjs\";\nimport \"./chunk-F6ZOHZVB.mjs\";\nimport \"./chunk-YIZSVS2R.mjs\";\nimport \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  getCurrencySymbol\n} from \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCampaigns,\n  useCreatePromotion\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/promotions/promotion-create/components/create-promotion-form/create-promotion-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Alert,\n  Badge,\n  Button,\n  clx,\n  CurrencyInput,\n  Divider,\n  Heading,\n  Input,\n  ProgressTabs,\n  RadioGroup,\n  Text,\n  toast\n} from \"@medusajs/ui\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useForm, useWatch } from \"react-hook-form\";\nimport { Trans, useTranslation } from \"react-i18next\";\n\n// src/routes/promotions/promotion-create/components/create-promotion-form/form-schema.ts\nimport { z } from \"zod\";\nvar RuleSchema = z.array(\n  z.object({\n    id: z.string().optional(),\n    attribute: z.string().min(1, { message: \"Required field\" }),\n    operator: z.string().min(1, { message: \"Required field\" }),\n    values: z.union([\n      z.number().min(1, { message: \"Required field\" }),\n      z.string().min(1, { message: \"Required field\" }),\n      z.array(z.string()).min(1, { message: \"Required field\" })\n    ]),\n    required: z.boolean().optional(),\n    disguised: z.boolean().optional(),\n    field_type: z.string().optional()\n  })\n);\nvar CreatePromotionSchema = z.object({\n  template_id: z.string().optional(),\n  campaign_id: z.string().optional(),\n  campaign_choice: z.enum([\"none\", \"existing\", \"new\"]).optional(),\n  is_automatic: z.string().toLowerCase(),\n  code: z.string().min(1),\n  type: z.enum([\"buyget\", \"standard\"]),\n  status: z.enum([\"draft\", \"active\", \"inactive\"]),\n  rules: RuleSchema,\n  application_method: z.object({\n    allocation: z.enum([\"each\", \"across\"]),\n    value: z.number().min(0),\n    currency_code: z.string().optional(),\n    max_quantity: z.number().optional().nullable(),\n    target_rules: RuleSchema,\n    buy_rules: RuleSchema,\n    type: z.enum([\"fixed\", \"percentage\"]),\n    target_type: z.enum([\"order\", \"shipping_methods\", \"items\"])\n  }),\n  campaign: CreateCampaignSchema.optional()\n}).refine(\n  (data) => {\n    if (data.application_method.allocation === \"across\") {\n      return true;\n    }\n    return data.application_method.allocation === \"each\" && typeof data.application_method.max_quantity === \"number\";\n  },\n  {\n    path: [\"application_method.max_quantity\"],\n    message: `required field`\n  }\n);\n\n// src/routes/promotions/promotion-create/components/create-promotion-form/templates.ts\nvar commonHiddenFields = [\n  \"type\",\n  \"application_method.type\",\n  \"application_method.allocation\"\n];\nvar templates = [\n  {\n    id: \"amount_off_products\",\n    type: \"standard\",\n    title: \"Amount off products\",\n    description: \"Discount specific products or collection of products\",\n    hiddenFields: [...commonHiddenFields],\n    defaults: {\n      is_automatic: \"false\",\n      type: \"standard\",\n      application_method: {\n        allocation: \"each\",\n        target_type: \"items\",\n        type: \"fixed\"\n      }\n    }\n  },\n  {\n    id: \"amount_off_order\",\n    type: \"standard\",\n    title: \"Amount off order\",\n    description: \"Discounts the total order amount\",\n    hiddenFields: [...commonHiddenFields],\n    defaults: {\n      is_automatic: \"false\",\n      type: \"standard\",\n      application_method: {\n        allocation: \"across\",\n        target_type: \"order\",\n        type: \"fixed\"\n      }\n    }\n  },\n  {\n    id: \"percentage_off_product\",\n    type: \"standard\",\n    title: \"Percentage off product\",\n    description: \"Discounts a percentage off selected products\",\n    hiddenFields: [...commonHiddenFields],\n    defaults: {\n      is_automatic: \"false\",\n      type: \"standard\",\n      application_method: {\n        allocation: \"each\",\n        target_type: \"items\",\n        type: \"percentage\"\n      }\n    }\n  },\n  {\n    id: \"percentage_off_order\",\n    type: \"standard\",\n    title: \"Percentage off order\",\n    description: \"Discounts a percentage of the total order amount\",\n    hiddenFields: [...commonHiddenFields],\n    defaults: {\n      is_automatic: \"false\",\n      type: \"standard\",\n      application_method: {\n        allocation: \"across\",\n        target_type: \"order\",\n        type: \"percentage\"\n      }\n    }\n  },\n  {\n    id: \"buy_get\",\n    type: \"buy_get\",\n    title: \"Buy X Get Y\",\n    description: \"Buy X product(s), get Y product(s)\",\n    hiddenFields: [...commonHiddenFields, \"application_method.value\"],\n    defaults: {\n      is_automatic: \"false\",\n      type: \"buyget\",\n      application_method: {\n        type: \"percentage\",\n        value: 100,\n        apply_to_quantity: 1,\n        max_quantity: 1\n      }\n    }\n  }\n];\n\n// src/routes/promotions/promotion-create/components/create-promotion-form/create-promotion-form.tsx\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar defaultValues = {\n  campaign_id: void 0,\n  template_id: templates[0].id,\n  campaign_choice: \"none\",\n  is_automatic: \"false\",\n  code: \"\",\n  type: \"standard\",\n  status: \"draft\",\n  rules: [],\n  application_method: {\n    allocation: \"each\",\n    type: \"fixed\",\n    target_type: \"items\",\n    max_quantity: 1,\n    target_rules: [],\n    buy_rules: []\n  },\n  campaign: void 0\n};\nvar CreatePromotionForm = () => {\n  const [tab, setTab] = useState(\"type\" /* TYPE */);\n  const [tabState, setTabState] = useState({\n    [\"type\" /* TYPE */]: \"in-progress\",\n    [\"promotion\" /* PROMOTION */]: \"not-started\",\n    [\"campaign\" /* CAMPAIGN */]: \"not-started\"\n  });\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues,\n    resolver: zodResolver(CreatePromotionSchema)\n  });\n  const { setValue, reset, getValues } = form;\n  const { mutateAsync: createPromotion } = useCreatePromotion();\n  const handleSubmit = form.handleSubmit(\n    async (data) => {\n      const {\n        campaign_choice: _campaignChoice,\n        is_automatic,\n        template_id: _templateId,\n        application_method,\n        rules,\n        ...promotionData\n      } = data;\n      const {\n        target_rules: targetRulesData = [],\n        buy_rules: buyRulesData = [],\n        ...applicationMethodData\n      } = application_method;\n      const disguisedRules = [\n        ...targetRulesData.filter((r) => !!r.disguised),\n        ...buyRulesData.filter((r) => !!r.disguised),\n        ...rules.filter((r) => !!r.disguised)\n      ];\n      const applicationMethodRuleData = {};\n      for (const rule of disguisedRules) {\n        applicationMethodRuleData[rule.attribute] = rule.field_type === \"number\" ? parseInt(rule.values) : rule.values;\n      }\n      const buildRulesData = (rules2) => {\n        return rules2.filter((r) => !r.disguised).map((rule) => ({\n          operator: rule.operator,\n          attribute: rule.attribute,\n          values: rule.values\n        }));\n      };\n      createPromotion(\n        {\n          ...promotionData,\n          rules: buildRulesData(rules),\n          application_method: {\n            ...applicationMethodData,\n            ...applicationMethodRuleData,\n            target_rules: buildRulesData(targetRulesData),\n            buy_rules: buildRulesData(buyRulesData)\n          },\n          is_automatic: is_automatic === \"true\"\n        },\n        {\n          onSuccess: ({ promotion }) => {\n            toast.success(\n              t(\"promotions.toasts.promotionCreateSuccess\", {\n                code: promotion.code\n              })\n            );\n            handleSuccess(`/promotions/${promotion.id}`);\n          },\n          onError: (e) => {\n            toast.error(e.message);\n          }\n        }\n      );\n    },\n    async (error) => {\n      const { campaign: _campaign, ...rest } = error || {};\n      const errorInPromotionTab = !!Object.keys(rest || {}).length;\n      if (errorInPromotionTab) {\n        toast.error(t(\"promotions.errors.promotionTabError\"));\n      }\n    }\n  );\n  const handleTabChange = async (tab2) => {\n    switch (tab2) {\n      case \"type\" /* TYPE */:\n        setTabState((prev) => ({\n          ...prev,\n          [\"type\" /* TYPE */]: \"in-progress\"\n        }));\n        setTab(tab2);\n        break;\n      case \"promotion\" /* PROMOTION */:\n        setTabState((prev) => ({\n          ...prev,\n          [\"type\" /* TYPE */]: \"completed\",\n          [\"promotion\" /* PROMOTION */]: \"in-progress\"\n        }));\n        setTab(tab2);\n        break;\n      case \"campaign\" /* CAMPAIGN */: {\n        const valid = await form.trigger();\n        if (!valid) {\n          setTabState({\n            [\"type\" /* TYPE */]: \"completed\",\n            [\"promotion\" /* PROMOTION */]: \"in-progress\",\n            [\"campaign\" /* CAMPAIGN */]: \"not-started\"\n          });\n          setTab(\"promotion\" /* PROMOTION */);\n          break;\n        }\n        setTabState((prev) => ({\n          ...prev,\n          [\"promotion\" /* PROMOTION */]: \"completed\",\n          [\"campaign\" /* CAMPAIGN */]: \"in-progress\"\n        }));\n        setTab(tab2);\n        break;\n      }\n    }\n  };\n  const handleContinue = async () => {\n    switch (tab) {\n      case \"type\" /* TYPE */:\n        handleTabChange(\"promotion\" /* PROMOTION */);\n        break;\n      case \"promotion\" /* PROMOTION */: {\n        const valid = await form.trigger();\n        if (valid) {\n          handleTabChange(\"campaign\" /* CAMPAIGN */);\n        }\n        break;\n      }\n      case \"campaign\" /* CAMPAIGN */:\n        break;\n    }\n  };\n  const watchTemplateId = useWatch({\n    control: form.control,\n    name: \"template_id\"\n  });\n  const currentTemplate = useMemo(() => {\n    const currentTemplate2 = templates.find(\n      (template) => template.id === watchTemplateId\n    );\n    if (!currentTemplate2) {\n      return;\n    }\n    reset({ ...defaultValues, template_id: watchTemplateId });\n    for (const [key, value] of Object.entries(currentTemplate2.defaults)) {\n      if (typeof value === \"object\") {\n        for (const [subKey, subValue] of Object.entries(value)) {\n          setValue(`application_method.${subKey}`, subValue);\n        }\n      } else {\n        setValue(key, value);\n      }\n    }\n    return currentTemplate2;\n  }, [watchTemplateId, setValue, reset]);\n  const watchValueType = useWatch({\n    control: form.control,\n    name: \"application_method.type\"\n  });\n  const isFixedValueType = watchValueType === \"fixed\";\n  const watchAllocation = useWatch({\n    control: form.control,\n    name: \"application_method.allocation\"\n  });\n  useEffect(() => {\n    if (watchAllocation === \"across\") {\n      setValue(\"application_method.max_quantity\", null);\n    }\n  }, [watchAllocation, setValue]);\n  const watchType = useWatch({\n    control: form.control,\n    name: \"type\"\n  });\n  const isTypeStandard = watchType === \"standard\";\n  const targetType = useWatch({\n    control: form.control,\n    name: \"application_method.target_type\"\n  });\n  const isTargetTypeOrder = targetType === \"order\";\n  const formData = form.getValues();\n  let campaignQuery = {};\n  if (isFixedValueType && formData.application_method.currency_code) {\n    campaignQuery = {\n      budget: { currency_code: formData.application_method.currency_code }\n    };\n  }\n  const { campaigns } = useCampaigns(campaignQuery);\n  const watchCampaignChoice = useWatch({\n    control: form.control,\n    name: \"campaign_choice\"\n  });\n  useEffect(() => {\n    const formData2 = getValues();\n    if (watchCampaignChoice !== \"existing\") {\n      setValue(\"campaign_id\", void 0);\n    }\n    if (watchCampaignChoice !== \"new\") {\n      setValue(\"campaign\", void 0);\n    }\n    if (watchCampaignChoice === \"new\") {\n      if (!formData2.campaign || !formData2.campaign?.budget?.type) {\n        setValue(\"campaign\", {\n          ...DEFAULT_CAMPAIGN_VALUES,\n          budget: {\n            ...DEFAULT_CAMPAIGN_VALUES.budget,\n            currency_code: formData2.application_method.currency_code\n          }\n        });\n      }\n    }\n  }, [watchCampaignChoice, getValues, setValue]);\n  const watchRules = useWatch({\n    control: form.control,\n    name: \"rules\"\n  });\n  const watchCurrencyRule = watchRules.find(\n    (rule) => rule.attribute === \"currency_code\"\n  );\n  if (watchCurrencyRule) {\n    const formData2 = form.getValues();\n    const currencyCode = formData2.application_method.currency_code;\n    const ruleValue = watchCurrencyRule.values;\n    if (!Array.isArray(ruleValue) && currencyCode !== ruleValue) {\n      form.setValue(\"application_method.currency_code\", ruleValue);\n    }\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { className: \"flex h-full flex-col\", onSubmit: handleSubmit, children: [\n    /* @__PURE__ */ jsxs(\n      ProgressTabs,\n      {\n        value: tab,\n        onValueChange: (tab2) => handleTabChange(tab2),\n        className: \"flex h-full flex-col overflow-hidden\",\n        children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex w-full items-center justify-between gap-x-4\", children: /* @__PURE__ */ jsx(\"div\", { className: \"-my-2 w-full max-w-[600px] border-l\", children: /* @__PURE__ */ jsxs(ProgressTabs.List, { className: \"grid w-full grid-cols-3\", children: [\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Trigger,\n              {\n                className: \"w-full\",\n                value: \"type\" /* TYPE */,\n                status: tabState[\"type\" /* TYPE */],\n                children: t(\"promotions.tabs.template\")\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Trigger,\n              {\n                className: \"w-full\",\n                value: \"promotion\" /* PROMOTION */,\n                status: tabState[\"promotion\" /* PROMOTION */],\n                children: t(\"promotions.tabs.details\")\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Trigger,\n              {\n                className: \"w-full\",\n                value: \"campaign\" /* CAMPAIGN */,\n                status: tabState[\"campaign\" /* CAMPAIGN */],\n                children: t(\"promotions.tabs.campaign\")\n              }\n            )\n          ] }) }) }) }),\n          /* @__PURE__ */ jsxs(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: [\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Content,\n              {\n                value: \"type\" /* TYPE */,\n                className: \"size-full overflow-y-auto\",\n                children: /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full flex-col items-center\", children: /* @__PURE__ */ jsx(\"div\", { className: \"w-full max-w-[720px] py-16\", children: /* @__PURE__ */ jsx(\n                  Form.Field,\n                  {\n                    control: form.control,\n                    name: \"template_id\",\n                    render: ({ field }) => {\n                      return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.fields.type\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                          RadioGroup,\n                          {\n                            className: \"flex-col gap-y-3\",\n                            ...field,\n                            onValueChange: field.onChange,\n                            children: templates.map((template) => {\n                              return /* @__PURE__ */ jsx(\n                                RadioGroup.ChoiceBox,\n                                {\n                                  value: template.id,\n                                  label: template.title,\n                                  description: template.description\n                                },\n                                template.id\n                              );\n                            })\n                          },\n                          \"template_id\"\n                        ) }),\n                        /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                      ] });\n                    }\n                  }\n                ) }) })\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Content,\n              {\n                value: \"promotion\" /* PROMOTION */,\n                className: \"size-full overflow-y-auto\",\n                children: /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full flex-col items-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 py-16\", children: [\n                  /* @__PURE__ */ jsxs(Heading, { level: \"h1\", className: \"text-fg-base\", children: [\n                    t(`promotions.sections.details`),\n                    currentTemplate?.title && /* @__PURE__ */ jsx(\n                      Badge,\n                      {\n                        className: \"ml-2 align-middle\",\n                        color: \"grey\",\n                        size: \"2xsmall\",\n                        rounded: \"full\",\n                        children: currentTemplate?.title\n                      }\n                    )\n                  ] }),\n                  form.formState.errors.root && /* @__PURE__ */ jsx(\n                    Alert,\n                    {\n                      variant: \"error\",\n                      dismissible: false,\n                      className: \"text-balance\",\n                      children: form.formState.errors.root.message\n                    }\n                  ),\n                  /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"is_automatic\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.form.method.label\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                            RadioGroup,\n                            {\n                              className: \"flex gap-y-3\",\n                              ...field,\n                              value: field.value,\n                              onValueChange: field.onChange,\n                              children: [\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"false\",\n                                    label: t(\"promotions.form.method.code.title\"),\n                                    description: t(\n                                      \"promotions.form.method.code.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                ),\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"true\",\n                                    label: t(\n                                      \"promotions.form.method.automatic.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.method.automatic.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                )\n                              ]\n                            }\n                          ) }),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                        ] });\n                      }\n                    }\n                  ),\n                  /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"status\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.form.status.label\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                            RadioGroup,\n                            {\n                              className: \"flex gap-y-3\",\n                              ...field,\n                              value: field.value,\n                              onValueChange: field.onChange,\n                              children: [\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"draft\",\n                                    label: t(\"promotions.form.status.draft.title\"),\n                                    description: t(\n                                      \"promotions.form.status.draft.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                ),\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"active\",\n                                    label: t(\"promotions.form.status.active.title\"),\n                                    description: t(\n                                      \"promotions.form.status.active.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                )\n                              ]\n                            }\n                          ) }),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                        ] });\n                      }\n                    }\n                  ),\n                  /* @__PURE__ */ jsx(\"div\", { className: \"flex gap-y-4\", children: /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"code\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { className: \"basis-1/2\", children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.form.code.title\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field, placeholder: \"SUMMER15\" }) }),\n                          /* @__PURE__ */ jsx(\n                            Text,\n                            {\n                              size: \"small\",\n                              leading: \"compact\",\n                              className: \"text-ui-fg-subtle\",\n                              children: /* @__PURE__ */ jsx(\n                                Trans,\n                                {\n                                  t,\n                                  i18nKey: \"promotions.form.code.description\",\n                                  components: [/* @__PURE__ */ jsx(\"br\", {}, \"break\")]\n                                }\n                              )\n                            }\n                          )\n                        ] });\n                      }\n                    }\n                  ) }),\n                  !currentTemplate?.hiddenFields?.includes(\"type\") && /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"type\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.fields.type\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                            RadioGroup,\n                            {\n                              className: \"flex gap-y-3\",\n                              ...field,\n                              onValueChange: field.onChange,\n                              children: [\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"standard\",\n                                    label: t(\n                                      \"promotions.form.type.standard.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.type.standard.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                ),\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"buyget\",\n                                    label: t(\"promotions.form.type.buyget.title\"),\n                                    description: t(\n                                      \"promotions.form.type.buyget.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                )\n                              ]\n                            }\n                          ) }),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                        ] });\n                      }\n                    }\n                  ),\n                  /* @__PURE__ */ jsx(Divider, {}),\n                  /* @__PURE__ */ jsx(RulesFormField, { form, ruleType: \"rules\" }),\n                  /* @__PURE__ */ jsx(Divider, {}),\n                  !currentTemplate?.hiddenFields?.includes(\n                    \"application_method.type\"\n                  ) && /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"application_method.type\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.fields.value_type\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                            RadioGroup,\n                            {\n                              className: \"flex gap-y-3\",\n                              ...field,\n                              onValueChange: field.onChange,\n                              children: [\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"fixed\",\n                                    label: t(\n                                      \"promotions.form.value_type.fixed.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.value_type.fixed.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                ),\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"percentage\",\n                                    label: t(\n                                      \"promotions.form.value_type.percentage.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.value_type.percentage.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                )\n                              ]\n                            }\n                          ) }),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                        ] });\n                      }\n                    }\n                  ),\n                  /* @__PURE__ */ jsxs(\"div\", { className: \"flex gap-x-2 gap-y-4\", children: [\n                    !currentTemplate?.hiddenFields?.includes(\n                      \"application_method.value\"\n                    ) && /* @__PURE__ */ jsx(\n                      Form.Field,\n                      {\n                        control: form.control,\n                        name: \"application_method.value\",\n                        render: ({ field: { onChange, value, ...field } }) => {\n                          const currencyCode = form.getValues().application_method.currency_code;\n                          return /* @__PURE__ */ jsxs(Form.Item, { className: \"basis-1/2\", children: [\n                            /* @__PURE__ */ jsx(\n                              Form.Label,\n                              {\n                                tooltip: currencyCode || !isFixedValueType ? void 0 : t(\"promotions.fields.amount.tooltip\"),\n                                children: t(\"promotions.form.value.title\")\n                              }\n                            ),\n                            /* @__PURE__ */ jsx(Form.Control, { children: isFixedValueType ? /* @__PURE__ */ jsx(\n                              CurrencyInput,\n                              {\n                                ...field,\n                                min: 0,\n                                onValueChange: (value2) => {\n                                  onChange(value2 ? parseInt(value2) : \"\");\n                                },\n                                code: currencyCode || \"USD\",\n                                symbol: currencyCode ? getCurrencySymbol(currencyCode) : \"$\",\n                                value,\n                                disabled: !currencyCode\n                              }\n                            ) : /* @__PURE__ */ jsx(\n                              DeprecatedPercentageInput,\n                              {\n                                className: \"text-right\",\n                                min: 0,\n                                max: 100,\n                                ...field,\n                                value,\n                                onChange: (e) => {\n                                  onChange(\n                                    e.target.value === \"\" ? null : parseInt(e.target.value)\n                                  );\n                                }\n                              },\n                              \"amount\"\n                            ) }),\n                            /* @__PURE__ */ jsx(\n                              Text,\n                              {\n                                size: \"small\",\n                                leading: \"compact\",\n                                className: \"text-ui-fg-subtle\",\n                                children: /* @__PURE__ */ jsx(\n                                  Trans,\n                                  {\n                                    t,\n                                    i18nKey: isFixedValueType ? \"promotions.form.value_type.fixed.description\" : \"promotions.form.value_type.percentage.description\",\n                                    components: [/* @__PURE__ */ jsx(\"br\", {}, \"break\")]\n                                  }\n                                )\n                              }\n                            ),\n                            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                          ] });\n                        }\n                      }\n                    ),\n                    isTypeStandard && watchAllocation === \"each\" && /* @__PURE__ */ jsx(\n                      Form.Field,\n                      {\n                        control: form.control,\n                        name: \"application_method.max_quantity\",\n                        render: ({ field }) => {\n                          return /* @__PURE__ */ jsxs(Form.Item, { className: \"basis-1/2\", children: [\n                            /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.form.max_quantity.title\") }),\n                            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                              Input,\n                              {\n                                ...form.register(\n                                  \"application_method.max_quantity\",\n                                  { valueAsNumber: true }\n                                ),\n                                type: \"number\",\n                                min: 1,\n                                placeholder: \"3\"\n                              }\n                            ) }),\n                            /* @__PURE__ */ jsx(\n                              Text,\n                              {\n                                size: \"small\",\n                                leading: \"compact\",\n                                className: \"text-ui-fg-subtle\",\n                                children: /* @__PURE__ */ jsx(\n                                  Trans,\n                                  {\n                                    t,\n                                    i18nKey: \"promotions.form.max_quantity.description\",\n                                    components: [/* @__PURE__ */ jsx(\"br\", {}, \"break\")]\n                                  }\n                                )\n                              }\n                            )\n                          ] });\n                        }\n                      }\n                    )\n                  ] }),\n                  isTypeStandard && !currentTemplate?.hiddenFields?.includes(\n                    \"application_method.allocation\"\n                  ) && /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: \"application_method.allocation\",\n                      render: ({ field }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                          /* @__PURE__ */ jsx(Form.Label, { children: t(\"promotions.fields.allocation\") }),\n                          /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n                            RadioGroup,\n                            {\n                              className: \"flex gap-y-3\",\n                              ...field,\n                              onValueChange: field.onChange,\n                              children: [\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"each\",\n                                    label: t(\n                                      \"promotions.form.allocation.each.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.allocation.each.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                ),\n                                /* @__PURE__ */ jsx(\n                                  RadioGroup.ChoiceBox,\n                                  {\n                                    value: \"across\",\n                                    label: t(\n                                      \"promotions.form.allocation.across.title\"\n                                    ),\n                                    description: t(\n                                      \"promotions.form.allocation.across.description\"\n                                    ),\n                                    className: clx(\"basis-1/2\")\n                                  }\n                                )\n                              ]\n                            }\n                          ) }),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                        ] });\n                      }\n                    }\n                  ),\n                  !isTypeStandard && /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx(\n                    RulesFormField,\n                    {\n                      form,\n                      ruleType: \"buy-rules\",\n                      scope: \"application_method.buy_rules\"\n                    }\n                  ) }),\n                  !isTargetTypeOrder && /* @__PURE__ */ jsxs(Fragment, { children: [\n                    /* @__PURE__ */ jsx(Divider, {}),\n                    /* @__PURE__ */ jsx(\n                      RulesFormField,\n                      {\n                        form,\n                        ruleType: \"target-rules\",\n                        scope: \"application_method.target_rules\"\n                      }\n                    )\n                  ] })\n                ] }) })\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              ProgressTabs.Content,\n              {\n                value: \"campaign\" /* CAMPAIGN */,\n                className: \"size-full overflow-auto\",\n                children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col items-center\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 py-16\", children: /* @__PURE__ */ jsx(\n                  AddCampaignPromotionFields,\n                  {\n                    form,\n                    campaigns: campaigns || []\n                  }\n                ) }) })\n              }\n            )\n          ] })\n        ]\n      }\n    ),\n    /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      tab === \"campaign\" /* CAMPAIGN */ ? /* @__PURE__ */ jsx(\n        Button,\n        {\n          type: \"submit\",\n          size: \"small\",\n          isLoading: false,\n          children: t(\"actions.save\")\n        },\n        \"save-btn\"\n      ) : /* @__PURE__ */ jsx(\n        Button,\n        {\n          type: \"button\",\n          onClick: handleContinue,\n          size: \"small\",\n          children: t(\"actions.continue\")\n        },\n        \"continue-btn\"\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/promotions/promotion-create/promotion-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar PromotionCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreatePromotionForm, {}) });\n};\nexport {\n  PromotionCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,mBAA6C;AAiJ7C,yBAAoC;AAqyBpC,IAAAA,sBAA4B;AAh7B5B,IAAI,aAAa,EAAE;AAAA,EACjB,EAAE,OAAO;AAAA,IACP,IAAI,EAAE,OAAO,EAAE,SAAS;AAAA,IACxB,WAAW,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,iBAAiB,CAAC;AAAA,IAC1D,UAAU,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,iBAAiB,CAAC;AAAA,IACzD,QAAQ,EAAE,MAAM;AAAA,MACd,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,iBAAiB,CAAC;AAAA,MAC/C,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,SAAS,iBAAiB,CAAC;AAAA,MAC/C,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,EAAE,SAAS,iBAAiB,CAAC;AAAA,IAC1D,CAAC;AAAA,IACD,UAAU,EAAE,QAAQ,EAAE,SAAS;AAAA,IAC/B,WAAW,EAAE,QAAQ,EAAE,SAAS;AAAA,IAChC,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,EAClC,CAAC;AACH;AACA,IAAI,wBAAwB,EAAE,OAAO;AAAA,EACnC,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,iBAAiB,EAAE,KAAK,CAAC,QAAQ,YAAY,KAAK,CAAC,EAAE,SAAS;AAAA,EAC9D,cAAc,EAAE,OAAO,EAAE,YAAY;AAAA,EACrC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,MAAM,EAAE,KAAK,CAAC,UAAU,UAAU,CAAC;AAAA,EACnC,QAAQ,EAAE,KAAK,CAAC,SAAS,UAAU,UAAU,CAAC;AAAA,EAC9C,OAAO;AAAA,EACP,oBAAoB,EAAE,OAAO;AAAA,IAC3B,YAAY,EAAE,KAAK,CAAC,QAAQ,QAAQ,CAAC;AAAA,IACrC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,IACvB,eAAe,EAAE,OAAO,EAAE,SAAS;AAAA,IACnC,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAC7C,cAAc;AAAA,IACd,WAAW;AAAA,IACX,MAAM,EAAE,KAAK,CAAC,SAAS,YAAY,CAAC;AAAA,IACpC,aAAa,EAAE,KAAK,CAAC,SAAS,oBAAoB,OAAO,CAAC;AAAA,EAC5D,CAAC;AAAA,EACD,UAAU,qBAAqB,SAAS;AAC1C,CAAC,EAAE;AAAA,EACD,CAAC,SAAS;AACR,QAAI,KAAK,mBAAmB,eAAe,UAAU;AACnD,aAAO;AAAA,IACT;AACA,WAAO,KAAK,mBAAmB,eAAe,UAAU,OAAO,KAAK,mBAAmB,iBAAiB;AAAA,EAC1G;AAAA,EACA;AAAA,IACE,MAAM,CAAC,iCAAiC;AAAA,IACxC,SAAS;AAAA,EACX;AACF;AAGA,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,YAAY;AAAA,EACd;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc,CAAC,GAAG,kBAAkB;AAAA,IACpC,UAAU;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,QAClB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc,CAAC,GAAG,kBAAkB;AAAA,IACpC,UAAU;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,QAClB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc,CAAC,GAAG,kBAAkB;AAAA,IACpC,UAAU;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,QAClB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc,CAAC,GAAG,kBAAkB;AAAA,IACpC,UAAU;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,QAClB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc,CAAC,GAAG,oBAAoB,0BAA0B;AAAA,IAChE,UAAU;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,oBAAoB;AAAA,QAClB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,mBAAmB;AAAA,QACnB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,gBAAgB;AAAA,EAClB,aAAa;AAAA,EACb,aAAa,UAAU,CAAC,EAAE;AAAA,EAC1B,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO,CAAC;AAAA,EACR,oBAAoB;AAAA,IAClB,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc,CAAC;AAAA,IACf,WAAW,CAAC;AAAA,EACd;AAAA,EACA,UAAU;AACZ;AACA,IAAI,sBAAsB,MAAM;AA/NhC;AAgOE,QAAM,CAAC,KAAK,MAAM,QAAI;AAAA,IAAS;AAAA;AAAA,EAAiB;AAChD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS;AAAA,IACvC;AAAA,MAAC;AAAA;AAAA,IAAiB,GAAG;AAAA,IACrB;AAAA,MAAC;AAAA;AAAA,IAA2B,GAAG;AAAA,IAC/B;AAAA,MAAC;AAAA;AAAA,IAAyB,GAAG;AAAA,EAC/B,CAAC;AACD,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB;AAAA,IACA,UAAU,EAAY,qBAAqB;AAAA,EAC7C,CAAC;AACD,QAAM,EAAE,UAAU,OAAO,UAAU,IAAI;AACvC,QAAM,EAAE,aAAa,gBAAgB,IAAI,mBAAmB;AAC5D,QAAM,eAAe,KAAK;AAAA,IACxB,OAAO,SAAS;AACd,YAAM;AAAA,QACJ,iBAAiB;AAAA,QACjB;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,YAAM;AAAA,QACJ,cAAc,kBAAkB,CAAC;AAAA,QACjC,WAAW,eAAe,CAAC;AAAA,QAC3B,GAAG;AAAA,MACL,IAAI;AACJ,YAAM,iBAAiB;AAAA,QACrB,GAAG,gBAAgB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS;AAAA,QAC9C,GAAG,aAAa,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS;AAAA,QAC3C,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS;AAAA,MACtC;AACA,YAAM,4BAA4B,CAAC;AACnC,iBAAW,QAAQ,gBAAgB;AACjC,kCAA0B,KAAK,SAAS,IAAI,KAAK,eAAe,WAAW,SAAS,KAAK,MAAM,IAAI,KAAK;AAAA,MAC1G;AACA,YAAM,iBAAiB,CAAC,WAAW;AACjC,eAAO,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU;AAAA,UACvD,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,UAChB,QAAQ,KAAK;AAAA,QACf,EAAE;AAAA,MACJ;AACA;AAAA,QACE;AAAA,UACE,GAAG;AAAA,UACH,OAAO,eAAe,KAAK;AAAA,UAC3B,oBAAoB;AAAA,YAClB,GAAG;AAAA,YACH,GAAG;AAAA,YACH,cAAc,eAAe,eAAe;AAAA,YAC5C,WAAW,eAAe,YAAY;AAAA,UACxC;AAAA,UACA,cAAc,iBAAiB;AAAA,QACjC;AAAA,QACA;AAAA,UACE,WAAW,CAAC,EAAE,UAAU,MAAM;AAC5B,kBAAM;AAAA,cACJA,GAAE,4CAA4C;AAAA,gBAC5C,MAAM,UAAU;AAAA,cAClB,CAAC;AAAA,YACH;AACA,0BAAc,eAAe,UAAU,EAAE,EAAE;AAAA,UAC7C;AAAA,UACA,SAAS,CAAC,MAAM;AACd,kBAAM,MAAM,EAAE,OAAO;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,OAAO,UAAU;AACf,YAAM,EAAE,UAAU,WAAW,GAAG,KAAK,IAAI,SAAS,CAAC;AACnD,YAAM,sBAAsB,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,EAAE;AACtD,UAAI,qBAAqB;AACvB,cAAM,MAAMA,GAAE,qCAAqC,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,OAAO,SAAS;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,oBAAY,CAAC,UAAU;AAAA,UACrB,GAAG;AAAA,UACH;AAAA,YAAC;AAAA;AAAA,UAAiB,GAAG;AAAA,QACvB,EAAE;AACF,eAAO,IAAI;AACX;AAAA,MACF,KAAK;AACH,oBAAY,CAAC,UAAU;AAAA,UACrB,GAAG;AAAA,UACH;AAAA,YAAC;AAAA;AAAA,UAAiB,GAAG;AAAA,UACrB;AAAA,YAAC;AAAA;AAAA,UAA2B,GAAG;AAAA,QACjC,EAAE;AACF,eAAO,IAAI;AACX;AAAA,MACF,KAAK,YAA2B;AAC9B,cAAM,QAAQ,MAAM,KAAK,QAAQ;AACjC,YAAI,CAAC,OAAO;AACV,sBAAY;AAAA,YACV;AAAA,cAAC;AAAA;AAAA,YAAiB,GAAG;AAAA,YACrB;AAAA,cAAC;AAAA;AAAA,YAA2B,GAAG;AAAA,YAC/B;AAAA,cAAC;AAAA;AAAA,YAAyB,GAAG;AAAA,UAC/B,CAAC;AACD;AAAA,YAAO;AAAA;AAAA,UAA2B;AAClC;AAAA,QACF;AACA,oBAAY,CAAC,UAAU;AAAA,UACrB,GAAG;AAAA,UACH;AAAA,YAAC;AAAA;AAAA,UAA2B,GAAG;AAAA,UAC/B;AAAA,YAAC;AAAA;AAAA,UAAyB,GAAG;AAAA,QAC/B,EAAE;AACF,eAAO,IAAI;AACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,YAAY;AACjC,YAAQ,KAAK;AAAA,MACX,KAAK;AACH;AAAA,UAAgB;AAAA;AAAA,QAA2B;AAC3C;AAAA,MACF,KAAK,aAA6B;AAChC,cAAM,QAAQ,MAAM,KAAK,QAAQ;AACjC,YAAI,OAAO;AACT;AAAA,YAAgB;AAAA;AAAA,UAAyB;AAAA,QAC3C;AACA;AAAA,MACF;AAAA,MACA,KAAK;AACH;AAAA,IACJ;AAAA,EACF;AACA,QAAM,kBAAkB,SAAS;AAAA,IAC/B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,sBAAkB,sBAAQ,MAAM;AACpC,UAAM,mBAAmB,UAAU;AAAA,MACjC,CAAC,aAAa,SAAS,OAAO;AAAA,IAChC;AACA,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,EAAE,GAAG,eAAe,aAAa,gBAAgB,CAAC;AACxD,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,iBAAiB,QAAQ,GAAG;AACpE,UAAI,OAAO,UAAU,UAAU;AAC7B,mBAAW,CAAC,QAAQ,QAAQ,KAAK,OAAO,QAAQ,KAAK,GAAG;AACtD,mBAAS,sBAAsB,MAAM,IAAI,QAAQ;AAAA,QACnD;AAAA,MACF,OAAO;AACL,iBAAS,KAAK,KAAK;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,iBAAiB,UAAU,KAAK,CAAC;AACrC,QAAM,iBAAiB,SAAS;AAAA,IAC9B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,mBAAmB,mBAAmB;AAC5C,QAAM,kBAAkB,SAAS;AAAA,IAC/B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,oBAAoB,UAAU;AAChC,eAAS,mCAAmC,IAAI;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,iBAAiB,QAAQ,CAAC;AAC9B,QAAM,YAAY,SAAS;AAAA,IACzB,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,iBAAiB,cAAc;AACrC,QAAM,aAAa,SAAS;AAAA,IAC1B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,oBAAoB,eAAe;AACzC,QAAM,WAAW,KAAK,UAAU;AAChC,MAAI,gBAAgB,CAAC;AACrB,MAAI,oBAAoB,SAAS,mBAAmB,eAAe;AACjE,oBAAgB;AAAA,MACd,QAAQ,EAAE,eAAe,SAAS,mBAAmB,cAAc;AAAA,IACrE;AAAA,EACF;AACA,QAAM,EAAE,UAAU,IAAI,aAAa,aAAa;AAChD,QAAM,sBAAsB,SAAS;AAAA,IACnC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,8BAAU,MAAM;AAjalB,QAAAC,KAAAC;AAkaI,UAAM,YAAY,UAAU;AAC5B,QAAI,wBAAwB,YAAY;AACtC,eAAS,eAAe,MAAM;AAAA,IAChC;AACA,QAAI,wBAAwB,OAAO;AACjC,eAAS,YAAY,MAAM;AAAA,IAC7B;AACA,QAAI,wBAAwB,OAAO;AACjC,UAAI,CAAC,UAAU,YAAY,GAACA,OAAAD,MAAA,UAAU,aAAV,gBAAAA,IAAoB,WAApB,gBAAAC,IAA4B,OAAM;AAC5D,iBAAS,YAAY;AAAA,UACnB,GAAG;AAAA,UACH,QAAQ;AAAA,YACN,GAAG,wBAAwB;AAAA,YAC3B,eAAe,UAAU,mBAAmB;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,qBAAqB,WAAW,QAAQ,CAAC;AAC7C,QAAM,aAAa,SAAS;AAAA,IAC1B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,oBAAoB,WAAW;AAAA,IACnC,CAAC,SAAS,KAAK,cAAc;AAAA,EAC/B;AACA,MAAI,mBAAmB;AACrB,UAAM,YAAY,KAAK,UAAU;AACjC,UAAM,eAAe,UAAU,mBAAmB;AAClD,UAAM,YAAY,kBAAkB;AACpC,QAAI,CAAC,MAAM,QAAQ,SAAS,KAAK,iBAAiB,WAAW;AAC3D,WAAK,SAAS,oCAAoC,SAAS;AAAA,IAC7D;AAAA,EACF;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,WAAW,wBAAwB,UAAU,cAAc,UAAU;AAAA,QAC3J;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,eAAe,CAAC,SAAS,gBAAgB,IAAI;AAAA,QAC7C,WAAW;AAAA,QACX,UAAU;AAAA,cACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,oDAAoD,cAA0B,wBAAI,OAAO,EAAE,WAAW,uCAAuC,cAA0B,yBAAK,aAAa,MAAM,EAAE,WAAW,2BAA2B,UAAU;AAAA,gBAC/T;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,QAAQ;AAAA,kBAAS;AAAA;AAAA,gBAAiB;AAAA,gBAClC,UAAUF,GAAE,0BAA0B;AAAA,cACxC;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,QAAQ;AAAA,kBAAS;AAAA;AAAA,gBAA2B;AAAA,gBAC5C,UAAUA,GAAE,yBAAyB;AAAA,cACvC;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,QAAQ;AAAA,kBAAS;AAAA;AAAA,gBAAyB;AAAA,gBAC1C,UAAUA,GAAE,0BAA0B;AAAA,cACxC;AAAA,YACF;AAAA,UACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,cACI,yBAAK,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,UAAU;AAAA,gBAC7E;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,cAA0B,wBAAI,OAAO,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,8BAA8B,cAA0B;AAAA,kBAClM,KAAK;AAAA,kBACL;AAAA,oBACE,SAAS,KAAK;AAAA,oBACd,MAAM;AAAA,oBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,iCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,4BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,wBAAwB,EAAE,CAAC;AAAA,4BACzD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,0BAC5D;AAAA,0BACA;AAAA,4BACE,WAAW;AAAA,4BACX,GAAG;AAAA,4BACH,eAAe,MAAM;AAAA,4BACrB,UAAU,UAAU,IAAI,CAAC,aAAa;AACpC,yCAAuB;AAAA,gCACrB,WAAW;AAAA,gCACX;AAAA,kCACE,OAAO,SAAS;AAAA,kCAChB,OAAO,SAAS;AAAA,kCAChB,aAAa,SAAS;AAAA,gCACxB;AAAA,gCACA,SAAS;AAAA,8BACX;AAAA,4BACF,CAAC;AAAA,0BACH;AAAA,0BACA;AAAA,wBACF,EAAE,CAAC;AAAA,4BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,sBAC3C,EAAE,CAAC;AAAA,oBACL;AAAA,kBACF;AAAA,gBACF,EAAE,CAAC,EAAE,CAAC;AAAA,cACR;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,cAA0B,wBAAI,OAAO,EAAE,WAAW,wCAAwC,cAA0B,yBAAK,OAAO,EAAE,WAAW,oDAAoD,UAAU;AAAA,sBACzL,yBAAK,SAAS,EAAE,OAAO,MAAM,WAAW,gBAAgB,UAAU;AAAA,oBAChFA,GAAE,6BAA6B;AAAA,qBAC/B,mDAAiB,cAAyB;AAAA,sBACxC;AAAA,sBACA;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,wBACP,MAAM;AAAA,wBACN,SAAS;AAAA,wBACT,UAAU,mDAAiB;AAAA,sBAC7B;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,kBACH,KAAK,UAAU,OAAO,YAAwB;AAAA,oBAC5C;AAAA,oBACA;AAAA,sBACE,SAAS;AAAA,sBACT,aAAa;AAAA,sBACb,WAAW;AAAA,sBACX,UAAU,KAAK,UAAU,OAAO,KAAK;AAAA,oBACvC;AAAA,kBACF;AAAA,sBACgB;AAAA,oBACd,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,8BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,8BAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,4BAC5D;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,8BACX,GAAG;AAAA,8BACH,OAAO,MAAM;AAAA,8BACb,eAAe,MAAM;AAAA,8BACrB,UAAU;AAAA,oCACQ;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA,GAAE,mCAAmC;AAAA,oCAC5C,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,oCACgB;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,8BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,wBAC3C,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF;AAAA,sBACgB;AAAA,oBACd,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,8BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,8BAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,4BAC5D;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,8BACX,GAAG;AAAA,8BACH,OAAO,MAAM;AAAA,8BACb,eAAe,MAAM;AAAA,8BACrB,UAAU;AAAA,oCACQ;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA,GAAE,oCAAoC;AAAA,oCAC7C,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,oCACgB;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA,GAAE,qCAAqC;AAAA,oCAC9C,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,8BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,wBAC3C,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF;AAAA,sBACgB,wBAAI,OAAO,EAAE,WAAW,gBAAgB,cAA0B;AAAA,oBAChF,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,8BACzD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,4BAA4B,EAAE,CAAC;AAAA,8BAC7D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,OAAO,aAAa,WAAW,CAAC,EAAE,CAAC;AAAA,8BACjG;AAAA,4BACd;AAAA,4BACA;AAAA,8BACE,MAAM;AAAA,8BACN,SAAS;AAAA,8BACT,WAAW;AAAA,8BACX,cAA0B;AAAA,gCACxB;AAAA,gCACA;AAAA,kCACE,GAAAA;AAAA,kCACA,SAAS;AAAA,kCACT,YAAY,KAAiB,wBAAI,MAAM,CAAC,GAAG,OAAO,CAAC;AAAA,gCACrD;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,kBACH,GAAC,wDAAiB,iBAAjB,mBAA+B,SAAS,gBAA2B;AAAA,oBAClE,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,8BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,wBAAwB,EAAE,CAAC;AAAA,8BACzD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,4BAC5D;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,8BACX,GAAG;AAAA,8BACH,eAAe,MAAM;AAAA,8BACrB,UAAU;AAAA,oCACQ;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,oCACgB;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA,GAAE,mCAAmC;AAAA,oCAC5C,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,8BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,wBAC3C,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF;AAAA,sBACgB,wBAAI,SAAS,CAAC,CAAC;AAAA,sBACf,wBAAI,gBAAgB,EAAE,MAAM,UAAU,QAAQ,CAAC;AAAA,sBAC/C,wBAAI,SAAS,CAAC,CAAC;AAAA,kBAC/B,GAAC,wDAAiB,iBAAjB,mBAA+B;AAAA,oBAC9B;AAAA,4BACmB;AAAA,oBACnB,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,8BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,8BAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,4BAC5D;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,8BACX,GAAG;AAAA,8BACH,eAAe,MAAM;AAAA,8BACrB,UAAU;AAAA,oCACQ;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,oCACgB;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,8BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,wBAC3C,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF;AAAA,sBACgB,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,oBACzE,GAAC,wDAAiB,iBAAjB,mBAA+B;AAAA,sBAC9B;AAAA,8BACmB;AAAA,sBACnB,KAAK;AAAA,sBACL;AAAA,wBACE,SAAS,KAAK;AAAA,wBACd,MAAM;AAAA,wBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,gCAAM,eAAe,KAAK,UAAU,EAAE,mBAAmB;AACzD,qCAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,gCACzD;AAAA,8BACd,KAAK;AAAA,8BACL;AAAA,gCACE,SAAS,gBAAgB,CAAC,mBAAmB,SAASA,GAAE,kCAAkC;AAAA,gCAC1F,UAAUA,GAAE,6BAA6B;AAAA,8BAC3C;AAAA,4BACF;AAAA,gCACgB,wBAAI,KAAK,SAAS,EAAE,UAAU,uBAAmC;AAAA,8BAC/E;AAAA,8BACA;AAAA,gCACE,GAAG;AAAA,gCACH,KAAK;AAAA,gCACL,eAAe,CAAC,WAAW;AACzB,2CAAS,SAAS,SAAS,MAAM,IAAI,EAAE;AAAA,gCACzC;AAAA,gCACA,MAAM,gBAAgB;AAAA,gCACtB,QAAQ,eAAe,kBAAkB,YAAY,IAAI;AAAA,gCACzD;AAAA,gCACA,UAAU,CAAC;AAAA,8BACb;AAAA,4BACF,QAAoB;AAAA,8BAClB;AAAA,8BACA;AAAA,gCACE,WAAW;AAAA,gCACX,KAAK;AAAA,gCACL,KAAK;AAAA,gCACL,GAAG;AAAA,gCACH;AAAA,gCACA,UAAU,CAAC,MAAM;AACf;AAAA,oCACE,EAAE,OAAO,UAAU,KAAK,OAAO,SAAS,EAAE,OAAO,KAAK;AAAA,kCACxD;AAAA,gCACF;AAAA,8BACF;AAAA,8BACA;AAAA,4BACF,EAAE,CAAC;AAAA,gCACa;AAAA,8BACd;AAAA,8BACA;AAAA,gCACE,MAAM;AAAA,gCACN,SAAS;AAAA,gCACT,WAAW;AAAA,gCACX,cAA0B;AAAA,kCACxB;AAAA,kCACA;AAAA,oCACE,GAAAA;AAAA,oCACA,SAAS,mBAAmB,iDAAiD;AAAA,oCAC7E,YAAY,KAAiB,wBAAI,MAAM,CAAC,GAAG,OAAO,CAAC;AAAA,kCACrD;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,gCACgB,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,0BAC3C,EAAE,CAAC;AAAA,wBACL;AAAA,sBACF;AAAA,oBACF;AAAA,oBACA,kBAAkB,oBAAoB,cAA0B;AAAA,sBAC9D,KAAK;AAAA,sBACL;AAAA,wBACE,SAAS,KAAK;AAAA,wBACd,MAAM;AAAA,wBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qCAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,gCACzD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,oCAAoC,EAAE,CAAC;AAAA,gCACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,8BAC5D;AAAA,8BACA;AAAA,gCACE,GAAG,KAAK;AAAA,kCACN;AAAA,kCACA,EAAE,eAAe,KAAK;AAAA,gCACxB;AAAA,gCACA,MAAM;AAAA,gCACN,KAAK;AAAA,gCACL,aAAa;AAAA,8BACf;AAAA,4BACF,EAAE,CAAC;AAAA,gCACa;AAAA,8BACd;AAAA,8BACA;AAAA,gCACE,MAAM;AAAA,gCACN,SAAS;AAAA,gCACT,WAAW;AAAA,gCACX,cAA0B;AAAA,kCACxB;AAAA,kCACA;AAAA,oCACE,GAAAA;AAAA,oCACA,SAAS;AAAA,oCACT,YAAY,KAAiB,wBAAI,MAAM,CAAC,GAAG,OAAO,CAAC;AAAA,kCACrD;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,wBACL;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,kBACH,kBAAkB,GAAC,wDAAiB,iBAAjB,mBAA+B;AAAA,oBAChD;AAAA,4BACmB;AAAA,oBACnB,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM;AAAA,sBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,mCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,8BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,8BAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,4BAC5D;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,8BACX,GAAG;AAAA,8BACH,eAAe,MAAM;AAAA,8BACrB,UAAU;AAAA,oCACQ;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,oCACgB;AAAA,kCACd,WAAW;AAAA,kCACX;AAAA,oCACE,OAAO;AAAA,oCACP,OAAOA;AAAA,sCACL;AAAA,oCACF;AAAA,oCACA,aAAaA;AAAA,sCACX;AAAA,oCACF;AAAA,oCACA,WAAW,IAAI,WAAW;AAAA,kCAC5B;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF,EAAE,CAAC;AAAA,8BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,wBAC3C,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,CAAC,sBAAkC,wBAAI,6BAAU,EAAE,cAA0B;AAAA,oBAC3E;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA,UAAU;AAAA,sBACV,OAAO;AAAA,oBACT;AAAA,kBACF,EAAE,CAAC;AAAA,kBACH,CAAC,yBAAqC,yBAAK,6BAAU,EAAE,UAAU;AAAA,wBAC/C,wBAAI,SAAS,CAAC,CAAC;AAAA,wBACf;AAAA,sBACd;AAAA,sBACA;AAAA,wBACE;AAAA,wBACA,UAAU;AAAA,wBACV,OAAO;AAAA,sBACT;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,gBACL,EAAE,CAAC,EAAE,CAAC;AAAA,cACR;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,cAA0B,wBAAI,OAAO,EAAE,WAAW,8BAA8B,cAA0B,wBAAI,OAAO,EAAE,WAAW,oDAAoD,cAA0B;AAAA,kBAC9M;AAAA,kBACA;AAAA,oBACE;AAAA,oBACA,WAAW,aAAa,CAAC;AAAA,kBAC3B;AAAA,gBACF,EAAE,CAAC,EAAE,CAAC;AAAA,cACR;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,MAC3K,QAAQ,iBAA4C;AAAA,QAClD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAUA,GAAE,cAAc;AAAA,QAC5B;AAAA,QACA;AAAA,MACF,QAAoB;AAAA,QAClB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAUA,GAAE,kBAAkB;AAAA,QAChC;AAAA,QACA;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,kBAAkB,MAAM;AAC1B,aAAuB,oBAAAG,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,qBAAqB,CAAC,CAAC,EAAE,CAAC;AAC1G;", "names": ["import_jsx_runtime", "t", "_a", "_b", "jsx2"]}