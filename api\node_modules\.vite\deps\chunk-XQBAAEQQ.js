import {
  getCountryByIso2
} from "./chunk-XGFC5LFP.js";
import {
  taxRegionsQueryKeys,
  useTaxRegion
} from "./chunk-PBNFBMP6.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-BC3M3N6P.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var TaxRegionDetailBreadcrumb = (props) => {
  var _a, _b;
  const { id } = props.params || {};
  const { tax_region } = useTaxRegion(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!tax_region) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: ((_a = getCountryByIso2(tax_region.country_code)) == null ? void 0 : _a.display_name) || ((_b = tax_region.country_code) == null ? void 0 : _b.toUpperCase()) });
};
var taxRegionDetailQuery = (id) => ({
  queryKey: taxRegionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.taxRegion.retrieve(id)
});
var taxRegionLoader = async ({ params }) => {
  const id = params.id;
  const query = taxRegionDetailQuery(id);
  return queryClient.ensureQueryData(query);
};

export {
  TaxRegionDetailBreadcrumb,
  taxRegionLoader
};
//# sourceMappingURL=chunk-XQBAAEQQ.js.map
