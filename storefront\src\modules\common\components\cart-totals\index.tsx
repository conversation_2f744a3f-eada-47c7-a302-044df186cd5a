"use client"

import { convertCurrencyToLocale } from "@lib/util/money"
import Typography from "components/ui/typography"
import { Loader2 } from "lucide-react"
import React from "react"
import { useTranslation } from "react-i18next"
import { TCartCustomField } from "types/cart"
import { useCartStateStore } from "zustand-store/useCartStore"

type CartTotalsProps = {
  cart: TCartCustomField
  // isShippingLoading?: boolean
  isSubtotalLoading?: boolean
}

const CartTotals: React.FC<CartTotalsProps> = ({
  cart,
  // isShippingLoading = false,
  isSubtotalLoading = false,
}) => {
  const { t } = useTranslation("cart")
  const { cart: storeCart } = useCartStateStore()

  // Use cart from store if available (fresh data), fallback to prop
  const currentCart = (storeCart as TCartCustomField) || cart

  console.log("🚀 ~ CartTotals ~ cart (prop):", cart?.total)
  console.log("🚀 ~ CartTotals ~ storeCart:", (storeCart as any)?.total)
  console.log("🚀 ~ CartTotals ~ currentCart (used):", currentCart?.total)

  const {
    currency_code,
    total,
    subtotal,
    item_subtotal,
    tax_total,
    shipping_subtotal,
    gift_card_total,
  } = currentCart || {}

  const calculatedSubtotal = React.useMemo(() => {
    if (item_subtotal && item_subtotal > 0) {
      return item_subtotal
    }

    // Fallback: calculate from items
    if (currentCart?.items?.length) {
      return currentCart.items.reduce((acc, item) => {
        return acc + (item.unit_price || 0) * (item.quantity || 1)
      }, 0)
    }

    return 0
  }, [item_subtotal, currentCart?.items])

  const discount_subtotal = (currentCart as any)?.discount_subtotal

  if (!currentCart) return null

  // const listPromotionAutoList =
  //   cart.promotions?.filter(
  //     (promotion) => !promotion.code?.startsWith("PREORDER")
  //   ) || []

  const taxDetails = currentCart.items?.flatMap((item) => item.tax_lines) || []

  const groupedTaxes = taxDetails.reduce(
    (acc, tax) => {
      if (!tax?.description || !tax?.rate) return acc

      if (!acc[tax.description]) {
        acc[tax.description] = { ...tax, amount: 0 }
      }
      acc[tax.description].amount +=
        ((tax.rate / 100) * (subtotal || 0)) / (cart.items?.length || 1)
      return acc
    },
    {} as Record<string, (typeof taxDetails)[number] & { amount: number }>
  )

  const renderTotalRow = (
    label: string,
    amount?: number | null,
    isDiscount = false,
    isLoading = false
  ) => (
    <div className="flex items-center justify-between">
      <Typography
        variant="p"
        size="base"
        className={`font-semibold ${isDiscount ? "text-primary-main" : ""}`}
      >
        {label}
      </Typography>
      <Typography
        variant="p"
        size="base"
        className={`font-semibold ${isDiscount ? "text-primary-main" : ""}`}
        data-value={amount || 0}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
        ) : (
          <>
            {isDiscount ? "- " : ""}
            {convertCurrencyToLocale({ amount: amount ?? 0, currency_code })}
          </>
        )}
      </Typography>
    </div>
  )

  return (
    <div>
      <div className="txt-medium flex flex-col gap-y-3 text-ui-fg-subtle">
        {renderTotalRow(
          t("subtotal"),
          calculatedSubtotal,
          false,
          isSubtotalLoading
        )}
        {discount_subtotal
          ? renderTotalRow(t("discount"), discount_subtotal, true)
          : null}

        {/* {listPromotionAutoList.map((promotion) => {
          const baseValue = Number(promotion.application_method?.value) || 0
          const getPricePromotion =
            promotion.application_method?.type === "percentage"
              ? (baseValue * (subtotal || 0)) / 100
              : baseValue
          return (
            <React.Fragment key={promotion.code}>
              {renderTotalRow(promotion.code ?? "", getPricePromotion, true)}
            </React.Fragment>
          )
        })} */}

        <div className="flex items-center justify-between">
          <Typography variant="p" size="base" className="font-semibold">
            {t("shipping")}
          </Typography>
          <Typography
            variant="p"
            size="base"
            className="font-semibold"
            data-value={shipping_subtotal || 0}
          >
            {shipping_subtotal
              ? convertCurrencyToLocale({
                  amount: shipping_subtotal ?? 0,
                  currency_code,
                })
              : t("shipping_processing")}
          </Typography>
        </div>

        {gift_card_total
          ? renderTotalRow(t("gift_card"), gift_card_total, true)
          : null}

        <div className="flex items-center justify-between font-semibold">
          <Typography variant="p" size="base">
            {t("taxes")}
          </Typography>
          <Typography variant="p" size="base">
            {convertCurrencyToLocale({
              amount: tax_total || 0,
              currency_code,
            })}
          </Typography>
        </div>
      </div>
      <div className="my-4 h-px w-full border-b border-gray-200" />
      <div className="txt-medium my-3 flex items-center justify-between text-ui-fg-base">
        <Typography variant="p" size="base" className="font-medium">
          {t("total")}
        </Typography>
        <Typography
          variant="p"
          className="font-bold text-primary-main"
          data-testid="cart-total"
          data-value={total || 0}
        >
          {convertCurrencyToLocale({ amount: total ?? 0, currency_code })}
        </Typography>
      </div>
    </div>
  )
}

export default CartTotals
