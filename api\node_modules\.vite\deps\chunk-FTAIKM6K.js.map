{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-3IRPEKIV.mjs"], "sourcesContent": ["import {\n  Date<PERSON><PERSON>,\n  DateHeader\n} from \"./chunk-3OHUAQUF.mjs\";\nimport {\n  MoneyAmountCell\n} from \"./chunk-NNBHHXXN.mjs\";\nimport {\n  getOrderFulfillmentStatus,\n  getOrderPaymentStatus\n} from \"./chunk-7DXVXBSA.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\n\n// src/hooks/table/columns/use-order-table-columns.tsx\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\n\n// src/components/table/table-cells/order/country-cell/country-cell.tsx\nimport { Tooltip } from \"@medusajs/ui\";\nimport ReactCountryFlag from \"react-country-flag\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CountryCell = ({\n  country\n}) => {\n  if (!country) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-5 items-center justify-center\", children: /* @__PURE__ */ jsx(Tooltip, { content: country.display_name, children: /* @__PURE__ */ jsx(\"div\", { className: \"flex size-4 items-center justify-center overflow-hidden rounded-sm\", children: /* @__PURE__ */ jsx(\n    ReactCountryFlag,\n    {\n      countryCode: country.iso_2.toUpperCase(),\n      svg: true,\n      style: {\n        width: \"16px\",\n        height: \"16px\"\n      },\n      \"aria-label\": country.display_name\n    }\n  ) }) }) });\n};\n\n// src/components/table/table-cells/order/customer-cell/customer-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerCell = ({\n  customer\n}) => {\n  if (!customer) {\n    return /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-muted\", children: \"-\" });\n  }\n  const { first_name, last_name, email } = customer;\n  const name = [first_name, last_name].filter(Boolean).join(\" \");\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: name || email }) });\n};\nvar CustomerHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: t(\"fields.customer\") }) });\n};\n\n// src/components/table/table-cells/order/display-id-cell/display-id-cell.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs } from \"react/jsx-runtime\";\nvar DisplayIdCell = ({ displayId }) => {\n  if (!displayId) {\n    return /* @__PURE__ */ jsx3(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"text-ui-fg-subtle txt-compact-small flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsxs(\"span\", { className: \"truncate\", children: [\n    \"#\",\n    displayId\n  ] }) });\n};\nvar DisplayIdHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: t(\"fields.order\") }) });\n};\n\n// src/components/table/table-cells/order/fulfillment-status-cell/fulfillment-status-cell.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar FulfillmentStatusCell = ({\n  status\n}) => {\n  const { t } = useTranslation3();\n  if (!status) {\n    return \"-\";\n  }\n  const { label, color } = getOrderFulfillmentStatus(t, status);\n  return /* @__PURE__ */ jsx4(StatusCell, { color, children: label });\n};\nvar FulfillmentStatusHeader = () => {\n  const { t } = useTranslation3();\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: t(\"fields.fulfillment\") }) });\n};\n\n// src/components/table/table-cells/order/payment-status-cell/payment-status-cell.tsx\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nvar PaymentStatusCell = ({ status }) => {\n  const { t } = useTranslation4();\n  const { label, color } = getOrderPaymentStatus(t, status);\n  return /* @__PURE__ */ jsx5(StatusCell, { color, children: label });\n};\nvar PaymentStatusHeader = () => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsx5(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx5(\"span\", { className: \"truncate\", children: t(\"fields.payment\") }) });\n};\n\n// src/components/table/table-cells/order/sales-channel-cell/sales-channel-cell.tsx\nimport { useTranslation as useTranslation5 } from \"react-i18next\";\nimport { jsx as jsx6 } from \"react/jsx-runtime\";\nvar SalesChannelCell = ({\n  channel\n}) => {\n  if (!channel) {\n    return /* @__PURE__ */ jsx6(\"span\", { className: \"text-ui-fg-muted\", children: \"-\" });\n  }\n  const { name } = channel;\n  return /* @__PURE__ */ jsx6(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx6(\"span\", { className: \"truncate\", children: name }) });\n};\nvar SalesChannelHeader = () => {\n  const { t } = useTranslation5();\n  return /* @__PURE__ */ jsx6(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx6(\"span\", { className: \"truncate\", children: t(\"fields.salesChannel\") }) });\n};\n\n// src/components/table/table-cells/order/total-cell/total-cell.tsx\nimport { useTranslation as useTranslation6 } from \"react-i18next\";\nimport { jsx as jsx7 } from \"react/jsx-runtime\";\nvar TotalCell = ({ currencyCode, total }) => {\n  if (!total) {\n    return /* @__PURE__ */ jsx7(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx7(MoneyAmountCell, { currencyCode, amount: total, align: \"right\" });\n};\nvar TotalHeader = () => {\n  const { t } = useTranslation6();\n  return /* @__PURE__ */ jsx7(\"div\", { className: \"flex h-full w-full items-center justify-end\", children: /* @__PURE__ */ jsx7(\"span\", { className: \"truncate\", children: t(\"fields.total\") }) });\n};\n\n// src/hooks/table/columns/use-order-table-columns.tsx\nimport { jsx as jsx8 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useOrderTableColumns = (props) => {\n  const { exclude = [] } = props ?? {};\n  const columns = useMemo(\n    () => [\n      columnHelper.accessor(\"display_id\", {\n        header: () => /* @__PURE__ */ jsx8(DisplayIdHeader, {}),\n        cell: ({ getValue }) => {\n          const id = getValue();\n          return /* @__PURE__ */ jsx8(DisplayIdCell, { displayId: id });\n        }\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: () => /* @__PURE__ */ jsx8(DateHeader, {}),\n        cell: ({ getValue }) => {\n          const date = new Date(getValue());\n          return /* @__PURE__ */ jsx8(DateCell, { date });\n        }\n      }),\n      columnHelper.accessor(\"customer\", {\n        header: () => /* @__PURE__ */ jsx8(CustomerHeader, {}),\n        cell: ({ getValue }) => {\n          const customer = getValue();\n          return /* @__PURE__ */ jsx8(CustomerCell, { customer });\n        }\n      }),\n      columnHelper.accessor(\"sales_channel\", {\n        header: () => /* @__PURE__ */ jsx8(SalesChannelHeader, {}),\n        cell: ({ getValue }) => {\n          const channel = getValue();\n          return /* @__PURE__ */ jsx8(SalesChannelCell, { channel });\n        }\n      }),\n      columnHelper.accessor(\"payment_status\", {\n        header: () => /* @__PURE__ */ jsx8(PaymentStatusHeader, {}),\n        cell: ({ getValue }) => {\n          const status = getValue();\n          return /* @__PURE__ */ jsx8(PaymentStatusCell, { status });\n        }\n      }),\n      columnHelper.accessor(\"fulfillment_status\", {\n        header: () => /* @__PURE__ */ jsx8(FulfillmentStatusHeader, {}),\n        cell: ({ getValue }) => {\n          const status = getValue();\n          return /* @__PURE__ */ jsx8(FulfillmentStatusCell, { status });\n        }\n      }),\n      columnHelper.accessor(\"total\", {\n        header: () => /* @__PURE__ */ jsx8(TotalHeader, {}),\n        cell: ({ getValue, row }) => {\n          const total = getValue();\n          const currencyCode = row.original.currency_code;\n          return /* @__PURE__ */ jsx8(TotalCell, { currencyCode, total });\n        }\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          const country = row.original.shipping_address?.country;\n          return /* @__PURE__ */ jsx8(CountryCell, { country });\n        }\n      })\n    ],\n    []\n  );\n  const isAccessorColumnDef = (c) => {\n    return c.accessorKey !== void 0;\n  };\n  const isDisplayColumnDef = (c) => {\n    return c.id !== void 0;\n  };\n  const shouldExclude = (c) => {\n    if (isAccessorColumnDef(c)) {\n      return exclude.includes(c.accessorKey);\n    } else if (isDisplayColumnDef(c)) {\n      return exclude.includes(c.id);\n    }\n    return false;\n  };\n  return columns.filter(\n    (c) => !shouldExclude(c)\n  );\n};\n\nexport {\n  useOrderTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,mBAAwB;AAKxB,yBAAoB;AAuBpB,IAAAA,sBAA4B;AAkB5B,IAAAC,sBAAkC;AAiBlC,IAAAC,sBAA4B;AAkB5B,IAAAC,sBAA4B;AAa5B,IAAAC,sBAA4B;AAiB5B,IAAAC,sBAA4B;AAa5B,IAAAA,sBAA4B;AAtH5B,IAAI,cAAc,CAAC;AAAA,EACjB;AACF,MAAM;AACJ,MAAI,CAAC,SAAS;AACZ,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,2CAA2C,cAA0B,wBAAI,SAAS,EAAE,SAAS,QAAQ,cAAc,cAA0B,wBAAI,OAAO,EAAE,WAAW,sEAAsE,cAA0B;AAAA,IAClT;AAAA,IACA;AAAA,MACE,aAAa,QAAQ,MAAM,YAAY;AAAA,MACvC,KAAK;AAAA,MACL,OAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,cAAc,QAAQ;AAAA,IACxB;AAAA,EACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACX;AAKA,IAAI,eAAe,CAAC;AAAA,EAClB;AACF,MAAM;AACJ,MAAI,CAAC,UAAU;AACb,eAAuB,oBAAAC,KAAK,QAAQ,EAAE,WAAW,oBAAoB,UAAU,IAAI,CAAC;AAAA,EACtF;AACA,QAAM,EAAE,YAAY,WAAW,MAAM,IAAI;AACzC,QAAM,OAAO,CAAC,YAAY,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC7D,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,QAAQ,MAAM,CAAC,EAAE,CAAC;AACjL;AACA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;AACxL;AAKA,IAAI,gBAAgB,CAAC,EAAE,UAAU,MAAM;AACrC,MAAI,CAAC,WAAW;AACd,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,uFAAuF,cAA0B,0BAAK,QAAQ,EAAE,WAAW,YAAY,UAAU;AAAA,IAC/M;AAAA,IACA;AAAA,EACF,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACrL;AAKA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,EAAE,OAAO,MAAM,IAAI,0BAA0B,GAAG,MAAM;AAC5D,aAAuB,oBAAAC,KAAK,YAAY,EAAE,OAAO,UAAU,MAAM,CAAC;AACpE;AACA,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,oBAAoB,EAAE,CAAC,EAAE,CAAC;AAC3L;AAKA,IAAI,oBAAoB,CAAC,EAAE,OAAO,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,OAAO,MAAM,IAAI,sBAAsB,GAAG,MAAM;AACxD,aAAuB,oBAAAC,KAAK,YAAY,EAAE,OAAO,UAAU,MAAM,CAAC;AACpE;AACA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AACvL;AAKA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AACF,MAAM;AACJ,MAAI,CAAC,SAAS;AACZ,eAAuB,oBAAAC,KAAK,QAAQ,EAAE,WAAW,oBAAoB,UAAU,IAAI,CAAC;AAAA,EACtF;AACA,QAAM,EAAE,KAAK,IAAI;AACjB,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AACxL;AACA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC;AAC5L;AAKA,IAAI,YAAY,CAAC,EAAE,cAAc,MAAM,MAAM;AAC3C,MAAI,CAAC,OAAO;AACV,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,iBAAiB,EAAE,cAAc,QAAQ,OAAO,OAAO,QAAQ,CAAC;AAC9F;AACA,IAAI,cAAc,MAAM;AACtB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACjM;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,uBAAuB,CAAC,UAAU;AACpC,QAAM,EAAE,UAAU,CAAC,EAAE,IAAI,SAAS,CAAC;AACnC,QAAM,cAAU;AAAA,IACd,MAAM;AAAA,MACJ,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,UAAsB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,QACtD,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,KAAK,SAAS;AACpB,qBAAuB,oBAAAA,KAAK,eAAe,EAAE,WAAW,GAAG,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,CAAC,CAAC;AAAA,QACjD,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,OAAO,IAAI,KAAK,SAAS,CAAC;AAChC,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,KAAK,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,YAAY;AAAA,QAChC,QAAQ,UAAsB,oBAAAA,KAAK,gBAAgB,CAAC,CAAC;AAAA,QACrD,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,WAAW,SAAS;AAC1B,qBAAuB,oBAAAA,KAAK,cAAc,EAAE,SAAS,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,iBAAiB;AAAA,QACrC,QAAQ,UAAsB,oBAAAA,KAAK,oBAAoB,CAAC,CAAC;AAAA,QACzD,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,UAAU,SAAS;AACzB,qBAAuB,oBAAAA,KAAK,kBAAkB,EAAE,QAAQ,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,kBAAkB;AAAA,QACtC,QAAQ,UAAsB,oBAAAA,KAAK,qBAAqB,CAAC,CAAC;AAAA,QAC1D,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,SAAS,SAAS;AACxB,qBAAuB,oBAAAA,KAAK,mBAAmB,EAAE,OAAO,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,sBAAsB;AAAA,QAC1C,QAAQ,UAAsB,oBAAAA,KAAK,yBAAyB,CAAC,CAAC;AAAA,QAC9D,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,SAAS,SAAS;AACxB,qBAAuB,oBAAAA,KAAK,uBAAuB,EAAE,OAAO,CAAC;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,UAAsB,oBAAAA,KAAK,aAAa,CAAC,CAAC;AAAA,QAClD,MAAM,CAAC,EAAE,UAAU,IAAI,MAAM;AAC3B,gBAAM,QAAQ,SAAS;AACvB,gBAAM,eAAe,IAAI,SAAS;AAClC,qBAAuB,oBAAAA,KAAK,WAAW,EAAE,cAAc,MAAM,CAAC;AAAA,QAChE;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AA5M3B;AA6MU,gBAAM,WAAU,SAAI,SAAS,qBAAb,mBAA+B;AAC/C,qBAAuB,oBAAAA,KAAK,aAAa,EAAE,QAAQ,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,sBAAsB,CAAC,MAAM;AACjC,WAAO,EAAE,gBAAgB;AAAA,EAC3B;AACA,QAAM,qBAAqB,CAAC,MAAM;AAChC,WAAO,EAAE,OAAO;AAAA,EAClB;AACA,QAAM,gBAAgB,CAAC,MAAM;AAC3B,QAAI,oBAAoB,CAAC,GAAG;AAC1B,aAAO,QAAQ,SAAS,EAAE,WAAW;AAAA,IACvC,WAAW,mBAAmB,CAAC,GAAG;AAChC,aAAO,QAAQ,SAAS,EAAE,EAAE;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACA,SAAO,QAAQ;AAAA,IACb,CAAC,MAAM,CAAC,cAAc,CAAC;AAAA,EACzB;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4", "jsx5", "jsx6", "jsx7", "jsx8"]}