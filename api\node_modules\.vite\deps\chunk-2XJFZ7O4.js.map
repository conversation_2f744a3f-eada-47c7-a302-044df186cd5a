{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-77BAMLUK.mjs"], "sourcesContent": ["import {\n  useCountries,\n  useCountryTableColumns,\n  useCountryTableQuery\n} from \"./chunk-NOAFLTPV.mjs\";\nimport {\n  GEO_ZONE_STACKED_MODAL_ID\n} from \"./chunk-PYIO3TDQ.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport {\n  ChipGroup\n} from \"./chunk-X5VECN6S.mjs\";\nimport {\n  StackedFocusModal,\n  useStackedModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  countries\n} from \"./chunk-VDBOSWVE.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\n\n// src/routes/locations/common/components/geo-zone-form/geo-zone-form.tsx\nimport { Button, Checkbox } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useFieldArray } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar GeoZoneSchema = z.object({\n  countries: z.array(\n    z.object({ iso_2: z.string().min(2), display_name: z.string() })\n  )\n});\nvar GeoZoneFormImpl = ({\n  form\n}) => {\n  const castForm = form;\n  const { t } = useTranslation();\n  const { fields, remove, replace } = useFieldArray({\n    control: castForm.control,\n    name: \"countries\",\n    keyName: \"iso_2\"\n  });\n  const handleClearAll = () => {\n    replace([]);\n  };\n  validateForm(form);\n  return /* @__PURE__ */ jsx(\n    Form.Field,\n    {\n      control: form.control,\n      name: \"countries\",\n      render: () => {\n        return /* @__PURE__ */ jsxs(Form.Item, { children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between gap-x-4\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"stockLocations.serviceZones.manageAreas.label\") }),\n              /* @__PURE__ */ jsx(Form.Hint, { children: t(\"stockLocations.serviceZones.manageAreas.hint\") })\n            ] }),\n            /* @__PURE__ */ jsx(StackedFocusModal.Trigger, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", type: \"button\", children: t(\"stockLocations.serviceZones.manageAreas.action\") }) })\n          ] }),\n          /* @__PURE__ */ jsx(Form.ErrorMessage, {}),\n          /* @__PURE__ */ jsx(Form.Control, { className: \"mt-0\", children: fields.length > 0 && /* @__PURE__ */ jsx(\n            ChipGroup,\n            {\n              onClearAll: handleClearAll,\n              onRemove: remove,\n              className: \"py-4\",\n              children: fields.map((field, index) => /* @__PURE__ */ jsx(ChipGroup.Chip, { index, children: field.display_name }, field.iso_2))\n            }\n          ) })\n        ] });\n      }\n    }\n  );\n};\nvar PREFIX = \"ac\";\nvar PAGE_SIZE = 50;\nvar AreaStackedModal = ({\n  form\n}) => {\n  const castForm = form;\n  const { t } = useTranslation();\n  const { getValues, setValue } = castForm;\n  const { setIsOpen, getIsOpen } = useStackedModal();\n  const open = getIsOpen(GEO_ZONE_STACKED_MODAL_ID);\n  const [selection, setSelection] = useState({});\n  const [state, setState] = useState(\n    []\n  );\n  const { searchParams, raw } = useCountryTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { countries: countries2, count } = useCountries({\n    countries: countries.map((c) => ({\n      display_name: c.display_name,\n      name: c.name,\n      iso_2: c.iso_2,\n      iso_3: c.iso_3,\n      num_code: c.num_code\n    })),\n    ...searchParams\n  });\n  useEffect(() => {\n    if (!open) {\n      return;\n    }\n    const countries3 = getValues(\"countries\");\n    if (countries3) {\n      setState(\n        countries3.map((country) => ({\n          iso_2: country.iso_2,\n          display_name: country.display_name\n        }))\n      );\n      setSelection(\n        countries3.reduce(\n          (acc, country) => ({\n            ...acc,\n            [country.iso_2]: true\n          }),\n          {}\n        )\n      );\n    }\n  }, [open, getValues]);\n  const updater = (fn) => {\n    const value = typeof fn === \"function\" ? fn(selection) : fn;\n    const ids = Object.keys(value);\n    const addedIdsSet = new Set(ids.filter((id) => value[id] && !selection[id]));\n    const addedCountries = [];\n    if (addedIdsSet.size > 0) {\n      const countriesToAdd = countries2?.filter((country) => addedIdsSet.has(country.iso_2)) ?? [];\n      for (const country of countriesToAdd) {\n        addedCountries.push({\n          iso_2: country.iso_2,\n          display_name: country.display_name\n        });\n      }\n    }\n    setState((prev) => {\n      const filteredPrev = prev.filter((country) => value[country.iso_2]);\n      return Array.from(/* @__PURE__ */ new Set([...filteredPrev, ...addedCountries]));\n    });\n    setSelection(value);\n  };\n  const handleAdd = () => {\n    setValue(\"countries\", state, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setIsOpen(GEO_ZONE_STACKED_MODAL_ID, false);\n  };\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: countries2 || [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    getRowId: (row) => row.iso_2,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: selection,\n      updater\n    },\n    prefix: PREFIX\n  });\n  validateForm(form);\n  return /* @__PURE__ */ jsxs(StackedFocusModal.Content, { className: \"flex flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsxs(StackedFocusModal.Header, { children: [\n      /* @__PURE__ */ jsx(StackedFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(\"span\", { className: \"sr-only\", children: t(\"stockLocations.serviceZones.manageAreas.label\") }) }),\n      /* @__PURE__ */ jsx(StackedFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(\"span\", { className: \"sr-only\", children: t(\"stockLocations.serviceZones.manageAreas.hint\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(StackedFocusModal.Body, { className: \"flex-1 overflow-hidden\", children: /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        search: true,\n        pagination: true,\n        layout: \"fill\",\n        orderBy: [\n          { key: \"display_name\", label: t(\"fields.name\") },\n          { key: \"iso_2\", label: t(\"fields.code\") }\n        ],\n        queryObject: raw,\n        prefix: PREFIX\n      }\n    ) }),\n    /* @__PURE__ */ jsx(StackedFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(StackedFocusModal.Close, { type: \"button\", asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"button\", onClick: handleAdd, children: t(\"actions.save\") })\n    ] }) })\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCountryTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreselected = !row.getCanSelect();\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: row.getIsSelected() || isPreselected,\n              disabled: isPreselected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [base]\n  );\n};\nfunction validateForm(form) {\n  if (form.getValues(\"countries\") === void 0) {\n    throw new Error(\n      \"The form does not have a field named 'countries'. This field is required to use the GeoZoneForm component.\"\n    );\n  }\n}\nvar GeoZoneForm = Object.assign(GeoZoneFormImpl, {\n  AreaDrawer: AreaStackedModal\n});\n\nexport {\n  GeoZoneForm\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,mBAA6C;AAI7C,yBAA0B;AAC1B,IAAI,gBAAgB,EAAE,OAAO;AAAA,EAC3B,WAAW,EAAE;AAAA,IACX,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,cAAc,EAAE,OAAO,EAAE,CAAC;AAAA,EACjE;AACF,CAAC;AACD,IAAI,kBAAkB,CAAC;AAAA,EACrB;AACF,MAAM;AACJ,QAAM,WAAW;AACjB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,QAAQ,QAAQ,QAAQ,IAAI,cAAc;AAAA,IAChD,SAAS,SAAS;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,EACX,CAAC;AACD,QAAM,iBAAiB,MAAM;AAC3B,YAAQ,CAAC,CAAC;AAAA,EACZ;AACA,eAAa,IAAI;AACjB,aAAuB;AAAA,IACrB,KAAK;AAAA,IACL;AAAA,MACE,SAAS,KAAK;AAAA,MACd,MAAM;AAAA,MACN,QAAQ,MAAM;AACZ,mBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,cACjC,yBAAK,OAAO,EAAE,WAAW,6CAA6C,UAAU;AAAA,gBAC9E,yBAAK,OAAO,EAAE,UAAU;AAAA,kBACtB,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,+CAA+C,EAAE,CAAC;AAAA,kBAChF,wBAAI,KAAK,MAAM,EAAE,UAAU,EAAE,8CAA8C,EAAE,CAAC;AAAA,YAChG,EAAE,CAAC;AAAA,gBACa,wBAAI,kBAAkB,SAAS,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,MAAM,UAAU,UAAU,EAAE,gDAAgD,EAAE,CAAC,EAAE,CAAC;AAAA,UACjO,EAAE,CAAC;AAAA,cACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cACzB,wBAAI,KAAK,SAAS,EAAE,WAAW,QAAQ,UAAU,OAAO,SAAS,SAAqB;AAAA,YACpG;AAAA,YACA;AAAA,cACE,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,WAAW;AAAA,cACX,UAAU,OAAO,IAAI,CAAC,OAAO,cAA0B,wBAAI,UAAU,MAAM,EAAE,OAAO,UAAU,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC;AAAA,YAClI;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,mBAAmB,CAAC;AAAA,EACtB;AACF,MAAM;AACJ,QAAM,WAAW;AACjB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,WAAW,SAAS,IAAI;AAChC,QAAM,EAAE,WAAW,UAAU,IAAI,gBAAgB;AACjD,QAAM,OAAO,UAAU,yBAAyB;AAChD,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,CAAC,CAAC;AAC7C,QAAM,CAAC,OAAO,QAAQ,QAAI;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,WAAW,YAAY,MAAM,IAAI,aAAa;AAAA,IACpD,WAAW,UAAU,IAAI,CAAC,OAAO;AAAA,MAC/B,cAAc,EAAE;AAAA,MAChB,MAAM,EAAE;AAAA,MACR,OAAO,EAAE;AAAA,MACT,OAAO,EAAE;AAAA,MACT,UAAU,EAAE;AAAA,IACd,EAAE;AAAA,IACF,GAAG;AAAA,EACL,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,UAAM,aAAa,UAAU,WAAW;AACxC,QAAI,YAAY;AACd;AAAA,QACE,WAAW,IAAI,CAAC,aAAa;AAAA,UAC3B,OAAO,QAAQ;AAAA,UACf,cAAc,QAAQ;AAAA,QACxB,EAAE;AAAA,MACJ;AACA;AAAA,QACE,WAAW;AAAA,UACT,CAAC,KAAK,aAAa;AAAA,YACjB,GAAG;AAAA,YACH,CAAC,QAAQ,KAAK,GAAG;AAAA,UACnB;AAAA,UACA,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,SAAS,CAAC;AACpB,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,QAAQ,OAAO,OAAO,aAAa,GAAG,SAAS,IAAI;AACzD,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,UAAM,cAAc,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;AAC3E,UAAM,iBAAiB,CAAC;AACxB,QAAI,YAAY,OAAO,GAAG;AACxB,YAAM,kBAAiB,yCAAY,OAAO,CAAC,YAAY,YAAY,IAAI,QAAQ,KAAK,OAAM,CAAC;AAC3F,iBAAW,WAAW,gBAAgB;AACpC,uBAAe,KAAK;AAAA,UAClB,OAAO,QAAQ;AAAA,UACf,cAAc,QAAQ;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AACA,aAAS,CAAC,SAAS;AACjB,YAAM,eAAe,KAAK,OAAO,CAAC,YAAY,MAAM,QAAQ,KAAK,CAAC;AAClE,aAAO,MAAM,KAAqB,oBAAI,IAAI,CAAC,GAAG,cAAc,GAAG,cAAc,CAAC,CAAC;AAAA,IACjF,CAAC;AACD,iBAAa,KAAK;AAAA,EACpB;AACA,QAAM,YAAY,MAAM;AACtB,aAAS,aAAa,OAAO;AAAA,MAC3B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,cAAU,2BAA2B,KAAK;AAAA,EAC5C;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACD,eAAa,IAAI;AACjB,aAAuB,yBAAK,kBAAkB,SAAS,EAAE,WAAW,iCAAiC,UAAU;AAAA,QAC7F,yBAAK,kBAAkB,QAAQ,EAAE,UAAU;AAAA,UACzC,wBAAI,kBAAkB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,WAAW,WAAW,UAAU,EAAE,+CAA+C,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7K,wBAAI,kBAAkB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,WAAW,WAAW,UAAU,EAAE,8CAA8C,EAAE,CAAC,EAAE,CAAC;AAAA,IACpM,EAAE,CAAC;AAAA,QACa,wBAAI,kBAAkB,MAAM,EAAE,WAAW,0BAA0B,cAA0B;AAAA,MAC3G;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,EAAE,KAAK,gBAAgB,OAAO,EAAE,aAAa,EAAE;AAAA,UAC/C,EAAE,KAAK,SAAS,OAAO,EAAE,aAAa,EAAE;AAAA,QAC1C;AAAA,QACA,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,kBAAkB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UACpI,wBAAI,kBAAkB,OAAO,EAAE,MAAM,UAAU,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7K,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,SAAS,WAAW,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,IAChH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc,KAAK;AAAA,cAChC,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,KAAK,UAAU,WAAW,MAAM,QAAQ;AAC1C,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,cAAc,OAAO,OAAO,iBAAiB;AAAA,EAC/C,YAAY;AACd,CAAC;", "names": []}