{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-P3DRE4IY.mjs"], "sourcesContent": ["// src/routes/orders/common/placeholders.tsx\nimport { Trans, useTranslation } from \"react-i18next\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ReturnShippingPlaceholder = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center\", children: [\n    /* @__PURE__ */ jsx(\"span\", { className: \"txt-small text-ui-fg-subtle font-medium\", children: t(\"orders.returns.placeholders.noReturnShippingOptions.title\") }),\n    /* @__PURE__ */ jsx(\"span\", { className: \"txt-small text-ui-fg-muted\", children: /* @__PURE__ */ jsx(\n      Trans,\n      {\n        i18nKey: \"orders.returns.placeholders.noReturnShippingOptions.hint\",\n        components: {\n          LinkComponent: /* @__PURE__ */ jsx(Link, { to: `/settings/locations`, className: \"text-blue-500\" })\n        }\n      }\n    ) })\n  ] });\n};\nvar OutboundShippingPlaceholder = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center\", children: [\n    /* @__PURE__ */ jsx(\"span\", { className: \"txt-small text-ui-fg-subtle font-medium\", children: t(\"orders.returns.placeholders.outboundShippingOptions.title\") }),\n    /* @__PURE__ */ jsx(\"span\", { className: \"txt-small text-ui-fg-muted\", children: /* @__PURE__ */ jsx(\n      Trans,\n      {\n        i18nKey: \"orders.returns.placeholders.outboundShippingOptions.hint\",\n        components: {\n          LinkComponent: /* @__PURE__ */ jsx(Link, { to: `/settings/locations`, className: \"text-blue-500\" })\n        }\n      }\n    ) })\n  ] });\n};\n\nexport {\n  ReturnShippingPlaceholder,\n  OutboundShippingPlaceholder\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAGA,yBAA0B;AAC1B,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,yBAAK,OAAO,EAAE,WAAW,6EAA6E,UAAU;AAAA,QACrH,wBAAI,QAAQ,EAAE,WAAW,2CAA2C,UAAU,EAAE,2DAA2D,EAAE,CAAC;AAAA,QAC9I,wBAAI,QAAQ,EAAE,WAAW,8BAA8B,cAA0B;AAAA,MAC/F;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,UACV,mBAA+B,wBAAI,MAAM,EAAE,IAAI,uBAAuB,WAAW,gBAAgB,CAAC;AAAA,QACpG;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,8BAA8B,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,yBAAK,OAAO,EAAE,WAAW,6EAA6E,UAAU;AAAA,QACrH,wBAAI,QAAQ,EAAE,WAAW,2CAA2C,UAAU,EAAE,2DAA2D,EAAE,CAAC;AAAA,QAC9I,wBAAI,QAAQ,EAAE,WAAW,8BAA8B,cAA0B;AAAA,MAC/F;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,UACV,mBAA+B,wBAAI,MAAM,EAAE,IAAI,uBAAuB,WAAW,gBAAgB,CAAC;AAAA,QACpG;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;", "names": []}