import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-LDJKJLBJ.mjs
var TAX_REGIONS_QUERY_KEY = "tax_regions";
var taxRegionsQueryKeys = queryKeysFactory(TAX_REGIONS_QUERY_KEY);
var useTaxRegion = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: taxRegionsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.taxRegion.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useTaxRegions = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.taxRegion.list(query),
    queryKey: taxRegionsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateTaxRegion = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.taxRegion.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteTaxRegion = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.taxRegion.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: taxRegionsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  taxRegionsQueryKeys,
  useTaxRegion,
  useTaxRegions,
  useCreateTaxRegion,
  useDeleteTaxRegion
};
//# sourceMappingURL=chunk-PBNFBMP6.js.map
