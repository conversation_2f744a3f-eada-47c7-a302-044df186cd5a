{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-Q5DI5VYN.mjs"], "sourcesContent": ["import {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\n\n// src/routes/inventory/inventory-detail/components/inventory-item-general-section.tsx\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar InventoryItemGeneralSection = ({\n  inventoryItem\n}) => {\n  const { t } = useTranslation();\n  const getQuantityFormat = (quantity) => {\n    if (quantity !== void 0 && !isNaN(quantity)) {\n      return t(\"inventory.quantityAcrossLocations\", {\n        quantity,\n        locations: inventoryItem.location_levels?.length\n      });\n    }\n    return \"-\";\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(Heading, { children: [\n        inventoryItem.title ?? inventoryItem.sku,\n        \" \",\n        t(\"fields.details\")\n      ] }),\n      /* @__PURE__ */ jsx(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n                  label: t(\"actions.edit\"),\n                  to: \"edit\"\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx(SectionRow, { title: t(\"fields.sku\"), value: inventoryItem.sku ?? \"-\" }),\n    /* @__PURE__ */ jsx(\n      SectionRow,\n      {\n        title: t(\"fields.inStock\"),\n        value: getQuantityFormat(inventoryItem.stocked_quantity)\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      SectionRow,\n      {\n        title: t(\"inventory.reserved\"),\n        value: getQuantityFormat(inventoryItem.reserved_quantity)\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      SectionRow,\n      {\n        title: t(\"inventory.available\"),\n        value: getQuantityFormat(\n          inventoryItem.stocked_quantity - inventoryItem.reserved_quantity\n        )\n      }\n    )\n  ] });\n};\n\nexport {\n  InventoryItemGeneralSection\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAWA,yBAA0B;AAC1B,IAAI,8BAA8B,CAAC;AAAA,EACjC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,oBAAoB,CAAC,aAAa;AAhB1C;AAiBI,QAAI,aAAa,UAAU,CAAC,MAAM,QAAQ,GAAG;AAC3C,aAAO,EAAE,qCAAqC;AAAA,QAC5C;AAAA,QACA,YAAW,mBAAc,oBAAd,mBAA+B;AAAA,MAC5C,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,yBAAK,SAAS,EAAE,UAAU;AAAA,QACxC,cAAc,SAAS,cAAc;AAAA,QACrC;AAAA,QACA,EAAE,gBAAgB;AAAA,MACpB,EAAE,CAAC;AAAA,UACa;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,kBAC1C,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,YAAY,EAAE,OAAO,EAAE,YAAY,GAAG,OAAO,cAAc,OAAO,IAAI,CAAC;AAAA,QAC3E;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gBAAgB;AAAA,QACzB,OAAO,kBAAkB,cAAc,gBAAgB;AAAA,MACzD;AAAA,IACF;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,oBAAoB;AAAA,QAC7B,OAAO,kBAAkB,cAAc,iBAAiB;AAAA,MAC1D;AAAA,IACF;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,qBAAqB;AAAA,QAC9B,OAAO;AAAA,UACL,cAAc,mBAAmB,cAAc;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": []}