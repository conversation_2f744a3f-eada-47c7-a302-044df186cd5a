{"version": 3, "sources": ["../../@medusajs/dashboard/dist/api-key-management-create-HWBQGG6O.mjs"], "sourcesContent": ["import {\n  getApiKeyTypeFromPathname\n} from \"./chunk-G22WWLPG.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateApiKey\n} from \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/api-key-management/api-key-management-create/api-key-management-create.tsx\nimport { useLocation } from \"react-router-dom\";\n\n// src/routes/api-key-management/api-key-management-create/components/api-key-create-form/api-key-create-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Prompt, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { Eye, EyeSlash } from \"@medusajs/icons\";\nimport { Fragment, useState } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ApiKeyCreateSchema = zod.object({\n  title: zod.string().min(1)\n});\nfunction getRedactedKey(key) {\n  if (!key) {\n    return \"\";\n  }\n  const firstThree = key.slice(0, 4);\n  const lastTwo = key.slice(-2);\n  return `${firstThree}${\"\\u2022\".repeat(key.length - 6)}${lastTwo}`;\n}\nvar ApiKeyCreateForm = ({ keyType }) => {\n  const [createdKey, setCreatedKey] = useState(null);\n  const [showRedactedKey, setShowRedactedKey] = useState(true);\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      title: \"\"\n    },\n    resolver: zodResolver(ApiKeyCreateSchema)\n  });\n  const { mutateAsync, isPending } = useCreateApiKey();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      // @ts-ignore\n      { title: values.title, type: keyType },\n      {\n        onSuccess: ({ api_key }) => {\n          toast.success(t(\"apiKeyManagement.create.successToast\"));\n          switch (keyType) {\n            case \"publishable\" /* PUBLISHABLE */:\n              handleSuccess(`/settings/publishable-api-keys/${api_key.id}`);\n              break;\n            case \"secret\" /* SECRET */:\n              setCreatedKey(api_key);\n              break;\n          }\n        },\n        onError: (err) => {\n          toast.error(err.message);\n        }\n      }\n    );\n  });\n  const handleCopyToken = () => {\n    if (!createdKey) {\n      toast.error(t(\"apiKeyManagement.create.copySecretTokenFailure\"));\n    }\n    navigator.clipboard.writeText(createdKey?.token ?? \"\");\n    toast.success(t(\"apiKeyManagement.create.copySecretTokenSuccess\"));\n  };\n  const handleGoToSecretKey = () => {\n    if (!createdKey) {\n      return;\n    }\n    handleSuccess(`/settings/secret-api-keys/${createdKey.id}`);\n  };\n  return /* @__PURE__ */ jsxs(Fragment, { children: [\n    /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n      KeyboundForm,\n      {\n        className: \"flex h-full flex-col overflow-hidden\",\n        onSubmit: handleSubmit,\n        children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n          /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { children: [\n              /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { children: keyType === \"publishable\" /* PUBLISHABLE */ ? t(\"apiKeyManagement.create.createPublishableHeader\") : t(\"apiKeyManagement.create.createSecretHeader\") }) }),\n              /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: keyType === \"publishable\" /* PUBLISHABLE */ ? t(\"apiKeyManagement.create.createPublishableHint\") : t(\"apiKeyManagement.create.createSecretHint\") }) })\n            ] }),\n            /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-2 gap-4\", children: /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"title\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ) })\n          ] }) }) }),\n          /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n            /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n            /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n          ] }) })\n        ]\n      }\n    ) }),\n    /* @__PURE__ */ jsx(Prompt, { variant: \"confirmation\", open: !!createdKey, children: /* @__PURE__ */ jsxs(Prompt.Content, { className: \"w-fit max-w-[42.5%]\", children: [\n      /* @__PURE__ */ jsxs(Prompt.Header, { children: [\n        /* @__PURE__ */ jsx(Prompt.Title, { children: t(\"apiKeyManagement.create.secretKeyCreatedHeader\") }),\n        /* @__PURE__ */ jsx(Prompt.Description, { children: t(\"apiKeyManagement.create.secretKeyCreatedHint\") })\n      ] }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-3 px-6 py-4\", children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"shadow-borders-base bg-ui-bg-component grid h-8 grid-cols-[1fr_32px] items-center overflow-hidden rounded-md\", children: [\n          /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center px-2\", children: /* @__PURE__ */ jsx(Text, { family: \"mono\", size: \"small\", children: showRedactedKey ? getRedactedKey(createdKey?.token) : createdKey?.token }) }),\n          /* @__PURE__ */ jsx(\n            \"button\",\n            {\n              className: \"transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed text-ui-fg-muted active:text-ui-fg-subtle flex size-8 appearance-none items-center justify-center border-l\",\n              type: \"button\",\n              onClick: () => setShowRedactedKey(!showRedactedKey),\n              children: showRedactedKey ? /* @__PURE__ */ jsx(EyeSlash, {}) : /* @__PURE__ */ jsx(Eye, {})\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(\n          Button,\n          {\n            size: \"small\",\n            variant: \"secondary\",\n            type: \"button\",\n            className: \"w-full\",\n            onClick: handleCopyToken,\n            children: t(\"apiKeyManagement.actions.copy\")\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx(Prompt.Footer, { className: \"border-t py-4\", children: /* @__PURE__ */ jsx(Prompt.Action, { onClick: handleGoToSecretKey, children: t(\"actions.continue\") }) })\n    ] }) })\n  ] });\n};\n\n// src/routes/api-key-management/api-key-management-create/api-key-management-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ApiKeyManagementCreate = () => {\n  const { pathname } = useLocation();\n  const keyType = getApiKeyTypeFromPathname(pathname);\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(ApiKeyCreateForm, { keyType }) });\n};\nexport {\n  ApiKeyManagementCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,mBAAmC;AACnC,yBAA0B;AAkI1B,IAAAA,sBAA4B;AAjI5B,IAAI,qBAAyB,WAAO;AAAA,EAClC,OAAW,WAAO,EAAE,IAAI,CAAC;AAC3B,CAAC;AACD,SAAS,eAAe,KAAK;AAC3B,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,MAAM,GAAG,CAAC;AACjC,QAAM,UAAU,IAAI,MAAM,EAAE;AAC5B,SAAO,GAAG,UAAU,GAAG,IAAS,OAAO,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;AAClE;AACA,IAAI,mBAAmB,CAAC,EAAE,QAAQ,MAAM;AACtC,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,IAAI;AACjD,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,uBAAS,IAAI;AAC3D,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,gBAAgB;AACnD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA;AAAA,MAEJ,EAAE,OAAO,OAAO,OAAO,MAAM,QAAQ;AAAA,MACrC;AAAA,QACE,WAAW,CAAC,EAAE,QAAQ,MAAM;AAC1B,gBAAM,QAAQA,GAAE,sCAAsC,CAAC;AACvD,kBAAQ,SAAS;AAAA,YACf,KAAK;AACH,4BAAc,kCAAkC,QAAQ,EAAE,EAAE;AAC5D;AAAA,YACF,KAAK;AACH,4BAAc,OAAO;AACrB;AAAA,UACJ;AAAA,QACF;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,gBAAM,MAAM,IAAI,OAAO;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,kBAAkB,MAAM;AAC5B,QAAI,CAAC,YAAY;AACf,YAAM,MAAMA,GAAE,gDAAgD,CAAC;AAAA,IACjE;AACA,cAAU,UAAU,WAAU,yCAAY,UAAS,EAAE;AACrD,UAAM,QAAQA,GAAE,gDAAgD,CAAC;AAAA,EACnE;AACA,QAAM,sBAAsB,MAAM;AAChC,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,kBAAc,6BAA6B,WAAW,EAAE,EAAE;AAAA,EAC5D;AACA,aAAuB,yBAAK,uBAAU,EAAE,UAAU;AAAA,QAChC,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,MAC1E;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,cACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,cAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,gBAC1S,yBAAK,OAAO,EAAE,UAAU;AAAA,kBACtB,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,UAAU,YAAY,gBAAkCA,GAAE,iDAAiD,IAAIA,GAAE,4CAA4C,EAAE,CAAC,EAAE,CAAC;AAAA,kBACxP,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAU,YAAY,gBAAkCA,GAAE,+CAA+C,IAAIA,GAAE,0CAA0C,EAAE,CAAC,EAAE,CAAC;AAAA,YACxT,EAAE,CAAC;AAAA,gBACa,wBAAI,OAAO,EAAE,WAAW,0BAA0B,cAA0B;AAAA,cAC1F,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,wBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,UACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,cACO,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,gBAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,gBAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,UAClH,EAAE,CAAC,EAAE,CAAC;AAAA,QACR;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,QAAQ,EAAE,SAAS,gBAAgB,MAAM,CAAC,CAAC,YAAY,cAA0B,yBAAK,OAAO,SAAS,EAAE,WAAW,uBAAuB,UAAU;AAAA,UACtJ,yBAAK,OAAO,QAAQ,EAAE,UAAU;AAAA,YAC9B,wBAAI,OAAO,OAAO,EAAE,UAAUA,GAAE,gDAAgD,EAAE,CAAC;AAAA,YACnF,wBAAI,OAAO,aAAa,EAAE,UAAUA,GAAE,8CAA8C,EAAE,CAAC;AAAA,MACzG,EAAE,CAAC;AAAA,UACa,yBAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,YACpE,yBAAK,OAAO,EAAE,WAAW,gHAAgH,UAAU;AAAA,cACjJ,wBAAI,OAAO,EAAE,WAAW,0BAA0B,cAA0B,wBAAI,MAAM,EAAE,QAAQ,QAAQ,MAAM,SAAS,UAAU,kBAAkB,eAAe,yCAAY,KAAK,IAAI,yCAAY,MAAM,CAAC,EAAE,CAAC;AAAA,cAC7M;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN,SAAS,MAAM,mBAAmB,CAAC,eAAe;AAAA,cAClD,UAAU,sBAAkC,wBAAI,UAAU,CAAC,CAAC,QAAoB,wBAAI,KAAK,CAAC,CAAC;AAAA,YAC7F;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,WAAW;AAAA,YACX,SAAS;AAAA,YACT,UAAUA,GAAE,+BAA+B;AAAA,UAC7C;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,UACa,wBAAI,OAAO,QAAQ,EAAE,WAAW,iBAAiB,cAA0B,wBAAI,OAAO,QAAQ,EAAE,SAAS,qBAAqB,UAAUA,GAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;AAAA,IACpL,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AAIA,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,SAAS,IAAI,YAAY;AACjC,QAAM,UAAU,0BAA0B,QAAQ;AAClD,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,kBAAkB,EAAE,QAAQ,CAAC,EAAE,CAAC;AAChH;", "names": ["import_jsx_runtime", "t", "jsx2"]}