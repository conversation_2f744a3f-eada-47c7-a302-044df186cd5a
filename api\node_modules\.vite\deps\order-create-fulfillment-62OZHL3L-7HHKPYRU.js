import {
  getFulfillableQuantity
} from "./chunk-ABXWNTVT.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import {
  useShippingOptions
} from "./chunk-BZZVTH5X.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import {
  useCreateOrderFulfillment,
  useOrder
} from "./chunk-IBPOGPJN.js";
import {
  useReservationItems
} from "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProductVariant
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  InformationCircleSolid,
  Input,
  Select,
  Switch,
  Text,
  Tooltip,
  clx,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-fulfillment-62OZHL3L.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var CreateFulfillmentSchema = z.object({
  quantity: z.record(z.string(), z.number()),
  location_id: z.string(),
  shipping_option_id: z.string().optional(),
  send_notification: z.boolean().optional()
});
function OrderCreateFulfillmentItem({
  item,
  form,
  locationId,
  itemReservedQuantitiesMap,
  disabled
}) {
  const { t: t2 } = useTranslation();
  const { variant } = useProductVariant(
    item.product_id,
    item.variant_id,
    {
      fields: "*inventory,*inventory.location_levels"
    },
    {
      enabled: !!item.variant
    }
  );
  const { availableQuantity, inStockQuantity } = (0, import_react2.useMemo)(() => {
    var _a, _b;
    if (!variant || !locationId) {
      return {};
    }
    const { inventory } = variant;
    const locationInventory = (_b = (_a = inventory[0]) == null ? void 0 : _a.location_levels) == null ? void 0 : _b.find(
      (inv) => inv.location_id === locationId
    );
    if (!locationInventory) {
      return {};
    }
    const reservedQuantityForItem = itemReservedQuantitiesMap.get(item.id) ?? 0;
    return {
      availableQuantity: locationInventory.available_quantity + reservedQuantityForItem,
      inStockQuantity: locationInventory.stocked_quantity
    };
  }, [variant, locationId, itemReservedQuantitiesMap]);
  const minValue = 0;
  const maxValue = Math.min(
    getFulfillableQuantity(item),
    availableQuantity || Number.MAX_SAFE_INTEGER
  );
  return (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-row items-center", children: [
    disabled && (0, import_jsx_runtime.jsx)("div", { className: "inline-flex items-center ml-4", children: (0, import_jsx_runtime.jsx)(
      Tooltip,
      {
        content: t2("orders.fulfillment.disabledItemTooltip"),
        side: "top",
        children: (0, import_jsx_runtime.jsx)(InformationCircleSolid, { className: "text-ui-tag-orange-icon" })
      }
    ) }),
    (0, import_jsx_runtime.jsxs)(
      "div",
      {
        className: clx(
          "flex flex-col flex-1 gap-x-2 gap-y-2 border-b p-3 text-sm sm:flex-row",
          disabled && "opacity-50 pointer-events-none"
        ),
        children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 items-center gap-x-3", children: [
            (0, import_jsx_runtime.jsx)(Thumbnail, { src: item.thumbnail }),
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
              (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", as: "span", weight: "plus", children: item.title }),
                item.variant_sku && (0, import_jsx_runtime.jsxs)("span", { children: [
                  "(",
                  item.variant_sku,
                  ")"
                ] })
              ] }),
              (0, import_jsx_runtime.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: item.variant_title })
            ] })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 items-center gap-x-1", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "mr-2 block h-[16px] w-[2px] bg-gray-200" }),
            (0, import_jsx_runtime.jsxs)("div", { className: "text-small flex flex-1 flex-col", children: [
              (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("orders.fulfillment.available") }),
              (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle", children: availableQuantity || "N/A" })
            ] }),
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 items-center gap-x-1", children: [
              (0, import_jsx_runtime.jsx)("div", { className: "mr-2 block h-[16px] w-[2px] bg-gray-200" }),
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("orders.fulfillment.inStock") }),
                (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-subtle", children: [
                  inStockQuantity || "N/A",
                  " ",
                  inStockQuantity && (0, import_jsx_runtime.jsxs)("span", { className: "font-medium text-red-500", children: [
                    "-",
                    form.getValues(`quantity.${item.id}`)
                  ] })
                ] })
              ] })
            ] }),
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 items-center gap-1", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `quantity.${item.id}`,
                  rules: { required: true, min: minValue, max: maxValue },
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Input,
                        {
                          className: "bg-ui-bg-base txt-small w-[50px] rounded-lg text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                          type: "number",
                          ...field,
                          onChange: (e) => {
                            const val = e.target.value === "" ? null : Number(e.target.value);
                            field.onChange(val);
                            if (!isNaN(val)) {
                              if (val < minValue || val > maxValue) {
                                form.setError(`quantity.${item.id}`, {
                                  type: "manual",
                                  message: t2(
                                    "orders.fulfillment.error.wrongQuantity",
                                    {
                                      count: maxValue,
                                      number: maxValue
                                    }
                                  )
                                });
                              } else {
                                form.clearErrors(`quantity.${item.id}`);
                              }
                            }
                          }
                        }
                      ) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-subtle", children: [
                "/ ",
                item.quantity,
                " ",
                t2("fields.qty")
              ] })
            ] })
          ] })
        ]
      }
    )
  ] }) });
}
function OrderCreateFulfillmentForm({
  order,
  requiresShipping
}) {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { mutateAsync: createOrderFulfillment, isPending: isMutating } = useCreateOrderFulfillment(order.id);
  const { reservations } = useReservationItems({
    line_item_id: order.items.map((i) => i.id)
  });
  const itemReservedQuantitiesMap = (0, import_react.useMemo)(
    () => new Map((reservations || []).map((r) => [r.line_item_id, r.quantity])),
    [reservations]
  );
  const [fulfillableItems, setFulfillableItems] = (0, import_react.useState)(
    () => (order.items || []).filter(
      (item) => item.requires_shipping === requiresShipping && getFulfillableQuantity(item) > 0
    )
  );
  const form = useForm({
    defaultValues: {
      quantity: fulfillableItems.reduce(
        (acc, item) => {
          acc[item.id] = getFulfillableQuantity(item);
          return acc;
        },
        {}
      ),
      send_notification: !order.no_notification
    },
    resolver: t(CreateFulfillmentSchema)
  });
  const selectedLocationId = useWatch({
    name: "location_id",
    control: form.control
  });
  const { stock_locations = [] } = useStockLocations();
  const { shipping_options = [], isLoading: isShippingOptionsLoading } = useShippingOptions({
    stock_location_id: selectedLocationId,
    // is_return: false, // TODO: 500 when enabled
    fields: "+service_zone.fulfillment_set.location.id"
  });
  const shippingOptionId = useWatch({
    name: "shipping_option_id",
    control: form.control
  });
  const handleSubmit = form.handleSubmit(async (data) => {
    const selectedShippingOption = shipping_options.find(
      (o) => o.id === shippingOptionId
    );
    if (!selectedShippingOption) {
      form.setError("shipping_option_id", {
        type: "manual",
        message: t2("orders.fulfillment.error.noShippingOption")
      });
      return;
    }
    if (!selectedLocationId) {
      form.setError("location_id", {
        type: "manual",
        message: t2("orders.fulfillment.error.noLocation")
      });
      return;
    }
    let items = Object.entries(data.quantity).map(([id, quantity]) => ({
      id,
      quantity
    })).filter(({ quantity }) => !!quantity);
    if (requiresShipping) {
      const selectedShippingProfileId = selectedShippingOption == null ? void 0 : selectedShippingOption.shipping_profile_id;
      const itemShippingProfileMap = order.items.reduce((acc, item) => {
        var _a2, _b2, _c;
        acc[item.id] = (_c = (_b2 = (_a2 = item.variant) == null ? void 0 : _a2.product) == null ? void 0 : _b2.shipping_profile) == null ? void 0 : _c.id;
        return acc;
      }, {});
      items = items.filter(
        ({ id }) => itemShippingProfileMap[id] === selectedShippingProfileId
      );
    }
    const payload = {
      location_id: selectedLocationId,
      shipping_option_id: shippingOptionId,
      no_notification: !data.send_notification,
      items
    };
    try {
      await createOrderFulfillment(payload);
      toast.success(t2("orders.fulfillment.toast.created"));
      handleSuccess(`/orders/${order.id}`);
    } catch (e) {
      toast.error(e.message);
    }
  });
  (0, import_react.useEffect)(() => {
    var _a2, _b2;
    if ((stock_locations == null ? void 0 : stock_locations.length) && (shipping_options == null ? void 0 : shipping_options.length)) {
      const initialShippingOptionId = (_b2 = (_a2 = order.shipping_methods) == null ? void 0 : _a2[0]) == null ? void 0 : _b2.shipping_option_id;
      if (initialShippingOptionId) {
        const shippingOption = shipping_options.find(
          (o) => o.id === initialShippingOptionId
        );
        if (shippingOption) {
          const locationId = shippingOption.service_zone.fulfillment_set.location.id;
          form.setValue("location_id", locationId);
          form.setValue(
            "shipping_option_id",
            initialShippingOptionId || void 0
          );
        }
      }
    }
  }, [stock_locations == null ? void 0 : stock_locations.length, shipping_options == null ? void 0 : shipping_options.length]);
  const fulfilledQuantityArray = (order.items || []).map(
    (item) => item.requires_shipping === requiresShipping && item.detail.fulfilled_quantity
  );
  (0, import_react.useEffect)(() => {
    var _a2;
    const itemsToFulfill = ((_a2 = order == null ? void 0 : order.items) == null ? void 0 : _a2.filter(
      (item) => item.requires_shipping === requiresShipping && getFulfillableQuantity(item) > 0
    )) || [];
    setFulfillableItems(itemsToFulfill);
    if (itemsToFulfill.length) {
      form.clearErrors("root");
    } else {
      form.setError("root", {
        type: "manual",
        message: t2("orders.fulfillment.error.noItems")
      });
    }
    const quantityMap = itemsToFulfill.reduce(
      (acc, item) => {
        acc[item.id] = getFulfillableQuantity(item);
        return acc;
      },
      {}
    );
    form.setValue("quantity", quantityMap);
  }, [...fulfilledQuantityArray, requiresShipping]);
  const differentOptionSelected = shippingOptionId && ((_b = (_a = order.shipping_methods) == null ? void 0 : _a[0]) == null ? void 0 : _b.shipping_option_id) !== shippingOptionId;
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "flex h-full w-full flex-col items-center divide-y overflow-y-auto", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col items-center overflow-auto p-16", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full max-w-[736px] flex-col justify-center px-2 pb-2", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col divide-y divide-dashed", children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "pb-8", children: (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "location_id",
              render: ({ field: { onChange, ref, ...field } }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-2 xl:flex-row xl:items-center", children: [
                    (0, import_jsx_runtime2.jsxs)("div", { className: "flex-1", children: [
                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.location") }),
                      (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("orders.fulfillment.locationDescription") })
                    ] }),
                    (0, import_jsx_runtime2.jsx)("div", { className: "flex-1", children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsxs)(Select, { onValueChange: onChange, ...field, children: [
                      (0, import_jsx_runtime2.jsx)(
                        Select.Trigger,
                        {
                          className: "bg-ui-bg-base",
                          ref,
                          children: (0, import_jsx_runtime2.jsx)(Select.Value, {})
                        }
                      ),
                      (0, import_jsx_runtime2.jsx)(Select.Content, { children: stock_locations.map((l) => (0, import_jsx_runtime2.jsx)(Select.Item, { value: l.id, children: l.name }, l.id)) })
                    ] }) }) })
                  ] }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "py-8", children: [
            (0, import_jsx_runtime2.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "shipping_option_id",
                render: ({ field: { onChange, ref, ...field } }) => {
                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-2 xl:flex-row xl:items-center", children: [
                      (0, import_jsx_runtime2.jsxs)("div", { className: "flex-1", children: [
                        (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.shippingMethod") }),
                        (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("orders.fulfillment.methodDescription") })
                      ] }),
                      (0, import_jsx_runtime2.jsx)("div", { className: "flex-1", children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsxs)(
                        Select,
                        {
                          onValueChange: onChange,
                          ...field,
                          disabled: !selectedLocationId,
                          children: [
                            (0, import_jsx_runtime2.jsx)(
                              Select.Trigger,
                              {
                                className: "bg-ui-bg-base",
                                ref,
                                children: isShippingOptionsLoading ? (0, import_jsx_runtime2.jsxs)("span", { className: "text-right", children: [
                                  t2("labels.loading"),
                                  "..."
                                ] }) : (0, import_jsx_runtime2.jsx)(Select.Value, {})
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(Select.Content, { children: shipping_options.map((o) => (0, import_jsx_runtime2.jsx)(Select.Item, { value: o.id, children: o.name }, o.id)) })
                          ]
                        }
                      ) }) })
                    ] }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            differentOptionSelected && (0, import_jsx_runtime2.jsxs)(Alert, { className: "mt-4 p-4", variant: "warning", children: [
              (0, import_jsx_runtime2.jsx)("span", { className: "-mt-[3px] block font-medium", children: t2("labels.beaware") }),
              (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-muted", children: t2("orders.fulfillment.differentOptionSelected") })
            ] })
          ] }),
          (0, import_jsx_runtime2.jsxs)("div", { children: [
            (0, import_jsx_runtime2.jsxs)(Form.Item, { className: "mt-8", children: [
              (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.fulfillment.itemsToFulfill") }),
              (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("orders.fulfillment.itemsToFulfillDesc") }),
              (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col gap-y-1", children: fulfillableItems.map((item) => {
                var _a2, _b2, _c, _d;
                const isShippingProfileMatching = ((_a2 = shipping_options.find(
                  (o) => o.id === shippingOptionId
                )) == null ? void 0 : _a2.shipping_profile_id) === ((_d = (_c = (_b2 = item.variant) == null ? void 0 : _b2.product) == null ? void 0 : _c.shipping_profile) == null ? void 0 : _d.id);
                return (0, import_jsx_runtime2.jsx)(
                  OrderCreateFulfillmentItem,
                  {
                    form,
                    item,
                    locationId: selectedLocationId,
                    disabled: requiresShipping && !isShippingProfileMatching,
                    itemReservedQuantitiesMap
                  },
                  item.id
                );
              }) })
            ] }),
            form.formState.errors.root && (0, import_jsx_runtime2.jsx)(
              Alert,
              {
                variant: "error",
                dismissible: false,
                className: "flex items-center",
                classNameInner: "flex justify-between flex-1 items-center",
                children: form.formState.errors.root.message
              }
            )
          ] }),
          (0, import_jsx_runtime2.jsx)("div", { className: "mt-8 pt-8 ", children: (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "send_notification",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between", children: [
                    (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.returns.sendNotification") }),
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      Switch,
                      {
                        checked: !!value,
                        onCheckedChange: onChange,
                        ...field
                      }
                    ) }) })
                  ] }),
                  (0, import_jsx_runtime2.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.fulfillment.sendNotificationHint") }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) })
        ] }) }) }) }),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(
            Button,
            {
              size: "small",
              type: "submit",
              isLoading: isMutating,
              disabled: !shippingOptionId,
              children: t2("orders.fulfillment.create")
            }
          )
        ] }) })
      ]
    }
  ) });
}
function OrderCreateFulfillment() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const requiresShipping = searchParams.get("requires_shipping") === "true";
  const { order, isLoading, isError, error } = useOrder(id, {
    fields: "currency_code,*items,*items.variant,+items.variant.product.shipping_profile.id,*shipping_address,+shipping_methods.shipping_option_id"
  });
  if (isError) {
    throw error;
  }
  const ready = !isLoading && order;
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime3.jsx)(
    OrderCreateFulfillmentForm,
    {
      order,
      requiresShipping
    }
  ) });
}
export {
  OrderCreateFulfillment as Component
};
//# sourceMappingURL=order-create-fulfillment-62OZHL3L-7HHKPYRU.js.map
