import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-LT4MVCA7.mjs
var useRegionTableFilters = () => {
  const { t } = useTranslation();
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at" },
    { label: t("fields.updatedAt"), key: "updated_at" }
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date"
  }));
  const filters = [...dateFilters];
  return filters;
};

export {
  useRegionTableFilters
};
//# sourceMappingURL=chunk-I7242KR3.js.map
