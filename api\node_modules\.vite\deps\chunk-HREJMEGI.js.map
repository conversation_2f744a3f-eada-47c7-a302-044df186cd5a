{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-B4GODIOW.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/product-types.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar PRODUCT_TYPES_QUERY_KEY = \"product_types\";\nvar productTypesQueryKeys = queryKeysFactory(PRODUCT_TYPES_QUERY_KEY);\nvar useProductType = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.productType.retrieve(id, query),\n    queryKey: productTypesQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useProductTypes = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.productType.list(query),\n    queryKey: productTypesQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateProductType = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productType.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateProductType = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productType.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: productTypesQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteProductType = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.productType.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: productTypesQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  productTypesQueryKeys,\n  useProductType,\n  useProductTypes,\n  useCreateProductType,\n  useUpdateProductType,\n  useDeleteProductType\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,0BAA0B;AAC9B,IAAI,wBAAwB,iBAAiB,uBAAuB;AACpE,IAAI,iBAAiB,CAAC,IAAI,OAAO,YAAY;AAC3C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,YAAY,SAAS,IAAI,KAAK;AAAA,IACvD,UAAU,sBAAsB,OAAO,EAAE;AAAA,IACzC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,OAAO,YAAY;AACxC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,YAAY,KAAK,KAAK;AAAA,IAC/C,UAAU,sBAAsB,KAAK,KAAK;AAAA,IAC1C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,uBAAuB,CAAC,YAAY;AACtC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,OAAO,OAAO;AAAA,IAC7D,WAAW,CAAC,MAAM,WAAW,YAAY;AApC7C;AAqCM,kBAAY,kBAAkB,EAAE,UAAU,sBAAsB,MAAM,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,uBAAuB,CAAC,IAAI,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,OAAO,IAAI,OAAO;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AA9C7C;AA+CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,sBAAsB,OAAO,EAAE;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,sBAAsB,MAAM,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,uBAAuB,CAAC,IAAI,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,YAAY,OAAO,EAAE;AAAA,IACjD,WAAW,CAAC,MAAM,WAAW,YAAY;AA3D7C;AA4DM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,sBAAsB,OAAO,EAAE;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,sBAAsB,MAAM,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}