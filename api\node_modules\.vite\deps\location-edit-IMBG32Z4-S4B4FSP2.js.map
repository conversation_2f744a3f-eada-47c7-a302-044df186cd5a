{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-edit-IMBG32Z4.mjs"], "sourcesContent": ["import {\n  CountrySelect\n} from \"./chunk-MW4K5NNY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocation,\n  useUpdateStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/locations/location-edit/location-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-edit/components/edit-location-form/edit-location-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditLocationSchema = zod.object({\n  name: zod.string().min(1),\n  address: zod.object({\n    address_1: zod.string().min(1),\n    address_2: zod.string().optional(),\n    country_code: zod.string().min(2).max(2),\n    city: zod.string().optional(),\n    postal_code: zod.string().optional(),\n    province: zod.string().optional(),\n    company: zod.string().optional(),\n    phone: zod.string().optional()\n    // TODO: Add validation\n  })\n});\nvar EditLocationForm = ({ location }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: location.name,\n      address: {\n        address_1: location.address?.address_1 || \"\",\n        address_2: location.address?.address_2 || \"\",\n        city: location.address?.city || \"\",\n        company: location.address?.company || \"\",\n        country_code: location.address?.country_code || \"\",\n        phone: location.address?.phone || \"\",\n        postal_code: location.address?.postal_code || \"\",\n        province: location.address?.province || \"\"\n      }\n    },\n    resolver: zodResolver(EditLocationSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateStockLocation(location.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const { name, address } = values;\n    await mutateAsync(\n      {\n        name,\n        address\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"stockLocations.edit.successToast\"));\n          handleSuccess();\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex flex-col gap-y-8 overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"name\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.address_1\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.address\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.address_2\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.address2\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.postal_code\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.postalCode\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.city\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.city\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.country_code\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.country\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(CountrySelect, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.province\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.state\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.company\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.company\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"address.phone\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.phone\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/locations/location-edit/location-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar LocationEdit = () => {\n  const { t } = useTranslation2();\n  const { location_id } = useParams();\n  const { stock_location, isPending, isError, error } = useStockLocation(\n    location_id,\n    {\n      fields: \"*address\"\n    }\n  );\n  const ready = !isPending && !!stock_location;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { className: \"capitalize\", children: t(\"locations.editLocation\") }) }),\n    ready && /* @__PURE__ */ jsx2(EditLocationForm, { location: stock_location })\n  ] });\n};\nexport {\n  LocationEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,yBAA0B;AAqM1B,IAAAA,sBAA2C;AApM3C,IAAI,qBAAyB,WAAO;AAAA,EAClC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,SAAa,WAAO;AAAA,IAClB,WAAe,WAAO,EAAE,IAAI,CAAC;AAAA,IAC7B,WAAe,WAAO,EAAE,SAAS;AAAA,IACjC,cAAkB,WAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,IACvC,MAAU,WAAO,EAAE,SAAS;AAAA,IAC5B,aAAiB,WAAO,EAAE,SAAS;AAAA,IACnC,UAAc,WAAO,EAAE,SAAS;AAAA,IAChC,SAAa,WAAO,EAAE,SAAS;AAAA,IAC/B,OAAW,WAAO,EAAE,SAAS;AAAA;AAAA,EAE/B,CAAC;AACH,CAAC;AACD,IAAI,mBAAmB,CAAC,EAAE,SAAS,MAAM;AAlDzC;AAmDE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,SAAS;AAAA,MACf,SAAS;AAAA,QACP,aAAW,cAAS,YAAT,mBAAkB,cAAa;AAAA,QAC1C,aAAW,cAAS,YAAT,mBAAkB,cAAa;AAAA,QAC1C,QAAM,cAAS,YAAT,mBAAkB,SAAQ;AAAA,QAChC,WAAS,cAAS,YAAT,mBAAkB,YAAW;AAAA,QACtC,gBAAc,cAAS,YAAT,mBAAkB,iBAAgB;AAAA,QAChD,SAAO,cAAS,YAAT,mBAAkB,UAAS;AAAA,QAClC,eAAa,cAAS,YAAT,mBAAkB,gBAAe;AAAA,QAC9C,YAAU,cAAS,YAAT,mBAAkB,aAAY;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,uBAAuB,SAAS,EAAE;AACrE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,kCAAkC,CAAC;AACnD,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,yCAAyC,cAA0B,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,cACjK;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,sBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,sBACjD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,sBAClE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,mBAAmB,EAAE,CAAC;AAAA,sBACpE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,sBAC9D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,sBACjD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,eAAe,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBAChF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,sBAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,sBACjE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,sBAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,UAAU;AAClC,QAAM,EAAE,gBAAgB,WAAW,SAAS,MAAM,IAAI;AAAA,IACpD;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,WAAW,cAAc,UAAUF,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,IACxJ,aAAyB,oBAAAE,KAAK,kBAAkB,EAAE,UAAU,eAAe,CAAC;AAAA,EAC9E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}