import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-R325LWWE.js";
import "./chunk-WNW4SNUS.js";
import {
  DataTable
} from "./chunk-EPUS4TBC.js";
import "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import {
  useProduct,
  useUpdateProduct
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  createDataTableColumnHelper
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-sales-channels-D3EBY5FT.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditSalesChannelsSchema = objectType({
  sales_channels: arrayType(stringType()).optional()
});
var PAGE_SIZE = 50;
var PREFIX = "sc";
var EditSalesChannelsForm = ({
  product
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      sales_channels: ((_a = product.sales_channels) == null ? void 0 : _a.map((sc) => sc.id)) ?? []
    },
    resolver: t(EditSalesChannelsSchema)
  });
  const { setValue } = form;
  const initialState = ((_b = product.sales_channels) == null ? void 0 : _b.reduce((acc, curr) => {
    acc[curr.id] = true;
    return acc;
  }, {})) ?? {};
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialState);
  (0, import_react.useEffect)(() => {
    const ids = Object.keys(rowSelection);
    setValue("sales_channels", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
  }, [rowSelection, setValue]);
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { sales_channels, count, isLoading, isError, error } = useSalesChannels(
    {
      ...searchParams
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useSalesChannelTableFilters();
  const emptyState = useSalesChannelTableEmptyState();
  const columns = useColumns();
  const { mutateAsync, isPending: isMutating } = useUpdateProduct(product.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    const arr = data.sales_channels ?? [];
    const sales_channels2 = arr.map((id) => {
      return {
        id
      };
    });
    await mutateAsync(
      {
        sales_channels: sales_channels2
      },
      {
        onSuccess: () => {
          handleSuccess();
        }
      }
    );
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)("div", { className: "flex h-full flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex-1 overflow-hidden", children: (0, import_jsx_runtime.jsx)(
      DataTable,
      {
        data: sales_channels,
        columns,
        getRowId: (row) => row.id,
        rowCount: count,
        isLoading,
        filters,
        rowSelection: {
          state: rowSelection,
          onRowSelectionChange: setRowSelection
        },
        autoFocusSearch: true,
        layout: "fill",
        emptyState,
        prefix: PREFIX
      }
    ) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", isLoading: isMutating, onClick: handleSubmit, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const columns = useSalesChannelTableColumns();
  return (0, import_react.useMemo)(() => [columnHelper.select(), ...columns], [columns]);
};
var ProductSalesChannels = () => {
  const { id } = useParams();
  const { product, isLoading, isError, error } = useProduct(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: !isLoading && product && (0, import_jsx_runtime2.jsx)(EditSalesChannelsForm, { product }) });
};
export {
  ProductSalesChannels as Component
};
//# sourceMappingURL=product-sales-channels-D3EBY5FT-EXZGCR6V.js.map
