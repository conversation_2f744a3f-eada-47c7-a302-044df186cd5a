import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-B4GODIOW.mjs
var PRODUCT_TYPES_QUERY_KEY = "product_types";
var productTypesQueryKeys = queryKeysFactory(PRODUCT_TYPES_QUERY_KEY);
var useProductType = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.productType.retrieve(id, query),
    queryKey: productTypesQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var useProductTypes = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.productType.list(query),
    queryKey: productTypesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateProductType = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productType.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductType = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productType.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: productTypesQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteProductType = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.productType.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: productTypesQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: productTypesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  productTypesQueryKeys,
  useProductType,
  useProductTypes,
  useCreateProductType,
  useUpdateProductType,
  useDeleteProductType
};
//# sourceMappingURL=chunk-HREJMEGI.js.map
