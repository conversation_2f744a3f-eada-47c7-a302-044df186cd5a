{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-media-2WJUTP6J.mjs"], "sourcesContent": ["import {\n  EditProductMediaSchema,\n  UploadMediaFormItem\n} from \"./chunk-QNPT2JGT.mjs\";\nimport {\n  CSS\n} from \"./chunk-ACBS6KFT.mjs\";\nimport \"./chunk-ZQRKUG6J.mjs\";\nimport \"./chunk-TYTNUPXB.mjs\";\nimport \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useProduct,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-media/product-media.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-media/components/product-media-view/product-media-view.tsx\nimport { useSearchParams } from \"react-router-dom\";\n\n// src/routes/products/product-media/components/edit-product-media-form/edit-product-media-form.tsx\nimport {\n  defaultDropAnimationSideEffects,\n  DndContext,\n  DragOverlay,\n  KeyboardSensor,\n  PointerSensor,\n  useSensor,\n  useSensors\n} from \"@dnd-kit/core\";\nimport {\n  arrayMove,\n  rectSortingStrategy,\n  SortableContext,\n  sortableKeyboardCoordinates,\n  useSortable\n} from \"@dnd-kit/sortable\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { ThumbnailBadge } from \"@medusajs/icons\";\nimport { Button, Checkbox, clx, CommandBar, toast, Tooltip } from \"@medusajs/ui\";\nimport { Fragment, useCallback, useState } from \"react\";\nimport { useFieldArray, useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditProductMediaForm = ({ product }) => {\n  const [selection, setSelection] = useState({});\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      media: getDefaultValues(product.images, product.thumbnail)\n    },\n    resolver: zodResolver(EditProductMediaSchema)\n  });\n  const { fields, append, remove, update } = useFieldArray({\n    name: \"media\",\n    control: form.control,\n    keyName: \"field_id\"\n  });\n  const [activeId, setActiveId] = useState(null);\n  const sensors = useSensors(\n    useSensor(PointerSensor),\n    useSensor(KeyboardSensor, {\n      coordinateGetter: sortableKeyboardCoordinates\n    })\n  );\n  const handleDragStart = (event) => {\n    setActiveId(event.active.id);\n  };\n  const handleDragEnd = (event) => {\n    setActiveId(null);\n    const { active, over } = event;\n    if (active.id !== over?.id) {\n      const oldIndex = fields.findIndex((item) => item.field_id === active.id);\n      const newIndex = fields.findIndex((item) => item.field_id === over?.id);\n      form.setValue(\"media\", arrayMove(fields, oldIndex, newIndex), {\n        shouldDirty: true,\n        shouldTouch: true\n      });\n    }\n  };\n  const handleDragCancel = () => {\n    setActiveId(null);\n  };\n  const { mutateAsync, isPending } = useUpdateProduct(product.id);\n  const handleSubmit = form.handleSubmit(async ({ media }) => {\n    const filesToUpload = media.map((m, i) => ({ file: m.file, index: i })).filter((m) => !!m.file);\n    let uploaded = [];\n    if (filesToUpload.length) {\n      const { files: uploads } = await sdk.admin.upload.create({ files: filesToUpload.map((m) => m.file) }).catch(() => {\n        form.setError(\"media\", {\n          type: \"invalid_file\",\n          message: t(\"products.media.failedToUpload\")\n        });\n        return { files: [] };\n      });\n      uploaded = uploads;\n    }\n    const withUpdatedUrls = media.map((entry, i) => {\n      const toUploadIndex = filesToUpload.findIndex((m) => m.index === i);\n      if (toUploadIndex > -1) {\n        return { ...entry, url: uploaded[toUploadIndex]?.url };\n      }\n      return entry;\n    });\n    const thumbnail = withUpdatedUrls.find((m) => m.isThumbnail)?.url;\n    await mutateAsync(\n      {\n        images: withUpdatedUrls.map((file) => ({ url: file.url, id: file.id })),\n        thumbnail: thumbnail || null\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"products.media.successToast\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  const handleCheckedChange = useCallback(\n    (id) => {\n      return (val) => {\n        if (!val) {\n          const { [id]: _, ...rest } = selection;\n          setSelection(rest);\n        } else {\n          setSelection((prev) => ({ ...prev, [id]: true }));\n        }\n      };\n    },\n    [selection]\n  );\n  const handleDelete = () => {\n    const ids = Object.keys(selection);\n    const indices = ids.map((id) => fields.findIndex((m) => m.id === id));\n    remove(indices);\n    setSelection({});\n  };\n  const handlePromoteToThumbnail = () => {\n    const ids = Object.keys(selection);\n    if (!ids.length) {\n      return;\n    }\n    const currentThumbnailIndex = fields.findIndex((m) => m.isThumbnail);\n    if (currentThumbnailIndex > -1) {\n      update(currentThumbnailIndex, {\n        ...fields[currentThumbnailIndex],\n        isThumbnail: false\n      });\n    }\n    const index = fields.findIndex((m) => m.id === ids[0]);\n    update(index, {\n      ...fields[index],\n      isThumbnail: true\n    });\n    setSelection({});\n  };\n  const selectionCount = Object.keys(selection).length;\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { blockSearchParams: true, form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex size-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: { pathname: \".\", search: void 0 }, children: t(\"products.media.galleryLabel\") }) }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-col overflow-hidden\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full flex-col-reverse lg:grid lg:grid-cols-[1fr_560px]\", children: [\n          /* @__PURE__ */ jsx(\n            DndContext,\n            {\n              sensors,\n              onDragEnd: handleDragEnd,\n              onDragStart: handleDragStart,\n              onDragCancel: handleDragCancel,\n              children: /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-subtle size-full overflow-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"grid h-fit auto-rows-auto grid-cols-4 gap-6 p-6\", children: [\n                /* @__PURE__ */ jsx(\n                  SortableContext,\n                  {\n                    items: fields.map((m) => m.field_id),\n                    strategy: rectSortingStrategy,\n                    children: fields.map((m) => {\n                      return /* @__PURE__ */ jsx(\n                        MediaGridItem,\n                        {\n                          onCheckedChange: handleCheckedChange(m.id),\n                          checked: !!selection[m.id],\n                          media: m\n                        },\n                        m.field_id\n                      );\n                    })\n                  }\n                ),\n                /* @__PURE__ */ jsx(DragOverlay, { dropAnimation: dropAnimationConfig, children: activeId ? /* @__PURE__ */ jsx(\n                  MediaGridItemOverlay,\n                  {\n                    media: fields.find((m) => m.field_id === activeId),\n                    checked: !!selection[fields.find((m) => m.field_id === activeId).id]\n                  }\n                ) : null })\n              ] }) })\n            }\n          ),\n          /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-base overflow-auto border-b px-6 py-4 lg:border-b-0 lg:border-l\", children: /* @__PURE__ */ jsx(UploadMediaFormItem, { form, append }) })\n        ] }) }),\n        /* @__PURE__ */ jsx(CommandBar, { open: !!selectionCount, children: /* @__PURE__ */ jsxs(CommandBar.Bar, { children: [\n          /* @__PURE__ */ jsx(CommandBar.Value, { children: t(\"general.countSelected\", {\n            count: selectionCount\n          }) }),\n          /* @__PURE__ */ jsx(CommandBar.Seperator, {}),\n          selectionCount === 1 && /* @__PURE__ */ jsxs(Fragment, { children: [\n            /* @__PURE__ */ jsx(\n              CommandBar.Command,\n              {\n                action: handlePromoteToThumbnail,\n                label: t(\"products.media.makeThumbnail\"),\n                shortcut: \"t\"\n              }\n            ),\n            /* @__PURE__ */ jsx(CommandBar.Seperator, {})\n          ] }),\n          /* @__PURE__ */ jsx(\n            CommandBar.Command,\n            {\n              action: handleDelete,\n              label: t(\"actions.delete\"),\n              shortcut: \"d\"\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\nvar getDefaultValues = (images, thumbnail) => {\n  const media = images?.map((image) => ({\n    id: image.id,\n    url: image.url,\n    isThumbnail: image.url === thumbnail,\n    file: null\n  })) || [];\n  if (thumbnail && !media.some((mediaItem) => mediaItem.url === thumbnail)) {\n    const id = Math.random().toString(36).substring(7);\n    media.unshift({\n      id,\n      url: thumbnail,\n      isThumbnail: true,\n      file: null\n    });\n  }\n  return media;\n};\nvar dropAnimationConfig = {\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: \"0.4\"\n      }\n    }\n  })\n};\nvar MediaGridItem = ({\n  media,\n  checked,\n  onCheckedChange\n}) => {\n  const { t } = useTranslation();\n  const handleToggle = useCallback(\n    (value) => {\n      onCheckedChange(value);\n    },\n    [onCheckedChange]\n  );\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({ id: media.field_id });\n  const style = {\n    opacity: isDragging ? 0.4 : void 0,\n    transform: CSS.Transform.toString(transform),\n    transition\n  };\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"shadow-elevation-card-rest hover:shadow-elevation-card-hover focus-visible:shadow-borders-focus bg-ui-bg-subtle-hover group relative aspect-square h-auto max-w-full overflow-hidden rounded-lg outline-none\"\n      ),\n      style,\n      ref: setNodeRef,\n      children: [\n        media.isThumbnail && /* @__PURE__ */ jsx(\"div\", { className: \"absolute left-2 top-2\", children: /* @__PURE__ */ jsx(Tooltip, { content: t(\"products.media.thumbnailTooltip\"), children: /* @__PURE__ */ jsx(ThumbnailBadge, {}) }) }),\n        /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: clx(\"absolute inset-0 cursor-grab touch-none outline-none\", {\n              \"cursor-grabbing\": isDragging\n            }),\n            ref: setActivatorNodeRef,\n            ...attributes,\n            ...listeners\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: clx(\"transition-fg absolute right-2 top-2 opacity-0\", {\n              \"group-focus-within:opacity-100 group-hover:opacity-100 group-focus:opacity-100\": !isDragging && !checked,\n              \"opacity-100\": checked\n            }),\n            children: /* @__PURE__ */ jsx(\n              Checkbox,\n              {\n                onClick: (e) => {\n                  e.stopPropagation();\n                },\n                checked,\n                onCheckedChange: handleToggle\n              }\n            )\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"img\",\n          {\n            src: media.url,\n            alt: \"\",\n            className: \"size-full object-cover object-center\"\n          }\n        )\n      ]\n    }\n  );\n};\nvar MediaGridItemOverlay = ({\n  media,\n  checked\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"shadow-elevation-card-rest hover:shadow-elevation-card-hover focus-visible:shadow-borders-focus bg-ui-bg-subtle-hover group relative aspect-square h-auto max-w-full cursor-grabbing overflow-hidden rounded-lg outline-none\", children: [\n    media.isThumbnail && /* @__PURE__ */ jsx(\"div\", { className: \"absolute left-2 top-2\", children: /* @__PURE__ */ jsx(ThumbnailBadge, {}) }),\n    /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        className: clx(\"transition-fg absolute right-2 top-2 opacity-0\", {\n          \"opacity-100\": checked\n        }),\n        children: /* @__PURE__ */ jsx(Checkbox, { checked })\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      \"img\",\n      {\n        src: media.url,\n        alt: \"\",\n        className: \"size-full object-cover object-center\"\n      }\n    )\n  ] });\n};\n\n// src/routes/products/product-media/components/product-media-gallery/product-media-gallery.tsx\nimport {\n  ArrowDownTray,\n  ThumbnailBadge as ThumbnailBadge2,\n  Trash,\n  TriangleLeftMini,\n  TriangleRightMini\n} from \"@medusajs/icons\";\nimport { Button as Button2, IconButton, Text, Tooltip as Tooltip2, clx as clx2, usePrompt } from \"@medusajs/ui\";\nimport { useCallback as useCallback2, useEffect, useState as useState2 } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link as Link2, useLocation } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductMediaGallery = ({ product }) => {\n  const { state } = useLocation();\n  const [curr, setCurr] = useState2(state?.curr || 0);\n  const { t } = useTranslation2();\n  const prompt = usePrompt();\n  const { mutateAsync, isPending } = useUpdateProduct(product.id);\n  const media = getMedia(product.images, product.thumbnail);\n  const next = useCallback2(() => {\n    if (isPending) {\n      return;\n    }\n    setCurr((prev2) => (prev2 + 1) % media.length);\n  }, [media, isPending]);\n  const prev = useCallback2(() => {\n    if (isPending) {\n      return;\n    }\n    setCurr((prev2) => (prev2 - 1 + media.length) % media.length);\n  }, [media, isPending]);\n  const goTo = useCallback2(\n    (index) => {\n      if (isPending) {\n        return;\n      }\n      setCurr(index);\n    },\n    [isPending]\n  );\n  const handleDownloadCurrent = () => {\n    if (isPending) {\n      return;\n    }\n    const a = document.createElement(\"a\");\n    a.href = media[curr].url;\n    a.download = \"image\";\n    a.target = \"_blank\";\n    a.click();\n  };\n  const handleDeleteCurrent = async () => {\n    const current = media[curr];\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: current.isThumbnail ? t(\"products.media.deleteWarningWithThumbnail\", { count: 1 }) : t(\"products.media.deleteWarning\", { count: 1 }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    const mediaToKeep = product.images?.filter((i) => i.id !== current.id).map((i) => ({ url: i.url })) || [];\n    if (curr === media.length - 1) {\n      setCurr((prev2) => prev2 - 1);\n    }\n    await mutateAsync({\n      images: mediaToKeep,\n      thumbnail: current.isThumbnail ? \"\" : void 0\n    });\n  };\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.key === \"ArrowRight\") {\n        next();\n      } else if (e.key === \"ArrowLeft\") {\n        prev();\n      }\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [next, prev]);\n  const noMedia = !media.length;\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"flex size-full flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsx2(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsxs2(\n        IconButton,\n        {\n          size: \"small\",\n          type: \"button\",\n          onClick: handleDeleteCurrent,\n          disabled: noMedia,\n          children: [\n            /* @__PURE__ */ jsx2(Trash, {}),\n            /* @__PURE__ */ jsx2(\"span\", { className: \"sr-only\", children: t(\"products.media.deleteImageLabel\") })\n          ]\n        }\n      ),\n      /* @__PURE__ */ jsxs2(\n        IconButton,\n        {\n          size: \"small\",\n          type: \"button\",\n          onClick: handleDownloadCurrent,\n          disabled: noMedia,\n          children: [\n            /* @__PURE__ */ jsx2(ArrowDownTray, {}),\n            /* @__PURE__ */ jsx2(\"span\", { className: \"sr-only\", children: t(\"products.media.downloadImageLabel\") })\n          ]\n        }\n      ),\n      /* @__PURE__ */ jsx2(Button2, { variant: \"secondary\", size: \"small\", asChild: true, children: /* @__PURE__ */ jsx2(Link2, { to: { pathname: \".\", search: \"view=edit\" }, children: t(\"actions.edit\") }) })\n    ] }) }),\n    /* @__PURE__ */ jsxs2(RouteFocusModal.Body, { className: \"flex flex-col overflow-hidden\", children: [\n      /* @__PURE__ */ jsx2(Canvas, { curr, media }),\n      /* @__PURE__ */ jsx2(\n        Preview,\n        {\n          curr,\n          media,\n          prev,\n          next,\n          goTo\n        }\n      )\n    ] })\n  ] });\n};\nvar Canvas = ({ media, curr }) => {\n  const { t } = useTranslation2();\n  if (media.length === 0) {\n    return /* @__PURE__ */ jsxs2(\"div\", { className: \"bg-ui-bg-subtle flex size-full flex-col items-center justify-center gap-y-4 pb-8 pt-6\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col items-center\", children: [\n        /* @__PURE__ */ jsx2(\n          Text,\n          {\n            size: \"small\",\n            leading: \"compact\",\n            weight: \"plus\",\n            className: \"text-ui-fg-subtle\",\n            children: t(\"products.media.emptyState.header\")\n          }\n        ),\n        /* @__PURE__ */ jsx2(Text, { size: \"small\", className: \"text-ui-fg-muted\", children: t(\"products.media.emptyState.description\") })\n      ] }),\n      /* @__PURE__ */ jsx2(Button2, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx2(Link2, { to: \"?view=edit\", children: t(\"products.media.emptyState.action\") }) })\n    ] });\n  }\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"bg-ui-bg-subtle relative size-full overflow-hidden\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center justify-center p-6\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"relative inline-block max-h-full max-w-full\", children: [\n    media[curr].isThumbnail && /* @__PURE__ */ jsx2(\"div\", { className: \"absolute left-2 top-2\", children: /* @__PURE__ */ jsx2(Tooltip2, { content: t(\"products.media.thumbnailTooltip\"), children: /* @__PURE__ */ jsx2(ThumbnailBadge2, {}) }) }),\n    /* @__PURE__ */ jsx2(\n      \"img\",\n      {\n        src: media[curr].url,\n        alt: \"\",\n        className: \"object-fit shadow-elevation-card-rest max-h-[calc(100vh-200px)] w-auto rounded-xl object-contain\"\n      }\n    )\n  ] }) }) });\n};\nvar MAX_VISIBLE_ITEMS = 8;\nvar Preview = ({\n  media,\n  curr,\n  prev,\n  next,\n  goTo\n}) => {\n  if (!media.length) {\n    return null;\n  }\n  const getVisibleItems = (media2, index) => {\n    if (media2.length <= MAX_VISIBLE_ITEMS) {\n      return media2;\n    }\n    const half = Math.floor(MAX_VISIBLE_ITEMS / 2);\n    const start = (index - half + media2.length) % media2.length;\n    const end = (start + MAX_VISIBLE_ITEMS) % media2.length;\n    if (end < start) {\n      return [...media2.slice(start), ...media2.slice(0, end)];\n    } else {\n      return media2.slice(start, end);\n    }\n  };\n  const visibleItems = getVisibleItems(media, curr);\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"flex shrink-0 items-center justify-center gap-x-2 border-t p-3\", children: [\n    /* @__PURE__ */ jsx2(\n      IconButton,\n      {\n        size: \"small\",\n        variant: \"transparent\",\n        className: \"text-ui-fg-muted\",\n        type: \"button\",\n        onClick: prev,\n        children: /* @__PURE__ */ jsx2(TriangleLeftMini, {})\n      }\n    ),\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center gap-x-2\", children: visibleItems.map((item) => {\n      const isCurrentImage = item.id === media[curr].id;\n      const originalIndex = media.findIndex((i) => i.id === item.id);\n      return /* @__PURE__ */ jsx2(\n        \"button\",\n        {\n          type: \"button\",\n          onClick: () => goTo(originalIndex),\n          className: clx2(\n            \"transition-fg size-7 overflow-hidden rounded-[4px] outline-none\",\n            {\n              \"shadow-borders-focus\": isCurrentImage\n            }\n          ),\n          children: /* @__PURE__ */ jsx2(\"img\", { src: item.url, alt: \"\", className: \"size-full object-cover\" })\n        },\n        item.id\n      );\n    }) }),\n    /* @__PURE__ */ jsx2(\n      IconButton,\n      {\n        size: \"small\",\n        variant: \"transparent\",\n        className: \"text-ui-fg-muted\",\n        type: \"button\",\n        onClick: next,\n        children: /* @__PURE__ */ jsx2(TriangleRightMini, {})\n      }\n    )\n  ] });\n};\nvar getMedia = (images, thumbnail) => {\n  const media = images?.map((image) => ({\n    id: image.id,\n    url: image.url,\n    isThumbnail: image.url === thumbnail\n  })) || [];\n  if (thumbnail && !media.some((mediaItem) => mediaItem.isThumbnail)) {\n    media.unshift({\n      id: \"thumbnail_only\",\n      url: thumbnail,\n      isThumbnail: true\n    });\n  }\n  return media;\n};\n\n// src/routes/products/product-media/components/product-media-view/product-media-view-context.tsx\nimport { createContext } from \"react\";\nvar ProductMediaViewContext = createContext(null);\n\n// src/routes/products/product-media/components/product-media-view/product-media-view.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar getView = (searchParams) => {\n  const view = searchParams.get(\"view\");\n  if (view === \"edit\" /* EDIT */) {\n    return \"edit\" /* EDIT */;\n  }\n  return \"gallery\" /* GALLERY */;\n};\nvar ProductMediaView = ({ product }) => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const view = getView(searchParams);\n  const handleGoToView = (view2) => {\n    return () => {\n      setSearchParams({ view: view2 });\n    };\n  };\n  return /* @__PURE__ */ jsx3(\n    ProductMediaViewContext.Provider,\n    {\n      value: {\n        goToGallery: handleGoToView(\"gallery\" /* GALLERY */),\n        goToEdit: handleGoToView(\"edit\" /* EDIT */)\n      },\n      children: renderView(view, product)\n    }\n  );\n};\nvar renderView = (view, product) => {\n  switch (view) {\n    case \"gallery\" /* GALLERY */:\n      return /* @__PURE__ */ jsx3(ProductMediaGallery, { product });\n    case \"edit\" /* EDIT */:\n      return /* @__PURE__ */ jsx3(EditProductMediaForm, { product });\n  }\n};\n\n// src/routes/products/product-media/product-media.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ProductMedia = () => {\n  const { t } = useTranslation3();\n  const { id } = useParams();\n  const { product, isLoading, isError, error } = useProduct(id);\n  const ready = !isLoading && product;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(RouteFocusModal, { children: [\n    /* @__PURE__ */ jsx4(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx4(\"span\", { className: \"sr-only\", children: t(\"products.media.label\") }) }),\n    /* @__PURE__ */ jsx4(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx4(\"span\", { className: \"sr-only\", children: t(\"products.media.editHint\") }) }),\n    ready && /* @__PURE__ */ jsx4(ProductMediaView, { product })\n  ] });\n};\nexport {\n  ProductMedia as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,mBAAgD;AAIhD,yBAA0B;AAiV1B,IAAAA,gBAA8E;AAG9E,IAAAC,sBAA2C;AA4O3C,IAAAC,gBAA8B;AAI9B,IAAAD,sBAA4B;AAqC5B,IAAAA,sBAA2C;AAxmB3C,IAAI,uBAAuB,CAAC,EAAE,QAAQ,MAAM;AAC1C,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,CAAC,CAAC;AAC7C,QAAM,EAAE,GAAAE,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,iBAAiB,QAAQ,QAAQ,QAAQ,SAAS;AAAA,IAC3D;AAAA,IACA,UAAU,EAAY,sBAAsB;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,QAAQ,QAAQ,QAAQ,OAAO,IAAI,cAAc;AAAA,IACvD,MAAM;AAAA,IACN,SAAS,KAAK;AAAA,IACd,SAAS;AAAA,EACX,CAAC;AACD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,IAAI;AAC7C,QAAM,UAAU;AAAA,IACd,UAAU,aAAa;AAAA,IACvB,UAAU,gBAAgB;AAAA,MACxB,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,gBAAY,MAAM,OAAO,EAAE;AAAA,EAC7B;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,gBAAY,IAAI;AAChB,UAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAI,OAAO,QAAO,6BAAM,KAAI;AAC1B,YAAM,WAAW,OAAO,UAAU,CAAC,SAAS,KAAK,aAAa,OAAO,EAAE;AACvE,YAAM,WAAW,OAAO,UAAU,CAAC,SAAS,KAAK,cAAa,6BAAM,GAAE;AACtE,WAAK,SAAS,SAAS,UAAU,QAAQ,UAAU,QAAQ,GAAG;AAAA,QAC5D,aAAa;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,mBAAmB,MAAM;AAC7B,gBAAY,IAAI;AAAA,EAClB;AACA,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,QAAQ,EAAE;AAC9D,QAAM,eAAe,KAAK,aAAa,OAAO,EAAE,MAAM,MAAM;AAxG9D;AAyGI,UAAM,gBAAgB,MAAM,IAAI,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI;AAC9F,QAAI,WAAW,CAAC;AAChB,QAAI,cAAc,QAAQ;AACxB,YAAM,EAAE,OAAO,QAAQ,IAAI,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE,OAAO,cAAc,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,MAAM;AAChH,aAAK,SAAS,SAAS;AAAA,UACrB,MAAM;AAAA,UACN,SAASA,GAAE,+BAA+B;AAAA,QAC5C,CAAC;AACD,eAAO,EAAE,OAAO,CAAC,EAAE;AAAA,MACrB,CAAC;AACD,iBAAW;AAAA,IACb;AACA,UAAM,kBAAkB,MAAM,IAAI,CAAC,OAAO,MAAM;AArHpD,UAAAC;AAsHM,YAAM,gBAAgB,cAAc,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC;AAClE,UAAI,gBAAgB,IAAI;AACtB,eAAO,EAAE,GAAG,OAAO,MAAKA,MAAA,SAAS,aAAa,MAAtB,gBAAAA,IAAyB,IAAI;AAAA,MACvD;AACA,aAAO;AAAA,IACT,CAAC;AACD,UAAM,aAAY,qBAAgB,KAAK,CAAC,MAAM,EAAE,WAAW,MAAzC,mBAA4C;AAC9D,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ,gBAAgB,IAAI,CAAC,UAAU,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,EAAE;AAAA,QACtE,WAAW,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQD,GAAE,6BAA6B,CAAC;AAC9C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,0BAAsB;AAAA,IAC1B,CAAC,OAAO;AACN,aAAO,CAAC,QAAQ;AACd,YAAI,CAAC,KAAK;AACR,gBAAM,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAC7B,uBAAa,IAAI;AAAA,QACnB,OAAO;AACL,uBAAa,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,KAAK,EAAE;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,SAAS;AAAA,EACZ;AACA,QAAM,eAAe,MAAM;AACzB,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,UAAM,UAAU,IAAI,IAAI,CAAC,OAAO,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;AACpE,WAAO,OAAO;AACd,iBAAa,CAAC,CAAC;AAAA,EACjB;AACA,QAAM,2BAA2B,MAAM;AACrC,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,QAAI,CAAC,IAAI,QAAQ;AACf;AAAA,IACF;AACA,UAAM,wBAAwB,OAAO,UAAU,CAAC,MAAM,EAAE,WAAW;AACnE,QAAI,wBAAwB,IAAI;AAC9B,aAAO,uBAAuB;AAAA,QAC5B,GAAG,OAAO,qBAAqB;AAAA,QAC/B,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AACA,UAAM,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;AACrD,WAAO,OAAO;AAAA,MACZ,GAAG,OAAO,KAAK;AAAA,MACf,aAAa;AAAA,IACf,CAAC;AACD,iBAAa,CAAC,CAAC;AAAA,EACjB;AACA,QAAM,iBAAiB,OAAO,KAAK,SAAS,EAAE;AAC9C,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,mBAAmB,MAAM,MAAM,cAA0B;AAAA,IAC1G;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yCAAyC,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,EAAE,UAAU,KAAK,QAAQ,OAAO,GAAG,UAAUA,GAAE,6BAA6B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACvV,wBAAI,gBAAgB,MAAM,EAAE,WAAW,iCAAiC,cAA0B,yBAAK,OAAO,EAAE,WAAW,oEAAoE,UAAU;AAAA,cACvM;AAAA,YACd;AAAA,YACA;AAAA,cACE;AAAA,cACA,WAAW;AAAA,cACX,aAAa;AAAA,cACb,cAAc;AAAA,cACd,cAA0B,wBAAI,OAAO,EAAE,WAAW,2CAA2C,cAA0B,yBAAK,OAAO,EAAE,WAAW,mDAAmD,UAAU;AAAA,oBAC3L;AAAA,kBACd;AAAA,kBACA;AAAA,oBACE,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,oBACnC,UAAU;AAAA,oBACV,UAAU,OAAO,IAAI,CAAC,MAAM;AAC1B,iCAAuB;AAAA,wBACrB;AAAA,wBACA;AAAA,0BACE,iBAAiB,oBAAoB,EAAE,EAAE;AAAA,0BACzC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE;AAAA,0BACzB,OAAO;AAAA,wBACT;AAAA,wBACA,EAAE;AAAA,sBACJ;AAAA,oBACF,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,oBACgB,wBAAI,aAAa,EAAE,eAAe,qBAAqB,UAAU,eAA2B;AAAA,kBAC1G;AAAA,kBACA;AAAA,oBACE,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,aAAa,QAAQ;AAAA,oBACjD,SAAS,CAAC,CAAC,UAAU,OAAO,KAAK,CAAC,MAAM,EAAE,aAAa,QAAQ,EAAE,EAAE;AAAA,kBACrE;AAAA,gBACF,IAAI,KAAK,CAAC;AAAA,cACZ,EAAE,CAAC,EAAE,CAAC;AAAA,YACR;AAAA,UACF;AAAA,cACgB,wBAAI,OAAO,EAAE,WAAW,4EAA4E,cAA0B,wBAAI,qBAAqB,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,QAC5L,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,YAAY,EAAE,MAAM,CAAC,CAAC,gBAAgB,cAA0B,yBAAK,WAAW,KAAK,EAAE,UAAU;AAAA,cACnG,wBAAI,WAAW,OAAO,EAAE,UAAUA,GAAE,yBAAyB;AAAA,YAC3E,OAAO;AAAA,UACT,CAAC,EAAE,CAAC;AAAA,cACY,wBAAI,WAAW,WAAW,CAAC,CAAC;AAAA,UAC5C,mBAAmB,SAAqB,yBAAK,uBAAU,EAAE,UAAU;AAAA,gBACjD;AAAA,cACd,WAAW;AAAA,cACX;AAAA,gBACE,QAAQ;AAAA,gBACR,OAAOA,GAAE,8BAA8B;AAAA,gBACvC,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,gBACgB,wBAAI,WAAW,WAAW,CAAC,CAAC;AAAA,UAC9C,EAAE,CAAC;AAAA,cACa;AAAA,YACd,WAAW;AAAA,YACX;AAAA,cACE,QAAQ;AAAA,cACR,OAAOA,GAAE,gBAAgB;AAAA,cACzB,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,mBAAmB,CAAC,QAAQ,cAAc;AAC5C,QAAM,SAAQ,iCAAQ,IAAI,CAAC,WAAW;AAAA,IACpC,IAAI,MAAM;AAAA,IACV,KAAK,MAAM;AAAA,IACX,aAAa,MAAM,QAAQ;AAAA,IAC3B,MAAM;AAAA,EACR,QAAO,CAAC;AACR,MAAI,aAAa,CAAC,MAAM,KAAK,CAAC,cAAc,UAAU,QAAQ,SAAS,GAAG;AACxE,UAAM,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AACjD,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,KAAK;AAAA,MACL,aAAa;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAI,sBAAsB;AAAA,EACxB,aAAa,gCAAgC;AAAA,IAC3C,QAAQ;AAAA,MACN,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,mBAAe;AAAA,IACnB,CAAC,UAAU;AACT,sBAAgB,KAAK;AAAA,IACvB;AAAA,IACA,CAAC,eAAe;AAAA,EAClB;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YAAY,EAAE,IAAI,MAAM,SAAS,CAAC;AACtC,QAAM,QAAQ;AAAA,IACZ,SAAS,aAAa,MAAM;AAAA,IAC5B,WAAW,IAAI,UAAU,SAAS,SAAS;AAAA,IAC3C;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,UAAU;AAAA,QACR,MAAM,mBAA+B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,wBAAI,SAAS,EAAE,SAASA,GAAE,iCAAiC,GAAG,cAA0B,wBAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACpN;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW,IAAI,wDAAwD;AAAA,cACrE,mBAAmB;AAAA,YACrB,CAAC;AAAA,YACD,KAAK;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW,IAAI,kDAAkD;AAAA,cAC/D,kFAAkF,CAAC,cAAc,CAAC;AAAA,cAClG,eAAe;AAAA,YACjB,CAAC;AAAA,YACD,cAA0B;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,SAAS,CAAC,MAAM;AACd,oBAAE,gBAAgB;AAAA,gBACpB;AAAA,gBACA;AAAA,gBACA,iBAAiB;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,KAAK,MAAM;AAAA,YACX,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,gOAAgO,UAAU;AAAA,IACxR,MAAM,mBAA+B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,wBAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC;AAAA,QACzH;AAAA,MACd;AAAA,MACA;AAAA,QACE,WAAW,IAAI,kDAAkD;AAAA,UAC/D,eAAe;AAAA,QACjB,CAAC;AAAA,QACD,cAA0B,wBAAI,UAAU,EAAE,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,KAAK,MAAM;AAAA,QACX,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAeA,IAAI,sBAAsB,CAAC,EAAE,QAAQ,MAAM;AACzC,QAAM,EAAE,MAAM,IAAI,YAAY;AAC9B,QAAM,CAAC,MAAM,OAAO,QAAI,cAAAE,WAAU,+BAAO,SAAQ,CAAC;AAClD,QAAM,EAAE,GAAAF,GAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,QAAQ,EAAE;AAC9D,QAAM,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,SAAS;AACxD,QAAM,WAAO,cAAAG,aAAa,MAAM;AAC9B,QAAI,WAAW;AACb;AAAA,IACF;AACA,YAAQ,CAAC,WAAW,QAAQ,KAAK,MAAM,MAAM;AAAA,EAC/C,GAAG,CAAC,OAAO,SAAS,CAAC;AACrB,QAAM,WAAO,cAAAA,aAAa,MAAM;AAC9B,QAAI,WAAW;AACb;AAAA,IACF;AACA,YAAQ,CAAC,WAAW,QAAQ,IAAI,MAAM,UAAU,MAAM,MAAM;AAAA,EAC9D,GAAG,CAAC,OAAO,SAAS,CAAC;AACrB,QAAM,WAAO,cAAAA;AAAA,IACX,CAAC,UAAU;AACT,UAAI,WAAW;AACb;AAAA,MACF;AACA,cAAQ,KAAK;AAAA,IACf;AAAA,IACA,CAAC,SAAS;AAAA,EACZ;AACA,QAAM,wBAAwB,MAAM;AAClC,QAAI,WAAW;AACb;AAAA,IACF;AACA,UAAM,IAAI,SAAS,cAAc,GAAG;AACpC,MAAE,OAAO,MAAM,IAAI,EAAE;AACrB,MAAE,WAAW;AACb,MAAE,SAAS;AACX,MAAE,MAAM;AAAA,EACV;AACA,QAAM,sBAAsB,YAAY;AAzb1C;AA0bI,UAAM,UAAU,MAAM,IAAI;AAC1B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAOH,GAAE,oBAAoB;AAAA,MAC7B,aAAa,QAAQ,cAAcA,GAAE,6CAA6C,EAAE,OAAO,EAAE,CAAC,IAAIA,GAAE,gCAAgC,EAAE,OAAO,EAAE,CAAC;AAAA,MAChJ,aAAaA,GAAE,gBAAgB;AAAA,MAC/B,YAAYA,GAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,gBAAc,aAAQ,WAAR,mBAAgB,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,QAAO,CAAC;AACxG,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,cAAQ,CAAC,UAAU,QAAQ,CAAC;AAAA,IAC9B;AACA,UAAM,YAAY;AAAA,MAChB,QAAQ;AAAA,MACR,WAAW,QAAQ,cAAc,KAAK;AAAA,IACxC,CAAC;AAAA,EACH;AACA,+BAAU,MAAM;AACd,UAAM,gBAAgB,CAAC,MAAM;AAC3B,UAAI,EAAE,QAAQ,cAAc;AAC1B,aAAK;AAAA,MACP,WAAW,EAAE,QAAQ,aAAa;AAChC,aAAK;AAAA,MACP;AAAA,IACF;AACA,aAAS,iBAAiB,WAAW,aAAa;AAClD,WAAO,MAAM;AACX,eAAS,oBAAoB,WAAW,aAAa;AAAA,IACvD;AAAA,EACF,GAAG,CAAC,MAAM,IAAI,CAAC;AACf,QAAM,UAAU,CAAC,MAAM;AACvB,aAAuB,oBAAAI,MAAM,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,QACpF,oBAAAC,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UACpI,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,gBACQ,oBAAAC,KAAK,OAAO,CAAC,CAAC;AAAA,gBACd,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUL,GAAE,iCAAiC,EAAE,CAAC;AAAA,UACvG;AAAA,QACF;AAAA,MACF;AAAA,UACgB,oBAAAI;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,gBACQ,oBAAAC,KAAK,eAAe,CAAC,CAAC;AAAA,gBACtB,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUL,GAAE,mCAAmC,EAAE,CAAC;AAAA,UACzG;AAAA,QACF;AAAA,MACF;AAAA,UACgB,oBAAAK,KAAK,QAAS,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAO,EAAE,IAAI,EAAE,UAAU,KAAK,QAAQ,YAAY,GAAG,UAAUL,GAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1M,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,oBAAAI,MAAM,gBAAgB,MAAM,EAAE,WAAW,iCAAiC,UAAU;AAAA,UAClF,oBAAAC,KAAK,QAAQ,EAAE,MAAM,MAAM,CAAC;AAAA,UAC5B,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,SAAS,CAAC,EAAE,OAAO,KAAK,MAAM;AAChC,QAAM,EAAE,GAAAL,GAAE,IAAI,eAAgB;AAC9B,MAAI,MAAM,WAAW,GAAG;AACtB,eAAuB,oBAAAI,MAAM,OAAO,EAAE,WAAW,yFAAyF,UAAU;AAAA,UAClI,oBAAAA,MAAM,OAAO,EAAE,WAAW,8BAA8B,UAAU;AAAA,YAChE,oBAAAC;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,UAAUL,GAAE,kCAAkC;AAAA,UAChD;AAAA,QACF;AAAA,YACgB,oBAAAK,KAAK,MAAM,EAAE,MAAM,SAAS,WAAW,oBAAoB,UAAUL,GAAE,uCAAuC,EAAE,CAAC;AAAA,MACnI,EAAE,CAAC;AAAA,UACa,oBAAAK,KAAK,QAAS,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAO,EAAE,IAAI,cAAc,UAAUL,GAAE,kCAAkC,EAAE,CAAC,EAAE,CAAC;AAAA,IACpM,EAAE,CAAC;AAAA,EACL;AACA,aAAuB,oBAAAK,KAAK,OAAO,EAAE,WAAW,sDAAsD,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,kDAAkD,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,IACtT,MAAM,IAAI,EAAE,mBAA+B,oBAAAC,KAAK,OAAO,EAAE,WAAW,yBAAyB,cAA0B,oBAAAA,KAAK,SAAU,EAAE,SAASL,GAAE,iCAAiC,GAAG,cAA0B,oBAAAK,KAAK,gBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QAC/N,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,KAAK,MAAM,IAAI,EAAE;AAAA,QACjB,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACX;AACA,IAAI,oBAAoB;AACxB,IAAI,UAAU,CAAC;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,QAAI,OAAO,UAAU,mBAAmB;AACtC,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,MAAM,oBAAoB,CAAC;AAC7C,UAAM,SAAS,QAAQ,OAAO,OAAO,UAAU,OAAO;AACtD,UAAM,OAAO,QAAQ,qBAAqB,OAAO;AACjD,QAAI,MAAM,OAAO;AACf,aAAO,CAAC,GAAG,OAAO,MAAM,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG,GAAG,CAAC;AAAA,IACzD,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,GAAG;AAAA,IAChC;AAAA,EACF;AACA,QAAM,eAAe,gBAAgB,OAAO,IAAI;AAChD,aAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,kEAAkE,UAAU;AAAA,QAC3G,oBAAAC;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,cAA0B,oBAAAA,KAAK,kBAAkB,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,QACgB,oBAAAA,KAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU,aAAa,IAAI,CAAC,SAAS;AACzG,YAAM,iBAAiB,KAAK,OAAO,MAAM,IAAI,EAAE;AAC/C,YAAM,gBAAgB,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;AAC7D,iBAAuB,oBAAAA;AAAA,QACrB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS,MAAM,KAAK,aAAa;AAAA,UACjC,WAAW;AAAA,YACT;AAAA,YACA;AAAA,cACE,wBAAwB;AAAA,YAC1B;AAAA,UACF;AAAA,UACA,cAA0B,oBAAAA,KAAK,OAAO,EAAE,KAAK,KAAK,KAAK,KAAK,IAAI,WAAW,yBAAyB,CAAC;AAAA,QACvG;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF,CAAC,EAAE,CAAC;AAAA,QACY,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,WAAW,CAAC,QAAQ,cAAc;AACpC,QAAM,SAAQ,iCAAQ,IAAI,CAAC,WAAW;AAAA,IACpC,IAAI,MAAM;AAAA,IACV,KAAK,MAAM;AAAA,IACX,aAAa,MAAM,QAAQ;AAAA,EAC7B,QAAO,CAAC;AACR,MAAI,aAAa,CAAC,MAAM,KAAK,CAAC,cAAc,UAAU,WAAW,GAAG;AAClE,UAAM,QAAQ;AAAA,MACZ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,IAAI,8BAA0B,6BAAc,IAAI;AAIhD,IAAI,UAAU,CAAC,iBAAiB;AAC9B,QAAM,OAAO,aAAa,IAAI,MAAM;AACpC,MAAI,SAAS,QAAmB;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,mBAAmB,CAAC,EAAE,QAAQ,MAAM;AACtC,QAAM,CAAC,cAAc,eAAe,IAAI,gBAAgB;AACxD,QAAM,OAAO,QAAQ,YAAY;AACjC,QAAM,iBAAiB,CAAC,UAAU;AAChC,WAAO,MAAM;AACX,sBAAgB,EAAE,MAAM,MAAM,CAAC;AAAA,IACjC;AAAA,EACF;AACA,aAAuB,oBAAAC;AAAA,IACrB,wBAAwB;AAAA,IACxB;AAAA,MACE,OAAO;AAAA,QACL,aAAa;AAAA,UAAe;AAAA;AAAA,QAAuB;AAAA,QACnD,UAAU;AAAA,UAAe;AAAA;AAAA,QAAiB;AAAA,MAC5C;AAAA,MACA,UAAU,WAAW,MAAM,OAAO;AAAA,IACpC;AAAA,EACF;AACF;AACA,IAAI,aAAa,CAAC,MAAM,YAAY;AAClC,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,iBAAuB,oBAAAA,KAAK,qBAAqB,EAAE,QAAQ,CAAC;AAAA,IAC9D,KAAK;AACH,iBAAuB,oBAAAA,KAAK,sBAAsB,EAAE,QAAQ,CAAC;AAAA,EACjE;AACF;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,GAAAN,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,EAAE;AAC5D,QAAM,QAAQ,CAAC,aAAa;AAC5B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAO,MAAM,iBAAiB,EAAE,UAAU;AAAA,QACxC,oBAAAC,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUR,GAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC;AAAA,QACpJ,oBAAAQ,KAAK,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAUR,GAAE,yBAAyB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC7K,aAAyB,oBAAAQ,KAAK,kBAAkB,EAAE,QAAQ,CAAC;AAAA,EAC7D,EAAE,CAAC;AACL;", "names": ["import_react", "import_jsx_runtime", "import_react", "t", "_a", "useState2", "useCallback2", "jsxs2", "jsx2", "jsx3", "jsxs3", "jsx4"]}