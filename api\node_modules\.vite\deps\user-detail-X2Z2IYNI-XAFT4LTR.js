import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import {
  useDeleteUser,
  useUser
} from "./chunk-7UV5UA6G.js";
import {
  productsQueryKeys
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/user-detail-X2Z2IYNI.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var UserDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { user } = useUser(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!user) {
    return null;
  }
  const name = [user.first_name, user.last_name].filter(Boolean).join(" ");
  const display = name || user.email;
  return (0, import_jsx_runtime.jsx)("span", { children: display });
};
var userDetailQuery = (id) => ({
  queryKey: productsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.user.retrieve(id)
});
var userLoader = async ({ params }) => {
  const id = params.id;
  const query = userDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var UserGeneralSection = ({ user }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteUser(user.id);
  const name = [user.first_name, user.last_name].filter(Boolean).join(" ");
  const handleDeleteUser = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("users.deleteUserWarning", {
        name: name ?? user.email
      }),
      verificationText: name ?? user.email,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("users.deleteUserSuccess", { name: user.email }));
        navigate("..");
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: user.email }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "edit",
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
                }
              ]
            },
            {
              actions: [
                {
                  label: t("actions.delete"),
                  onClick: handleDeleteUser,
                  icon: (0, import_jsx_runtime2.jsx)(Trash, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.name") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: name ?? "-" })
    ] })
  ] });
};
var UserDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const {
    user,
    isPending: isLoading,
    isError,
    error
  } = useUser(id, void 0, {
    initialData
  });
  const { getWidgets } = useExtension();
  if (isLoading || !user) {
    return (0, import_jsx_runtime3.jsx)(SingleColumnPageSkeleton, { sections: 1, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      data: user,
      showJSON: true,
      showMetadata: true,
      widgets: {
        after: getWidgets("user.details.after"),
        before: getWidgets("user.details.before")
      },
      children: (0, import_jsx_runtime3.jsx)(UserGeneralSection, { user })
    }
  );
};
export {
  UserDetailBreadcrumb as Breadcrumb,
  UserDetail as Component,
  userLoader as loader
};
//# sourceMappingURL=user-detail-X2Z2IYNI-XAFT4LTR.js.map
