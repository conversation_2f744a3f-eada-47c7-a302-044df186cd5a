import {
  ConditionalPriceForm,
  ShippingOptionPriceProvider,
  UpdateConditionalPriceSchema,
  buildShippingOptionPriceRules,
  useShippingOptionPriceColumns
} from "./chunk-XVMTVC52.js";
import {
  CONDITIONAL_PRICES_STACKED_MODAL_ID,
  ITEM_TOTAL_ATTRIBUTE,
  REGION_ID_ATTRIBUTE
} from "./chunk-S4XCFSZC.js";
import "./chunk-UDMOPZAP.js";
import {
  DataGrid
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  numberType,
  objectType,
  recordType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useShippingOption,
  useUpdateShippingOptions
} from "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  useRegions
} from "./chunk-AKXAI3UV.js";
import {
  usePricePreferences
} from "./chunk-662EXSHO.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  json,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-shipping-option-pricing-SKUSE5YE.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditShippingOptionPricingSchema = objectType({
  region_prices: recordType(
    stringType(),
    stringType().or(numberType()).optional()
  ),
  currency_prices: recordType(
    stringType(),
    stringType().or(numberType()).optional()
  ),
  conditional_region_prices: recordType(
    stringType(),
    arrayType(UpdateConditionalPriceSchema)
  ),
  conditional_currency_prices: recordType(
    stringType(),
    arrayType(UpdateConditionalPriceSchema)
  )
});
function EditShippingOptionsPricingForm({
  shippingOption
}) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { getIsOpen, setIsOpen } = useStackedModal();
  const [selectedPrice, setSelectedPrice] = (0, import_react.useState)(null);
  const onOpenConditionalPricesModal = (info) => {
    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, true);
    setSelectedPrice(info);
  };
  const onCloseConditionalPricesModal = () => {
    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, false);
    setSelectedPrice(null);
  };
  const form = useForm({
    defaultValues: getDefaultValues(shippingOption.prices),
    resolver: t(EditShippingOptionPricingSchema)
  });
  const { mutateAsync, isPending } = useUpdateShippingOptions(shippingOption.id);
  const {
    store,
    isLoading: isStoreLoading,
    isError: isStoreError,
    error: storeError
  } = useStore();
  const currencies = (0, import_react.useMemo)(
    () => {
      var _a;
      return ((_a = store == null ? void 0 : store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)) || [];
    },
    [store]
  );
  const {
    regions,
    isLoading: isRegionsLoading,
    isError: isRegionsError,
    error: regionsError
  } = useRegions({
    fields: "id,name,currency_code",
    limit: 999
  });
  const { price_preferences: pricePreferences } = usePricePreferences({});
  const { setCloseOnEscape } = useRouteModal();
  const columns = useShippingOptionPriceColumns({
    name: shippingOption.name,
    currencies,
    regions,
    pricePreferences
  });
  const data = (0, import_react.useMemo)(
    () => [[...currencies || [], ...regions || []]],
    [currencies, regions]
  );
  const handleSubmit = form.handleSubmit(async (data2) => {
    const currencyPrices = Object.entries(data2.currency_prices).map(([code, value]) => {
      if (!value || !currencies.some((c) => c.toLowerCase() === code.toLowerCase())) {
        return void 0;
      }
      const priceRecord = {
        currency_code: code,
        amount: castNumber(value)
      };
      const existingPrice = shippingOption.prices.find(
        (p) => p.currency_code === code && !p.price_rules.length
      );
      if (existingPrice) {
        priceRecord.id = existingPrice.id;
      }
      return priceRecord;
    }).filter((p) => !!p);
    const conditionalCurrencyPrices = Object.entries(
      data2.conditional_currency_prices
    ).flatMap(
      ([currency_code, value]) => value == null ? void 0 : value.map((rule) => ({
        id: rule.id,
        currency_code,
        amount: castNumber(rule.amount),
        rules: buildShippingOptionPriceRules(rule)
      }))
    );
    const regionPrices = Object.entries(data2.region_prices).map(([region_id, value]) => {
      if (!value || !(regions == null ? void 0 : regions.some((region) => region.id === region_id))) {
        return void 0;
      }
      const priceRecord = {
        region_id,
        amount: castNumber(value)
      };
      return priceRecord;
    }).filter((p) => !!p);
    const conditionalRegionPrices = Object.entries(
      data2.conditional_region_prices
    ).flatMap(
      ([region_id, value]) => value == null ? void 0 : value.map((rule) => ({
        id: rule.id,
        region_id,
        amount: castNumber(rule.amount),
        rules: buildShippingOptionPriceRules(rule)
      }))
    );
    const allPrices = [
      ...currencyPrices,
      ...conditionalCurrencyPrices,
      ...regionPrices,
      ...conditionalRegionPrices
    ];
    await mutateAsync(
      { prices: allPrices },
      {
        onSuccess: () => {
          toast.success(t2("general.success"));
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  const isLoading = isStoreLoading || isRegionsLoading || !currencies || !regions;
  if (isStoreError) {
    throw storeError;
  }
  if (isRegionsError) {
    throw regionsError;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { children: (0, import_jsx_runtime.jsx)(
          StackedFocusModal,
          {
            id: CONDITIONAL_PRICES_STACKED_MODAL_ID,
            onOpenChangeCallback: (open) => {
              if (!open) {
                setSelectedPrice(null);
              }
            },
            children: (0, import_jsx_runtime.jsxs)(
              ShippingOptionPriceProvider,
              {
                onOpenConditionalPricesModal,
                onCloseConditionalPricesModal,
                children: [
                  (0, import_jsx_runtime.jsx)("div", { className: "flex size-full flex-col divide-y overflow-hidden", children: (0, import_jsx_runtime.jsx)(
                    DataGrid,
                    {
                      isLoading,
                      data,
                      columns,
                      state: form,
                      onEditingChange: (editing) => setCloseOnEscape(!editing),
                      disableInteractions: getIsOpen(
                        CONDITIONAL_PRICES_STACKED_MODAL_ID
                      )
                    }
                  ) }),
                  selectedPrice && (0, import_jsx_runtime.jsx)(ConditionalPriceForm, { info: selectedPrice, variant: "update" })
                ]
              }
            )
          }
        ) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              size: "small",
              className: "whitespace-nowrap",
              isLoading: isPending,
              onClick: handleSubmit,
              type: "button",
              children: t2("actions.save")
            }
          )
        ] }) })
      ]
    }
  ) });
}
var findRuleValue = (rules, operator) => {
  var _a;
  const fallbackValue = ["eq", "gt", "lt"].includes(operator) ? void 0 : null;
  return ((_a = rules == null ? void 0 : rules.find(
    (r) => r.attribute === ITEM_TOTAL_ATTRIBUTE && r.operator === operator
  )) == null ? void 0 : _a.value) || fallbackValue;
};
var mapToConditionalPrice = (price) => {
  const rules = price.price_rules || [];
  return {
    id: price.id,
    amount: price.amount,
    gte: findRuleValue(rules, "gte"),
    lte: findRuleValue(rules, "lte"),
    gt: findRuleValue(rules, "gt"),
    lt: findRuleValue(rules, "lt"),
    eq: findRuleValue(rules, "eq")
  };
};
var getDefaultValues = (prices) => {
  const hasAttributes = (price, required, forbidden = []) => {
    var _a;
    const attributes = ((_a = price.price_rules) == null ? void 0 : _a.map((r) => r.attribute)) || [];
    return required.every((attr) => attributes.includes(attr)) && !forbidden.some((attr) => attributes.includes(attr));
  };
  const currency_prices = {};
  const conditional_currency_prices = {};
  const region_prices = {};
  const conditional_region_prices = {};
  prices.forEach((price) => {
    var _a, _b, _c;
    if (!((_a = price.price_rules) == null ? void 0 : _a.length)) {
      currency_prices[price.currency_code] = price.amount;
      return;
    }
    if (hasAttributes(price, [ITEM_TOTAL_ATTRIBUTE], [REGION_ID_ATTRIBUTE])) {
      const code = price.currency_code;
      if (!conditional_currency_prices[code]) {
        conditional_currency_prices[code] = [];
      }
      conditional_currency_prices[code].push(mapToConditionalPrice(price));
      return;
    }
    if (hasAttributes(price, [REGION_ID_ATTRIBUTE], [ITEM_TOTAL_ATTRIBUTE])) {
      const regionId = (_b = price.price_rules.find(
        (r) => r.attribute === REGION_ID_ATTRIBUTE
      )) == null ? void 0 : _b.value;
      region_prices[regionId] = price.amount;
      return;
    }
    if (hasAttributes(price, [REGION_ID_ATTRIBUTE, ITEM_TOTAL_ATTRIBUTE])) {
      const regionId = (_c = price.price_rules.find(
        (r) => r.attribute === REGION_ID_ATTRIBUTE
      )) == null ? void 0 : _c.value;
      if (!conditional_region_prices[regionId]) {
        conditional_region_prices[regionId] = [];
      }
      conditional_region_prices[regionId].push(mapToConditionalPrice(price));
    }
  });
  return {
    currency_prices,
    conditional_currency_prices,
    region_prices,
    conditional_region_prices
  };
};
function LocationServiceZoneShippingOptionPricing() {
  const { so_id, location_id } = useParams();
  if (!so_id) {
    throw json({
      message: "Shipping Option ID paramater is missing",
      status: 404
    });
  }
  const {
    shipping_option: shippingOption,
    isError,
    error
  } = useShippingOption(so_id, {
    fields: "*prices,*prices.price_rules"
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: shippingOption && (0, import_jsx_runtime2.jsx)(EditShippingOptionsPricingForm, { shippingOption }) });
}
export {
  LocationServiceZoneShippingOptionPricing as Component
};
//# sourceMappingURL=location-service-zone-shipping-option-pricing-SKUSE5YE-5CCSC6UT.js.map
