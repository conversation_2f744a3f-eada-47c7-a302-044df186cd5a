{"version": 3, "sources": ["../../@medusajs/dashboard/dist/campaign-budget-edit-57NSOYJ3.mjs"], "sourcesContent": ["import {\n  getCurrencySymbol\n} from \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCampaign,\n  useUpdateCampaign\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/campaign-budget-edit/campaign-budget-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/campaigns/campaign-budget-edit/components/edit-campaign-budget-form/edit-campaign-budget-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, CurrencyInput, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditCampaignSchema = zod.object({\n  limit: zod.number().min(0).optional().nullable()\n});\nvar EditCampaignBudgetForm = ({\n  campaign\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      limit: campaign?.budget?.limit || void 0\n    },\n    resolver: zodResolver(EditCampaignSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateCampaign(campaign.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        budget: {\n          limit: data.limit ? data.limit : null\n        }\n      },\n      {\n        onSuccess: ({ campaign: campaign2 }) => {\n          toast.success(\n            t(\"campaigns.edit.successToast\", {\n              name: campaign2.name\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsx(\n      Form.Field,\n      {\n        control: form.control,\n        name: \"limit\",\n        render: ({ field: { onChange, value, ...field } }) => {\n          return /* @__PURE__ */ jsxs(Form.Item, { className: \"basis-1/2\", children: [\n            /* @__PURE__ */ jsx(Form.Label, { children: t(\"campaigns.budget.fields.limit\") }),\n            /* @__PURE__ */ jsx(Form.Control, { children: campaign.budget?.type === \"spend\" ? /* @__PURE__ */ jsx(\n              CurrencyInput,\n              {\n                min: 0,\n                onValueChange: (value2) => onChange(value2 ? parseInt(value2) : null),\n                code: campaign.budget?.currency_code,\n                symbol: campaign.budget?.currency_code ? getCurrencySymbol(\n                  campaign.budget?.currency_code\n                ) : \"\",\n                ...field,\n                value: value || void 0\n              }\n            ) : /* @__PURE__ */ jsx(\n              Input,\n              {\n                min: 0,\n                ...field,\n                value,\n                onChange: (e) => {\n                  onChange(\n                    e.target.value === \"\" ? null : parseInt(e.target.value)\n                  );\n                }\n              },\n              \"usage\"\n            ) }),\n            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n          ] });\n        }\n      }\n    ) }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(\n        Button,\n        {\n          isLoading: isPending,\n          type: \"submit\",\n          variant: \"primary\",\n          size: \"small\",\n          children: t(\"actions.save\")\n        }\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/campaigns/campaign-budget-edit/campaign-budget-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CampaignBudgetEdit = () => {\n  const { t } = useTranslation2();\n  const { id } = useParams();\n  const { campaign, isLoading, isError, error } = useCampaign(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"campaigns.budget.edit.header\") }) }) }),\n    !isLoading && campaign && /* @__PURE__ */ jsx2(EditCampaignBudgetForm, { campaign })\n  ] });\n};\nexport {\n  CampaignBudgetEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,yBAA0B;AA+F1B,IAAAA,sBAA2C;AA9F3C,IAAI,qBAAyB,WAAO;AAAA,EAClC,OAAW,WAAO,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,SAAS;AACjD,CAAC;AACD,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AAxCN;AAyCE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,SAAO,0CAAU,WAAV,mBAAkB,UAAS;AAAA,IACpC;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,kBAAkB,SAAS,EAAE;AAChE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ;AAAA,UACN,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,UAAU,UAAU,MAAM;AACtC,gBAAM;AAAA,YACJA,GAAE,+BAA+B;AAAA,cAC/B,MAAM,UAAU;AAAA,YAClB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B;AAAA,MAC3I,KAAK;AAAA,MACL;AAAA,QACE,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AA9E9D,cAAAC,KAAA;AA+EU,qBAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,gBACzD,wBAAI,KAAK,OAAO,EAAE,UAAUD,GAAE,+BAA+B,EAAE,CAAC;AAAA,gBAChE,wBAAI,KAAK,SAAS,EAAE,YAAUC,MAAA,SAAS,WAAT,gBAAAA,IAAiB,UAAS,cAA0B;AAAA,cAChG;AAAA,cACA;AAAA,gBACE,KAAK;AAAA,gBACL,eAAe,CAAC,WAAW,SAAS,SAAS,SAAS,MAAM,IAAI,IAAI;AAAA,gBACpE,OAAM,cAAS,WAAT,mBAAiB;AAAA,gBACvB,UAAQ,cAAS,WAAT,mBAAiB,iBAAgB;AAAA,mBACvC,cAAS,WAAT,mBAAiB;AAAA,gBACnB,IAAI;AAAA,gBACJ,GAAG;AAAA,gBACH,OAAO,SAAS;AAAA,cAClB;AAAA,YACF,QAAoB;AAAA,cAClB;AAAA,cACA;AAAA,gBACE,KAAK;AAAA,gBACL,GAAG;AAAA,gBACH;AAAA,gBACA,UAAU,CAAC,MAAM;AACf;AAAA,oBACE,EAAE,OAAO,UAAU,KAAK,OAAO,SAAS,EAAE,OAAO,KAAK;AAAA,kBACxD;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3C,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUD,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAUA,GAAE,cAAc;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI,YAAY,EAAE;AAC9D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUH,GAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IAC3M,CAAC,aAAa,gBAA4B,oBAAAG,KAAK,wBAAwB,EAAE,SAAS,CAAC;AAAA,EACrF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "_a", "jsxs2", "jsx2"]}