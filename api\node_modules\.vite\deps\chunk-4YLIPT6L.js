import {
  isProductRow
} from "./chunk-DC7EOWEK.js";
import {
  DataGrid,
  createDataGridHelper,
  createDataGridPriceColumns
} from "./chunk-JGBVAJ3K.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  useRegions
} from "./chunk-AKXAI3UV.js";
import {
  usePricePreferences
} from "./chunk-662EXSHO.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-CF64SRBE.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var usePriceListCurrencyData = () => {
  const {
    store,
    isPending: isStorePending,
    isError: isStoreError,
    error: storeError
  } = useStore({
    fields: "+supported_currencies"
  });
  const currencies = store == null ? void 0 : store.supported_currencies;
  const {
    regions,
    isPending: isRegionsPending,
    isError: isRegionsError,
    error: regionsError
  } = useRegions({
    fields: "id,name,currency_code",
    limit: 999
  });
  const {
    price_preferences: pricePreferences,
    isPending: isPreferencesPending,
    isError: isPreferencesError,
    error: preferencesError
  } = usePricePreferences({});
  const isReady = !!currencies && !!regions && !!pricePreferences && !isStorePending && !isRegionsPending && !isPreferencesPending;
  if (isRegionsError) {
    throw regionsError;
  }
  if (isStoreError) {
    throw storeError;
  }
  if (isPreferencesError) {
    throw preferencesError;
  }
  if (!isReady) {
    return {
      regions: void 0,
      currencies: void 0,
      pricePreferences: void 0,
      isReady: false
    };
  }
  return { regions, currencies, pricePreferences, isReady };
};
var columnHelper = createDataGridHelper();
var usePriceListGridColumns = ({
  currencies = [],
  regions = [],
  pricePreferences = []
}) => {
  const { t } = useTranslation();
  const colDefs = (0, import_react.useMemo)(() => {
    return [
      columnHelper.column({
        id: t("fields.title"),
        header: t("fields.title"),
        cell: (context) => {
          const entity = context.row.original;
          if (isProductRow(entity)) {
            return (0, import_jsx_runtime.jsx)(DataGrid.ReadonlyCell, { context, children: (0, import_jsx_runtime.jsxs)("div", { className: "flex h-full w-full items-center gap-x-2 overflow-hidden", children: [
              (0, import_jsx_runtime.jsx)(Thumbnail, { src: entity.thumbnail, size: "small" }),
              (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: entity.title })
            ] }) });
          }
          return (0, import_jsx_runtime.jsx)(DataGrid.ReadonlyCell, { context, color: "normal", children: (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center gap-x-2 overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: entity.title }) }) });
        },
        disableHiding: true
      }),
      ...createDataGridPriceColumns({
        currencies: currencies.map((c) => c.currency_code),
        regions,
        pricePreferences,
        isReadyOnly: (context) => {
          const entity = context.row.original;
          return isProductRow(entity);
        },
        getFieldName: (context, value) => {
          var _a;
          const entity = context.row.original;
          if (isProductRow(entity)) {
            return null;
          }
          if ((_a = context.column.id) == null ? void 0 : _a.startsWith("currency_prices")) {
            return `products.${entity.product_id}.variants.${entity.id}.currency_prices.${value}.amount`;
          }
          return `products.${entity.product_id}.variants.${entity.id}.region_prices.${value}.amount`;
        },
        t
      })
    ];
  }, [t, currencies, regions, pricePreferences]);
  return colDefs;
};
var PriceListCustomerGroupSchema = z.object({
  id: z.string(),
  name: z.string()
});
var PriceListRulesSchema = z.object({
  customer_group_id: z.array(PriceListCustomerGroupSchema).nullish()
});
var PriceListCreateCurrencyPriceSchema = z.object({
  amount: z.string().or(z.number()).optional()
});
var PriceListCreateRegionPriceSchema = z.object({
  amount: z.string().or(z.number()).optional()
});
var PriceListCreateProductVariantSchema = z.object({
  currency_prices: z.record(PriceListCreateCurrencyPriceSchema.optional()),
  region_prices: z.record(PriceListCreateRegionPriceSchema.optional())
});
var PriceListCreateProductVariantsSchema = z.record(
  PriceListCreateProductVariantSchema
);
var PriceListCreateProductsSchema = z.record(
  z.object({
    variants: PriceListCreateProductVariantsSchema
  })
);
var PriceListUpdateCurrencyPriceSchema = z.object({
  amount: z.string().or(z.number()).optional(),
  id: z.string().nullish()
});
var PriceListUpdateRegionPriceSchema = z.object({
  amount: z.string().or(z.number()).optional(),
  id: z.string().nullish()
});
var PriceListUpdateProductVariantsSchema = z.record(
  z.object({
    currency_prices: z.record(PriceListUpdateCurrencyPriceSchema.optional()),
    region_prices: z.record(PriceListUpdateRegionPriceSchema.optional())
  })
);
var PriceListUpdateProductsSchema = z.record(
  z.object({
    variants: PriceListUpdateProductVariantsSchema
  })
);

export {
  usePriceListCurrencyData,
  usePriceListGridColumns,
  PriceListRulesSchema,
  PriceListCreateProductsSchema,
  PriceListUpdateProductsSchema
};
//# sourceMappingURL=chunk-4YLIPT6L.js.map
