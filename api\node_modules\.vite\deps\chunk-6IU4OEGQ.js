// node_modules/@medusajs/dashboard/dist/chunk-R2O6QX4D.mjs
function isReturnOption(shippingOption) {
  var _a;
  return !!((_a = shippingOption.rules) == null ? void 0 : _a.find(
    (r) => r.attribute === "is_return" && r.value === "true" && r.operator === "eq"
  ));
}
function isOptionEnabledInStore(shippingOption) {
  var _a;
  return !!((_a = shippingOption.rules) == null ? void 0 : _a.find(
    (r) => r.attribute === "enabled_in_store" && r.value === "true" && r.operator === "eq"
  ));
}

export {
  isReturnOption,
  isOptionEnabledInStore
};
//# sourceMappingURL=chunk-6IU4OEGQ.js.map
