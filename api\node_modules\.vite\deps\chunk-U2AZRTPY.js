import {
  ListSummary
} from "./chunk-WE5QYYBJ.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  countries
} from "./chunk-XGFC5LFP.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-DGRTPKQC.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var CountriesCell = ({ countries: countries2 }) => {
  if (!countries2 || countries2.length === 0) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  const list = countries2.map(
    (country) => {
      var _a;
      return (_a = countries.find((c) => c.iso_2 === country.iso_2)) == null ? void 0 : _a.display_name;
    }
  ).filter(Boolean);
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)(ListSummary, { list }) });
};
var CountriesHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center", children: (0, import_jsx_runtime.jsx)("span", { children: t("fields.countries") }) });
};
var PaymentProvidersCell = ({
  paymentProviders
}) => {
  if (!paymentProviders || paymentProviders.length === 0) {
    return (0, import_jsx_runtime2.jsx)(PlaceholderCell, {});
  }
  const displayValues = paymentProviders.map((p) => formatProvider(p.id));
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)(ListSummary, { list: displayValues }) });
};
var PaymentProvidersHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: t("fields.paymentProviders") }) });
};
var RegionCell = ({ name }) => {
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: name }) });
};
var RegionHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: t("fields.name") }) });
};
var columnHelper = createColumnHelper();
var useRegionTableColumns = () => {
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: () => (0, import_jsx_runtime4.jsx)(RegionHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime4.jsx)(RegionCell, { name: getValue() })
      }),
      columnHelper.accessor("countries", {
        header: () => (0, import_jsx_runtime4.jsx)(CountriesHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime4.jsx)(CountriesCell, { countries: getValue() })
      }),
      columnHelper.accessor("payment_providers", {
        header: () => (0, import_jsx_runtime4.jsx)(PaymentProvidersHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime4.jsx)(PaymentProvidersCell, { paymentProviders: getValue() })
      })
    ],
    []
  );
};

export {
  useRegionTableColumns
};
//# sourceMappingURL=chunk-U2AZRTPY.js.map
