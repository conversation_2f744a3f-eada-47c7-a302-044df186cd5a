import {
  useCountries,
  useCountryTableColumns,
  useCountryTableQuery
} from "./chunk-2VXAS6UY.js";
import {
  GEO_ZONE_STACKED_MODAL_ID
} from "./chunk-S4XCFSZC.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import {
  ChipGroup
} from "./chunk-ETYCFWJC.js";
import {
  StackedFocusModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import {
  countries
} from "./chunk-XGFC5LFP.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useFieldArray
} from "./chunk-DPO7J5IQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Button,
  Checkbox,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-77BAMLUK.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var GeoZoneSchema = z.object({
  countries: z.array(
    z.object({ iso_2: z.string().min(2), display_name: z.string() })
  )
});
var GeoZoneFormImpl = ({
  form
}) => {
  const castForm = form;
  const { t } = useTranslation();
  const { fields, remove, replace } = useFieldArray({
    control: castForm.control,
    name: "countries",
    keyName: "iso_2"
  });
  const handleClearAll = () => {
    replace([]);
  };
  validateForm(form);
  return (0, import_jsx_runtime.jsx)(
    Form.Field,
    {
      control: form.control,
      name: "countries",
      render: () => {
        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between gap-x-4", children: [
            (0, import_jsx_runtime.jsxs)("div", { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t("stockLocations.serviceZones.manageAreas.label") }),
              (0, import_jsx_runtime.jsx)(Form.Hint, { children: t("stockLocations.serviceZones.manageAreas.hint") })
            ] }),
            (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t("stockLocations.serviceZones.manageAreas.action") }) })
          ] }),
          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {}),
          (0, import_jsx_runtime.jsx)(Form.Control, { className: "mt-0", children: fields.length > 0 && (0, import_jsx_runtime.jsx)(
            ChipGroup,
            {
              onClearAll: handleClearAll,
              onRemove: remove,
              className: "py-4",
              children: fields.map((field, index) => (0, import_jsx_runtime.jsx)(ChipGroup.Chip, { index, children: field.display_name }, field.iso_2))
            }
          ) })
        ] });
      }
    }
  );
};
var PREFIX = "ac";
var PAGE_SIZE = 50;
var AreaStackedModal = ({
  form
}) => {
  const castForm = form;
  const { t } = useTranslation();
  const { getValues, setValue } = castForm;
  const { setIsOpen, getIsOpen } = useStackedModal();
  const open = getIsOpen(GEO_ZONE_STACKED_MODAL_ID);
  const [selection, setSelection] = (0, import_react.useState)({});
  const [state, setState] = (0, import_react.useState)(
    []
  );
  const { searchParams, raw } = useCountryTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { countries: countries2, count } = useCountries({
    countries: countries.map((c) => ({
      display_name: c.display_name,
      name: c.name,
      iso_2: c.iso_2,
      iso_3: c.iso_3,
      num_code: c.num_code
    })),
    ...searchParams
  });
  (0, import_react.useEffect)(() => {
    if (!open) {
      return;
    }
    const countries3 = getValues("countries");
    if (countries3) {
      setState(
        countries3.map((country) => ({
          iso_2: country.iso_2,
          display_name: country.display_name
        }))
      );
      setSelection(
        countries3.reduce(
          (acc, country) => ({
            ...acc,
            [country.iso_2]: true
          }),
          {}
        )
      );
    }
  }, [open, getValues]);
  const updater = (fn) => {
    const value = typeof fn === "function" ? fn(selection) : fn;
    const ids = Object.keys(value);
    const addedIdsSet = new Set(ids.filter((id) => value[id] && !selection[id]));
    const addedCountries = [];
    if (addedIdsSet.size > 0) {
      const countriesToAdd = (countries2 == null ? void 0 : countries2.filter((country) => addedIdsSet.has(country.iso_2))) ?? [];
      for (const country of countriesToAdd) {
        addedCountries.push({
          iso_2: country.iso_2,
          display_name: country.display_name
        });
      }
    }
    setState((prev) => {
      const filteredPrev = prev.filter((country) => value[country.iso_2]);
      return Array.from(/* @__PURE__ */ new Set([...filteredPrev, ...addedCountries]));
    });
    setSelection(value);
  };
  const handleAdd = () => {
    setValue("countries", state, {
      shouldDirty: true,
      shouldTouch: true
    });
    setIsOpen(GEO_ZONE_STACKED_MODAL_ID, false);
  };
  const columns = useColumns();
  const { table } = useDataTable({
    data: countries2 || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.iso_2,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: selection,
      updater
    },
    prefix: PREFIX
  });
  validateForm(form);
  return (0, import_jsx_runtime.jsxs)(StackedFocusModal.Content, { className: "flex flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsxs)(StackedFocusModal.Header, { children: [
      (0, import_jsx_runtime.jsx)(StackedFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)("span", { className: "sr-only", children: t("stockLocations.serviceZones.manageAreas.label") }) }),
      (0, import_jsx_runtime.jsx)(StackedFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)("span", { className: "sr-only", children: t("stockLocations.serviceZones.manageAreas.hint") }) })
    ] }),
    (0, import_jsx_runtime.jsx)(StackedFocusModal.Body, { className: "flex-1 overflow-hidden", children: (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count,
        search: true,
        pagination: true,
        layout: "fill",
        orderBy: [
          { key: "display_name", label: t("fields.name") },
          { key: "iso_2", label: t("fields.code") }
        ],
        queryObject: raw,
        prefix: PREFIX
      }
    ) }),
    (0, import_jsx_runtime.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(StackedFocusModal.Close, { type: "button", asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "button", onClick: handleAdd, children: t("actions.save") })
    ] }) })
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCountryTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreselected = !row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected() || isPreselected,
              disabled: isPreselected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
function validateForm(form) {
  if (form.getValues("countries") === void 0) {
    throw new Error(
      "The form does not have a field named 'countries'. This field is required to use the GeoZoneForm component."
    );
  }
}
var GeoZoneForm = Object.assign(GeoZoneFormImpl, {
  AreaDrawer: AreaStackedModal
});

export {
  GeoZoneForm
};
//# sourceMappingURL=chunk-2XJFZ7O4.js.map
