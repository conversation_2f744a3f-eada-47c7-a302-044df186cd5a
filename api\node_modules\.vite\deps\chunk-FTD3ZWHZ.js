import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-C5P5PL3E.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ExtensionContext = (0, import_react2.createContext)(
  null
);
var useExtension = () => {
  const context = (0, import_react.useContext)(ExtensionContext);
  if (!context) {
    throw new Error("useExtension must be used within a ExtensionProvider");
  }
  return context;
};
var ExtensionProvider = ({
  api,
  children
}) => {
  return (0, import_jsx_runtime.jsx)(ExtensionContext.Provider, { value: api, children });
};

export {
  useExtension,
  ExtensionProvider
};
//# sourceMappingURL=chunk-FTD3ZWHZ.js.map
