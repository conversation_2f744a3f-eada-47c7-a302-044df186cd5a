{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-EMIHDNB7.mjs"], "sourcesContent": ["// src/components/common/empty-table-content/empty-table-content.tsx\nimport { ExclamationCircle, MagnifyingGlass, PlusMini } from \"@medusajs/icons\";\nimport { Button, Text, clx } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar NoResults = ({ title, message, className }) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\n        \"flex h-[400px] w-full items-center justify-center\",\n        className\n      ),\n      children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col items-center gap-y-2\", children: [\n        /* @__PURE__ */ jsx(MagnifyingGlass, {}),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: title ?? t(\"general.noResultsTitle\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: message ?? t(\"general.noResultsMessage\") })\n      ] })\n    }\n  );\n};\nvar DefaultButton = ({ action }) => action && /* @__PURE__ */ jsx(Link, { to: action.to, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: action.label }) });\nvar TransparentIconLeftButton = ({ action }) => action && /* @__PURE__ */ jsx(Link, { to: action.to, children: /* @__PURE__ */ jsxs(Button, { variant: \"transparent\", className: \"text-ui-fg-interactive\", children: [\n  /* @__PURE__ */ jsx(PlusMini, {}),\n  \" \",\n  action.label\n] }) });\nvar NoRecords = ({\n  title,\n  message,\n  action,\n  className,\n  buttonVariant = \"default\",\n  icon = /* @__PURE__ */ jsx(ExclamationCircle, { className: \"text-ui-fg-subtle\" })\n}) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"flex h-[150px] w-full flex-col items-center justify-center gap-y-4\",\n        className\n      ),\n      children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col items-center gap-y-3\", children: [\n          icon,\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col items-center gap-y-1\", children: [\n            /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: title ?? t(\"general.noRecordsTitle\") }),\n            /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-muted\", children: message ?? t(\"general.noRecordsMessage\") })\n          ] })\n        ] }),\n        buttonVariant === \"default\" && /* @__PURE__ */ jsx(DefaultButton, { action }),\n        buttonVariant === \"transparentIconLeft\" && /* @__PURE__ */ jsx(TransparentIconLeftButton, { action })\n      ]\n    }\n  );\n};\n\nexport {\n  NoResults,\n  NoRecords\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKA,yBAA0B;AAC1B,IAAI,YAAY,CAAC,EAAE,OAAO,SAAS,UAAU,MAAM;AACjD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAA0B,yBAAK,OAAO,EAAE,WAAW,sCAAsC,UAAU;AAAA,YACjF,wBAAI,iBAAiB,CAAC,CAAC;AAAA,YACvB,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,SAAS,EAAE,wBAAwB,EAAE,CAAC;AAAA,YAC/G,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAU,WAAW,EAAE,0BAA0B,EAAE,CAAC;AAAA,MACjI,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,OAAO,IAAI,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC;AACjM,IAAI,4BAA4B,CAAC,EAAE,OAAO,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,OAAO,IAAI,cAA0B,yBAAK,QAAQ,EAAE,SAAS,eAAe,WAAW,0BAA0B,UAAU;AAAA,MACnM,wBAAI,UAAU,CAAC,CAAC;AAAA,EAChC;AAAA,EACA,OAAO;AACT,EAAE,CAAC,EAAE,CAAC;AACN,IAAI,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,WAAuB,wBAAI,mBAAmB,EAAE,WAAW,oBAAoB,CAAC;AAClF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,YACQ,yBAAK,OAAO,EAAE,WAAW,sCAAsC,UAAU;AAAA,UACvF;AAAA,cACgB,yBAAK,OAAO,EAAE,WAAW,sCAAsC,UAAU;AAAA,gBACvE,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,SAAS,EAAE,wBAAwB,EAAE,CAAC;AAAA,gBAC/G,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,oBAAoB,UAAU,WAAW,EAAE,0BAA0B,EAAE,CAAC;AAAA,UAChI,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,QACH,kBAAkB,iBAA6B,wBAAI,eAAe,EAAE,OAAO,CAAC;AAAA,QAC5E,kBAAkB,6BAAyC,wBAAI,2BAA2B,EAAE,OAAO,CAAC;AAAA,MACtG;AAAA,IACF;AAAA,EACF;AACF;", "names": []}