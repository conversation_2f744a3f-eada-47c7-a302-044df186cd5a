import {
  Text,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-LFLGEXIG.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var SectionRow = ({ title, value, actions }) => {
  const isValueString = typeof value === "string" || !value;
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        `text-ui-fg-subtle grid w-full grid-cols-2 items-center gap-4 px-6 py-4`,
        {
          "grid-cols-[1fr_1fr_28px]": !!actions
        }
      ),
      children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: title }),
        isValueString ? (0, import_jsx_runtime.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            className: "whitespace-pre-line text-pretty",
            children: value ?? "-"
          }
        ) : (0, import_jsx_runtime.jsx)("div", { className: "flex flex-wrap gap-1", children: value }),
        actions && (0, import_jsx_runtime.jsx)("div", { children: actions })
      ]
    }
  );
};

export {
  SectionRow
};
//# sourceMappingURL=chunk-KTCGZHOS.js.map
