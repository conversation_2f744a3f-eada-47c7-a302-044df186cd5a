{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-shipping-profile-QA3Q4N5Q.mjs"], "sourcesContent": ["import {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  PRODUCT_DETAIL_FIELDS\n} from \"./chunk-GSDEAUND.mjs\";\nimport \"./chunk-L4KSRFES.mjs\";\nimport \"./chunk-BC3M3N6P.mjs\";\nimport \"./chunk-ONB3JEHR.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-D3YQN7HV.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-XKXNQ2KV.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProduct,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-shipping-profile/product-shipping-profile.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-shipping-profile/components/product-organization-form/product-shipping-profile-form.tsx\nimport { Button, toast } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useEffect } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ProductShippingProfileSchema = zod.object({\n  shipping_profile_id: zod.string()\n});\nvar ProductShippingProfileForm = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const shippingProfiles = useComboboxData({\n    queryKey: [\"shipping_profiles\"],\n    queryFn: (params) => sdk.admin.shippingProfile.list(params),\n    getOptions: (data) => data.shipping_profiles.map((shippingProfile) => ({\n      label: shippingProfile.name,\n      value: shippingProfile.id\n    }))\n  });\n  const form = useForm({\n    defaultValues: {\n      shipping_profile_id: product.shipping_profile?.id ?? \"\"\n    },\n    resolver: zodResolver(ProductShippingProfileSchema)\n  });\n  const selectedShippingProfile = form.watch(\"shipping_profile_id\");\n  const { mutateAsync, isPending } = useUpdateProduct(product.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        shipping_profile_id: data.shipping_profile_id === \"\" ? null : data.shipping_profile_id\n      },\n      {\n        onSuccess: ({ product: product2 }) => {\n          toast.success(\n            t(\"products.shippingProfile.edit.toasts.success\", {\n              title: product2.title\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  useEffect(() => {\n    if (typeof selectedShippingProfile === \"undefined\") {\n      form.setValue(\"shipping_profile_id\", \"\");\n    }\n  }, [selectedShippingProfile]);\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full flex-col gap-y-4\", children: /* @__PURE__ */ jsx(\n      Form.Field,\n      {\n        control: form.control,\n        name: \"shipping_profile_id\",\n        render: ({ field }) => {\n          return /* @__PURE__ */ jsxs(Form.Item, { children: [\n            /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.fields.shipping_profile.label\") }),\n            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n              Combobox,\n              {\n                ...field,\n                allowClear: true,\n                options: shippingProfiles.options,\n                searchValue: shippingProfiles.searchValue,\n                onSearchValueChange: shippingProfiles.onSearchValueChange,\n                fetchNextPage: shippingProfiles.fetchNextPage\n              }\n            ) }),\n            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n          ] });\n        }\n      }\n    ) }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/products/product-shipping-profile/product-shipping-profile.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductShippingProfile = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product, isLoading, isError, error } = useProduct(id, {\n    fields: PRODUCT_DETAIL_FIELDS\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"products.shippingProfile.edit.header\") }) }) }),\n    !isLoading && product && /* @__PURE__ */ jsx2(ProductShippingProfileForm, { product })\n  ] });\n};\nexport {\n  ProductShippingProfile as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,mBAA0B;AAC1B,yBAA0B;AAmF1B,IAAAA,sBAA2C;AAlF3C,IAAI,+BAAmC,WAAO;AAAA,EAC5C,qBAAyB,WAAO;AAClC,CAAC;AACD,IAAI,6BAA6B,CAAC;AAAA,EAChC;AACF,MAAM;AA1FN;AA2FE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,mBAAmB,gBAAgB;AAAA,IACvC,UAAU,CAAC,mBAAmB;AAAA,IAC9B,SAAS,CAAC,WAAW,IAAI,MAAM,gBAAgB,KAAK,MAAM;AAAA,IAC1D,YAAY,CAAC,SAAS,KAAK,kBAAkB,IAAI,CAAC,qBAAqB;AAAA,MACrE,OAAO,gBAAgB;AAAA,MACvB,OAAO,gBAAgB;AAAA,IACzB,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,uBAAqB,aAAQ,qBAAR,mBAA0B,OAAM;AAAA,IACvD;AAAA,IACA,UAAU,EAAY,4BAA4B;AAAA,EACpD,CAAC;AACD,QAAM,0BAA0B,KAAK,MAAM,qBAAqB;AAChE,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,QAAQ,EAAE;AAC9D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,qBAAqB,KAAK,wBAAwB,KAAK,OAAO,KAAK;AAAA,MACrE;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,SAAS,SAAS,MAAM;AACpC,gBAAM;AAAA,YACJA,GAAE,gDAAgD;AAAA,cAChD,OAAO,SAAS;AAAA,YAClB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,OAAO,4BAA4B,aAAa;AAClD,WAAK,SAAS,uBAAuB,EAAE;AAAA,IACzC;AAAA,EACF,GAAG,CAAC,uBAAuB,CAAC;AAC5B,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,gCAAgC,cAA0B;AAAA,MAClJ,KAAK;AAAA,MACL;AAAA,QACE,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,wCAAwC,EAAE,CAAC;AAAA,gBACzE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,cAC5D;AAAA,cACA;AAAA,gBACE,GAAG;AAAA,gBACH,YAAY;AAAA,gBACZ,SAAS,iBAAiB;AAAA,gBAC1B,aAAa,iBAAiB;AAAA,gBAC9B,qBAAqB,iBAAiB;AAAA,gBACtC,eAAe,iBAAiB;AAAA,cAClC;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3C,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,IAAI;AAAA,IAC5D,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,sCAAsC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IACnN,CAAC,aAAa,eAA2B,oBAAAE,KAAK,4BAA4B,EAAE,QAAQ,CAAC;AAAA,EACvF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}