{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-MSDRGCRR.mjs"], "sourcesContent": ["import {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\n\n// src/components/table/table-cells/common/text-cell/text-cell.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TextCell = ({ text, align = \"left\", maxWidth = 220 }) => {\n  if (!text) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  const stringLength = text.toString().length;\n  return /* @__PURE__ */ jsx(ConditionalTooltip, { content: text, showTooltip: stringLength > 20, children: /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\"flex h-full w-full items-center gap-x-3 overflow-hidden\", {\n        \"justify-start text-start\": align === \"left\",\n        \"justify-center text-center\": align === \"center\",\n        \"justify-end text-end\": align === \"right\"\n      }),\n      style: {\n        maxWidth\n      },\n      children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: text })\n    }\n  ) });\n};\nvar TextHeader = ({ text, align = \"left\" }) => {\n  return /* @__PURE__ */ jsx(\"div\", { className: clx(\"flex h-full w-full items-center\", {\n    \"justify-start text-start\": align === \"left\",\n    \"justify-center text-center\": align === \"center\",\n    \"justify-end text-end\": align === \"right\"\n  }), children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: text }) });\n};\n\nexport {\n  TextCell,\n  TextHeader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AASA,yBAAoB;AACpB,IAAI,WAAW,CAAC,EAAE,MAAM,QAAQ,QAAQ,WAAW,IAAI,MAAM;AAC3D,MAAI,CAAC,MAAM;AACT,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,QAAM,eAAe,KAAK,SAAS,EAAE;AACrC,aAAuB,wBAAI,oBAAoB,EAAE,SAAS,MAAM,aAAa,eAAe,IAAI,cAA0B;AAAA,IACxH;AAAA,IACA;AAAA,MACE,WAAW,IAAI,2DAA2D;AAAA,QACxE,4BAA4B,UAAU;AAAA,QACtC,8BAA8B,UAAU;AAAA,QACxC,wBAAwB,UAAU;AAAA,MACpC,CAAC;AAAA,MACD,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC;AAAA,IACjF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,aAAa,CAAC,EAAE,MAAM,QAAQ,OAAO,MAAM;AAC7C,aAAuB,wBAAI,OAAO,EAAE,WAAW,IAAI,mCAAmC;AAAA,IACpF,4BAA4B,UAAU;AAAA,IACtC,8BAA8B,UAAU;AAAA,IACxC,wBAAwB,UAAU;AAAA,EACpC,CAAC,GAAG,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AACxF;", "names": []}