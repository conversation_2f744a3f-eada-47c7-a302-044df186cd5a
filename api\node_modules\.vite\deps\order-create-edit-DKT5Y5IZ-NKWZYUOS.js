import {
  useAddOrderEditItems,
  useCancelOrderEdit,
  useCreateOrderEdit,
  useRemoveOrderEditItem,
  useRequestOrderEdit,
  useUpdateOrderEditAddedItem,
  useUpdateOrderEditOriginalItem
} from "./chunk-TZZI45BU.js";
import {
  MoneyAmountCell
} from "./chunk-NO4BKKGC.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import {
  ProductCell,
  ProductHeader
} from "./chunk-EUULPFXI.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useVariants
} from "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import {
  useOrder,
  useOrderPreview
} from "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowUturnLeft,
  Badge,
  Button,
  Checkbox,
  DocumentSeries,
  Heading,
  Input,
  Switch,
  Text,
  XCircle,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-edit-DKT5Y5IZ.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useOrderEditItemsTableColumns = (currencyCode) => {
  const { t: t2 } = useTranslation();
  return (0, import_react4.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isSelectable = row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              disabled: !isSelectable,
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      columnHelper.display({
        id: "product",
        header: () => (0, import_jsx_runtime.jsx)(ProductHeader, {}),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(ProductCell, { product: row.original.product });
        }
      }),
      columnHelper.accessor("sku", {
        header: t2("fields.sku"),
        cell: ({ getValue }) => {
          return getValue() || "-";
        }
      }),
      columnHelper.accessor("title", {
        header: t2("fields.title")
      })
    ],
    [t2, currencyCode]
  );
};
var useOrderEditItemTableFilters = () => {
  const { t: t2 } = useTranslation();
  const filters = [
    {
      key: "created_at",
      label: t2("fields.createdAt"),
      type: "date"
    },
    {
      key: "updated_at",
      label: t2("fields.updatedAt"),
      type: "date"
    }
  ];
  return filters;
};
var useOrderEditItemTableQuery = ({
  pageSize = 50,
  prefix
}) => {
  const raw = useQueryParams(
    ["q", "offset", "order", "created_at", "updated_at"],
    prefix
  );
  const { offset, created_at, updated_at, ...rest } = raw;
  const searchParams = {
    ...rest,
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0
  };
  return { searchParams, raw };
};
var PAGE_SIZE = 50;
var PREFIX = "rit";
var AddOrderEditItemsTable = ({
  onSelectionChange,
  currencyCode
}) => {
  const { t: t2 } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react3.useState)({});
  const updater = (fn) => {
    const newState = typeof fn === "function" ? fn(rowSelection) : fn;
    setRowSelection(newState);
    onSelectionChange(Object.keys(newState));
  };
  const { searchParams, raw } = useOrderEditItemTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { variants = [], count } = useVariants({
    ...searchParams,
    fields: "*inventory_items.inventory.location_levels,+inventory_quantity"
  });
  const columns = useOrderEditItemsTableColumns(currencyCode);
  const filters = useOrderEditItemTableFilters();
  const { table } = useDataTable({
    data: variants,
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    enableRowSelection: (_row) => {
      return true;
    },
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col overflow-hidden", children: (0, import_jsx_runtime2.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      filters,
      pagination: true,
      layout: "fill",
      search: true,
      orderBy: [
        { key: "product_id", label: t2("fields.product") },
        { key: "title", label: t2("fields.title") },
        { key: "sku", label: t2("fields.sku") }
      ],
      prefix: PREFIX,
      queryObject: raw
    }
  ) });
};
function OrderEditItem({ item, currencyCode, orderId }) {
  const { t: t2 } = useTranslation();
  const { mutateAsync: addItems } = useAddOrderEditItems(orderId);
  const { mutateAsync: updateAddedItem } = useUpdateOrderEditAddedItem(orderId);
  const { mutateAsync: updateOriginalItem } = useUpdateOrderEditOriginalItem(orderId);
  const { mutateAsync: undoAction } = useRemoveOrderEditItem(orderId);
  const isAddedItem = (0, import_react5.useMemo)(
    () => {
      var _a;
      return !!((_a = item.actions) == null ? void 0 : _a.find((a) => a.action === "ITEM_ADD"));
    },
    [item]
  );
  const isItemUpdated = (0, import_react5.useMemo)(
    () => {
      var _a;
      return !!((_a = item.actions) == null ? void 0 : _a.find((a) => a.action === "ITEM_UPDATE"));
    },
    [item]
  );
  const isItemRemoved = (0, import_react5.useMemo)(() => {
    var _a;
    const updateAction = (_a = item.actions) == null ? void 0 : _a.find((a) => a.action === "ITEM_UPDATE");
    return !!updateAction && item.quantity === item.detail.fulfilled_quantity;
  }, [item]);
  const onUpdate = async (quantity) => {
    var _a;
    if (quantity <= item.detail.fulfilled_quantity) {
      toast.error(t2("orders.edits.validation.quantityLowerThanFulfillment"));
      return;
    }
    if (quantity === item.quantity) {
      return;
    }
    const addItemAction = (_a = item.actions) == null ? void 0 : _a.find((a) => a.action === "ITEM_ADD");
    try {
      if (addItemAction) {
        await updateAddedItem({ quantity, actionId: addItemAction.id });
      } else {
        await updateOriginalItem({ quantity, itemId: item.id });
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  const onRemove = async () => {
    var _a;
    const addItemAction = (_a = item.actions) == null ? void 0 : _a.find((a) => a.action === "ITEM_ADD");
    try {
      if (addItemAction) {
        await undoAction(addItemAction.id);
      } else {
        await updateOriginalItem({
          quantity: item.detail.fulfilled_quantity,
          //
          itemId: item.id
        });
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  const onRemoveUndo = async () => {
    var _a;
    const updateItemAction = (_a = item.actions) == null ? void 0 : _a.find(
      (a) => a.action === "ITEM_UPDATE"
    );
    try {
      if (updateItemAction) {
        await undoAction(updateItemAction.id);
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  const onDuplicate = async () => {
    try {
      await addItems({
        items: [
          {
            variant_id: item.variant_id,
            quantity: item.quantity
          }
        ]
      });
    } catch (e) {
      toast.error(e.message);
    }
  };
  return (0, import_jsx_runtime3.jsx)(
    "div",
    {
      className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl ",
      children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col items-center gap-x-2 gap-y-2 p-3 text-sm md:flex-row", children: [
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 items-center justify-between", children: [
          (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-row items-center gap-x-3", children: [
            (0, import_jsx_runtime3.jsx)(Thumbnail, { src: item.thumbnail }),
            (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col", children: [
              (0, import_jsx_runtime3.jsxs)("div", { children: [
                (0, import_jsx_runtime3.jsxs)(Text, { className: "txt-small", as: "span", weight: "plus", children: [
                  item.title,
                  " "
                ] }),
                item.variant_sku && (0, import_jsx_runtime3.jsxs)("span", { children: [
                  "(",
                  item.variant_sku,
                  ")"
                ] })
              ] }),
              (0, import_jsx_runtime3.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: item.product_title })
            ] })
          ] }),
          isAddedItem && (0, import_jsx_runtime3.jsx)(Badge, { size: "2xsmall", rounded: "full", color: "blue", className: "mr-1", children: t2("general.new") }),
          isItemRemoved ? (0, import_jsx_runtime3.jsx)(Badge, { size: "2xsmall", rounded: "full", color: "red", className: "mr-1", children: t2("general.removed") }) : isItemUpdated && (0, import_jsx_runtime3.jsx)(
            Badge,
            {
              size: "2xsmall",
              rounded: "full",
              color: "orange",
              className: "mr-1",
              children: t2("general.modified")
            }
          )
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 justify-between", children: [
          (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-grow items-center gap-2", children: [
            (0, import_jsx_runtime3.jsx)(
              Input,
              {
                className: "bg-ui-bg-base txt-small w-[67px] rounded-lg [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                type: "number",
                disabled: item.detail.fulfilled_quantity === item.quantity,
                min: item.detail.fulfilled_quantity,
                defaultValue: item.quantity,
                onBlur: (e) => {
                  const val = e.target.value;
                  const payload = val === "" ? null : Number(val);
                  if (payload) {
                    onUpdate(payload);
                  }
                }
              }
            ),
            (0, import_jsx_runtime3.jsx)(Text, { className: "txt-small text-ui-fg-subtle", children: t2("fields.qty") })
          ] }),
          (0, import_jsx_runtime3.jsx)("div", { className: "text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0", children: (0, import_jsx_runtime3.jsx)(MoneyAmountCell, { currencyCode, amount: item.total }) }),
          (0, import_jsx_runtime3.jsx)(
            ActionMenu,
            {
              groups: [
                {
                  actions: [
                    {
                      label: t2("actions.duplicate"),
                      onClick: onDuplicate,
                      icon: (0, import_jsx_runtime3.jsx)(DocumentSeries, {})
                    }
                  ]
                },
                {
                  actions: [
                    !isItemRemoved ? {
                      label: t2("actions.remove"),
                      onClick: onRemove,
                      icon: (0, import_jsx_runtime3.jsx)(XCircle, {}),
                      disabled: item.detail.fulfilled_quantity === item.quantity
                    } : {
                      label: t2("actions.undo"),
                      onClick: onRemoveUndo,
                      icon: (0, import_jsx_runtime3.jsx)(ArrowUturnLeft, {})
                    }
                  ].filter(Boolean)
                }
              ]
            }
          )
        ] })
      ] })
    },
    item.quantity
  );
}
var addedVariants = [];
var OrderEditItemsSection = ({
  order,
  preview
}) => {
  const { t: t2 } = useTranslation();
  const { setIsOpen } = useStackedModal();
  const [filterTerm, setFilterTerm] = (0, import_react2.useState)("");
  const { mutateAsync: addItems, isPending } = useAddOrderEditItems(order.id);
  const onItemsSelected = async () => {
    await addItems(
      {
        items: addedVariants.map((i) => ({
          variant_id: i,
          quantity: 1
        }))
      },
      {
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
    setIsOpen("inbound-items", false);
  };
  const filteredItems = (0, import_react2.useMemo)(() => {
    return preview.items.filter(
      (i) => i.title.toLowerCase().includes(filterTerm) || i.product_title.toLowerCase().includes(filterTerm)
    );
  }, [preview, filterTerm]);
  return (0, import_jsx_runtime4.jsxs)("div", { children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "mb-3 mt-8 flex items-center justify-between", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t2("fields.items") }),
      (0, import_jsx_runtime4.jsxs)("div", { className: "flex gap-2", children: [
        (0, import_jsx_runtime4.jsx)(
          Input,
          {
            value: filterTerm,
            onChange: (e) => setFilterTerm(e.target.value),
            placeholder: t2("fields.search"),
            autoComplete: "off",
            type: "search"
          }
        ),
        (0, import_jsx_runtime4.jsxs)(StackedFocusModal, { id: "inbound-items", children: [
          (0, import_jsx_runtime4.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime4.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.addItems") }) }),
          (0, import_jsx_runtime4.jsxs)(StackedFocusModal.Content, { children: [
            (0, import_jsx_runtime4.jsx)(StackedFocusModal.Header, {}),
            (0, import_jsx_runtime4.jsx)(
              AddOrderEditItemsTable,
              {
                currencyCode: order.currency_code,
                onSelectionChange: (finalSelection) => {
                  addedVariants = finalSelection;
                }
              }
            ),
            (0, import_jsx_runtime4.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime4.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime4.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime4.jsx)(Button, { type: "button", variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
              (0, import_jsx_runtime4.jsx)(
                Button,
                {
                  type: "submit",
                  variant: "primary",
                  size: "small",
                  role: "button",
                  disabled: isPending,
                  onClick: async () => await onItemsSelected(),
                  children: t2("actions.save")
                },
                "submit-button"
              )
            ] }) }) })
          ] })
        ] })
      ] })
    ] }),
    filteredItems.map((item) => (0, import_jsx_runtime4.jsx)(
      OrderEditItem,
      {
        item,
        orderId: order.id,
        currencyCode: order.currency_code
      },
      item.id
    )),
    filterTerm && !filteredItems.length && (0, import_jsx_runtime4.jsx)(
      "div",
      {
        style: {
          background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
        },
        className: "bg-ui-bg-field mt-4 block h-[56px] w-full rounded-lg border border-dashed"
      }
    )
  ] });
};
var OrderEditCreateSchema = z.object({
  note: z.string().optional(),
  send_notification: z.boolean().optional()
});
var OrderEditCreateForm = ({
  order,
  preview
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { mutateAsync: cancelOrderEditRequest, isPending: isCanceling } = useCancelOrderEdit(order.id);
  const { mutateAsync: requestOrderEdit, isPending: isRequesting } = useRequestOrderEdit(order.id);
  const isRequestRunning = isCanceling || isRequesting;
  const form = useForm({
    defaultValues: () => {
      return Promise.resolve({
        note: "",
        // TODO: add note when update edit route is added
        send_notification: false
        // TODO: not supported in the API ATM
      });
    },
    resolver: t(OrderEditCreateSchema)
  });
  const prompt = usePrompt();
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const res = await prompt({
        title: t2("general.areYouSure"),
        description: t2("orders.edits.confirmText"),
        confirmText: t2("actions.continue"),
        cancelText: t2("actions.cancel"),
        variant: "confirmation"
      });
      if (!res) {
        return;
      }
      await requestOrderEdit();
      toast.success(t2("orders.edits.createSuccessToast"));
      handleSuccess();
    } catch (e) {
      toast.error(t2("general.error"), {
        description: e.message
      });
    }
  });
  return (0, import_jsx_runtime5.jsx)(
    RouteFocusModal.Form,
    {
      form,
      onClose: (isSubmitSuccessful) => {
        if (!isSubmitSuccessful) {
          cancelOrderEditRequest(void 0, {
            onSuccess: () => {
              toast.success(t2("orders.edits.cancelSuccessToast"));
            },
            onError: (error) => {
              toast.error(error.message);
            }
          });
        }
      },
      children: (0, import_jsx_runtime5.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
        (0, import_jsx_runtime5.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime5.jsx)(RouteFocusModal.Body, { className: "flex size-full justify-center overflow-y-auto", children: (0, import_jsx_runtime5.jsxs)("div", { className: "mt-16 w-[720px] max-w-[100%] px-4 md:p-0", children: [
          (0, import_jsx_runtime5.jsx)(Heading, { level: "h1", children: t2("orders.edits.create") }),
          (0, import_jsx_runtime5.jsx)(OrderEditItemsSection, { preview, order }),
          (0, import_jsx_runtime5.jsxs)("div", { className: "mt-8 border-y border-dotted py-4", children: [
            (0, import_jsx_runtime5.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.edits.currentTotal") }),
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(order.total, order.currency_code) })
            ] }),
            (0, import_jsx_runtime5.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.edits.newTotal") }),
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(preview.total, order.currency_code) })
            ] }),
            (0, import_jsx_runtime5.jsxs)("div", { className: "mt-4 flex items-center justify-between border-t border-dotted pt-4", children: [
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small font-medium", children: t2("orders.exchanges.refundAmount") }),
              (0, import_jsx_runtime5.jsx)("span", { className: "txt-small font-medium", children: getStylizedAmount(
                preview.summary.pending_difference,
                order.currency_code
              ) })
            ] })
          ] }),
          (0, import_jsx_runtime5.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "note",
              render: ({ field }) => {
                return (0, import_jsx_runtime5.jsx)(Form.Item, { children: (0, import_jsx_runtime5.jsxs)("div", { className: "mt-8 flex", children: [
                  (0, import_jsx_runtime5.jsxs)("div", { className: "block flex-1", children: [
                    (0, import_jsx_runtime5.jsx)(Form.Label, { children: t2("fields.note") }),
                    (0, import_jsx_runtime5.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.edits.noteHint") })
                  ] }),
                  (0, import_jsx_runtime5.jsx)("div", { className: "w-full flex-1 flex-grow", children: (0, import_jsx_runtime5.jsx)(Form.Control, { children: (0, import_jsx_runtime5.jsx)(Input, { ...field, placeholder: t2("fields.note") }) }) })
                ] }) });
              }
            }
          ),
          (0, import_jsx_runtime5.jsx)("div", { className: "bg-ui-bg-field mt-8 rounded-lg border py-2 pl-2 pr-4", children: (0, import_jsx_runtime5.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "send_notification",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime5.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center", children: [
                    (0, import_jsx_runtime5.jsx)(Form.Control, { className: "mr-4 self-start", children: (0, import_jsx_runtime5.jsx)(
                      Switch,
                      {
                        className: "mt-[2px]",
                        checked: !!value,
                        onCheckedChange: onChange,
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime5.jsxs)("div", { className: "block", children: [
                      (0, import_jsx_runtime5.jsx)(Form.Label, { children: t2("orders.returns.sendNotification") }),
                      (0, import_jsx_runtime5.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.sendNotificationHint") })
                    ] })
                  ] }),
                  (0, import_jsx_runtime5.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime5.jsx)("div", { className: "p-8" })
        ] }) }),
        (0, import_jsx_runtime5.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime5.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime5.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime5.jsx)(Button, { type: "button", variant: "secondary", size: "small", children: t2("orders.edits.cancel") }) }),
          (0, import_jsx_runtime5.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isRequestRunning,
              children: t2("orders.edits.confirm")
            },
            "submit-button"
          )
        ] }) }) })
      ] })
    }
  );
};
var IS_REQUEST_RUNNING = false;
var OrderEditCreate = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t: t2 } = useTranslation();
  const { order } = useOrder(id, {
    fields: DEFAULT_FIELDS
  });
  const { order: preview } = useOrderPreview(id);
  const { mutateAsync: createOrderEdit } = useCreateOrderEdit(order.id);
  (0, import_react.useEffect)(() => {
    async function run() {
      if (IS_REQUEST_RUNNING || !preview) {
        return;
      }
      if (preview.order_change) {
        if (preview.order_change.change_type !== "edit") {
          navigate(`/orders/${preview.id}`, { replace: true });
          toast.error(t2("orders.edits.activeChangeError"));
        }
        return;
      }
      IS_REQUEST_RUNNING = true;
      try {
        const { order: order2 } = await createOrderEdit({
          order_id: preview.id
        });
      } catch (e) {
        toast.error(e.message);
        navigate(`/orders/${preview.id}`, { replace: true });
      } finally {
        IS_REQUEST_RUNNING = false;
      }
    }
    run();
  }, [preview]);
  return (0, import_jsx_runtime6.jsx)(RouteFocusModal, { children: preview && order && (0, import_jsx_runtime6.jsx)(OrderEditCreateForm, { order, preview }) });
};
export {
  OrderEditCreate as Component
};
//# sourceMappingURL=order-create-edit-DKT5Y5IZ-NKWZYUOS.js.map
