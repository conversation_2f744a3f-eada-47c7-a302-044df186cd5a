import {
  require_copy_to_clipboard
} from "./chunk-TWHCESJX.js";
import {
  formatCurrency
} from "./chunk-SCWUY6XB.js";
import {
  getOrderPaymentStatus
} from "./chunk-5ZQBU3TD.js";
import {
  getLocaleAmount,
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import {
  format
} from "./chunk-7UAYECTW.js";
import {
  useCapturePayment
} from "./chunk-HQL74AJG.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  __toESM as __toESM2
} from "./chunk-XMKQFEQ4.js";
import {
  ArrowDownRightMini,
  Badge,
  Button,
  Container,
  DocumentText,
  Heading,
  StatusBadge,
  Text,
  Tooltip,
  XCircle,
  clx,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-DDV2HJEV.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_copy_to_clipboard = __toESM2(require_copy_to_clipboard());
function DisplayId({ id, className }) {
  const { t } = useTranslation();
  const [open, setOpen] = (0, import_react.useState)(false);
  const onClick = () => {
    (0, import_copy_to_clipboard.default)(id);
    toast.success(t("actions.idCopiedToClipboard"));
  };
  return (0, import_jsx_runtime.jsx)(Tooltip, { maxWidth: 260, content: id, open, onOpenChange: setOpen, children: (0, import_jsx_runtime.jsxs)("span", { onClick, className: clx("cursor-pointer", className), children: [
    "#",
    id.slice(-7)
  ] }) });
}
var display_id_default = DisplayId;
var getTotalCaptured = (paymentCollections) => paymentCollections.reduce((acc, paymentCollection) => {
  acc = acc + (paymentCollection.captured_amount - paymentCollection.refunded_amount);
  return acc;
}, 0);
var getTotalPending = (paymentCollections) => paymentCollections.filter((pc) => pc.status !== "canceled").reduce((acc, paymentCollection) => {
  acc += paymentCollection.amount - paymentCollection.captured_amount;
  return acc;
}, 0);
var getPaymentsFromOrder = (order) => {
  return order.payment_collections.map((collection) => collection.payments).flat(1).filter(Boolean);
};
var OrderPaymentSection = ({ order }) => {
  const payments = getPaymentsFromOrder(order);
  const refunds = payments.map((payment) => payment == null ? void 0 : payment.refunds).flat(1).filter(Boolean);
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y divide-dashed p-0", children: [
    (0, import_jsx_runtime2.jsx)(Header, { order }),
    (0, import_jsx_runtime2.jsx)(
      PaymentBreakdown,
      {
        order,
        payments,
        refunds,
        currencyCode: order.currency_code
      }
    ),
    (0, import_jsx_runtime2.jsx)(Total, { order })
  ] });
};
var Header = ({ order }) => {
  const { t } = useTranslation();
  const { label, color } = getOrderPaymentStatus(t, order.payment_status);
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
    (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("orders.payment.title") }),
    (0, import_jsx_runtime2.jsx)(StatusBadge, { color, className: "text-nowrap", children: label })
  ] });
};
var Refund = ({
  refund,
  currencyCode
}) => {
  const { t } = useTranslation();
  const RefundReasonBadge = (refund == null ? void 0 : refund.refund_reason) && (0, import_jsx_runtime2.jsx)(
    Badge,
    {
      size: "2xsmall",
      className: "cursor-default select-none capitalize",
      rounded: "full",
      children: refund.refund_reason.label
    }
  );
  const RefundNoteIndicator = refund.note && (0, import_jsx_runtime2.jsx)(Tooltip, { content: refund.note, children: (0, import_jsx_runtime2.jsx)(DocumentText, { className: "text-ui-tag-neutral-icon ml-1 inline" }) });
  return (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-subtle text-ui-fg-subtle grid grid-cols-[1fr_1fr_1fr_20px] items-center gap-x-4 px-6 py-4", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-row", children: [
      (0, import_jsx_runtime2.jsx)("div", { className: "self-center pr-3", children: (0, import_jsx_runtime2.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted" }) }),
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsxs)(Text, { size: "small", leading: "compact", weight: "plus", children: [
          t("orders.payment.refund"),
          " ",
          RefundNoteIndicator
        ] }),
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: format(new Date(refund.created_at), "dd MMM, yyyy, HH:mm:ss") })
      ] })
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-end", children: RefundReasonBadge }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime2.jsxs)(Text, { size: "small", leading: "compact", children: [
      "- ",
      getLocaleAmount(refund.amount, currencyCode)
    ] }) })
  ] });
};
var Payment = ({
  order,
  payment,
  refunds,
  currencyCode
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useCapturePayment(order.id, payment.id);
  const handleCapture = async () => {
    const res = await prompt({
      title: t("orders.payment.capture"),
      description: t("orders.payment.capturePayment", {
        amount: formatCurrency(payment.amount, currencyCode)
      }),
      confirmText: t("actions.confirm"),
      cancelText: t("actions.cancel"),
      variant: "confirmation"
    });
    if (!res) {
      return;
    }
    await mutateAsync(
      { amount: payment.amount },
      {
        onSuccess: () => {
          toast.success(
            t("orders.payment.capturePaymentSuccess", {
              amount: formatCurrency(payment.amount, currencyCode)
            })
          );
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  };
  const getPaymentStatusAttributes = (payment2) => {
    if (payment2.canceled_at) {
      return ["Canceled", "red"];
    } else if (payment2.captured_at) {
      return ["Captured", "green"];
    } else {
      return ["Pending", "orange"];
    }
  };
  const [status, color] = getPaymentStatusAttributes(payment);
  const showCapture = payment.captured_at === null && payment.canceled_at === null;
  const totalRefunded = payment.refunds.reduce(
    (acc, next) => next.amount + acc,
    0
  );
  return (0, import_jsx_runtime2.jsxs)("div", { className: "divide-y divide-dashed", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-[1fr_1fr_1fr_20px] items-center gap-x-4 px-6 py-4 sm:grid-cols-[1fr_1fr_1fr_1fr_20px]", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "w-full min-w-[60px] overflow-hidden", children: [
        (0, import_jsx_runtime2.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            weight: "plus",
            className: "truncate",
            children: (0, import_jsx_runtime2.jsx)(display_id_default, { id: payment.id })
          }
        ),
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: format(
          new Date(payment.created_at),
          "dd MMM, yyyy, HH:mm:ss"
        ) })
      ] }),
      (0, import_jsx_runtime2.jsx)("div", { className: "hidden items-center justify-end sm:flex", children: (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", className: "capitalize", children: payment.provider_id }) }),
      (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime2.jsx)(StatusBadge, { color, className: "text-nowrap", children: status }) }),
      (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: getLocaleAmount(payment.amount, payment.currency_code) }) }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("orders.payment.refund"),
                  icon: (0, import_jsx_runtime2.jsx)(XCircle, {}),
                  to: `/orders/${order.id}/refund?paymentId=${payment.id}`,
                  disabled: !payment.captured_at || !!payment.canceled_at || totalRefunded >= payment.amount
                }
              ]
            }
          ]
        }
      )
    ] }),
    showCapture && (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-subtle flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime2.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted shrink-0" }),
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: (0, import_jsx_runtime2.jsx)(
          Trans,
          {
            i18nKey: "orders.payment.isReadyToBeCaptured",
            components: [(0, import_jsx_runtime2.jsx)(display_id_default, { id: payment.id })]
          }
        ) })
      ] }),
      (0, import_jsx_runtime2.jsxs)(
        Button,
        {
          className: "shrink-0",
          size: "small",
          variant: "secondary",
          onClick: handleCapture,
          children: [
            (0, import_jsx_runtime2.jsx)("span", { className: "hidden sm:block", children: t("orders.payment.capture") }),
            (0, import_jsx_runtime2.jsx)("span", { className: "sm:hidden", children: t("orders.payment.capture_short") })
          ]
        }
      )
    ] }),
    refunds.map((refund) => (0, import_jsx_runtime2.jsx)(Refund, { refund, currencyCode }, refund.id))
  ] });
};
var PaymentBreakdown = ({
  order,
  payments,
  refunds,
  currencyCode
}) => {
  const orderRefunds = refunds.filter((refund) => refund.payment_id === null);
  const entries = [...orderRefunds, ...payments].sort((a, b) => {
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  }).map((entry) => {
    return {
      event: entry,
      type: entry.id.startsWith("pay_") ? "payment" : "refund"
    };
  });
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col divide-y divide-dashed", children: entries.map(({ type, event }) => {
    switch (type) {
      case "payment":
        return (0, import_jsx_runtime2.jsx)(
          Payment,
          {
            order,
            payment: event,
            refunds: refunds.filter(
              (refund) => refund.payment_id === event.id
            ),
            currencyCode
          },
          event.id
        );
      case "refund":
        return (0, import_jsx_runtime2.jsx)(
          Refund,
          {
            refund: event,
            currencyCode
          },
          event.id
        );
    }
  }) });
};
var Total = ({ order }) => {
  const { t } = useTranslation();
  const totalPending = getTotalPending(order.payment_collections);
  return (0, import_jsx_runtime2.jsxs)("div", { children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t("orders.payment.totalPaidByCustomer") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: getStylizedAmount(
        getTotalCaptured(order.payment_collections),
        order.currency_code
      ) })
    ] }),
    order.status !== "canceled" && totalPending > 0 && (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: "Total pending" }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: getStylizedAmount(totalPending, order.currency_code) })
    ] })
  ] });
};

export {
  getTotalCaptured,
  getPaymentsFromOrder,
  OrderPaymentSection
};
//# sourceMappingURL=chunk-TBLRQ3E5.js.map
