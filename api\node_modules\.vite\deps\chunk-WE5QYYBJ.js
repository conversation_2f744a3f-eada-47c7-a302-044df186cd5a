import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Tooltip,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-I3VB6NM2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ListSummary = ({
  list,
  className,
  variant = "compact",
  inline,
  n = 2
}) => {
  const { t } = useTranslation();
  const title = t("general.plusCountMore", {
    count: list.length - n
  });
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "text-ui-fg-subtle gap-x-1 overflow-hidden",
        {
          "inline-flex": inline,
          flex: !inline,
          "txt-compact-small": variant === "compact",
          "txt-small": variant === "base"
        },
        className
      ),
      children: [
        (0, import_jsx_runtime.jsx)("div", { className: "flex-1 truncate", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: list.slice(0, n).join(", ") }) }),
        list.length > n && (0, import_jsx_runtime.jsx)("div", { className: "whitespace-nowrap", children: (0, import_jsx_runtime.jsx)(
          Tooltip,
          {
            content: (0, import_jsx_runtime.jsx)("ul", { children: list.slice(n).map((c) => (0, import_jsx_runtime.jsx)("li", { children: c }, c)) }),
            children: (0, import_jsx_runtime.jsx)("span", { className: "cursor-default whitespace-nowrap", children: title })
          }
        ) })
      ]
    }
  );
};

export {
  ListSummary
};
//# sourceMappingURL=chunk-WE5QYYBJ.js.map
