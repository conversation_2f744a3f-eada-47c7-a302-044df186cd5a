import "./chunk-EGRHWZRV.js";
import {
  MetadataForm
} from "./chunk-LNSD7AQU.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import "./chunk-XMQMXYDG.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-DPO7J5IQ.js";
import {
  useCustomer,
  useUpdateCustomer
} from "./chunk-FSXJE4G7.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-metadata-TPIFKFIQ.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CustomerMetadata = () => {
  const { id } = useParams();
  const { customer, isPending, isError, error } = useCustomer(id);
  const { mutateAsync, isPending: isMutating } = useUpdateCustomer(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    MetadataForm,
    {
      metadata: customer == null ? void 0 : customer.metadata,
      hook: mutateAsync,
      isPending,
      isMutating
    }
  );
};
export {
  CustomerMetadata as Component
};
//# sourceMappingURL=customer-metadata-TPIFKFIQ-YUFXC6TJ.js.map
