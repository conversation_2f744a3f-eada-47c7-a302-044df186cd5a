import {
  customerGroupsQueryKeys
} from "./chunk-FSXJE4G7.js";
import {
  productsQueryKeys
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-YS65UGPC.mjs
var PRICE_LISTS_QUERY_KEY = "price-lists";
var priceListsQueryKeys = queryKeysFactory(PRICE_LISTS_QUERY_KEY);
var usePriceList = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.priceList.retrieve(id, query),
    queryKey: priceListsQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var usePriceLists = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.priceList.list(query),
    queryKey: priceListsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreatePriceList = (query, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.priceList.create(payload, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerGroupsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdatePriceList = (id, query, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.priceList.update(id, payload, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: priceListsQueryKeys.details()
      });
      queryClient.invalidateQueries({ queryKey: customerGroupsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeletePriceList = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.priceList.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useBatchPriceListPrices = (id, query, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.priceList.batchPrices(id, payload, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: priceListsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var usePriceListLinkProducts = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.priceList.linkProducts(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: priceListsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  priceListsQueryKeys,
  usePriceList,
  usePriceLists,
  useCreatePriceList,
  useUpdatePriceList,
  useDeletePriceList,
  useBatchPriceListPrices,
  usePriceListLinkProducts
};
//# sourceMappingURL=chunk-XBIMCDU6.js.map
