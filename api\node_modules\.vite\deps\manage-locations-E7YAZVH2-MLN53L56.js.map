{"version": 3, "sources": ["../../@medusajs/dashboard/dist/manage-locations-E7YAZVH2.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  useBatchInventoryItemLocationLevels,\n  useInventoryItem\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/inventory/inventory-detail/components/manage-locations/manage-locations-drawer.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/manage-locations/components/manage-locations-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Text as Text2, toast } from \"@medusajs/ui\";\nimport { useFieldArray, useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { useEffect, useMemo } from \"react\";\n\n// src/routes/inventory/inventory-detail/components/manage-locations/components/location-item.tsx\nimport { Checkbox, Text, clx } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar LocationItem = ({\n  selected,\n  onSelect,\n  location\n}) => {\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"flex w-full cursor-pointer gap-x-2 rounded-lg border px-2 py-2\",\n        {\n          \"border-ui-border-interactive \": selected\n        }\n      ),\n      onClick: () => onSelect(!selected),\n      children: [\n        /* @__PURE__ */ jsx(\"div\", { className: \"h-5 w-5\", children: /* @__PURE__ */ jsx(\n          Checkbox,\n          {\n            onClick: (e) => {\n              e.stopPropagation();\n              onSelect(!selected);\n            },\n            checked: selected\n          }\n        ) }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full flex-col\", children: [\n          /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: location.name }),\n          /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", className: \"text-ui-fg-subtle\", children: [\n            location.address?.address_1,\n            location.address?.city,\n            location.address?.country_code\n          ].filter((el) => !!el).join(\", \") })\n        ] })\n      ]\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/manage-locations/components/manage-locations-form.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar EditInventoryItemAttributesSchema = z.object({\n  locations: z.array(\n    z.object({\n      id: z.string(),\n      location_id: z.string(),\n      selected: z.boolean()\n    })\n  )\n});\nvar getDefaultValues = (allLocations, existingLevels) => {\n  return {\n    locations: allLocations.map((location) => ({\n      ...location,\n      location_id: location.id,\n      selected: existingLevels.has(location.id)\n    }))\n  };\n};\nvar ManageLocationsForm = ({\n  item,\n  locations\n}) => {\n  const existingLocationLevels = useMemo(\n    () => new Set(item.location_levels?.map((l) => l.location_id) ?? []),\n    item.location_levels\n  );\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: getDefaultValues(locations, existingLocationLevels),\n    resolver: zodResolver(EditInventoryItemAttributesSchema)\n  });\n  const { fields: locationFields, update: updateField } = useFieldArray({\n    control: form.control,\n    name: \"locations\"\n  });\n  useEffect(() => {\n    form.setValue(\n      \"locations\",\n      getDefaultValues(locations, existingLocationLevels).locations\n    );\n  }, [existingLocationLevels, locations]);\n  const { mutateAsync } = useBatchInventoryItemLocationLevels(item.id);\n  const handleSubmit = form.handleSubmit(async ({ locations: locations2 }) => {\n    const [selectedLocations, unselectedLocations] = locations2.reduce(\n      (acc, location) => {\n        if (!location.selected && !existingLocationLevels.has(location.location_id) || location.selected && existingLocationLevels.has(location.location_id)) {\n          return acc;\n        }\n        if (location.selected) {\n          acc[0].push(location.location_id);\n        } else {\n          acc[1].push(location.location_id);\n        }\n        return acc;\n      },\n      [[], []]\n    );\n    if (selectedLocations.length === 0 && unselectedLocations.length === 0) {\n      return handleSuccess();\n    }\n    await mutateAsync(\n      {\n        create: selectedLocations.map((location_id) => ({\n          location_id\n        })),\n        delete: unselectedLocations\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"inventory.toast.updateLocations\"));\n          handleSuccess();\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx2(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs2(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs2(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-4 overflow-auto\", children: [\n          /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-2 divide-y rounded-lg border\", children: [\n            /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-2 divide-x\", children: [\n              /* @__PURE__ */ jsx2(Text2, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: t(\"fields.title\") }),\n              /* @__PURE__ */ jsx2(Text2, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: item.title ?? \"-\" })\n            ] }),\n            /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-2 divide-x\", children: [\n              /* @__PURE__ */ jsx2(Text2, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: t(\"fields.sku\") }),\n              /* @__PURE__ */ jsx2(Text2, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: item.sku })\n            ] })\n          ] }),\n          /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col\", children: [\n            /* @__PURE__ */ jsx2(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"locations.domain\") }),\n            /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle flex w-full justify-between\", children: [\n              /* @__PURE__ */ jsx2(Text2, { size: \"small\", leading: \"compact\", children: t(\"locations.selectLocations\") }),\n              /* @__PURE__ */ jsxs2(Text2, { size: \"small\", leading: \"compact\", children: [\n                \"(\",\n                t(\"general.countOfTotalSelected\", {\n                  count: locationFields.filter((l) => l.selected).length,\n                  total: locations.length\n                }),\n                \")\"\n              ] })\n            ] })\n          ] }),\n          locationFields.map((location, idx) => {\n            return /* @__PURE__ */ jsx2(\n              LocationItem,\n              {\n                selected: location.selected,\n                location,\n                onSelect: () => updateField(idx, {\n                  ...location,\n                  selected: !location.selected\n                })\n              },\n              location.id\n            );\n          })\n        ] }),\n        /* @__PURE__ */ jsx2(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx2(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx2(Button, { type: \"submit\", size: \"small\", isLoading: false, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/inventory/inventory-detail/components/manage-locations/manage-locations-drawer.tsx\nimport { jsx as jsx3, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ManageLocationsDrawer = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const {\n    inventory_item: inventoryItem,\n    isPending: isLoading,\n    isError,\n    error\n  } = useInventoryItem(id);\n  const { stock_locations, isLoading: loadingLocations } = useStockLocations();\n  const ready = !isLoading && !loadingLocations && inventoryItem && stock_locations;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx3(RouteDrawer.Header, { children: /* @__PURE__ */ jsx3(Heading, { children: t(\"inventory.manageLocations\") }) }),\n    ready && /* @__PURE__ */ jsx3(ManageLocationsForm, { item: inventoryItem, locations: stock_locations })\n  ] });\n};\nexport {\n  ManageLocationsDrawer as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,mBAAmC;AAInC,yBAA0B;AAyC1B,IAAAA,sBAA2C;AAwI3C,IAAAA,sBAA2C;AAhL3C,IAAI,eAAe,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AAzCN;AA0CE,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,iCAAiC;AAAA,QACnC;AAAA,MACF;AAAA,MACA,SAAS,MAAM,SAAS,CAAC,QAAQ;AAAA,MACjC,UAAU;AAAA,YACQ,wBAAI,OAAO,EAAE,WAAW,WAAW,cAA0B;AAAA,UAC3E;AAAA,UACA;AAAA,YACE,SAAS,CAAC,MAAM;AACd,gBAAE,gBAAgB;AAClB,uBAAS,CAAC,QAAQ;AAAA,YACpB;AAAA,YACA,SAAS;AAAA,UACX;AAAA,QACF,EAAE,CAAC;AAAA,YACa,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,cACzD,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,SAAS,KAAK,CAAC;AAAA,cACxF,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,qBAAqB,UAAU;AAAA,aACvG,cAAS,YAAT,mBAAkB;AAAA,aAClB,cAAS,YAAT,mBAAkB;AAAA,aAClB,cAAS,YAAT,mBAAkB;AAAA,UACpB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,QACrC,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,oCAAoC,EAAE,OAAO;AAAA,EAC/C,WAAW,EAAE;AAAA,IACX,EAAE,OAAO;AAAA,MACP,IAAI,EAAE,OAAO;AAAA,MACb,aAAa,EAAE,OAAO;AAAA,MACtB,UAAU,EAAE,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH;AACF,CAAC;AACD,IAAI,mBAAmB,CAAC,cAAc,mBAAmB;AACvD,SAAO;AAAA,IACL,WAAW,aAAa,IAAI,CAAC,cAAc;AAAA,MACzC,GAAG;AAAA,MACH,aAAa,SAAS;AAAA,MACtB,UAAU,eAAe,IAAI,SAAS,EAAE;AAAA,IAC1C,EAAE;AAAA,EACJ;AACF;AACA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,6BAAyB;AAAA,IAC7B,MAAG;AArGP;AAqGU,iBAAI,MAAI,UAAK,oBAAL,mBAAsB,IAAI,CAAC,MAAM,EAAE,iBAAgB,CAAC,CAAC;AAAA;AAAA,IACnE,KAAK;AAAA,EACP;AACA,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,iBAAiB,WAAW,sBAAsB;AAAA,IACjE,UAAU,EAAY,iCAAiC;AAAA,EACzD,CAAC;AACD,QAAM,EAAE,QAAQ,gBAAgB,QAAQ,YAAY,IAAI,cAAc;AAAA,IACpE,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,8BAAU,MAAM;AACd,SAAK;AAAA,MACH;AAAA,MACA,iBAAiB,WAAW,sBAAsB,EAAE;AAAA,IACtD;AAAA,EACF,GAAG,CAAC,wBAAwB,SAAS,CAAC;AACtC,QAAM,EAAE,YAAY,IAAI,oCAAoC,KAAK,EAAE;AACnE,QAAM,eAAe,KAAK,aAAa,OAAO,EAAE,WAAW,WAAW,MAAM;AAC1E,UAAM,CAAC,mBAAmB,mBAAmB,IAAI,WAAW;AAAA,MAC1D,CAAC,KAAK,aAAa;AACjB,YAAI,CAAC,SAAS,YAAY,CAAC,uBAAuB,IAAI,SAAS,WAAW,KAAK,SAAS,YAAY,uBAAuB,IAAI,SAAS,WAAW,GAAG;AACpJ,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU;AACrB,cAAI,CAAC,EAAE,KAAK,SAAS,WAAW;AAAA,QAClC,OAAO;AACL,cAAI,CAAC,EAAE,KAAK,SAAS,WAAW;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AAAA,MACA,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,IACT;AACA,QAAI,kBAAkB,WAAW,KAAK,oBAAoB,WAAW,GAAG;AACtE,aAAO,cAAc;AAAA,IACvB;AACA,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ,kBAAkB,IAAI,CAAC,iBAAiB;AAAA,UAC9C;AAAA,QACF,EAAE;AAAA,QACF,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,iCAAiC,CAAC;AAClD,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,oBAAAC,KAAK,YAAY,MAAM,EAAE,MAAM,cAA0B,oBAAAC;AAAA,IAC9E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,oBAAAA,MAAM,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC3F,oBAAAA,MAAM,OAAO,EAAE,WAAW,4FAA4F,UAAU;AAAA,gBAC9H,oBAAAA,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,kBAC/D,oBAAAD,KAAK,MAAO,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAUD,GAAE,cAAc,EAAE,CAAC;AAAA,kBACxG,oBAAAC,KAAK,MAAO,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,KAAK,SAAS,IAAI,CAAC;AAAA,YAC1H,EAAE,CAAC;AAAA,gBACa,oBAAAC,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,kBAC/D,oBAAAD,KAAK,MAAO,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAUD,GAAE,YAAY,EAAE,CAAC;AAAA,kBACtG,oBAAAC,KAAK,MAAO,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,KAAK,IAAI,CAAC;AAAA,YACjH,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,cACa,oBAAAC,MAAM,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,gBACnD,oBAAAD,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAUD,GAAE,kBAAkB,EAAE,CAAC;AAAA,gBAClG,oBAAAE,MAAM,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,kBACnF,oBAAAD,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAUD,GAAE,2BAA2B,EAAE,CAAC;AAAA,kBAC3F,oBAAAE,MAAM,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU;AAAA,gBAC1E;AAAA,gBACAF,GAAE,gCAAgC;AAAA,kBAChC,OAAO,eAAe,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE;AAAA,kBAChD,OAAO,UAAU;AAAA,gBACnB,CAAC;AAAA,gBACD;AAAA,cACF,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,UACH,eAAe,IAAI,CAAC,UAAU,QAAQ;AACpC,uBAAuB,oBAAAC;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,UAAU,SAAS;AAAA,gBACnB;AAAA,gBACA,UAAU,MAAM,YAAY,KAAK;AAAA,kBAC/B,GAAG;AAAA,kBACH,UAAU,CAAC,SAAS;AAAA,gBACtB,CAAC;AAAA,cACH;AAAA,cACA,SAAS;AAAA,YACX;AAAA,UACF,CAAC;AAAA,QACH,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAChI,oBAAAD,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUD,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACzJ,oBAAAC,KAAK,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,OAAO,UAAUD,GAAE,cAAc,EAAE,CAAC;AAAA,QAC/G,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,EAAE;AACvB,QAAM,EAAE,iBAAiB,WAAW,iBAAiB,IAAI,kBAAkB;AAC3E,QAAM,QAAQ,CAAC,aAAa,CAAC,oBAAoB,iBAAiB;AAClE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAG,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUJ,GAAE,2BAA2B,EAAE,CAAC,EAAE,CAAC;AAAA,IAClI,aAAyB,oBAAAI,KAAK,qBAAqB,EAAE,MAAM,eAAe,WAAW,gBAAgB,CAAC;AAAA,EACxG,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2", "jsxs2", "jsxs3", "jsx3"]}