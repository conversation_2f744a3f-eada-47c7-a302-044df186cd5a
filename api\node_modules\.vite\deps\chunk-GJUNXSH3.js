import {
  TaxRateRuleReferenceType
} from "./chunk-VD6KBTYK.js";
import {
  LocalizedTablePagination,
  useDeleteTaxRateAction
} from "./chunk-HJV4BH76.js";
import {
  formatPercentage
} from "./chunk-XYHEMHQ5.js";
import {
  DataTableSearch
} from "./chunk-733UOLIB.js";
import {
  DataTableOrderBy
} from "./chunk-CZLMELES.js";
import {
  NoRecords,
  NoResults
} from "./chunk-QL4D35SJ.js";
import {
  TableFooterSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import {
  useProductTypes
} from "./chunk-HREJMEGI.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Link,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowDownRightMini,
  Badge,
  Button,
  Divider,
  IconButton,
  PencilSquare,
  StatusBadge,
  Text,
  Tooltip,
  Trash,
  TriangleRightMini,
  dist_exports2 as dist_exports,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-2MPQMCLO.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var TaxRateLine = ({
  taxRate,
  isSublevelTaxRate
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-[1fr_1fr_auto] items-center gap-4 px-6 py-4", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: taxRate.name }),
      taxRate.code && (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "·" }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: taxRate.code })
      ] })
    ] }),
    (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: formatPercentage(taxRate.rate) }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      isSublevelTaxRate && (0, import_jsx_runtime.jsx)(StatusBadge, { color: taxRate.is_combinable ? "green" : "grey", children: taxRate.is_combinable ? t("taxRegions.fields.isCombinable.true") : t("taxRegions.fields.isCombinable.false") }),
      (0, import_jsx_runtime.jsx)(TaxRateActions, { taxRate })
    ] })
  ] });
};
var TaxRateActions = ({ taxRate }) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteTaxRateAction(taxRate);
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              to: `tax-rates/${taxRate.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var TaxOverrideCard = ({ taxRate }) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteTaxRateAction(taxRate);
  if (taxRate.is_default) {
    return null;
  }
  const groupedRules = taxRate.rules.reduce(
    (acc, rule) => {
      if (!acc[rule.reference]) {
        acc[rule.reference] = [];
      }
      acc[rule.reference].push(rule.reference_id);
      return acc;
    },
    {}
  );
  const validKeys = Object.values(TaxRateRuleReferenceType);
  const numberOfTargets = Object.keys(groupedRules).map(
    (key) => validKeys.includes(key)
  ).length;
  return (0, import_jsx_runtime2.jsxs)(dist_exports.Root, { children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-3", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime2.jsx)(dist_exports.Trigger, { asChild: true, children: (0, import_jsx_runtime2.jsx)(IconButton, { size: "2xsmall", variant: "transparent", className: "group", children: (0, import_jsx_runtime2.jsx)(TriangleRightMini, { className: "text-ui-fg-muted transition-transform group-data-[state='open']:rotate-90" }) }) }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
          (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: taxRate.name }),
          taxRate.code && (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle flex items-center gap-x-1.5", children: [
            (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: "·" }),
            (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: taxRate.code })
          ] })
        ] })
      ] }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-3", children: [
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: t("taxRegions.fields.targets.numberOfTargets", {
          count: numberOfTargets
        }) }),
        (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-border-base h-3 w-px" }),
        (0, import_jsx_runtime2.jsx)(StatusBadge, { color: taxRate.is_combinable ? "green" : "grey", children: taxRate.is_combinable ? t("taxRegions.fields.isCombinable.true") : t("taxRegions.fields.isCombinable.false") }),
        (0, import_jsx_runtime2.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                    to: `overrides/${taxRate.id}/edit`
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime2.jsx)(dist_exports.Content, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-subtle", children: [
      (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
      (0, import_jsx_runtime2.jsx)("div", { className: "px-6 py-3", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-3", children: [
        (0, import_jsx_runtime2.jsx)("div", { className: "text-ui-fg-muted flex size-5 items-center justify-center", children: (0, import_jsx_runtime2.jsx)(ArrowDownRightMini, {}) }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-wrap items-center gap-x-1.5 gap-y-2", children: [
          (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: formatPercentage(taxRate.rate) }),
          (0, import_jsx_runtime2.jsx)(
            Text,
            {
              size: "small",
              leading: "compact",
              className: "text-ui-fg-subtle",
              children: t("taxRegions.fields.targets.operators.on")
            }
          ),
          Object.entries(groupedRules).map(([reference, ids], index) => {
            return (0, import_jsx_runtime2.jsxs)(
              "div",
              {
                className: "flex items-center gap-x-1.5",
                children: [
                  (0, import_jsx_runtime2.jsx)(
                    Reference,
                    {
                      reference,
                      ids
                    },
                    reference
                  ),
                  index < Object.keys(groupedRules).length - 1 && (0, import_jsx_runtime2.jsx)(
                    Text,
                    {
                      size: "small",
                      leading: "compact",
                      className: "text-ui-fg-subtle",
                      children: t("taxRegions.fields.targets.operators.and")
                    }
                  )
                ]
              },
              reference
            );
          })
        ] })
      ] }) })
    ] }) })
  ] });
};
var Reference = ({
  reference,
  ids
}) => {
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
    (0, import_jsx_runtime2.jsx)(ReferenceBadge, { reference }),
    (0, import_jsx_runtime2.jsx)(ReferenceValues, { type: reference, ids })
  ] });
};
var ReferenceBadge = ({
  reference
}) => {
  const { t } = useTranslation();
  let label = null;
  switch (reference) {
    case "product":
      label = t("taxRegions.fields.targets.tags.product");
      break;
    case "product_type":
      label = t("taxRegions.fields.targets.tags.productType");
      break;
  }
  if (!label) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: label });
};
var ReferenceValues = ({
  type,
  ids
}) => {
  const { t } = useTranslation();
  const { isPending, additional, labels, isError, error } = useReferenceValues(
    type,
    ids
  );
  if (isError) {
    throw error;
  }
  if (isPending) {
    return (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-tag-neutral-bg border-ui-tag-neutral-border h-5 w-14 animate-pulse rounded-md" });
  }
  return (0, import_jsx_runtime2.jsx)(
    Tooltip,
    {
      content: (0, import_jsx_runtime2.jsxs)("ul", { children: [
        labels == null ? void 0 : labels.map((label, index) => (0, import_jsx_runtime2.jsx)("li", { children: label }, index)),
        additional > 0 && (0, import_jsx_runtime2.jsx)("li", { children: t("taxRegions.fields.targets.additionalValues", {
          count: additional
        }) })
      ] }),
      children: (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: t("taxRegions.fields.targets.values", {
        count: ids.length
      }) })
    }
  );
};
var useReferenceValues = (type, ids) => {
  var _a, _b;
  const products = useProducts(
    {
      id: ids,
      limit: 10
    },
    {
      enabled: !!ids.length && type === "product"
      /* PRODUCT */
    }
  );
  const productTypes = useProductTypes(
    {
      id: ids,
      limit: 10
    },
    {
      enabled: !!ids.length && type === "product_type"
      /* PRODUCT_TYPE */
    }
  );
  switch (type) {
    case "product":
      return {
        labels: (_a = products.products) == null ? void 0 : _a.map((product) => product.title),
        isPending: products.isPending,
        additional: products.products && products.count ? products.count - products.products.length : 0,
        isError: products.isError,
        error: products.error
      };
    case "product_type":
      return {
        labels: (_b = productTypes.product_types) == null ? void 0 : _b.map((type2) => type2.value),
        isPending: productTypes.isPending,
        additional: productTypes.product_types && productTypes.count ? productTypes.count - productTypes.product_types.length : 0,
        isError: productTypes.isError,
        error: productTypes.error
      };
  }
};
var TaxOverrideTable = ({
  isPending,
  action,
  count = 0,
  table,
  queryObject,
  prefix,
  children
}) => {
  if (isPending) {
    return (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col divide-y", children: [
      Array.from({ length: 3 }).map((_, index) => {
        return (0, import_jsx_runtime3.jsx)(
          "div",
          {
            className: "bg-ui-bg-field-component h-[52px] w-full animate-pulse"
          },
          index
        );
      }),
      (0, import_jsx_runtime3.jsx)(TableFooterSkeleton, { layout: "fit" })
    ] });
  }
  const noQuery = Object.values(queryObject).filter((v) => Boolean(v)).length === 0;
  const noResults = !isPending && count === 0 && !noQuery;
  const noRecords = !isPending && count === 0 && noQuery;
  const { pageIndex, pageSize } = table.getState().pagination;
  return (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col divide-y", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col justify-between gap-x-4 gap-y-3 px-6 py-4 md:flex-row md:items-center", children: [
      (0, import_jsx_runtime3.jsx)("div", { children }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        !noRecords && (0, import_jsx_runtime3.jsxs)("div", { className: "flex w-full items-center gap-x-2 md:w-fit", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "w-full md:w-fit", children: (0, import_jsx_runtime3.jsx)(DataTableSearch, { prefix }) }),
          (0, import_jsx_runtime3.jsx)(
            DataTableOrderBy,
            {
              keys: ["name", "rate", "code", "updated_at", "created_at"],
              prefix
            }
          )
        ] }),
        (0, import_jsx_runtime3.jsx)(Link, { to: action.to, children: (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", children: action.label }) })
      ] })
    ] }),
    noResults && (0, import_jsx_runtime3.jsx)(NoResults, {}),
    noRecords && (0, import_jsx_runtime3.jsx)(NoRecords, {}),
    !noRecords && !noResults ? !isPending ? table.getRowModel().rows.map((row) => {
      return (0, import_jsx_runtime3.jsx)(
        TaxOverrideCard,
        {
          taxRate: row.original,
          role: "row",
          "aria-rowindex": row.index
        },
        row.id
      );
    }) : Array.from({ length: 3 }).map((_, index) => {
      return (0, import_jsx_runtime3.jsx)(
        "div",
        {
          className: "bg-ui-bg-field-component h-[60px] w-full animate-pulse"
        },
        index
      );
    }) : null,
    !noRecords && (0, import_jsx_runtime3.jsx)(
      LocalizedTablePagination,
      {
        prefix,
        canNextPage: table.getCanNextPage(),
        canPreviousPage: table.getCanPreviousPage(),
        count,
        nextPage: table.nextPage,
        previousPage: table.previousPage,
        pageCount: table.getPageCount(),
        pageIndex,
        pageSize
      }
    )
  ] });
};
var useTaxOverrideTable = ({
  data = [],
  count = 0,
  pageSize: _pageSize = 10,
  prefix
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const offsetKey = `${prefix ? `${prefix}_` : ""}offset`;
  const offset = searchParams.get(offsetKey);
  const [{ pageIndex, pageSize }, setPagination] = (0, import_react.useState)({
    pageIndex: offset ? Math.ceil(Number(offset) / _pageSize) : 0,
    pageSize: _pageSize
  });
  const pagination = (0, import_react.useMemo)(
    () => ({
      pageIndex,
      pageSize
    }),
    [pageIndex, pageSize]
  );
  (0, import_react.useEffect)(() => {
    const index = offset ? Math.ceil(Number(offset) / _pageSize) : 0;
    if (index === pageIndex) {
      return;
    }
    setPagination((prev) => ({
      ...prev,
      pageIndex: index
    }));
  }, [offset, _pageSize, pageIndex]);
  const onPaginationChange = (updater) => {
    const state = updater(pagination);
    const { pageIndex: pageIndex2, pageSize: pageSize2 } = state;
    setSearchParams((prev) => {
      if (!pageIndex2) {
        prev.delete(offsetKey);
        return prev;
      }
      const newSearch = new URLSearchParams(prev);
      newSearch.set(offsetKey, String(pageIndex2 * pageSize2));
      return newSearch;
    });
    setPagination(state);
    return state;
  };
  const table = useReactTable({
    data,
    columns: [],
    // We don't actually want to render any columns
    pageCount: Math.ceil(count / pageSize),
    state: {
      pagination
    },
    getCoreRowModel: getCoreRowModel(),
    onPaginationChange,
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true
  });
  return {
    table
  };
};

export {
  TaxRateLine,
  TaxOverrideTable,
  useTaxOverrideTable
};
//# sourceMappingURL=chunk-GJUNXSH3.js.map
