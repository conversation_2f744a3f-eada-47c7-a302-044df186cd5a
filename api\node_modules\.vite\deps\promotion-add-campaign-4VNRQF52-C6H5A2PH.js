import {
  AddCampaignPromotionForm
} from "./chunk-7V4J6PB2.js";
import "./chunk-2T4FRC44.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import {
  RouteDrawer
} from "./chunk-XMQMXYDG.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-DPO7J5IQ.js";
import {
  useCampaigns,
  usePromotion
} from "./chunk-S32V3COL.js";
import "./chunk-RWC53K5F.js";
import "./chunk-662EXSHO.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Heading
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-add-campaign-4VNRQF52.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PromotionAddCampaign = () => {
  var _a, _b;
  const { id } = useParams();
  const { t } = useTranslation();
  const { promotion, isPending, isError, error } = usePromotion(id);
  let campaignQuery = {};
  if ((_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.currency_code) {
    campaignQuery = {
      budget: {
        currency_code: (_b = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _b.currency_code
      }
    };
  }
  const {
    campaigns,
    isPending: areCampaignsLoading,
    isError: isCampaignError,
    error: campaignError
  } = useCampaigns(campaignQuery);
  if (isError || isCampaignError) {
    throw error || campaignError;
  }
  return (0, import_jsx_runtime.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime.jsx)(Heading, { children: t("promotions.campaign.edit.header") }) }),
    !isPending && !areCampaignsLoading && promotion && campaigns && (0, import_jsx_runtime.jsx)(AddCampaignPromotionForm, { promotion, campaigns })
  ] });
};
export {
  PromotionAddCampaign as Component
};
//# sourceMappingURL=promotion-add-campaign-4VNRQF52-C6H5A2PH.js.map
