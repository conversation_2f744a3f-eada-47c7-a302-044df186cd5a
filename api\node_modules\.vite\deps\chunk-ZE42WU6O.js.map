{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-TYTNUPXB.mjs"], "sourcesContent": ["// src/components/common/file-upload/file-upload.tsx\nimport { ArrowDownTray } from \"@medusajs/icons\";\nimport { Text, clx } from \"@medusajs/ui\";\nimport { useRef, useState } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar FileUpload = ({\n  label,\n  hint,\n  multiple = true,\n  hasError,\n  formats,\n  onUploaded\n}) => {\n  const [isDragOver, setIsDragOver] = useState(false);\n  const inputRef = useRef(null);\n  const dropZoneRef = useRef(null);\n  const handleOpenFileSelector = () => {\n    inputRef.current?.click();\n  };\n  const handleDragEnter = (event) => {\n    event.preventDefault();\n    event.stopPropagation();\n    const files = event.dataTransfer?.files;\n    if (!files) {\n      return;\n    }\n    setIsDragOver(true);\n  };\n  const handleDragLeave = (event) => {\n    event.preventDefault();\n    event.stopPropagation();\n    if (!dropZoneRef.current || dropZoneRef.current.contains(event.relatedTarget)) {\n      return;\n    }\n    setIsDragOver(false);\n  };\n  const handleUploaded = (files) => {\n    if (!files) {\n      return;\n    }\n    const fileList = Array.from(files);\n    const fileObj = fileList.map((file) => {\n      const id = Math.random().toString(36).substring(7);\n      const previewUrl = URL.createObjectURL(file);\n      return {\n        id,\n        url: previewUrl,\n        file\n      };\n    });\n    onUploaded(fileObj);\n  };\n  const handleDrop = (event) => {\n    event.preventDefault();\n    event.stopPropagation();\n    setIsDragOver(false);\n    handleUploaded(event.dataTransfer?.files);\n  };\n  const handleFileChange = async (event) => {\n    handleUploaded(event.target.files);\n  };\n  return /* @__PURE__ */ jsxs(\"div\", { children: [\n    /* @__PURE__ */ jsxs(\n      \"button\",\n      {\n        ref: dropZoneRef,\n        type: \"button\",\n        onClick: handleOpenFileSelector,\n        onDrop: handleDrop,\n        onDragOver: (e) => e.preventDefault(),\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        className: clx(\n          \"bg-ui-bg-component border-ui-border-strong transition-fg group flex w-full flex-col items-center gap-y-2 rounded-lg border border-dashed p-8\",\n          \"hover:border-ui-border-interactive focus:border-ui-border-interactive\",\n          \"focus:shadow-borders-focus outline-none focus:border-solid\",\n          {\n            \"!border-ui-border-error\": hasError,\n            \"!border-ui-border-interactive\": isDragOver\n          }\n        ),\n        children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle group-disabled:text-ui-fg-disabled flex items-center gap-x-2\", children: [\n            /* @__PURE__ */ jsx(ArrowDownTray, {}),\n            /* @__PURE__ */ jsx(Text, { children: label })\n          ] }),\n          !!hint && /* @__PURE__ */ jsx(\n            Text,\n            {\n              size: \"small\",\n              leading: \"compact\",\n              className: \"text-ui-fg-muted group-disabled:text-ui-fg-disabled\",\n              children: hint\n            }\n          )\n        ]\n      }\n    ),\n    /* @__PURE__ */ jsx(\n      \"input\",\n      {\n        hidden: true,\n        ref: inputRef,\n        onChange: handleFileChange,\n        type: \"file\",\n        accept: formats.join(\",\"),\n        multiple\n      }\n    )\n  ] });\n};\n\nexport {\n  FileUpload\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,mBAAiC;AACjC,yBAA0B;AAC1B,IAAI,aAAa,CAAC;AAAA,EAChB;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAClD,QAAM,eAAW,qBAAO,IAAI;AAC5B,QAAM,kBAAc,qBAAO,IAAI;AAC/B,QAAM,yBAAyB,MAAM;AAhBvC;AAiBI,mBAAS,YAAT,mBAAkB;AAAA,EACpB;AACA,QAAM,kBAAkB,CAAC,UAAU;AAnBrC;AAoBI,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,UAAM,SAAQ,WAAM,iBAAN,mBAAoB;AAClC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,kBAAc,IAAI;AAAA,EACpB;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,QAAI,CAAC,YAAY,WAAW,YAAY,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7E;AAAA,IACF;AACA,kBAAc,KAAK;AAAA,EACrB;AACA,QAAM,iBAAiB,CAAC,UAAU;AAChC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,WAAW,MAAM,KAAK,KAAK;AACjC,UAAM,UAAU,SAAS,IAAI,CAAC,SAAS;AACrC,YAAM,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AACjD,YAAM,aAAa,IAAI,gBAAgB,IAAI;AAC3C,aAAO;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAC;AACD,eAAW,OAAO;AAAA,EACpB;AACA,QAAM,aAAa,CAAC,UAAU;AApDhC;AAqDI,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,kBAAc,KAAK;AACnB,oBAAe,WAAM,iBAAN,mBAAoB,KAAK;AAAA,EAC1C;AACA,QAAM,mBAAmB,OAAO,UAAU;AACxC,mBAAe,MAAM,OAAO,KAAK;AAAA,EACnC;AACA,aAAuB,yBAAK,OAAO,EAAE,UAAU;AAAA,QAC7B;AAAA,MACd;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY,CAAC,MAAM,EAAE,eAAe;AAAA,QACpC,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,2BAA2B;AAAA,YAC3B,iCAAiC;AAAA,UACnC;AAAA,QACF;AAAA,QACA,UAAU;AAAA,cACQ,yBAAK,OAAO,EAAE,WAAW,kFAAkF,UAAU;AAAA,gBACnH,wBAAI,eAAe,CAAC,CAAC;AAAA,gBACrB,wBAAI,MAAM,EAAE,UAAU,MAAM,CAAC;AAAA,UAC/C,EAAE,CAAC;AAAA,UACH,CAAC,CAAC,YAAwB;AAAA,YACxB;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,cACX,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,QACgB;AAAA,MACd;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ,QAAQ,KAAK,GAAG;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": []}