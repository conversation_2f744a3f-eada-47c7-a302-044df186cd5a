{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-list-VNZUNUOQ.mjs"], "sourcesContent": ["import {\n  useCustomerTableColumns\n} from \"./chunk-DT7QVGFJ.mjs\";\nimport {\n  useCustomerTableQuery\n} from \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-3OHUAQUF.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useCustomerTableFilters\n} from \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  useCustomers\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/customers/customer-list/components/customer-list-table/customer-list-table.tsx\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { Button, Container, Heading } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar CustomerListTable = () => {\n  const { t } = useTranslation();\n  const { searchParams, raw } = useCustomerTableQuery({ pageSize: PAGE_SIZE });\n  const { customers, count, isLoading, isError, error } = useCustomers(\n    {\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useCustomerTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: customers ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Heading, { children: t(\"customers.domain\") }),\n      /* @__PURE__ */ jsx(Link, { to: \"/customers/create\", children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        filters,\n        orderBy: [\n          { key: \"email\", label: t(\"fields.email\") },\n          { key: \"first_name\", label: t(\"fields.firstName\") },\n          { key: \"last_name\", label: t(\"fields.lastName\") },\n          { key: \"has_account\", label: t(\"customers.hasAccount\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        isLoading,\n        navigateTo: (row) => row.original.id,\n        search: true,\n        queryObject: raw,\n        noRecords: {\n          message: t(\"customers.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar CustomerActions = ({\n  customer\n}) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `/customers/${customer.id}/edit`\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const columns = useCustomerTableColumns();\n  return useMemo(\n    () => [\n      ...columns,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx(CustomerActions, { customer: row.original })\n      })\n    ],\n    [columns]\n  );\n};\n\n// src/routes/customers/customer-list/customer-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomersList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"customer.list.after\"),\n        before: getWidgets(\"customer.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(CustomerListTable, {})\n    }\n  );\n};\nexport {\n  CustomersList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,mBAAwB;AAGxB,yBAA0B;AA+F1B,IAAAA,sBAA4B;AA9F5B,IAAI,YAAY;AAChB,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,IAAI,sBAAsB,EAAE,UAAU,UAAU,CAAC;AAC3E,QAAM,EAAE,WAAW,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,wBAAwB;AACxC,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,aAAa,CAAC;AAAA,IACpB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,wBAAI,SAAS,EAAE,UAAU,EAAE,kBAAkB,EAAE,CAAC;AAAA,UAChD,wBAAI,MAAM,EAAE,IAAI,qBAAqB,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IACtK,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,aAAa,OAAO,EAAE,iBAAiB,EAAE;AAAA,UAChD,EAAE,KAAK,eAAe,OAAO,EAAE,sBAAsB,EAAE;AAAA,UACvD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA;AAAA,QACA,YAAY,CAAC,QAAQ,IAAI,SAAS;AAAA,QAClC,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,iCAAiC;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,cAAc,SAAS,EAAE;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,UAAU,wBAAwB;AACxC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,wBAAI,iBAAiB,EAAE,UAAU,IAAI,SAAS,CAAC;AAAA,MACpF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AACF;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,qBAAqB;AAAA,QACvC,QAAQ,WAAW,sBAAsB;AAAA,MAC3C;AAAA,MACA,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}