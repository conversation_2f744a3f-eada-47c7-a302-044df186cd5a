{"version": 3, "sources": ["../../@medusajs/dashboard/dist/store-metadata-GLAZZPP6.mjs"], "sourcesContent": ["import \"./chunk-XRTVFYCW.mjs\";\nimport {\n  MetadataForm\n} from \"./chunk-AL4WDQTN.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useStore,\n  useUpdateStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/store/store-metadata/store-metadata.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar StoreMetadata = () => {\n  const { store, isPending, isError, error } = useStore();\n  const { mutateAsync, isPending: isMutating } = useUpdateStore(store?.id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteDrawer, { children: /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      isPending,\n      isMutating,\n      hook: mutateAsync,\n      metadata: store?.metadata\n    }\n  ) });\n};\nexport {\n  StoreMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,yBAAoB;AACpB,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,OAAO,WAAW,SAAS,MAAM,IAAI,SAAS;AACtD,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,eAAe,+BAAO,EAAE;AACvE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,aAAa,EAAE,cAA0B;AAAA,IAClE;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,+BAAO;AAAA,IACnB;AAAA,EACF,EAAE,CAAC;AACL;", "names": []}