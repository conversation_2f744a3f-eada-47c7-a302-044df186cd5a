import {
  PriceListCreateProductsSchema,
  PriceListRulesSchema,
  usePriceListCurrencyData,
  usePriceListGridColumns
} from "./chunk-4YLIPT6L.js";
import {
  exctractPricesFromProducts,
  isProductRow
} from "./chunk-DC7EOWEK.js";
import "./chunk-EG6IR476.js";
import {
  PriceListCustomerGroupRuleForm
} from "./chunk-WV6RFGPW.js";
import "./chunk-RYLAHPUE.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-7HUCBNCQ.js";
import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-EUULPFXI.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  DataGrid
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-XNFM7P3M.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-XYBN3KRC.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import {
  useCreatePriceList
} from "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Heading,
  IconButton,
  Input,
  MagnifyingGlass,
  ProgressTabs,
  RadioGroup,
  Select,
  Text,
  Textarea,
  XMarkMini,
  clx,
  createColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-create-AFGEP5GO.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var PriceListDetailsForm = ({ form }) => {
  const { t: t2 } = useTranslation();
  const { fields, remove, append } = useFieldArray({
    control: form.control,
    name: "rules.customer_group_id",
    keyName: "cg_id"
  });
  const { setIsOpen } = useStackedModal();
  const handleAddCustomerGroup = (groups) => {
    const newIds = groups.map((group) => group.id);
    const fieldsToAdd = groups.filter(
      (group) => !fields.some((field) => field.id === group.id)
    );
    for (const field of fields) {
      if (!newIds.includes(field.id)) {
        remove(fields.indexOf(field));
      }
    }
    append(fieldsToAdd);
    setIsOpen("cg", false);
  };
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-8 py-16", children: [
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("priceLists.create.header") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("priceLists.create.subheader") })
    ] }),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "type",
        render: ({ field: { onChange, ...rest } }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
              (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("priceLists.fields.type.label") }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.type.hint") })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                RadioGroup,
                {
                  onValueChange: onChange,
                  ...rest,
                  className: "grid grid-cols-1 gap-4 md:grid-cols-2",
                  children: [
                    (0, import_jsx_runtime.jsx)(
                      RadioGroup.ChoiceBox,
                      {
                        value: "sale",
                        label: t2("priceLists.fields.type.options.sale.label"),
                        description: t2(
                          "priceLists.fields.type.options.sale.description"
                        )
                      }
                    ),
                    (0, import_jsx_runtime.jsx)(
                      RadioGroup.ChoiceBox,
                      {
                        value: "override",
                        label: t2(
                          "priceLists.fields.type.options.override.label"
                        ),
                        description: t2(
                          "priceLists.fields.type.options.override.description"
                        )
                      }
                    )
                  ]
                }
              ) })
            ] }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1  gap-4 md:grid-cols-2", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "title",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "status",
            render: ({ field: { onChange, ref, ...field } }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("priceLists.fields.status.label") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                  (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                  (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "active", children: t2("priceLists.fields.status.options.active") }),
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "draft", children: t2("priceLists.fields.status.options.draft") })
                  ] })
                ] }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "description",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.description") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)(Divider, {}),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "starts_at",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-3 md:grid-cols-2", children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.startsAt.label") }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.startsAt.hint") })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                DatePicker,
                {
                  granularity: "minute",
                  shouldCloseOnSelect: false,
                  ...field
                }
              ) })
            ] }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsx)(Divider, {}),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "ends_at",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-3 md:grid-cols-2", children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.endsAt.label") }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.endsAt.hint") })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                DatePicker,
                {
                  granularity: "minute",
                  shouldCloseOnSelect: false,
                  ...field
                }
              ) })
            ] }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsx)(Divider, {}),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "rules.customer_group_id",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsxs)("div", { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("priceLists.fields.customerAvailability.label") }),
              (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.customerAvailability.hint") })
            ] }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
              "div",
              {
                className: clx(
                  "bg-ui-bg-component shadow-elevation-card-rest transition-fg grid gap-1.5 rounded-xl py-1.5",
                  "aria-[invalid='true']:shadow-borders-error"
                ),
                role: "application",
                ref: field.ref,
                children: [
                  (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid gap-1.5 px-1.5 md:grid-cols-2", children: [
                    (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: t2("priceLists.fields.customerAvailability.attribute") }),
                    (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: t2("operators.in") })
                  ] }),
                  (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1.5 px-1.5", children: (0, import_jsx_runtime.jsxs)(StackedFocusModal, { id: "cg", children: [
                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsxs)(
                      "button",
                      {
                        type: "button",
                        className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover shadow-borders-base txt-compact-small text-ui-fg-muted transition-fg focus-visible:shadow-borders-interactive-with-active flex flex-1 items-center gap-x-2 rounded-md px-2 py-1.5 outline-none",
                        children: [
                          (0, import_jsx_runtime.jsx)(MagnifyingGlass, {}),
                          t2(
                            "priceLists.fields.customerAvailability.placeholder"
                          )
                        ]
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", children: t2("actions.browse") }) }),
                    (0, import_jsx_runtime.jsxs)(StackedFocusModal.Content, { children: [
                      (0, import_jsx_runtime.jsx)(StackedFocusModal.Header, {}),
                      (0, import_jsx_runtime.jsx)(
                        PriceListCustomerGroupRuleForm,
                        {
                          state: fields,
                          setState: handleAddCustomerGroup,
                          type: "focus"
                        }
                      )
                    ] })
                  ] }) }),
                  fields.length > 0 ? (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1.5", children: [
                    (0, import_jsx_runtime.jsx)(Divider, { variant: "dashed" }),
                    (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-1.5 px-1.5", children: fields.map((field2, index) => {
                      return (0, import_jsx_runtime.jsxs)(
                        "div",
                        {
                          className: "bg-ui-bg-field-component shadow-borders-base flex items-center justify-between gap-2 rounded-md px-2 py-0.5",
                          children: [
                            (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: field2.name }),
                            (0, import_jsx_runtime.jsx)(
                              IconButton,
                              {
                                size: "small",
                                variant: "transparent",
                                type: "button",
                                onClick: () => remove(index),
                                children: (0, import_jsx_runtime.jsx)(XMarkMini, {})
                              }
                            )
                          ]
                        },
                        field2.cg_id
                      );
                    }) })
                  ] }) : null
                ]
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    )
  ] }) });
};
var PriceListPricesForm = ({
  form,
  currencies,
  regions,
  pricePreferences
}) => {
  const ids = useWatch({
    control: form.control,
    name: "product_ids"
  });
  const existingProducts = useWatch({
    control: form.control,
    name: "products"
  });
  const { products, isLoading, isError, error } = useProducts({
    id: ids.map((id) => id.id),
    limit: ids.length,
    fields: "title,thumbnail,*variants"
  });
  const { setCloseOnEscape } = useRouteModal();
  const { setValue } = form;
  (0, import_react2.useEffect)(() => {
    if (!isLoading && products) {
      products.forEach((product) => {
        if (existingProducts[product.id] || !product.variants) {
          return;
        }
        setValue(`products.${product.id}.variants`, {
          ...product.variants.reduce((variants, variant) => {
            variants[variant.id] = {
              currency_prices: {},
              region_prices: {}
            };
            return variants;
          }, {})
        });
      });
    }
  }, [products, existingProducts, isLoading, setValue]);
  const columns = usePriceListGridColumns({
    currencies,
    regions,
    pricePreferences
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col divide-y overflow-hidden", children: (0, import_jsx_runtime2.jsx)(
    DataGrid,
    {
      isLoading,
      columns,
      data: products,
      getSubRows: (row) => {
        if (isProductRow(row) && row.variants) {
          return row.variants;
        }
      },
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  ) });
};
var PAGE_SIZE = 50;
var PREFIX = "p";
function getInitialSelection(products) {
  return products.reduce((acc, curr) => {
    acc[curr.id] = true;
    return acc;
  }, {});
}
var PriceListProductsForm = ({ form }) => {
  const { t: t2 } = useTranslation();
  const { control, setValue } = form;
  const selectedIds = useWatch({
    control,
    name: "product_ids"
  });
  const productRecords = useWatch({
    control,
    name: "products"
  });
  const [rowSelection, setRowSelection] = (0, import_react3.useState)(
    getInitialSelection(selectedIds)
  );
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { products, count, isLoading, isError, error } = useProducts(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (fn) => {
    const state = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(state);
    const productRecordKeys = Object.keys(productRecords);
    const updatedRecords = productRecordKeys.reduce((acc, key) => {
      if (ids.includes(key)) {
        acc[key] = productRecords[key];
      }
      return acc;
    }, {});
    const update = ids.map((id) => ({ id }));
    setValue("product_ids", update, { shouldDirty: true, shouldTouch: true });
    setValue("products", updatedRecords, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(state);
  };
  const columns = useColumns();
  const filters = useProductTableFilters();
  const { table } = useDataTable({
    data: products || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: (row) => {
      var _a;
      return !!((_a = row.original.variants) == null ? void 0 : _a.length);
    },
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex size-full flex-col", children: (0, import_jsx_runtime3.jsx)(
    _DataTable,
    {
      table,
      columns,
      filters,
      pageSize: PAGE_SIZE,
      prefix: PREFIX,
      count,
      isLoading,
      layout: "fill",
      orderBy: [
        { key: "title", label: t2("fields.title") },
        { key: "status", label: t2("fields.status") },
        { key: "created_at", label: t2("fields.createdAt") },
        { key: "updated_at", label: t2("fields.updatedAt") }
      ],
      pagination: true,
      search: true,
      queryObject: raw,
      noRecords: {
        message: t2("priceLists.create.products.list.noRecordsMessage")
      }
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTableColumns();
  return (0, import_react3.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime3.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime3.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var PricingCustomerGroupsArray = z.array(
  z.object({
    id: z.string(),
    name: z.string()
  })
);
var PricingCreateSchema = z.object({
  type: z.enum(["sale", "override"]),
  status: z.enum(["draft", "active"]),
  title: z.string().min(1),
  description: z.string().min(1),
  starts_at: z.date().nullish(),
  ends_at: z.date().nullish(),
  product_ids: z.array(z.object({ id: z.string() })).min(1),
  products: PriceListCreateProductsSchema,
  rules: PriceListRulesSchema.nullish()
});
var PricingDetailsSchema = PricingCreateSchema.pick({
  type: true,
  title: true,
  description: true,
  starts_at: true,
  ends_at: true,
  customer_group_ids: true
});
var PricingDetailsFields = Object.keys(
  PricingDetailsSchema.shape
);
var PricingProductsSchema = PricingCreateSchema.pick({
  product_ids: true
});
var PricingProductsFields = Object.keys(
  PricingProductsSchema.shape
);
var PricingPricesSchema = PricingCreateSchema.pick({
  products: true
});
var PricingPricesFields = Object.keys(
  PricingPricesSchema.shape
);
var tabOrder = [
  "detail",
  "product",
  "price"
  /* PRICE */
];
var initialTabState = {
  [
    "detail"
    /* DETAIL */
  ]: "in-progress",
  [
    "product"
    /* PRODUCT */
  ]: "not-started",
  [
    "price"
    /* PRICE */
  ]: "not-started"
};
var PriceListCreateForm = ({
  regions,
  currencies,
  pricePreferences
}) => {
  const [tab, setTab] = (0, import_react.useState)(
    "detail"
    /* DETAIL */
  );
  const [tabState, setTabState] = (0, import_react.useState)(initialTabState);
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      type: "sale",
      status: "active",
      title: "",
      description: "",
      starts_at: null,
      ends_at: null,
      product_ids: [],
      products: {},
      rules: {
        customer_group_id: []
      }
    },
    resolver: t(PricingCreateSchema)
  });
  const { mutateAsync, isPending } = useCreatePriceList();
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a;
    const { rules, products } = data;
    const rulesPayload = ((_a = rules == null ? void 0 : rules.customer_group_id) == null ? void 0 : _a.length) ? { "customer.groups.id": rules.customer_group_id.map((cg) => cg.id) } : void 0;
    const prices = exctractPricesFromProducts(products, regions);
    await mutateAsync(
      {
        title: data.title,
        type: data.type,
        status: data.status,
        description: data.description,
        starts_at: data.starts_at ? data.starts_at.toISOString() : null,
        ends_at: data.ends_at ? data.ends_at.toISOString() : null,
        rules: rulesPayload,
        prices
      },
      {
        onSuccess: ({ price_list }) => {
          toast.success(
            t2("priceLists.create.successToast", {
              title: price_list.title
            })
          );
          handleSuccess(`../${price_list.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const partialFormValidation = (fields, schema) => {
    form.clearErrors(fields);
    const values = fields.reduce(
      (acc, key) => {
        acc[key] = form.getValues(key);
        return acc;
      },
      {}
    );
    const validationResult = schema.safeParse(values);
    if (!validationResult.success) {
      validationResult.error.errors.forEach(({ path, message, code }) => {
        form.setError(path.join("."), {
          type: code,
          message
        });
      });
      return false;
    }
    return true;
  };
  const isTabDirty = (tab2) => {
    switch (tab2) {
      case "detail": {
        const fields = PricingDetailsFields;
        return fields.some((field) => {
          return form.getFieldState(field).isDirty;
        });
      }
      case "product": {
        const fields = PricingProductsFields;
        return fields.some((field) => {
          return form.getFieldState(field).isDirty;
        });
      }
      case "price": {
        const fields = PricingPricesFields;
        return fields.some((field) => {
          return form.getFieldState(field).isDirty;
        });
      }
    }
  };
  const handleChangeTab = (update) => {
    if (tab === update) {
      return;
    }
    if (tabOrder.indexOf(update) < tabOrder.indexOf(tab)) {
      const isCurrentTabDirty = isTabDirty(tab);
      setTabState((prev) => ({
        ...prev,
        [tab]: isCurrentTabDirty ? prev[tab] : "not-started",
        [update]: "in-progress"
      }));
      setTab(update);
      return;
    }
    const tabs = tabOrder.slice(0, tabOrder.indexOf(update));
    for (const tab2 of tabs) {
      if (tab2 === "detail") {
        if (!partialFormValidation(PricingDetailsFields, PricingDetailsSchema)) {
          setTabState((prev) => ({
            ...prev,
            [tab2]: "in-progress"
          }));
          setTab(tab2);
          return;
        }
        setTabState((prev) => ({
          ...prev,
          [tab2]: "completed"
        }));
      } else if (tab2 === "product") {
        if (!partialFormValidation(PricingProductsFields, PricingProductsSchema)) {
          setTabState((prev) => ({
            ...prev,
            [tab2]: "in-progress"
          }));
          setTab(tab2);
          return;
        }
        setTabState((prev) => ({
          ...prev,
          [tab2]: "completed"
        }));
      }
    }
    setTabState((prev) => ({
      ...prev,
      [tab]: "completed",
      [update]: "in-progress"
    }));
    setTab(update);
  };
  const handleNextTab = (tab2) => {
    if (tabOrder.indexOf(tab2) + 1 >= tabOrder.length) {
      return;
    }
    const nextTab = tabOrder[tabOrder.indexOf(tab2) + 1];
    handleChangeTab(nextTab);
  };
  return (0, import_jsx_runtime4.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime4.jsx)(
    ProgressTabs,
    {
      value: tab,
      onValueChange: (tab2) => handleChangeTab(tab2),
      className: "flex h-full flex-col overflow-hidden",
      children: (0, import_jsx_runtime4.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
        (0, import_jsx_runtime4.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime4.jsx)("div", { className: "flex w-full items-center justify-between gap-x-4", children: (0, import_jsx_runtime4.jsx)("div", { className: "-my-2 w-full max-w-[600px] border-l", children: (0, import_jsx_runtime4.jsxs)(ProgressTabs.List, { className: "grid w-full grid-cols-3", children: [
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Trigger,
            {
              status: tabState.detail,
              value: "detail",
              children: t2("priceLists.create.tabs.details")
            }
          ),
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Trigger,
            {
              status: tabState.product,
              value: "product",
              children: t2("priceLists.create.tabs.products")
            }
          ),
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Trigger,
            {
              status: tabState.price,
              value: "price",
              children: t2("priceLists.create.tabs.prices")
            }
          )
        ] }) }) }) }),
        (0, import_jsx_runtime4.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Content,
            {
              className: "size-full overflow-y-auto",
              value: "detail",
              children: (0, import_jsx_runtime4.jsx)(PriceListDetailsForm, { form })
            }
          ),
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Content,
            {
              className: "size-full overflow-y-auto",
              value: "product",
              children: (0, import_jsx_runtime4.jsx)(PriceListProductsForm, { form })
            }
          ),
          (0, import_jsx_runtime4.jsx)(
            ProgressTabs.Content,
            {
              className: "size-full overflow-hidden",
              value: "price",
              children: (0, import_jsx_runtime4.jsx)(
                PriceListPricesForm,
                {
                  form,
                  regions,
                  currencies,
                  pricePreferences
                }
              )
            }
          )
        ] }),
        (0, import_jsx_runtime4.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime4.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime4.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime4.jsx)(
            PrimaryButton,
            {
              tab,
              next: handleNextTab,
              isLoading: isPending
            }
          )
        ] }) })
      ] })
    }
  ) });
};
var PrimaryButton = ({ tab, next, isLoading }) => {
  const { t: t2 } = useTranslation();
  if (tab === "price") {
    return (0, import_jsx_runtime4.jsx)(
      Button,
      {
        type: "submit",
        variant: "primary",
        size: "small",
        isLoading,
        children: t2("actions.save")
      },
      "submit-button"
    );
  }
  return (0, import_jsx_runtime4.jsx)(
    Button,
    {
      type: "button",
      variant: "primary",
      size: "small",
      onClick: () => next(tab),
      children: t2("actions.continue")
    },
    "next-button"
  );
};
var PriceListCreate = () => {
  const { isReady, regions, currencies, pricePreferences } = usePriceListCurrencyData();
  return (0, import_jsx_runtime5.jsx)(RouteFocusModal, { children: isReady && (0, import_jsx_runtime5.jsx)(
    PriceListCreateForm,
    {
      regions,
      currencies,
      pricePreferences
    }
  ) });
};
export {
  PriceListCreate as Component
};
//# sourceMappingURL=price-list-create-AFGEP5GO-UEU6YVZA.js.map
