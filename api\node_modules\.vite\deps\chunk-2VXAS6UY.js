import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  json
} from "./chunk-T7YBVUWZ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NOAFLTPV.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var acceptedOrderKeys = ["name", "code"];
var useCountries = ({
  countries,
  q,
  order = "name",
  limit,
  offset = 0
}) => {
  const data = countries.slice(offset, offset + limit);
  if (order) {
    const direction = order.startsWith("-") ? -1 : 1;
    const key = order.replace("-", "");
    if (!acceptedOrderKeys.includes(key)) {
      console.log("The key ${key} is not a valid order key");
      throw json(`The key ${key} is not a valid order key`, 500);
    }
    const sortKey = key === "code" ? "iso_2" : "name";
    data.sort((a, b) => {
      if (a[sortKey] === null && b[sortKey] === null) {
        return 0;
      }
      if (a[sortKey] === null) {
        return direction;
      }
      if (b[sortKey] === null) {
        return -direction;
      }
      return a[sortKey] > b[sortKey] ? direction : -direction;
    });
  }
  if (q) {
    const query = q.toLowerCase();
    const results = countries.filter(
      (c) => c.name.toLowerCase().includes(query) || c.iso_2.toLowerCase().includes(query)
    );
    return {
      countries: results,
      count: results.length
    };
  }
  return {
    countries: data,
    count: countries.length
  };
};
var columnHelper = createColumnHelper();
var useCountryTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("display_name", {
        header: t("fields.name"),
        cell: ({ getValue }) => getValue()
      }),
      columnHelper.accessor("iso_2", {
        header: t("fields.code"),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)("span", { className: "uppercase", children: getValue() })
      })
    ],
    [t]
  );
};
var useCountryTableQuery = ({
  pageSize,
  prefix
}) => {
  const raw = useQueryParams(["order", "q", "offset"], prefix);
  const { offset, order, q } = raw;
  const searchParams = {
    limit: pageSize,
    offset: offset ? parseInt(offset, 10) : 0,
    order,
    q
  };
  return {
    searchParams,
    raw
  };
};

export {
  useCountries,
  useCountryTableColumns,
  useCountryTableQuery
};
//# sourceMappingURL=chunk-2VXAS6UY.js.map
