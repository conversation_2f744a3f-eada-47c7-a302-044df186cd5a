import { notFound } from "next/navigation"
import { Suspense } from "react"

import { HttpTypes } from "@medusajs/types"
import ItemsTemplate from "@modules/cart/templates/items"
import AutoScroll from "@modules/categories/components/auto-scroll"
import FilterSection from "@modules/collections/components/filter-section"
import SkeletonProductGrid from "@modules/skeletons/templates/skeleton-product-grid"
import { SortOptions } from "@modules/store/components/refinement-list/sort-products"
import PaginatedProducts from "@modules/store/templates/paginated-products"
import Image from "next/image"
import { TCartCustomField } from "types/cart"
import { ICategoryListWithMetadata } from "types/category"
import ErrorBoundary from "../../../components/error-boundary"
import { validatePageNumber } from "../../../lib/utils/error-handler"
import CategoryHeader from "./category-header"

// Client component for translated category header

export default function CategoryTemplate({
  category,
  sortBy,
  page,
  countryCode,
  categoryList,
  cart,
  priceRange,
}: {
  category: HttpTypes.StoreProductCategory
  sortBy?: SortOptions
  page?: string
  countryCode: string
  categoryList: ICategoryListWithMetadata[]
  cart?: TCartCustomField | null
  priceRange?: string
}) {
  // Validate and sanitize page number using utility function
  const pageNumber = validatePageNumber(page)
  const sort = sortBy || "created_at"

  if (!category || !countryCode) notFound()

  const parents = [] as HttpTypes.StoreProductCategory[]

  const getParents = (category: HttpTypes.StoreProductCategory) => {
    if (category.parent_category) {
      parents.push(category.parent_category)
      getParents(category.parent_category)
    }
  }

  getParents(category)

  return (
    <div className="flex flex-col" data-testid="category-container">
      <AutoScroll targetId="category-content" offset={170} />
      <Image
        src={"/images/product/product-banner.png"}
        alt="product-banner"
        width={0}
        height={0}
        className="h-full w-full object-cover"
        sizes="100vw"
      />
      {/* <RefinementList sortBy={sort} data-testid="sort-by-container" /> */}
      <div id="category-content">
        <CategoryHeader category={category} />
      </div>
      <div className="content-container grid w-full grid-cols-1 gap-x-8 py-10 md:grid-cols-12 lg:flex">
        <div className="md:col-span-7 lg:flex-1 xl:col-span-9">
          <div className="flex h-fit justify-end">
            <FilterSection
              categoryList={categoryList}
              // regions={region}
              handle={category.handle}
            />
          </div>
          <ErrorBoundary>
            <Suspense
              fallback={
                <SkeletonProductGrid
                  numberOfProducts={category.products?.length ?? 8}
                />
              }
            >
              <PaginatedProducts
                sortBy={sort}
                page={pageNumber}
                categoryId={category.id}
                countryCode={countryCode}
              />
            </Suspense>
          </ErrorBoundary>
        </div>
        <div className="mt-4 md:col-span-5 md:mt-0 xl:col-span-3">
          <ItemsTemplate cart={cart as any} isCard />
        </div>
      </div>
    </div>
  )
}
