{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-create-shipment-FQNPWRSW.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport {\n  useCreateOrderShipment,\n  useOrder\n} from \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/orders/order-create-shipment/order-create-shipment.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/orders/order-create-shipment/components/order-create-shipment-form/order-create-shipment-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useTranslation } from \"react-i18next\";\nimport { Button, Heading, Input, Switch, toast } from \"@medusajs/ui\";\nimport { useFieldArray, useForm } from \"react-hook-form\";\n\n// src/routes/orders/order-create-shipment/components/order-create-shipment-form/constants.ts\nimport { z } from \"zod\";\nvar CreateShipmentSchema = z.object({\n  labels: z.array(\n    z.object({\n      tracking_number: z.string(),\n      // TODO: this 2 are not optional in the API\n      tracking_url: z.string().optional(),\n      label_url: z.string().optional()\n    })\n  ),\n  send_notification: z.boolean().optional()\n});\n\n// src/routes/orders/order-create-shipment/components/order-create-shipment-form/order-create-shipment-form.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction OrderCreateShipmentForm({\n  order,\n  fulfillment\n}) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { mutateAsync: createShipment, isPending: isMutating } = useCreateOrderShipment(order.id, fulfillment?.id);\n  const form = useForm({\n    defaultValues: {\n      send_notification: !order.no_notification\n    },\n    resolver: zodResolver(CreateShipmentSchema)\n  });\n  const { fields: labels, append } = useFieldArray({\n    name: \"labels\",\n    control: form.control\n  });\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const addedLabels = data.labels.filter((l) => !!l.tracking_number).map((l) => ({\n      tracking_number: l.tracking_number,\n      tracking_url: \"#\",\n      label_url: \"#\"\n    }));\n    await createShipment(\n      {\n        items: fulfillment?.items?.map((i) => ({\n          id: i.line_item_id,\n          quantity: i.quantity\n        })),\n        labels: [...addedLabels, ...fulfillment?.labels || []],\n        no_notification: !data.send_notification\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"orders.shipment.toastCreated\"));\n          handleSuccess(`/orders/${order.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isMutating, children: t(\"actions.save\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex h-full w-full flex-col items-center divide-y overflow-y-auto\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full flex-col items-center overflow-auto p-16\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex w-full max-w-[736px] flex-col justify-center px-2 pb-2\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col divide-y\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-col\", children: [\n            /* @__PURE__ */ jsx(Heading, { className: \"mb-4\", children: t(\"orders.shipment.title\") }),\n            labels.map((label, index) => /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: `labels.${index}.tracking_number`,\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { className: \"mb-4\", children: [\n                    index === 0 && /* @__PURE__ */ jsx(Form.Label, { children: t(\"orders.shipment.trackingNumber\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field, placeholder: \"123-456-789\" }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              },\n              label.id\n            )),\n            /* @__PURE__ */ jsx(\n              Button,\n              {\n                type: \"button\",\n                onClick: () => append({ tracking_number: \"\" }),\n                className: \"self-end\",\n                variant: \"secondary\",\n                children: t(\"orders.shipment.addTracking\")\n              }\n            )\n          ] }),\n          /* @__PURE__ */ jsx(\"div\", { className: \"mt-8 pt-8 \", children: /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"send_notification\",\n              render: ({ field: { onChange, value, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"orders.shipment.sendNotification\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Switch,\n                      {\n                        checked: !!value,\n                        onCheckedChange: onChange,\n                        ...field\n                      }\n                    ) }) })\n                  ] }),\n                  /* @__PURE__ */ jsx(Form.Hint, { className: \"!mt-1\", children: t(\"orders.shipment.sendNotificationHint\") }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ) })\n        ] }) }) }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/orders/order-create-shipment/order-create-shipment.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction OrderCreateShipment() {\n  const { id, f_id } = useParams();\n  const { order, isLoading, isError, error } = useOrder(id, {\n    fields: \"*fulfillments,*fulfillments.items,*fulfillments.labels\"\n  });\n  if (isError) {\n    throw error;\n  }\n  const ready = !isLoading && order;\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(\n    OrderCreateShipmentForm,\n    {\n      order,\n      fulfillment: order.fulfillments?.find((f) => f.id === f_id)\n    }\n  ) });\n}\nexport {\n  OrderCreateShipment as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,yBAA0B;AAkH1B,IAAAA,sBAA4B;AA/H5B,IAAI,uBAAuB,EAAE,OAAO;AAAA,EAClC,QAAQ,EAAE;AAAA,IACR,EAAE,OAAO;AAAA,MACP,iBAAiB,EAAE,OAAO;AAAA;AAAA,MAE1B,cAAc,EAAE,OAAO,EAAE,SAAS;AAAA,MAClC,WAAW,EAAE,OAAO,EAAE,SAAS;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,EAAE,QAAQ,EAAE,SAAS;AAC1C,CAAC;AAID,SAAS,wBAAwB;AAAA,EAC/B;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,aAAa,gBAAgB,WAAW,WAAW,IAAI,uBAAuB,MAAM,IAAI,2CAAa,EAAE;AAC/G,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,mBAAmB,CAAC,MAAM;AAAA,IAC5B;AAAA,IACA,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,EAAE,QAAQ,QAAQ,OAAO,IAAI,cAAc;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AAxFzD;AAyFI,UAAM,cAAc,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,OAAO;AAAA,MAC7E,iBAAiB,EAAE;AAAA,MACnB,cAAc;AAAA,MACd,WAAW;AAAA,IACb,EAAE;AACF,UAAM;AAAA,MACJ;AAAA,QACE,QAAO,gDAAa,UAAb,mBAAoB,IAAI,CAAC,OAAO;AAAA,UACrC,IAAI,EAAE;AAAA,UACN,UAAU,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,CAAC,GAAG,aAAa,IAAG,2CAAa,WAAU,CAAC,CAAC;AAAA,QACrD,iBAAiB,CAAC,KAAK;AAAA,MACzB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,8BAA8B,CAAC;AAC/C,wBAAc,WAAW,MAAM,EAAE,EAAE;AAAA,QACrC;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,YAAY,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACnH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,qEAAqE,cAA0B,wBAAI,OAAO,EAAE,WAAW,2DAA2D,cAA0B,wBAAI,OAAO,EAAE,WAAW,+DAA+D,cAA0B,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,cAC/Z,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,gBACzD,wBAAI,SAAS,EAAE,WAAW,QAAQ,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,YACxF,OAAO,IAAI,CAAC,OAAO,cAA0B;AAAA,cAC3C,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM,UAAU,KAAK;AAAA,gBACrB,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,QAAQ,UAAU;AAAA,oBACpE,UAAU,SAAqB,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gCAAgC,EAAE,CAAC;AAAA,wBAChF,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,OAAO,aAAa,cAAc,CAAC,EAAE,CAAC;AAAA,wBACpG,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,cACA,MAAM;AAAA,YACR,CAAC;AAAA,gBACe;AAAA,cACd;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,SAAS,MAAM,OAAO,EAAE,iBAAiB,GAAG,CAAC;AAAA,gBAC7C,WAAW;AAAA,gBACX,SAAS;AAAA,gBACT,UAAUA,GAAE,6BAA6B;AAAA,cAC3C;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,OAAO,EAAE,WAAW,cAAc,cAA0B;AAAA,YAC9E,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,wBACtE,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,wBACnE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC1G;AAAA,sBACA;AAAA,wBACE,SAAS,CAAC,CAAC;AAAA,wBACX,iBAAiB;AAAA,wBACjB,GAAG;AAAA,sBACL;AAAA,oBACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,MAAM,EAAE,WAAW,SAAS,UAAUA,GAAE,sCAAsC,EAAE,CAAC;AAAA,sBAC1F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,SAAS,sBAAsB;AAzL/B;AA0LE,QAAM,EAAE,IAAI,KAAK,IAAI,UAAU;AAC/B,QAAM,EAAE,OAAO,WAAW,SAAS,MAAM,IAAI,SAAS,IAAI;AAAA,IACxD,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,QAAM,QAAQ,CAAC,aAAa;AAC5B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA;AAAA,IAChF;AAAA,IACA;AAAA,MACE;AAAA,MACA,cAAa,WAAM,iBAAN,mBAAoB,KAAK,CAAC,MAAM,EAAE,OAAO;AAAA,IACxD;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}