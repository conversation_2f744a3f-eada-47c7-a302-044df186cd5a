{"version": 3, "sources": ["../../@medusajs/dashboard/dist/campaign-detail-Z33NIWPP.mjs"], "sourcesContent": ["import {\n  DateRangeDisplay\n} from \"./chunk-FOD6BULO.mjs\";\nimport {\n  usePromotionTableColumns,\n  usePromotionTableQuery\n} from \"./chunk-LADMQDYD.mjs\";\nimport \"./chunk-KD3INMVA.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  currencies\n} from \"./chunk-MWVM4TYO.mjs\";\nimport {\n  usePromotionTableFilters\n} from \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport {\n  campaignsQueryKeys,\n  useAddOrRemoveCampaignPromotions,\n  useCampaign,\n  useDeleteCampaign,\n  usePromotions\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/campaign-detail/constants.ts\nvar CAMPAIGN_DETAIL_FIELDS = \"+promotions.id\";\n\n// src/routes/campaigns/campaign-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CampaignDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { campaign } = useCampaign(\n    id,\n    {\n      fields: CAMPAIGN_DETAIL_FIELDS\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id)\n    }\n  );\n  if (!campaign) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: campaign.name });\n};\n\n// src/routes/campaigns/campaign-detail/campaign-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/campaigns/campaign-detail/components/campaign-budget/campaign-budget.tsx\nimport { ChartPie, PencilSquare } from \"@medusajs/icons\";\nimport { Container, Heading, Text } from \"@medusajs/ui\";\nimport { Trans, useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar CampaignBudget = ({ campaign }) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(Container, { className: \"flex flex-col gap-y-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex justify-between\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex-grow\", children: [\n        /* @__PURE__ */ jsx2(\"div\", { className: \"bg-ui-bg-base shadow-borders-base float-left flex size-7 items-center justify-center rounded-md\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]\", children: /* @__PURE__ */ jsx2(ChartPie, { className: \"text-ui-fg-subtle\" }) }) }),\n        /* @__PURE__ */ jsx2(\n          Heading,\n          {\n            className: \"text-ui-fg-subtle ml-10 mt-[1.5px] font-normal\",\n            level: \"h3\",\n            children: t(\"campaigns.fields.budget_limit\")\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                  label: t(\"actions.edit\"),\n                  to: `edit-budget`\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(\"div\", { children: /* @__PURE__ */ jsx2(\n      Text,\n      {\n        className: \"text-ui-fg-subtle border-ui-border-strong border-l-4 pl-3\",\n        size: \"small\",\n        leading: \"compact\",\n        children: /* @__PURE__ */ jsx2(\n          Trans,\n          {\n            i18nKey: \"campaigns.totalSpend\",\n            values: {\n              amount: campaign?.budget?.limit || \"no limit\",\n              currency: campaign?.budget?.type === \"spend\" && campaign?.budget.limit ? campaign.budget?.currency_code : \"\"\n            },\n            components: [\n              /* @__PURE__ */ jsx2(\n                \"span\",\n                {\n                  className: \"text-ui-fg-base txt-compact-medium-plus text-lg\"\n                },\n                \"amount\"\n              ),\n              /* @__PURE__ */ jsx2(\n                \"span\",\n                {\n                  className: \"text-ui-fg-base txt-compact-medium-plus text-lg\"\n                },\n                \"currency\"\n              )\n            ]\n          }\n        )\n      }\n    ) })\n  ] });\n};\n\n// src/routes/campaigns/campaign-detail/components/campaign-general-section/campaign-general-section.tsx\nimport { PencilSquare as PencilSquare2, Trash } from \"@medusajs/icons\";\nimport {\n  Badge,\n  Container as Container2,\n  Heading as Heading2,\n  StatusBadge,\n  Text as Text2,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\n\n// src/routes/campaigns/common/utils/campaign-status.ts\nimport { isAfter, isBefore } from \"date-fns\";\nfunction campaignStatus(campaign) {\n  if (campaign.ends_at) {\n    if (isBefore(new Date(campaign.ends_at), /* @__PURE__ */ new Date())) {\n      return \"expired\";\n    }\n  }\n  if (campaign.starts_at) {\n    if (isAfter(new Date(campaign.starts_at), /* @__PURE__ */ new Date())) {\n      return \"scheduled\";\n    }\n  }\n  return \"active\";\n}\nvar statusColor = (status) => {\n  switch (status) {\n    case \"expired\":\n      return \"red\";\n    case \"scheduled\":\n      return \"orange\";\n    case \"active\":\n      return \"green\";\n    default:\n      return \"grey\";\n  }\n};\n\n// src/routes/campaigns/campaign-detail/components/campaign-general-section/campaign-general-section.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CampaignGeneralSection = ({\n  campaign\n}) => {\n  const { t } = useTranslation2();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteCampaign(campaign.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"campaigns.delete.title\"),\n      description: t(\"campaigns.delete.description\", {\n        name: campaign.name\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"campaigns.delete.successToast\", {\n            name: campaign.name\n          })\n        );\n        navigate(\"/campaigns\", { replace: true });\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  };\n  const status = campaignStatus(campaign);\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { children: campaign.name }),\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-4\", children: [\n        /* @__PURE__ */ jsx3(StatusBadge, { color: statusColor(status), children: t(`campaigns.status.${status}`) }),\n        /* @__PURE__ */ jsx3(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n                    label: t(\"actions.edit\"),\n                    to: `/campaigns/${campaign.id}/edit`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx3(Trash, {}),\n                    label: t(\"actions.delete\"),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"campaigns.fields.identifier\") }),\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", children: campaign.campaign_identifier })\n    ] }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.description\") }),\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", children: campaign.description || \"-\" })\n    ] }),\n    campaign?.budget && campaign.budget.type === \"spend\" && /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.currency\") }),\n      /* @__PURE__ */ jsxs2(\"div\", { children: [\n        /* @__PURE__ */ jsx3(Badge, { size: \"xsmall\", children: campaign?.budget.currency_code }),\n        /* @__PURE__ */ jsx3(Text2, { className: \"inline pl-3\", size: \"small\", leading: \"compact\", children: currencies[campaign?.budget.currency_code?.toUpperCase()]?.name })\n      ] })\n    ] })\n  ] });\n};\n\n// src/routes/campaigns/campaign-detail/components/campaign-promotion-section/campaign-promotion-section.tsx\nimport { PencilSquare as PencilSquare3, Trash as Trash2 } from \"@medusajs/icons\";\nimport { Button, Checkbox, Container as Container3, Heading as Heading3, usePrompt as usePrompt2 } from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar CampaignPromotionSection = ({\n  campaign\n}) => {\n  const [rowSelection, setRowSelection] = useState({});\n  const { t } = useTranslation3();\n  const prompt = usePrompt2();\n  const columns = useColumns();\n  const filters = usePromotionTableFilters();\n  const { searchParams, raw } = usePromotionTableQuery({ pageSize: PAGE_SIZE });\n  const { promotions, count, isLoading, isError, error } = usePromotions({\n    ...searchParams,\n    campaign_id: campaign.id\n  });\n  const { table } = useDataTable({\n    data: promotions ?? [],\n    columns,\n    count,\n    getRowId: (row) => row.id,\n    enablePagination: true,\n    enableRowSelection: true,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    },\n    meta: { campaignId: campaign.id }\n  });\n  if (isError) {\n    throw error;\n  }\n  const { mutateAsync } = useAddOrRemoveCampaignPromotions(campaign.id);\n  const handleRemove = async () => {\n    const keys = Object.keys(rowSelection);\n    const res = await prompt({\n      title: t(\"campaigns.promotions.remove.title\", { count: keys.length }),\n      description: t(\"campaigns.promotions.remove.description\", {\n        count: keys.length\n      }),\n      confirmText: t(\"actions.continue\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      { remove: keys },\n      { onSuccess: () => setRowSelection({}) }\n    );\n  };\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Heading3, { level: \"h2\", children: t(\"promotions.domain\") }),\n      /* @__PURE__ */ jsx4(Link, { to: `/campaigns/${campaign.id}/add-promotions`, children: /* @__PURE__ */ jsx4(Button, { variant: \"secondary\", size: \"small\", children: t(\"general.add\") }) })\n    ] }),\n    /* @__PURE__ */ jsx4(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        isLoading,\n        count,\n        navigateTo: (row) => `/promotions/${row.id}`,\n        filters,\n        search: true,\n        pagination: true,\n        orderBy: [\n          { key: \"code\", label: t(\"fields.code\") },\n          { key: \"type\", label: t(\"fields.type\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        commands: [\n          {\n            action: handleRemove,\n            label: t(\"actions.remove\"),\n            shortcut: \"r\"\n          }\n        ],\n        noRecords: {\n          message: t(\"campaigns.promotions.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar PromotionActions = ({\n  promotion,\n  campaignId\n}) => {\n  const { t } = useTranslation3();\n  const { mutateAsync } = useAddOrRemoveCampaignPromotions(campaignId);\n  const prompt = usePrompt2();\n  const handleRemove = async () => {\n    const res = await prompt({\n      title: t(\"campaigns.promotions.remove.title\", {\n        count: 1\n      }),\n      description: t(\"campaigns.promotions.remove.description\", {\n        count: 1\n      }),\n      confirmText: t(\"actions.continue\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync({\n      remove: [promotion.id]\n    });\n  };\n  return /* @__PURE__ */ jsx4(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx4(PencilSquare3, {}),\n              label: t(\"actions.edit\"),\n              to: `/promotions/${promotion.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx4(Trash2, {}),\n              label: t(\"actions.remove\"),\n              onClick: handleRemove\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const columns = usePromotionTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx4(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx4(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...columns,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row, table }) => {\n          const { campaignId } = table.options.meta;\n          return /* @__PURE__ */ jsx4(\n            PromotionActions,\n            {\n              promotion: row.original,\n              campaignId\n            }\n          );\n        }\n      })\n    ],\n    [columns]\n  );\n};\n\n// src/routes/campaigns/campaign-detail/components/campaign-spend/campaign-spend.tsx\nimport { CurrencyDollar } from \"@medusajs/icons\";\nimport { Container as Container4, Heading as Heading4, Text as Text3 } from \"@medusajs/ui\";\nimport { Trans as Trans2, useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar CampaignSpend = ({ campaign }) => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsxs4(Container4, { className: \"flex flex-col gap-y-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs4(\"div\", { className: \"mb-2 grid grid-cols-[28px_1fr] items-center gap-x-3\", children: [\n      /* @__PURE__ */ jsx5(\"div\", { className: \"bg-ui-bg-base shadow-borders-base flex size-7 items-center justify-center rounded-md\", children: /* @__PURE__ */ jsx5(\"div\", { className: \"bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]\", children: /* @__PURE__ */ jsx5(CurrencyDollar, { className: \"text-ui-fg-subtle\" }) }) }),\n      /* @__PURE__ */ jsx5(Heading4, { level: \"h3\", className: \"text-ui-fg-subtle font-normal\", children: campaign.budget?.type === \"spend\" ? t(\"campaigns.fields.total_spend\") : t(\"campaigns.fields.total_used\") })\n    ] }),\n    /* @__PURE__ */ jsx5(\"div\", { children: /* @__PURE__ */ jsx5(\n      Text3,\n      {\n        className: \"text-ui-fg-subtle border-ui-border-strong border-l-4 pl-3\",\n        size: \"small\",\n        leading: \"compact\",\n        children: /* @__PURE__ */ jsx5(\n          Trans2,\n          {\n            i18nKey: \"campaigns.totalSpend\",\n            values: {\n              amount: campaign?.budget?.used || 0,\n              currency: campaign?.budget?.type === \"spend\" ? campaign?.budget?.currency_code : \"\"\n            },\n            components: [\n              /* @__PURE__ */ jsx5(\n                \"span\",\n                {\n                  className: \"text-ui-fg-base txt-compact-medium-plus text-lg\"\n                },\n                \"amount\"\n              ),\n              /* @__PURE__ */ jsx5(\n                \"span\",\n                {\n                  className: \"text-ui-fg-base txt-compact-medium-plus text-lg\"\n                },\n                \"currency\"\n              )\n            ]\n          }\n        )\n      }\n    ) })\n  ] });\n};\n\n// src/routes/campaigns/campaign-detail/components/campaign-configuration-section/campaign-configuration-section.tsx\nimport { Container as Container5, Heading as Heading5 } from \"@medusajs/ui\";\nimport { PencilSquare as PencilSquare4 } from \"@medusajs/icons\";\nimport { useTranslation as useTranslation5 } from \"react-i18next\";\nimport { jsx as jsx6, jsxs as jsxs5 } from \"react/jsx-runtime\";\nvar CampaignConfigurationSection = ({\n  campaign\n}) => {\n  const { t } = useTranslation5();\n  return /* @__PURE__ */ jsxs5(Container5, { className: \"flex flex-col gap-y-4\", children: [\n    /* @__PURE__ */ jsxs5(\"div\", { className: \"flex items-center justify-between\", children: [\n      /* @__PURE__ */ jsx6(Heading5, { level: \"h2\", children: t(\"campaigns.configuration.header\") }),\n      /* @__PURE__ */ jsx6(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  icon: /* @__PURE__ */ jsx6(PencilSquare4, {}),\n                  to: \"configuration\"\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx6(\n      DateRangeDisplay,\n      {\n        startsAt: campaign.starts_at,\n        endsAt: campaign.ends_at,\n        showTime: true\n      }\n    )\n  ] });\n};\n\n// src/routes/campaigns/campaign-detail/campaign-detail.tsx\nimport { jsx as jsx7, jsxs as jsxs6 } from \"react/jsx-runtime\";\nvar CampaignDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const { campaign, isLoading, isError, error } = useCampaign(\n    id,\n    { fields: CAMPAIGN_DETAIL_FIELDS },\n    { initialData }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !campaign) {\n    return /* @__PURE__ */ jsx7(\n      TwoColumnPageSkeleton,\n      {\n        mainSections: 2,\n        sidebarSections: 3,\n        showJSON: true,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs6(\n    TwoColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"campaign.details.after\"),\n        before: getWidgets(\"campaign.details.before\"),\n        sideAfter: getWidgets(\"campaign.details.side.after\"),\n        sideBefore: getWidgets(\"campaign.details.side.before\")\n      },\n      hasOutlet: true,\n      showJSON: true,\n      showMetadata: true,\n      data: campaign,\n      children: [\n        /* @__PURE__ */ jsxs6(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx7(CampaignGeneralSection, { campaign }),\n          /* @__PURE__ */ jsx7(CampaignPromotionSection, { campaign })\n        ] }),\n        /* @__PURE__ */ jsxs6(TwoColumnPage.Sidebar, { children: [\n          /* @__PURE__ */ jsx7(CampaignConfigurationSection, { campaign }),\n          /* @__PURE__ */ jsx7(CampaignSpend, { campaign }),\n          /* @__PURE__ */ jsx7(CampaignBudget, { campaign })\n        ] })\n      ]\n    }\n  );\n};\n\n// src/routes/campaigns/campaign-detail/loader.ts\nvar campaignDetailQuery = (id) => ({\n  queryKey: campaignsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.campaign.retrieve(id, {\n    fields: CAMPAIGN_DETAIL_FIELDS\n  })\n});\nvar campaignLoader = async ({ params }) => {\n  const id = params.id;\n  const query = campaignDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  CampaignDetailBreadcrumb as Breadcrumb,\n  CampaignDetail as Component,\n  campaignLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA,yBAAoB;AA0BpB,IAAAA,sBAAkC;AAiHlC,IAAAC,sBAA2C;AAyF3C,mBAAkC;AAGlC,IAAAC,sBAA2C;AAgM3C,IAAAC,sBAA2C;AAiD3C,IAAAC,sBAA2C;AAqC3C,IAAAA,sBAA2C;AAhgB3C,IAAI,yBAAyB;AAI7B,IAAI,2BAA2B,CAAC,UAAU;AACxC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,SAAS,IAAI;AAAA,IACnB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF;AACA,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,SAAS,KAAK,CAAC;AAChE;AAUA,IAAI,iBAAiB,CAAC,EAAE,SAAS,MAAM;AAtHvC;AAuHE,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,0BAAK,WAAW,EAAE,WAAW,mCAAmC,UAAU;AAAA,QAC/E,0BAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,UACzD,0BAAK,OAAO,EAAE,WAAW,aAAa,UAAU;AAAA,YAC9C,oBAAAC,KAAK,OAAO,EAAE,WAAW,mGAAmG,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,4EAA4E,cAA0B,oBAAAA,KAAK,UAAU,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YAC5U,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU,EAAE,+BAA+B;AAAA,UAC7C;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,UACa,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,cAA0B,oBAAAA;AAAA,MACtD;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,cAA0B,oBAAAA;AAAA,UACxB;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAQ,0CAAU,WAAV,mBAAkB,UAAS;AAAA,cACnC,YAAU,0CAAU,WAAV,mBAAkB,UAAS,YAAW,qCAAU,OAAO,UAAQ,cAAS,WAAT,mBAAiB,gBAAgB;AAAA,YAC5G;AAAA,YACA,YAAY;AAAA,kBACM,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,cACF;AAAA,kBACgB,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAkBA,SAAS,eAAe,UAAU;AAChC,MAAI,SAAS,SAAS;AACpB,QAAI,SAAS,IAAI,KAAK,SAAS,OAAO,GAAmB,oBAAI,KAAK,CAAC,GAAG;AACpE,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,SAAS,WAAW;AACtB,QAAI,QAAQ,IAAI,KAAK,SAAS,SAAS,GAAmB,oBAAI,KAAK,CAAC,GAAG;AACrE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,cAAc,CAAC,WAAW;AAC5B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAIA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AAzON;AA0OE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,kBAAkB,SAAS,EAAE;AACrD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,wBAAwB;AAAA,MACjC,aAAa,EAAE,gCAAgC;AAAA,QAC7C,MAAM,SAAS;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,iCAAiC;AAAA,YACjC,MAAM,SAAS;AAAA,UACjB,CAAC;AAAA,QACH;AACA,iBAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AAAA,MAC1C;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,SAAS,eAAe,QAAQ;AACtC,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,UAAU,SAAS,KAAK,CAAC;AAAA,UAC1C,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,aAAa,EAAE,OAAO,YAAY,MAAM,GAAG,UAAU,EAAE,oBAAoB,MAAM,EAAE,EAAE,CAAC;AAAA,YAC3F,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,oBAC5C,OAAO,EAAE,cAAc;AAAA,oBACvB,IAAI,cAAc,SAAS,EAAE;AAAA,kBAC/B;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,oBACpC,OAAO,EAAE,gBAAgB;AAAA,oBACzB,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,6BAA6B,EAAE,CAAC;AAAA,UAC7G,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,SAAS,oBAAoB,CAAC;AAAA,IAC3G,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,oBAAoB,EAAE,CAAC;AAAA,UACpG,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,SAAS,eAAe,IAAI,CAAC;AAAA,IAC1G,EAAE,CAAC;AAAA,KACH,qCAAU,WAAU,SAAS,OAAO,SAAS,eAA2B,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UACvJ,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,iBAAiB,EAAE,CAAC;AAAA,UACjG,oBAAAD,MAAM,OAAO,EAAE,UAAU;AAAA,YACvB,oBAAAC,KAAK,OAAO,EAAE,MAAM,UAAU,UAAU,qCAAU,OAAO,cAAc,CAAC;AAAA,YACxE,oBAAAA,KAAK,MAAO,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,WAAU,iBAAW,0CAAU,OAAO,kBAAjB,mBAAgC,aAAa,MAAxD,mBAA2D,KAAK,CAAC;AAAA,MACxK,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAUA,IAAI,YAAY;AAChB,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAW;AAC1B,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,yBAAyB;AACzC,QAAM,EAAE,cAAc,IAAI,IAAI,uBAAuB,EAAE,UAAU,UAAU,CAAC;AAC5E,QAAM,EAAE,YAAY,OAAO,WAAW,SAAS,MAAM,IAAI,cAAc;AAAA,IACrE,GAAG;AAAA,IACH,aAAa,SAAS;AAAA,EACxB,CAAC;AACD,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,MAAM,EAAE,YAAY,SAAS,GAAG;AAAA,EAClC,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,QAAM,EAAE,YAAY,IAAI,iCAAiC,SAAS,EAAE;AACpE,QAAM,eAAe,YAAY;AAC/B,UAAM,OAAO,OAAO,KAAK,YAAY;AACrC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,qCAAqC,EAAE,OAAO,KAAK,OAAO,CAAC;AAAA,MACpE,aAAa,EAAE,2CAA2C;AAAA,QACxD,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,MACD,aAAa,EAAE,kBAAkB;AAAA,MACjC,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ,EAAE,QAAQ,KAAK;AAAA,MACf,EAAE,WAAW,MAAM,gBAAgB,CAAC,CAAC,EAAE;AAAA,IACzC;AAAA,EACF;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,mBAAmB,EAAE,CAAC;AAAA,UAChE,oBAAAA,KAAK,MAAM,EAAE,IAAI,cAAc,SAAS,EAAE,mBAAmB,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AAAA,IAC5L,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,YAAY,CAAC,QAAQ,eAAe,IAAI,EAAE;AAAA,QAC1C;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,QAAQ;AAAA,YACR,OAAO,EAAE,gBAAgB;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS,EAAE,4CAA4C;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,iCAAiC,UAAU;AACnE,QAAM,SAAS,UAAW;AAC1B,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,qCAAqC;AAAA,QAC5C,OAAO;AAAA,MACT,CAAC;AAAA,MACD,aAAa,EAAE,2CAA2C;AAAA,QACxD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,aAAa,EAAE,kBAAkB;AAAA,MACjC,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAAA,MAChB,QAAQ,CAAC,UAAU,EAAE;AAAA,IACvB,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,eAAe,UAAU,EAAE;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,cACrC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,UAAU,yBAAyB;AACzC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AACxB,gBAAM,EAAE,WAAW,IAAI,MAAM,QAAQ;AACrC,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,WAAW,IAAI;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AACF;AAOA,IAAI,gBAAgB,CAAC,EAAE,SAAS,MAAM;AAngBtC;AAogBE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,mCAAmC,UAAU;AAAA,QACjF,oBAAAA,MAAM,OAAO,EAAE,WAAW,uDAAuD,UAAU;AAAA,UACzF,oBAAAC,KAAK,OAAO,EAAE,WAAW,wFAAwF,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,4EAA4E,cAA0B,oBAAAA,KAAK,gBAAgB,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,UACvU,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,WAAW,iCAAiC,YAAU,cAAS,WAAT,mBAAiB,UAAS,UAAU,EAAE,8BAA8B,IAAI,EAAE,6BAA6B,EAAE,CAAC;AAAA,IAChN,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,cAA0B,oBAAAA;AAAA,MACtD;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,cAA0B,oBAAAA;AAAA,UACxB;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAQ,0CAAU,WAAV,mBAAkB,SAAQ;AAAA,cAClC,YAAU,0CAAU,WAAV,mBAAkB,UAAS,WAAU,0CAAU,WAAV,mBAAkB,gBAAgB;AAAA,YACnF;AAAA,YACA,YAAY;AAAA,kBACM,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,cACF;AAAA,kBACgB,oBAAAA;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAOA,IAAI,+BAA+B,CAAC;AAAA,EAClC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,yBAAyB,UAAU;AAAA,QACvE,oBAAAA,MAAM,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,UACvE,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,gCAAgC,EAAE,CAAC;AAAA,UAC7E,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,kBAC5C,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,UAAU,SAAS;AAAA,QACnB,QAAQ,SAAS;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI;AAAA,IAC9C;AAAA,IACA,EAAE,QAAQ,uBAAuB;AAAA,IACjC,EAAE,YAAY;AAAA,EAChB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,UAAU;AAC1B,eAAuB,oBAAAC;AAAA,MACrB;AAAA,MACA;AAAA,QACE,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,wBAAwB;AAAA,QAC1C,QAAQ,WAAW,yBAAyB;AAAA,QAC5C,WAAW,WAAW,6BAA6B;AAAA,QACnD,YAAY,WAAW,8BAA8B;AAAA,MACvD;AAAA,MACA,WAAW;AAAA,MACX,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAD,KAAK,wBAAwB,EAAE,SAAS,CAAC;AAAA,cACzC,oBAAAA,KAAK,0BAA0B,EAAE,SAAS,CAAC;AAAA,QAC7D,EAAE,CAAC;AAAA,YACa,oBAAAC,MAAM,cAAc,SAAS,EAAE,UAAU;AAAA,cACvC,oBAAAD,KAAK,8BAA8B,EAAE,SAAS,CAAC;AAAA,cAC/C,oBAAAA,KAAK,eAAe,EAAE,SAAS,CAAC;AAAA,cAChC,oBAAAA,KAAK,gBAAgB,EAAE,SAAS,CAAC;AAAA,QACnD,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,sBAAsB,CAAC,QAAQ;AAAA,EACjC,UAAU,mBAAmB,OAAO,EAAE;AAAA,EACtC,SAAS,YAAY,IAAI,MAAM,SAAS,SAAS,IAAI;AAAA,IACnD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,iBAAiB,OAAO,EAAE,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,oBAAoB,EAAE;AACpC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsxs3", "jsx4", "jsxs4", "jsx5", "jsxs5", "jsx6", "jsx7", "jsxs6"]}