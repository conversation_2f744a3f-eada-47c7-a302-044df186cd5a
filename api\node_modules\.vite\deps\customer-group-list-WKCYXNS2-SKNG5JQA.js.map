{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-group-list-WKCYXNS2.mjs"], "sourcesContent": ["import {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  DataTable,\n  useDataTableDateFilters\n} from \"./chunk-3IIOXMXN.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport {\n  useDate\n} from \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport {\n  useCustomerGroups,\n  useDeleteCustomerGroupLazy\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/customer-groups/customer-group-list/components/customer-group-list-table/customer-group-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport {\n  Container,\n  createDataTableColumnHelper,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useCallback, useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar CustomerGroupListTable = () => {\n  const { t } = useTranslation();\n  const { getWidgets } = useExtension();\n  const { q, order, offset, created_at, updated_at } = useQueryParams([\n    \"q\",\n    \"order\",\n    \"offset\",\n    \"created_at\",\n    \"updated_at\"\n  ]);\n  const columns = useColumns();\n  const filters = useFilters();\n  const { customer_groups, count, isPending, isError, error } = useCustomerGroups(\n    {\n      q,\n      order,\n      offset: offset ? parseInt(offset) : void 0,\n      limit: PAGE_SIZE,\n      created_at: created_at ? JSON.parse(created_at) : void 0,\n      updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n      fields: \"id,name,created_at,updated_at,customers.id\"\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"customer_group.list.before\"),\n        after: getWidgets(\"customer_group.list.after\")\n      },\n      children: /* @__PURE__ */ jsx(Container, { className: \"overflow-hidden p-0\", children: /* @__PURE__ */ jsx(\n        DataTable,\n        {\n          data: customer_groups,\n          columns,\n          filters,\n          heading: t(\"customerGroups.domain\"),\n          rowCount: count,\n          getRowId: (row) => row.id,\n          rowHref: (row) => `/customer-groups/${row.id}`,\n          action: {\n            label: t(\"actions.create\"),\n            to: \"/customer-groups/create\"\n          },\n          emptyState: {\n            empty: {\n              heading: t(\"customerGroups.list.empty.heading\"),\n              description: t(\"customerGroups.list.empty.description\")\n            },\n            filtered: {\n              heading: t(\"customerGroups.list.filtered.heading\"),\n              description: t(\"customerGroups.list.filtered.description\")\n            }\n          },\n          pageSize: PAGE_SIZE,\n          isLoading: isPending\n        }\n      ) })\n    }\n  );\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const { getFullDate } = useDate();\n  const navigate = useNavigate();\n  const prompt = usePrompt();\n  const { mutateAsync: deleteCustomerGroup } = useDeleteCustomerGroupLazy();\n  const handleDeleteCustomerGroup = useCallback(\n    async ({ id, name }) => {\n      const res = await prompt({\n        title: t(\"customerGroups.delete.title\"),\n        description: t(\"customerGroups.delete.description\", {\n          name\n        }),\n        verificationText: name,\n        verificationInstruction: t(\"general.typeToConfirm\"),\n        confirmText: t(\"actions.delete\"),\n        cancelText: t(\"actions.cancel\")\n      });\n      if (!res) {\n        return;\n      }\n      await deleteCustomerGroup(\n        { id },\n        {\n          onSuccess: () => {\n            toast.success(t(\"customerGroups.delete.successToast\", { name }));\n          },\n          onError: (e) => {\n            toast.error(e.message);\n          }\n        }\n      );\n    },\n    [t, prompt, deleteCustomerGroup]\n  );\n  return useMemo(() => {\n    return [\n      columnHelper.accessor(\"name\", {\n        header: t(\"fields.name\"),\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      columnHelper.accessor(\"customers\", {\n        header: t(\"customers.domain\"),\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\"span\", { children: row.original.customers?.length ?? 0 });\n        }\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: t(\"fields.createdAt\"),\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\"span\", { children: getFullDate({\n            date: row.original.created_at,\n            includeTime: true\n          }) });\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.dateAsc\"),\n        sortDescLabel: t(\"filters.sorting.dateDesc\")\n      }),\n      columnHelper.accessor(\"updated_at\", {\n        header: t(\"fields.updatedAt\"),\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\"span\", { children: getFullDate({\n            date: row.original.updated_at,\n            includeTime: true\n          }) });\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.dateAsc\"),\n        sortDescLabel: t(\"filters.sorting.dateDesc\")\n      }),\n      columnHelper.action({\n        actions: [\n          [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              onClick: (row) => {\n                navigate(`/customer-groups/${row.row.original.id}/edit`);\n              }\n            }\n          ],\n          [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: (row) => {\n                handleDeleteCustomerGroup({\n                  id: row.row.original.id,\n                  name: row.row.original.name ?? \"\"\n                });\n              }\n            }\n          ]\n        ]\n      })\n    ];\n  }, [t, navigate, getFullDate, handleDeleteCustomerGroup]);\n};\nvar useFilters = () => {\n  const dateFilters = useDataTableDateFilters();\n  return useMemo(() => {\n    return dateFilters;\n  }, [dateFilters]);\n};\n\n// src/routes/customer-groups/customer-group-list/customer-group-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerGroupsList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"customer_group.list.after\"),\n        before: getWidgets(\"customer_group.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(CustomerGroupListTable, {})\n    }\n  );\n};\nexport {\n  CustomerGroupsList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,mBAAqC;AAGrC,yBAAoB;AAmLpB,IAAAA,sBAA4B;AAlL5B,IAAI,YAAY;AAChB,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,QAAM,EAAE,GAAG,OAAO,QAAQ,YAAY,WAAW,IAAI,eAAe;AAAA,IAClE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,iBAAiB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC5D;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,MACpC,OAAO;AAAA,MACP,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,MAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,MAClD,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,4BAA4B;AAAA,QAC/C,OAAO,WAAW,2BAA2B;AAAA,MAC/C;AAAA,MACA,cAA0B,wBAAI,WAAW,EAAE,WAAW,uBAAuB,cAA0B;AAAA,QACrG;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,SAAS,EAAE,uBAAuB;AAAA,UAClC,UAAU;AAAA,UACV,UAAU,CAAC,QAAQ,IAAI;AAAA,UACvB,SAAS,CAAC,QAAQ,oBAAoB,IAAI,EAAE;AAAA,UAC5C,QAAQ;AAAA,YACN,OAAO,EAAE,gBAAgB;AAAA,YACzB,IAAI;AAAA,UACN;AAAA,UACA,YAAY;AAAA,YACV,OAAO;AAAA,cACL,SAAS,EAAE,mCAAmC;AAAA,cAC9C,aAAa,EAAE,uCAAuC;AAAA,YACxD;AAAA,YACA,UAAU;AAAA,cACR,SAAS,EAAE,sCAAsC;AAAA,cACjD,aAAa,EAAE,0CAA0C;AAAA,YAC3D;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,WAAW;AAAA,QACb;AAAA,MACF,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,YAAY,IAAI,QAAQ;AAChC,QAAM,WAAW,YAAY;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,aAAa,oBAAoB,IAAI,2BAA2B;AACxE,QAAM,gCAA4B;AAAA,IAChC,OAAO,EAAE,IAAI,KAAK,MAAM;AACtB,YAAM,MAAM,MAAM,OAAO;AAAA,QACvB,OAAO,EAAE,6BAA6B;AAAA,QACtC,aAAa,EAAE,qCAAqC;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,QACD,kBAAkB;AAAA,QAClB,yBAAyB,EAAE,uBAAuB;AAAA,QAClD,aAAa,EAAE,gBAAgB;AAAA,QAC/B,YAAY,EAAE,gBAAgB;AAAA,MAChC,CAAC;AACD,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM;AAAA,QACJ,EAAE,GAAG;AAAA,QACL;AAAA,UACE,WAAW,MAAM;AACf,kBAAM,QAAQ,EAAE,sCAAsC,EAAE,KAAK,CAAC,CAAC;AAAA,UACjE;AAAA,UACA,SAAS,CAAC,MAAM;AACd,kBAAM,MAAM,EAAE,OAAO;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,GAAG,QAAQ,mBAAmB;AAAA,EACjC;AACA,aAAO,sBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,EAAE,aAAa;AAAA,QACvB,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AArL3B;AAsLU,qBAAuB,wBAAI,QAAQ,EAAE,YAAU,SAAI,SAAS,cAAb,mBAAwB,WAAU,EAAE,CAAC;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,QAAQ,EAAE,UAAU,YAAY;AAAA,YACzD,MAAM,IAAI,SAAS;AAAA,YACnB,aAAa;AAAA,UACf,CAAC,EAAE,CAAC;AAAA,QACN;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,yBAAyB;AAAA,QACzC,eAAe,EAAE,0BAA0B;AAAA,MAC7C,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,QAAQ,EAAE,UAAU,YAAY;AAAA,YACzD,MAAM,IAAI,SAAS;AAAA,YACnB,aAAa;AAAA,UACf,CAAC,EAAE,CAAC;AAAA,QACN;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,yBAAyB;AAAA,QACzC,eAAe,EAAE,0BAA0B;AAAA,MAC7C,CAAC;AAAA,MACD,aAAa,OAAO;AAAA,QAClB,SAAS;AAAA,UACP;AAAA,YACE;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,SAAS,CAAC,QAAQ;AAChB,yBAAS,oBAAoB,IAAI,IAAI,SAAS,EAAE,OAAO;AAAA,cACzD;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS,CAAC,QAAQ;AAChB,0CAA0B;AAAA,kBACxB,IAAI,IAAI,IAAI,SAAS;AAAA,kBACrB,MAAM,IAAI,IAAI,SAAS,QAAQ;AAAA,gBACjC,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,GAAG,UAAU,aAAa,yBAAyB,CAAC;AAC1D;AACA,IAAI,aAAa,MAAM;AACrB,QAAM,cAAc,wBAAwB;AAC5C,aAAO,sBAAQ,MAAM;AACnB,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,CAAC;AAClB;AAIA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,2BAA2B;AAAA,QAC7C,QAAQ,WAAW,4BAA4B;AAAA,MACjD;AAAA,MACA,cAA0B,oBAAAA,KAAK,wBAAwB,CAAC,CAAC;AAAA,IAC3D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}