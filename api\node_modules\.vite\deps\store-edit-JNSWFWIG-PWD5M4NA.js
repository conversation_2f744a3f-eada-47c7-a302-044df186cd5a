import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useStore,
  useUpdateStore
} from "./chunk-RWC53K5F.js";
import "./chunk-662EXSHO.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/store-edit-JNSWFWIG.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditStoreSchema = z.object({
  name: z.string().min(1),
  default_currency_code: z.string().optional(),
  default_region_id: z.string().optional(),
  default_sales_channel_id: z.string().optional(),
  default_location_id: z.string().optional()
});
var EditStoreForm = ({ store }) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: store.name,
      default_region_id: store.default_region_id || void 0,
      default_currency_code: ((_b = (_a = store.supported_currencies) == null ? void 0 : _a.find((c) => c.is_default)) == null ? void 0 : _b.currency_code) || void 0,
      default_sales_channel_id: store.default_sales_channel_id || void 0,
      default_location_id: store.default_location_id || void 0
    },
    resolver: t(EditStoreSchema)
  });
  const { mutateAsync, isPending } = useUpdateStore(store.id);
  const regionsCombobox = useComboboxData({
    queryKey: ["regions", "default_region_id"],
    queryFn: (params) => sdk.admin.region.list({ ...params, fields: "id,name" }),
    defaultValue: store.default_region_id || void 0,
    getOptions: (data) => data.regions.map((r) => ({ label: r.name, value: r.id }))
  });
  const salesChannelsCombobox = useComboboxData({
    queryFn: (params) => sdk.admin.salesChannel.list({ ...params, fields: "id,name" }),
    getOptions: (data) => data.sales_channels.map((sc) => ({ label: sc.name, value: sc.id })),
    queryKey: ["sales_channels", "default_sales_channel_id"],
    defaultValue: store.default_sales_channel_id || void 0
  });
  const locationsCombobox = useComboboxData({
    queryFn: (params) => sdk.admin.stockLocation.list({ ...params, fields: "id,name" }),
    getOptions: (data) => data.stock_locations.map((l) => ({ label: l.name, value: l.id })),
    queryKey: ["stock_locations", "default_location_id"],
    defaultValue: store.default_location_id || void 0
  });
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a2;
    const { default_currency_code, ...rest } = values;
    const normalizedMutation = {
      ...rest,
      supported_currencies: (_a2 = store.supported_currencies) == null ? void 0 : _a2.map((c) => ({
        ...c,
        is_default: c.currency_code === default_currency_code
      }))
    };
    await mutateAsync(normalizedMutation, {
      onSuccess: () => {
        toast.success(t2("store.toast.update"));
        handleSuccess();
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "name",
          render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { placeholder: "ACME", ...field }) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] })
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "default_currency_code",
          render: ({ field: { onChange, ...field } }) => {
            var _a2;
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("store.defaultCurrency") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                (0, import_jsx_runtime.jsx)(Select.Trigger, { ref: field.ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                (0, import_jsx_runtime.jsx)(Select.Content, { children: (_a2 = store.supported_currencies) == null ? void 0 : _a2.map((currency) => (0, import_jsx_runtime.jsx)(
                  Select.Item,
                  {
                    value: currency.currency_code,
                    children: currency.currency_code.toUpperCase()
                  },
                  currency.currency_code
                )) })
              ] }) })
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "default_region_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("store.defaultRegion") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: regionsCombobox.options,
                  searchValue: regionsCombobox.searchValue,
                  onSearchValueChange: regionsCombobox.onSearchValueChange,
                  disabled: regionsCombobox.disabled
                }
              ) })
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "default_sales_channel_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("store.defaultSalesChannel") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: salesChannelsCombobox.options,
                  searchValue: salesChannelsCombobox.searchValue,
                  onSearchValueChange: salesChannelsCombobox.onSearchValueChange,
                  disabled: salesChannelsCombobox.disabled
                }
              ) })
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "default_location_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("store.defaultLocation") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: locationsCombobox.options,
                  searchValue: locationsCombobox.searchValue,
                  onSearchValueChange: locationsCombobox.onSearchValueChange,
                  disabled: locationsCombobox.disabled
                }
              ) })
            ] });
          }
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", isLoading: isPending, type: "submit", children: t2("actions.save") })
    ] }) })
  ] }) });
};
var StoreEdit = () => {
  const { t: t2 } = useTranslation();
  const { store, isPending: isLoading, isError, error } = useStore();
  if (isError) {
    throw error;
  }
  const ready = !!store && !isLoading;
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("store.edit.header") }) }),
    ready && (0, import_jsx_runtime2.jsx)(EditStoreForm, { store })
  ] });
};
export {
  StoreEdit as Component
};
//# sourceMappingURL=store-edit-JNSWFWIG-PWD5M4NA.js.map
