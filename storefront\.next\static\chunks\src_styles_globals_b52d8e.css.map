{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/styles/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #EBEFF4; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #C1CDD7; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #C1CDD7; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n* {\n  border-color: var(--border-base);\n}\n\n:root {\n  --tag-neutral-border: rgba(228, 228, 231, 1);\n  --tag-neutral-icon: rgba(161, 161, 170, 1);\n  --bg-switch-off-hover: rgba(212, 212, 216, 1);\n  --border-menu-bot: rgba(255, 255, 255, 1);\n  --border-menu-top: rgba(228, 228, 231, 1);\n  --bg-subtle-hover: rgba(244, 244, 245, 1);\n  --contrast-fg-primary: rgba(255, 255, 255, 0.88);\n  --bg-switch-off: rgba(228, 228, 231, 1);\n  --contrast-bg-base-pressed: rgba(63, 63, 70, 1);\n  --bg-field-component-hover: rgba(250, 250, 250, 1);\n  --bg-base-pressed: rgba(228, 228, 231, 1);\n  --tag-neutral-text: rgba(82, 82, 91, 1);\n  --tag-red-text: rgba(159, 18, 57, 1);\n  --contrast-bg-base: rgba(24, 24, 27, 1);\n  --border-strong: rgba(212, 212, 216, 1);\n  --contrast-border-base: rgba(255, 255, 255, 0.15);\n  --bg-field: rgba(250, 250, 250, 1);\n  --tag-blue-text: rgba(30, 64, 175, 1);\n  --button-inverted-pressed: rgba(82, 82, 91, 1);\n  --border-interactive: rgba(59, 130, 246, 1);\n  --bg-base-hover: rgba(244, 244, 245, 1);\n  --contrast-bg-subtle: rgba(39, 39, 42, 1);\n  --bg-highlight: rgba(239, 246, 255, 1);\n  --contrast-fg-secondary: rgba(255, 255, 255, 0.56);\n  --tag-red-bg: rgba(255, 228, 230, 1);\n  --button-transparent: rgba(255, 255, 255, 0);\n  --button-danger-pressed: rgba(159, 18, 57, 1);\n  --fg-on-color: rgba(255, 255, 255, 1);\n  --button-inverted-hover: rgba(63, 63, 70, 1);\n  --bg-field-component: rgba(255, 255, 255, 1);\n  --tag-orange-text: rgba(154, 52, 18, 1);\n  --tag-green-icon: rgba(16, 185, 129, 1);\n  --border-base: rgba(228, 228, 231, 1);\n  --bg-base: rgba(255, 255, 255, 1);\n  --tag-orange-border: rgba(254, 215, 170, 1);\n  --tag-red-border: rgba(254, 205, 211, 1);\n  --tag-green-border: rgba(167, 243, 208, 1);\n  --tag-green-text: rgba(6, 95, 70, 1);\n  --button-neutral: rgba(255, 255, 255, 1);\n  --tag-blue-border: rgba(191, 219, 254, 1);\n  --fg-interactive-hover: rgba(37, 99, 235, 1);\n  --tag-orange-icon: rgba(249, 115, 22, 1);\n  --button-neutral-hover: rgba(244, 244, 245, 1);\n  --fg-interactive: rgba(59, 130, 246, 1);\n  --bg-component-pressed: rgba(228, 228, 231, 1);\n  --tag-purple-bg: rgba(237, 233, 254, 1);\n  --contrast-bg-base-hover: rgba(39, 39, 42, 1);\n  --bg-component: rgba(250, 250, 250, 1);\n  --bg-subtle: rgba(250, 250, 250, 1);\n  --tag-purple-text: rgba(91, 33, 182, 1);\n  --contrast-border-bot: rgba(255, 255, 255, 0.1);\n  --button-inverted: rgba(39, 39, 42, 1);\n  --tag-red-icon: rgba(244, 63, 94, 1);\n  --button-transparent-hover: rgba(244, 244, 245, 1);\n  --button-neutral-pressed: rgba(228, 228, 231, 1);\n  --tag-purple-icon: rgba(167, 139, 250, 1);\n  --bg-field-hover: rgba(244, 244, 245, 1);\n  --fg-on-inverted: rgba(255, 255, 255, 1);\n  --bg-interactive: rgba(59, 130, 246, 1);\n  --border-danger: rgba(190, 18, 60, 1);\n  --button-transparent-pressed: rgba(228, 228, 231, 1);\n  --tag-purple-border: rgba(221, 214, 254, 1);\n  --bg-highlight-hover: rgba(219, 234, 254, 1);\n  --border-error: rgba(225, 29, 72, 1);\n  --button-danger: rgba(225, 29, 72, 1);\n  --tag-blue-bg: rgba(219, 234, 254, 1);\n  --border-transparent: rgba(255, 255, 255, 0);\n  --button-danger-hover: rgba(190, 18, 60, 1);\n  --bg-subtle-pressed: rgba(228, 228, 231, 1);\n  --fg-error: rgba(225, 29, 72, 1);\n  --bg-component-hover: rgba(244, 244, 245, 1);\n  --bg-disabled: rgba(244, 244, 245, 1);\n  --tag-blue-icon: rgba(96, 165, 250, 1);\n  --fg-subtle: rgba(82, 82, 91, 1);\n  --tag-orange-bg-hover: rgba(254, 215, 170, 1);\n  --tag-green-bg-hover: rgba(167, 243, 208, 1);\n  --tag-red-bg-hover: rgba(254, 205, 211, 1);\n  --tag-purple-bg-hover: rgba(221, 214, 254, 1);\n  --tag-neutral-bg-hover: rgba(228, 228, 231, 1);\n  --tag-blue-bg-hover: rgba(191, 219, 254, 1);\n  --tag-green-bg: rgba(209, 250, 229, 1);\n  --tag-neutral-bg: rgba(244, 244, 245, 1);\n  --tag-orange-bg: rgba(255, 237, 213, 1);\n  --fg-base: rgba(24, 24, 27, 1);\n  --contrast-border-top: rgba(24, 24, 27, 1);\n  --bg-overlay: rgba(24, 24, 27, 0.4);\n  --fg-disabled: rgba(161, 161, 170, 1);\n  --fg-muted: rgba(113, 113, 122, 1);\n  --borders-interactive-with-active: 0px 0px 0px 1px rgba(59, 130, 246, 1), 0px 0px 0px 4px rgba(59, 130, 246, 0.2);\n  --buttons-danger-focus: 0px 0.75px 0px 0px rgba(255, 255, 255, 0.2) inset, 0px 1px 2px 0px rgba(190, 18, 60, 0.4), 0px 0px 0px 1px rgba(190, 18, 60, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, 0.6);\n  --details-contrast-on-bg-interactive: 0px 1px 2px 0px rgba(30, 58, 138, 0.6);\n  --borders-interactive-with-focus: 0px 1px 2px 0px rgba(30, 58, 138, 0.5), 0px 0px 0px 1px rgba(59, 130, 246, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, 0.6);\n  --borders-error: 0px 0px 0px 1px rgba(225, 29, 72, 1), 0px 0px 0px 3px rgba(225, 29, 72, 0.15);\n  --borders-focus: 0px 0px 0px 1px rgba(255, 255, 255, 1), 0px 0px 0px 3px rgba(59, 130, 246, 0.6);\n  --borders-interactive-with-shadow: 0px 1px 2px 0px rgba(30, 58, 138, 0.5), 0px 0px 0px 1px rgba(59, 130, 246, 1);\n  --buttons-danger: 0px 0.75px 0px 0px rgba(255, 255, 255, 0.2) inset, 0px 1px 2px 0px rgba(190, 18, 60, 0.4), 0px 0px 0px 1px rgba(190, 18, 60, 1);\n  --buttons-inverted-focus: 0px 0.75px 0px 0px rgba(255, 255, 255, 0.2) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.4), 0px 0px 0px 1px rgba(24, 24, 27, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, 0.6);\n  --elevation-card-hover: 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 1px 2px -1px rgba(0, 0, 0, 0.08), 0px 2px 8px 0px rgba(0, 0, 0, 0.1);\n  --details-switch-handle: 0px 0px 2px 1px rgba(255, 255, 255, 1) inset, 0px 1px 0px 0px rgba(255, 255, 255, 1) inset, 0px 0px 0px 0.5px rgba(0, 0, 0, 0.02), 0px 5px 4px 0px rgba(0, 0, 0, 0.02), 0px 3px 3px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.12), 0px 0px 1px 0px rgba(0, 0, 0, 0.08);\n  --buttons-neutral: 0px 1px 2px 0px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.08);\n  --borders-base: 0px 1px 2px 0px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.08);\n  --elevation-card-rest: 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 1px 2px -1px rgba(0, 0, 0, 0.08), 0px 2px 4px 0px rgba(0, 0, 0, 0.04);\n  --buttons-neutral-focus: 0px 1px 2px 0px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, 0.6);\n  --details-switch-background-focus: 0px 0px 0px 1px rgba(255, 255, 255, 1), 0px 0px 0px 3px rgba(59, 130, 246, 0.6), 0px 1px 1px 0px rgba(0, 0, 0, 0.04) inset, 0px 2px 4px 0px rgba(0, 0, 0, 0.04) inset, 0px 0px 0px 0.75px rgba(0, 0, 0, 0.06) inset, 0px 0px 8px 0px rgba(0, 0, 0, 0.02) inset, 0px 2px 4px 0px rgba(0, 0, 0, 0.04);\n  --details-switch-background: 0px 1px 1px 0px rgba(0, 0, 0, 0.04) inset, 0px 2px 4px 0px rgba(0, 0, 0, 0.04) inset, 0px 0px 0px 0.75px rgba(0, 0, 0, 0.06) inset, 0px 0px 8px 0px rgba(0, 0, 0, 0.02) inset, 0px 2px 4px 0px rgba(0, 0, 0, 0.04);\n  --elevation-flyout: 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 4px 8px 0px rgba(0, 0, 0, 0.08), 0px 8px 16px 0px rgba(0, 0, 0, 0.08);\n  --elevation-tooltip: 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 2px 4px 0px rgba(0, 0, 0, 0.08), 0px 4px 8px 0px rgba(0, 0, 0, 0.08);\n  --elevation-modal: 0px 0px 0px 1px rgba(255, 255, 255, 1) inset, 0px 0px 0px 1.5px rgba(228, 228, 231, 0.6) inset, 0px 0px 0px 1px rgba(0, 0, 0, 0.08), 0px 8px 16px 0px rgba(0, 0, 0, 0.08), 0px 16px 32px 0px rgba(0, 0, 0, 0.08);\n  --elevation-commandbar: 0px 0px 0px 1px rgba(39, 39, 42, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, 0.3) inset, 0px 8px 16px 0px rgba(0, 0, 0, 0.08), 0px 16px 32px 0px rgba(0, 0, 0, 0.08);\n  --elevation-code-block: 0px 0px 0px 1px rgba(24, 24, 27, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, 0.2) inset;\n  --buttons-inverted: 0px 0.75px 0px 0px rgba(255, 255, 255, 0.2) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.4), 0px 0px 0px 1px rgba(24, 24, 27, 1);\r\n    --background: 0 0% 100%;\r\n    --foreground: 240 10% 3.9%;\r\n\r\n    --muted: 240 4.8% 95.9%;\r\n    --muted-foreground: 240 3.8% 46.1%;\r\n\r\n    --popover: 0 0% 100%;\r\n    --popover-foreground: 240 10% 3.9%;\r\n\r\n    --border: 240 5.9% 90%;\r\n    --input: 240 5.9% 90%;\r\n\r\n    --card: 0 0% 100%;\r\n    --card-foreground: 240 10% 3.9%;\r\n\r\n    --primary: 22 34.8% 44.51%;\r\n    --primary-foreground: 0 0% 98%;\r\n\r\n    --secondary: 240 4.8% 95.9%;\r\n    --secondary-foreground: 240 5.9% 10%;\r\n\r\n    --accent: 240 4.8% 95.9%;\r\n    --accent-foreground: 240 5.9% 10%;\r\n\r\n    --destructive: 0 84.2% 60.2%;\r\n    --destructive-foreground: 0 0% 98%;\r\n\r\n    --ring: 240 10% 3.9%;\r\n\r\n    --radius: 0.5rem;\r\n\r\n    --chart-1: 12 76% 61%;\r\n\r\n    --chart-2: 173 58% 39%;\r\n\r\n    --chart-3: 197 37% 24%;\r\n\r\n    --chart-4: 43 74% 66%;\r\n\r\n    --chart-5: 27 87% 67%;\r\n\r\n    --font-body: \"Inter\", sans-serif;\r\n\r\n    --font-mulish: \"Mulish\", sans-serif;\r\n\r\n    --font-sans: \"Sans\", sans-serif;\r\n\r\n    --font-display: \"Mulish\", sans-serif;\r\n\r\n    --font-lora: \"Lora\", serif;\r\n\r\n    --font-poppins: \"Poppins\", sans-serif;\r\n\r\n    --font-montserrat: \"Montserrat\", sans-serif;\r\n\r\n    --font-didot: \"GFS Didot\", serif;\r\n\r\n    --font-playfair: \"Playfair Display\", serif;\r\n\r\n    --sidebar-background: 0 0% 98%;\r\n\r\n    --sidebar-foreground: 240 5.3% 26.1%;\r\n\r\n    --sidebar-primary: 240 5.9% 10%;\r\n\r\n    --sidebar-primary-foreground: 0 0% 98%;\r\n\r\n    --sidebar-accent: 240 4.8% 95.9%;\r\n\r\n    --sidebar-accent-foreground: 240 5.9% 10%;\r\n\r\n    --sidebar-border: 220 13% 91%;\r\n\r\n    --sidebar-ring: 217.2 91.2% 59.8%;\r\n\r\n    --swiper-theme-color: #59b71f !important;\r\n\r\n    --swiper-pagination-color: #59b71f !important;\r\n\r\n    --borders-interactive-with-active: #59b71f !important;\r\n\r\n    --swiper-navigation-size: 12px !important;\n}\r\n  body {\n  font-family: var(--font-body), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-weight: 400;\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 1rem;\n  padding-left: 1rem;\n}\r\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1440px) {\n\n  .container {\n    max-width: 1440px;\n  }\n}\r\n.txt-compact-xsmall {\n  font-size: 0.75rem;\n  line-height: 1.25rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-xsmall-plus {\n  font-size: 0.75rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-xlarge {\n  font-size: 1.125rem;\n  line-height: 1.6875rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-small-plus {\n  font-size: 0.8125rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-medium {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-large-plus {\n  font-size: 1rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.\\!txt-medium {\n  font-size: 0.875rem !important;\n  line-height: 1.3125rem !important;\n  font-weight: 400 !important;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji !important;\n}\r\n.txt-medium {\n  font-size: 0.875rem;\n  line-height: 1.3125rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-large {\n  font-size: 1rem;\n  line-height: 1.25rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-medium-plus {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-compact-xlarge {\n  font-size: 1.125rem;\n  line-height: 1.25rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.\\!txt-compact-small {\n  font-size: 0.8125rem !important;\n  line-height: 1.25rem !important;\n  font-weight: 400 !important;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji !important;\n}\r\n.txt-compact-small {\n  font-size: 0.8125rem;\n  line-height: 1.25rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-xsmall-plus {\n  font-size: 0.75rem;\n  line-height: 1.125rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-small {\n  font-size: 0.8125rem;\n  line-height: 1.21875rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-small-plus {\n  font-size: 0.8125rem;\n  line-height: 1.21875rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-large {\n  font-size: 1rem;\n  line-height: 1.5rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-medium-plus {\n  font-size: 0.875rem;\n  line-height: 1.3125rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-xsmall {\n  font-size: 0.75rem;\n  line-height: 1.125rem;\n  font-weight: 400;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.txt-xlarge-plus {\n  font-size: 1.125rem;\n  line-height: 1.6875rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.code-body {\n  font-size: 0.75rem;\n  line-height: 1.125rem;\n  font-weight: 400;\n  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\n}\r\n.h2-core {\n  font-size: 1rem;\n  line-height: 1.5rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.h3-core {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.h1-core {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.container {\n  max-width: 100%;\n}\r\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 992px) and (max-width: 1024px) {\n\n  .container {\n    max-width: 992px;\n  }\n}\r\n@media (min-width: 1024px) and (max-width: 1280px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px) and (max-width: 1440px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1441px) {\n\n  .container {\n    max-width: 1440px;\n  }\n}\r\n.content-container {\n  margin-left: auto;\n  margin-right: auto;\n  width: 100%;\n  max-width: 1440px;\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.text-small-regular {\n  font-size: 0.75rem;\n  line-height: 1.2;\n  font-weight: 400;\n  line-height: 1.25rem;\n}\r\n.text-base-regular {\n  font-size: 0.875rem;\n  line-height: 1.2;\n  font-weight: 400;\n  line-height: 1.5rem;\n}\r\n.text-base-semi {\n  font-size: 0.875rem;\n  line-height: 1.2;\n  font-weight: 600;\n  line-height: 1.5rem;\n}\r\n.text-large-regular {\n  font-size: 1rem;\n  line-height: 1.2;\n  font-weight: 400;\n  line-height: 1.5rem;\n}\r\n.text-large-semi {\n  font-size: 1rem;\n  line-height: 1.2;\n  font-weight: 600;\n  line-height: 1.5rem;\n}\r\n.text-xl-semi {\n  font-size: 2rem;\n  line-height: 1.2;\n  font-weight: 600;\n  line-height: 36px;\n}\r\n.text-2xl-regular {\n  font-size: 30px;\n  font-weight: 400;\n  line-height: 48px;\n}\r\n.text-2xl-semi {\n  font-size: 30px;\n  font-weight: 600;\n  line-height: 48px;\n}\r\n.text-3xl-regular {\n  font-size: 32px;\n  font-weight: 400;\n  line-height: 44px;\n}\r\n.text-3xl-semi {\n  font-size: 32px;\n  font-weight: 600;\n  line-height: 44px;\n}\r\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none {\n  pointer-events: none;\n}\r\n.visible {\n  visibility: visible;\n}\r\n.invisible {\n  visibility: hidden;\n}\r\n.static {\n  position: static;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.sticky {\n  position: sticky;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.inset-2 {\n  inset: 0.5rem;\n}\r\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\r\n.inset-x-\\[31\\.75\\%\\] {\n  left: 31.75%;\n  right: 31.75%;\n}\r\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\r\n.inset-y-2 {\n  top: 0.5rem;\n  bottom: 0.5rem;\n}\r\n.inset-y-\\[31\\.75\\%\\] {\n  top: 31.75%;\n  bottom: 31.75%;\n}\r\n.-bottom-\\[calc\\(100\\%-36px\\)\\] {\n  bottom: calc(calc(100% - 36px) * -1);\n}\r\n.-right-1 {\n  right: -0.25rem;\n}\r\n.-right-2 {\n  right: -0.5rem;\n}\r\n.-right-3 {\n  right: -0.75rem;\n}\r\n.-top-1\\/2 {\n  top: -50%;\n}\r\n.-top-2 {\n  top: -0.5rem;\n}\r\n.-top-3 {\n  top: -0.75rem;\n}\r\n.-top-7 {\n  top: -1.75rem;\n}\r\n.bottom-0 {\n  bottom: 0px;\n}\r\n.bottom-1\\/2 {\n  bottom: 50%;\n}\r\n.bottom-4 {\n  bottom: 1rem;\n}\r\n.bottom-6 {\n  bottom: 1.5rem;\n}\r\n.bottom-8 {\n  bottom: 2rem;\n}\r\n.bottom-\\[3px\\] {\n  bottom: 3px;\n}\r\n.left-0 {\n  left: 0px;\n}\r\n.left-1 {\n  left: 0.25rem;\n}\r\n.left-1\\/2 {\n  left: 50%;\n}\r\n.left-2 {\n  left: 0.5rem;\n}\r\n.left-3 {\n  left: 0.75rem;\n}\r\n.left-4 {\n  left: 1rem;\n}\r\n.left-\\[48\\%\\] {\n  left: 48%;\n}\r\n.left-\\[50\\%\\] {\n  left: 50%;\n}\r\n.left-\\[calc\\(20px\\+24px\\+24px\\)\\] {\n  left: calc(20px + 24px + 24px);\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-1 {\n  right: 0.25rem;\n}\r\n.right-1\\/2 {\n  right: 50%;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-3 {\n  right: 0.75rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.right-5 {\n  right: 1.25rem;\n}\r\n.right-6 {\n  right: 1.5rem;\n}\r\n.top-0 {\n  top: 0px;\n}\r\n.top-1\\/2 {\n  top: 50%;\n}\r\n.top-12 {\n  top: 3rem;\n}\r\n.top-16 {\n  top: 4rem;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-3 {\n  top: 0.75rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-\\[1px\\] {\n  top: 1px;\n}\r\n.top-\\[48\\%\\] {\n  top: 48%;\n}\r\n.top-\\[50\\%\\] {\n  top: 50%;\n}\r\n.top-\\[60\\%\\] {\n  top: 60%;\n}\r\n.top-full {\n  top: 100%;\n}\r\n.isolate {\n  isolation: isolate;\n}\r\n.z-0 {\n  z-index: 0;\n}\r\n.z-10 {\n  z-index: 10;\n}\r\n.z-30 {\n  z-index: 30;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.z-\\[1000\\] {\n  z-index: 1000;\n}\r\n.z-\\[1\\] {\n  z-index: 1;\n}\r\n.z-\\[75\\] {\n  z-index: 75;\n}\r\n.z-\\[900\\] {\n  z-index: 900;\n}\r\n.z-\\[9998\\] {\n  z-index: 9998;\n}\r\n.z-\\[9999\\] {\n  z-index: 9999;\n}\r\n.col-span-1 {\n  grid-column: span 1 / span 1;\n}\r\n.col-span-12 {\n  grid-column: span 12 / span 12;\n}\r\n.col-span-2 {\n  grid-column: span 2 / span 2;\n}\r\n.col-span-3 {\n  grid-column: span 3 / span 3;\n}\r\n.col-span-4 {\n  grid-column: span 4 / span 4;\n}\r\n.col-span-9 {\n  grid-column: span 9 / span 9;\n}\r\n.col-span-full {\n  grid-column: 1 / -1;\n}\r\n.-col-start-1 {\n  grid-column-start: -1;\n}\r\n.-col-start-10 {\n  grid-column-start: -10;\n}\r\n.-col-start-11 {\n  grid-column-start: -11;\n}\r\n.-col-start-12 {\n  grid-column-start: -12;\n}\r\n.-col-start-13 {\n  grid-column-start: -13;\n}\r\n.-col-start-2 {\n  grid-column-start: -2;\n}\r\n.-col-start-3 {\n  grid-column-start: -3;\n}\r\n.-col-start-4 {\n  grid-column-start: -4;\n}\r\n.-col-start-5 {\n  grid-column-start: -5;\n}\r\n.-col-start-6 {\n  grid-column-start: -6;\n}\r\n.-col-start-7 {\n  grid-column-start: -7;\n}\r\n.-col-start-8 {\n  grid-column-start: -8;\n}\r\n.-col-start-9 {\n  grid-column-start: -9;\n}\r\n.col-start-1 {\n  grid-column-start: 1;\n}\r\n.col-start-10 {\n  grid-column-start: 10;\n}\r\n.col-start-11 {\n  grid-column-start: 11;\n}\r\n.col-start-12 {\n  grid-column-start: 12;\n}\r\n.col-start-13 {\n  grid-column-start: 13;\n}\r\n.col-start-2 {\n  grid-column-start: 2;\n}\r\n.col-start-3 {\n  grid-column-start: 3;\n}\r\n.col-start-4 {\n  grid-column-start: 4;\n}\r\n.col-start-5 {\n  grid-column-start: 5;\n}\r\n.col-start-6 {\n  grid-column-start: 6;\n}\r\n.col-start-7 {\n  grid-column-start: 7;\n}\r\n.col-start-8 {\n  grid-column-start: 8;\n}\r\n.col-start-9 {\n  grid-column-start: 9;\n}\r\n.-col-end-1 {\n  grid-column-end: -1;\n}\r\n.-col-end-10 {\n  grid-column-end: -10;\n}\r\n.-col-end-11 {\n  grid-column-end: -11;\n}\r\n.-col-end-12 {\n  grid-column-end: -12;\n}\r\n.-col-end-13 {\n  grid-column-end: -13;\n}\r\n.-col-end-2 {\n  grid-column-end: -2;\n}\r\n.-col-end-3 {\n  grid-column-end: -3;\n}\r\n.-col-end-4 {\n  grid-column-end: -4;\n}\r\n.-col-end-5 {\n  grid-column-end: -5;\n}\r\n.-col-end-6 {\n  grid-column-end: -6;\n}\r\n.-col-end-7 {\n  grid-column-end: -7;\n}\r\n.-col-end-8 {\n  grid-column-end: -8;\n}\r\n.-col-end-9 {\n  grid-column-end: -9;\n}\r\n.col-end-1 {\n  grid-column-end: 1;\n}\r\n.col-end-10 {\n  grid-column-end: 10;\n}\r\n.col-end-11 {\n  grid-column-end: 11;\n}\r\n.col-end-12 {\n  grid-column-end: 12;\n}\r\n.col-end-13 {\n  grid-column-end: 13;\n}\r\n.col-end-2 {\n  grid-column-end: 2;\n}\r\n.col-end-3 {\n  grid-column-end: 3;\n}\r\n.col-end-4 {\n  grid-column-end: 4;\n}\r\n.col-end-5 {\n  grid-column-end: 5;\n}\r\n.col-end-6 {\n  grid-column-end: 6;\n}\r\n.col-end-7 {\n  grid-column-end: 7;\n}\r\n.col-end-8 {\n  grid-column-end: 8;\n}\r\n.col-end-9 {\n  grid-column-end: 9;\n}\r\n.row-span-2 {\n  grid-row: span 2 / span 2;\n}\r\n.\\!m-0 {\n  margin: 0px !important;\n}\r\n.m-2 {\n  margin: 0.5rem;\n}\r\n.m-4 {\n  margin: 1rem;\n}\r\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.mx-1 {\n  margin-left: 0.25rem;\n  margin-right: 0.25rem;\n}\r\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-3 {\n  margin-left: 0.75rem;\n  margin-right: 0.75rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.my-10 {\n  margin-top: 2.5rem;\n  margin-bottom: 2.5rem;\n}\r\n.my-12 {\n  margin-top: 3rem;\n  margin-bottom: 3rem;\n}\r\n.my-2 {\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\r\n.my-3 {\n  margin-top: 0.75rem;\n  margin-bottom: 0.75rem;\n}\r\n.my-4 {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n.my-6 {\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\r\n.my-\\[5vh\\] {\n  margin-top: 5vh;\n  margin-bottom: 5vh;\n}\r\n.\\!-mt-0 {\n  margin-top: -0px !important;\n}\r\n.-mt-6 {\n  margin-top: -1.5rem;\n}\r\n.mb-0 {\n  margin-bottom: 0px;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-10 {\n  margin-bottom: 2.5rem;\n}\r\n.mb-12 {\n  margin-bottom: 3rem;\n}\r\n.mb-14 {\n  margin-bottom: 3.5rem;\n}\r\n.mb-16 {\n  margin-bottom: 4rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-20 {\n  margin-bottom: 5rem;\n}\r\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-5 {\n  margin-bottom: 1.25rem;\n}\r\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-1 {\n  margin-left: 0.25rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-3 {\n  margin-left: 0.75rem;\n}\r\n.ml-4 {\n  margin-left: 1rem;\n}\r\n.ml-auto {\n  margin-left: auto;\n}\r\n.mr-2 {\n  margin-right: 0.5rem;\n}\r\n.mt-0 {\n  margin-top: 0px;\n}\r\n.mt-1 {\n  margin-top: 0.25rem;\n}\r\n.mt-10 {\n  margin-top: 2.5rem;\n}\r\n.mt-12 {\n  margin-top: 3rem;\n}\r\n.mt-16 {\n  margin-top: 4rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-20 {\n  margin-top: 5rem;\n}\r\n.mt-24 {\n  margin-top: 6rem;\n}\r\n.mt-3 {\n  margin-top: 0.75rem;\n}\r\n.mt-32 {\n  margin-top: 8rem;\n}\r\n.mt-4 {\n  margin-top: 1rem;\n}\r\n.mt-5 {\n  margin-top: 1.25rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.mt-8 {\n  margin-top: 2rem;\n}\r\n.mt-\\[10vh\\] {\n  margin-top: 10vh;\n}\r\n.mt-\\[30vh\\] {\n  margin-top: 30vh;\n}\r\n.mt-auto {\n  margin-top: auto;\n}\r\n.-mt-4 {\n  margin-top: -1rem;\n}\r\n.box-border {\n  box-sizing: border-box;\n}\r\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\r\n.line-clamp-3 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\r\n.line-clamp-4 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 4;\n}\r\n.block {\n  display: block;\n}\r\n.inline-block {\n  display: inline-block;\n}\r\n.inline {\n  display: inline;\n}\r\n.\\!flex {\n  display: flex !important;\n}\r\n.flex {\n  display: flex;\n}\r\n.inline-flex {\n  display: inline-flex;\n}\r\n.table {\n  display: table;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.aspect-\\[1\\/1\\] {\n  aspect-ratio: 1/1;\n}\r\n.aspect-\\[11\\/14\\] {\n  aspect-ratio: 11/14;\n}\r\n.aspect-\\[16\\/10\\] {\n  aspect-ratio: 16/10;\n}\r\n.aspect-\\[29\\/34\\] {\n  aspect-ratio: 29/34;\n}\r\n.aspect-\\[3\\/4\\] {\n  aspect-ratio: 3/4;\n}\r\n.aspect-\\[9\\/16\\] {\n  aspect-ratio: 9/16;\n}\r\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\r\n.size-1 {\n  width: 0.25rem;\n  height: 0.25rem;\n}\r\n.size-11 {\n  width: 2.75rem;\n  height: 2.75rem;\n}\r\n.size-4 {\n  width: 1rem;\n  height: 1rem;\n}\r\n.size-5 {\n  width: 1.25rem;\n  height: 1.25rem;\n}\r\n.size-6 {\n  width: 1.5rem;\n  height: 1.5rem;\n}\r\n.size-7 {\n  width: 1.75rem;\n  height: 1.75rem;\n}\r\n.size-8 {\n  width: 2rem;\n  height: 2rem;\n}\r\n.size-9 {\n  width: 2.25rem;\n  height: 2.25rem;\n}\r\n.size-\\[15px\\] {\n  width: 15px;\n  height: 15px;\n}\r\n.size-\\[3px\\] {\n  width: 3px;\n  height: 3px;\n}\r\n.size-full {\n  width: 100%;\n  height: 100%;\n}\r\n.\\!h-10 {\n  height: 2.5rem !important;\n}\r\n.\\!h-12 {\n  height: 3rem !important;\n}\r\n.\\!h-5 {\n  height: 1.25rem !important;\n}\r\n.\\!h-8 {\n  height: 2rem !important;\n}\r\n.\\!h-9 {\n  height: 2.25rem !important;\n}\r\n.\\!h-\\[90vh\\] {\n  height: 90vh !important;\n}\r\n.\\!h-fit {\n  height: -moz-fit-content !important;\n  height: fit-content !important;\n}\r\n.h-0\\.5 {\n  height: 0.125rem;\n}\r\n.h-1 {\n  height: 0.25rem;\n}\r\n.h-1\\.5 {\n  height: 0.375rem;\n}\r\n.h-1\\/2 {\n  height: 50%;\n}\r\n.h-1\\/3 {\n  height: 33.333333%;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-11 {\n  height: 2.75rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-14 {\n  height: 3.5rem;\n}\r\n.h-16 {\n  height: 4rem;\n}\r\n.h-18 {\n  height: 4.5rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-2\\.5 {\n  height: 0.625rem;\n}\r\n.h-20 {\n  height: 5rem;\n}\r\n.h-24 {\n  height: 6rem;\n}\r\n.h-25 {\n  height: 6.25rem;\n}\r\n.h-28 {\n  height: 7rem;\n}\r\n.h-3 {\n  height: 0.75rem;\n}\r\n.h-3\\.5 {\n  height: 0.875rem;\n}\r\n.h-30 {\n  height: 7.5rem;\n}\r\n.h-32 {\n  height: 8rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-44 {\n  height: 11rem;\n}\r\n.h-48 {\n  height: 12rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-56 {\n  height: 14rem;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-7 {\n  height: 1.75rem;\n}\r\n.h-72 {\n  height: 18rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-80 {\n  height: 20rem;\n}\r\n.h-9 {\n  height: 2.25rem;\n}\r\n.h-\\[1\\.5px\\] {\n  height: 1.5px;\n}\r\n.h-\\[12px\\] {\n  height: 12px;\n}\r\n.h-\\[14px\\] {\n  height: 14px;\n}\r\n.h-\\[15px\\] {\n  height: 15px;\n}\r\n.h-\\[16px\\] {\n  height: 16px;\n}\r\n.h-\\[18px\\] {\n  height: 18px;\n}\r\n.h-\\[1px\\] {\n  height: 1px;\n}\r\n.h-\\[2\\.2em\\] {\n  height: 2.2em;\n}\r\n.h-\\[200px\\] {\n  height: 200px;\n}\r\n.h-\\[2px\\] {\n  height: 2px;\n}\r\n.h-\\[300px\\] {\n  height: 300px;\n}\r\n.h-\\[320px\\] {\n  height: 320px;\n}\r\n.h-\\[360px\\] {\n  height: 360px;\n}\r\n.h-\\[400px\\] {\n  height: 400px;\n}\r\n.h-\\[40px\\] {\n  height: 40px;\n}\r\n.h-\\[40vh\\] {\n  height: 40vh;\n}\r\n.h-\\[52px\\] {\n  height: 52px;\n}\r\n.h-\\[56px\\] {\n  height: 56px;\n}\r\n.h-\\[90vh\\] {\n  height: 90vh;\n}\r\n.h-\\[calc\\(100vh-10rem\\)\\] {\n  height: calc(100vh - 10rem);\n}\r\n.h-\\[calc\\(100vh-1rem\\)\\] {\n  height: calc(100vh - 1rem);\n}\r\n.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n  height: var(--radix-navigation-menu-viewport-height);\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\r\n.h-auto {\n  height: auto;\n}\r\n.h-fit {\n  height: -moz-fit-content;\n  height: fit-content;\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.h-screen {\n  height: 100vh;\n}\r\n.max-h-0 {\n  max-height: 0px;\n}\r\n.max-h-48 {\n  max-height: 12rem;\n}\r\n.max-h-60 {\n  max-height: 15rem;\n}\r\n.max-h-96 {\n  max-height: 24rem;\n}\r\n.max-h-\\[1000px\\] {\n  max-height: 1000px;\n}\r\n.max-h-\\[200px\\] {\n  max-height: 200px;\n}\r\n.max-h-\\[300px\\] {\n  max-height: 300px;\n}\r\n.max-h-\\[400px\\] {\n  max-height: 400px;\n}\r\n.max-h-\\[442px\\] {\n  max-height: 442px;\n}\r\n.max-h-\\[500px\\] {\n  max-height: 500px;\n}\r\n.max-h-\\[534px\\] {\n  max-height: 534px;\n}\r\n.max-h-\\[70px\\] {\n  max-height: 70px;\n}\r\n.max-h-\\[75vh\\] {\n  max-height: 75vh;\n}\r\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\r\n.max-h-\\[var\\(--radix-popper-available-height\\)\\] {\n  max-height: var(--radix-popper-available-height);\n}\r\n.max-h-full {\n  max-height: 100%;\n}\r\n.\\!min-h-5 {\n  min-height: 1.25rem !important;\n}\r\n.min-h-0 {\n  min-height: 0px;\n}\r\n.min-h-10 {\n  min-height: 2.5rem;\n}\r\n.min-h-40 {\n  min-height: 10rem;\n}\r\n.min-h-\\[120px\\] {\n  min-height: 120px;\n}\r\n.min-h-\\[180px\\] {\n  min-height: 180px;\n}\r\n.min-h-\\[200px\\] {\n  min-height: 200px;\n}\r\n.min-h-\\[250px\\] {\n  min-height: 250px;\n}\r\n.min-h-\\[25px\\] {\n  min-height: 25px;\n}\r\n.min-h-\\[32px\\] {\n  min-height: 32px;\n}\r\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\r\n.min-h-\\[50px\\] {\n  min-height: 50px;\n}\r\n.min-h-\\[50vh\\] {\n  min-height: 50vh;\n}\r\n.min-h-\\[55vh\\] {\n  min-height: 55vh;\n}\r\n.min-h-\\[60dvh\\] {\n  min-height: 60dvh;\n}\r\n.min-h-\\[60px\\] {\n  min-height: 60px;\n}\r\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\r\n.min-h-\\[calc\\(100vh-100px\\)\\] {\n  min-height: calc(100vh - 100px);\n}\r\n.min-h-\\[calc\\(100vh-64px\\)\\] {\n  min-height: calc(100vh - 64px);\n}\r\n.min-h-full {\n  min-height: 100%;\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.\\!w-5 {\n  width: 1.25rem !important;\n}\r\n.w-1 {\n  width: 0.25rem;\n}\r\n.w-1\\.5 {\n  width: 0.375rem;\n}\r\n.w-1\\/2 {\n  width: 50%;\n}\r\n.w-1\\/3 {\n  width: 33.333333%;\n}\r\n.w-1\\/4 {\n  width: 25%;\n}\r\n.w-1\\/6 {\n  width: 16.666667%;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-11 {\n  width: 2.75rem;\n}\r\n.w-12 {\n  width: 3rem;\n}\r\n.w-14 {\n  width: 3.5rem;\n}\r\n.w-16 {\n  width: 4rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-2\\.5 {\n  width: 0.625rem;\n}\r\n.w-2\\/3 {\n  width: 66.666667%;\n}\r\n.w-2\\/5 {\n  width: 40%;\n}\r\n.w-2\\/6 {\n  width: 33.333333%;\n}\r\n.w-20 {\n  width: 5rem;\n}\r\n.w-24 {\n  width: 6rem;\n}\r\n.w-25 {\n  width: 6.25rem;\n}\r\n.w-28 {\n  width: 7rem;\n}\r\n.w-3 {\n  width: 0.75rem;\n}\r\n.w-3\\.5 {\n  width: 0.875rem;\n}\r\n.w-3\\/4 {\n  width: 75%;\n}\r\n.w-3\\/5 {\n  width: 60%;\n}\r\n.w-3\\/6 {\n  width: 50%;\n}\r\n.w-32 {\n  width: 8rem;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-40 {\n  width: 10rem;\n}\r\n.w-44 {\n  width: 11rem;\n}\r\n.w-48 {\n  width: 12rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-60 {\n  width: 15rem;\n}\r\n.w-64 {\n  width: 16rem;\n}\r\n.w-7 {\n  width: 1.75rem;\n}\r\n.w-72 {\n  width: 18rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-9 {\n  width: 2.25rem;\n}\r\n.w-96 {\n  width: 24rem;\n}\r\n.w-\\[1\\.5px\\] {\n  width: 1.5px;\n}\r\n.w-\\[100px\\] {\n  width: 100px;\n}\r\n.w-\\[128px\\] {\n  width: 128px;\n}\r\n.w-\\[12px\\] {\n  width: 12px;\n}\r\n.w-\\[138px\\] {\n  width: 138px;\n}\r\n.w-\\[14px\\] {\n  width: 14px;\n}\r\n.w-\\[15px\\] {\n  width: 15px;\n}\r\n.w-\\[180px\\] {\n  width: 180px;\n}\r\n.w-\\[28px\\] {\n  width: 28px;\n}\r\n.w-\\[290px\\] {\n  width: 290px;\n}\r\n.w-\\[2px\\] {\n  width: 2px;\n}\r\n.w-\\[32px\\] {\n  width: 32px;\n}\r\n.w-\\[40\\%\\] {\n  width: 40%;\n}\r\n.w-\\[408px\\] {\n  width: 408px;\n}\r\n.w-\\[440px\\] {\n  width: 440px;\n}\r\n.w-\\[50\\%\\] {\n  width: 50%;\n}\r\n.w-\\[60\\%\\] {\n  width: 60%;\n}\r\n.w-\\[66px\\] {\n  width: 66px;\n}\r\n.w-\\[90vw\\] {\n  width: 90vw;\n}\r\n.w-\\[95vw\\] {\n  width: 95vw;\n}\r\n.w-\\[calc\\(100\\%-2rem\\)\\] {\n  width: calc(100% - 2rem);\n}\r\n.w-\\[calc\\(20px\\+24px\\+24px\\)\\] {\n  width: calc(20px + 24px + 24px);\n}\r\n.w-\\[calc\\(28px\\+24px\\+4px\\)\\] {\n  width: calc(28px + 24px + 4px);\n}\r\n.w-auto {\n  width: auto;\n}\r\n.w-fit {\n  width: -moz-fit-content;\n  width: fit-content;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.w-max {\n  width: -moz-max-content;\n  width: max-content;\n}\r\n.w-px {\n  width: 1px;\n}\r\n.w-screen {\n  width: 100vw;\n}\r\n.\\!min-w-5 {\n  min-width: 1.25rem !important;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-20 {\n  min-width: 5rem;\n}\r\n.min-w-\\[20px\\] {\n  min-width: 20px;\n}\r\n.min-w-\\[210px\\] {\n  min-width: 210px;\n}\r\n.min-w-\\[220px\\] {\n  min-width: 220px;\n}\r\n.min-w-\\[300px\\] {\n  min-width: 300px;\n}\r\n.min-w-\\[320px\\] {\n  min-width: 320px;\n}\r\n.min-w-\\[32px\\] {\n  min-width: 32px;\n}\r\n.min-w-\\[360px\\] {\n  min-width: 360px;\n}\r\n.min-w-\\[40px\\] {\n  min-width: 40px;\n}\r\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\r\n.min-w-\\[calc\\(20px\\+24px\\+24px\\)\\] {\n  min-width: calc(20px + 24px + 24px);\n}\r\n.min-w-\\[calc\\(28px\\+24px\\+4px\\)\\] {\n  min-width: calc(28px + 24px + 4px);\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\r\n.\\!max-w-\\[700px\\] {\n  max-width: 700px !important;\n}\r\n.max-w-2xl {\n  max-width: 42rem;\n}\r\n.max-w-3xl {\n  max-width: 48rem;\n}\r\n.max-w-42 {\n  max-width: 10.5rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-6xl {\n  max-width: 72rem;\n}\r\n.max-w-\\[1200px\\] {\n  max-width: 1200px;\n}\r\n.max-w-\\[1280px\\] {\n  max-width: 1280px;\n}\r\n.max-w-\\[200px\\] {\n  max-width: 200px;\n}\r\n.max-w-\\[280px\\] {\n  max-width: 280px;\n}\r\n.max-w-\\[400px\\] {\n  max-width: 400px;\n}\r\n.max-w-\\[440px\\] {\n  max-width: 440px;\n}\r\n.max-w-\\[767px\\] {\n  max-width: 767px;\n}\r\n.max-w-\\[90\\%\\] {\n  max-width: 90%;\n}\r\n.max-w-\\[900px\\] {\n  max-width: 900px;\n}\r\n.max-w-\\[calc\\(20px\\+24px\\+24px\\)\\] {\n  max-width: calc(20px + 24px + 24px);\n}\r\n.max-w-\\[calc\\(28px\\+24px\\+4px\\)\\] {\n  max-width: calc(28px + 24px + 4px);\n}\r\n.max-w-full {\n  max-width: 100%;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-max {\n  max-width: -moz-max-content;\n  max-width: max-content;\n}\r\n.max-w-md {\n  max-width: 28rem;\n}\r\n.max-w-screen-lg {\n  max-width: 1024px;\n}\r\n.max-w-screen-md {\n  max-width: 768px;\n}\r\n.max-w-sm {\n  max-width: 24rem;\n}\r\n.max-w-xl {\n  max-width: 36rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\r\n.shrink-0 {\n  flex-shrink: 0;\n}\r\n.flex-grow {\n  flex-grow: 1;\n}\r\n.grow {\n  flex-grow: 1;\n}\r\n.basis-0 {\n  flex-basis: 0px;\n}\r\n.caption-bottom {\n  caption-side: bottom;\n}\r\n.border-collapse {\n  border-collapse: collapse;\n}\r\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1 {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-1\\/2 {\n  --tw-translate-y: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-rotate-90 {\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-95 {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\!transform-none {\n  transform: none !important;\n}\r\n@keyframes bounce {\n\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\r\n.animate-bounce {\n  animation: bounce 1s infinite;\n}\r\n@keyframes marquee {\n\n  from {\n    transform: translateX(0);\n  }\n\n  to {\n    transform: translateX(calc(-100% - var(--gap)));\n  }\n}\r\n.animate-marquee {\n  animation: marquee var(--duration) linear infinite;\n}\r\n@keyframes marquee-vertical {\n\n  from {\n    transform: translateY(0);\n  }\n\n  to {\n    transform: translateY(calc(-100% - var(--gap)));\n  }\n}\r\n.animate-marquee-vertical {\n  animation: marquee-vertical var(--duration) linear infinite;\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\r\n.cursor-default {\n  cursor: default;\n}\r\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.cursor-text {\n  cursor: text;\n}\r\n.cursor-zoom-in {\n  cursor: zoom-in;\n}\r\n.touch-none {\n  touch-action: none;\n}\r\n.touch-manipulation {\n  touch-action: manipulation;\n}\r\n.select-none {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\r\n.resize {\n  resize: both;\n}\r\n.scroll-m-20 {\n  scroll-margin: 5rem;\n}\r\n.list-none {\n  list-style-type: none;\n}\r\n.appearance-none {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n}\r\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.grid-cols-12 {\n  grid-template-columns: repeat(12, minmax(0, 1fr));\n}\r\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\r\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\r\n.grid-cols-5 {\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\r\n.grid-cols-6 {\n  grid-template-columns: repeat(6, minmax(0, 1fr));\n}\r\n.grid-cols-7 {\n  grid-template-columns: repeat(7, minmax(0, 1fr));\n}\r\n.grid-cols-8 {\n  grid-template-columns: repeat(8, minmax(0, 1fr));\n}\r\n.grid-cols-9 {\n  grid-template-columns: repeat(9, minmax(0, 1fr));\n}\r\n.grid-cols-\\[122px_1fr\\] {\n  grid-template-columns: 122px 1fr;\n}\r\n.grid-cols-\\[144px_1fr\\] {\n  grid-template-columns: 144px 1fr;\n}\r\n.grid-cols-\\[15px_1fr\\] {\n  grid-template-columns: 15px 1fr;\n}\r\n.grid-cols-\\[1fr_80px\\] {\n  grid-template-columns: 1fr 80px;\n}\r\n.grid-cols-\\[20px_1fr\\] {\n  grid-template-columns: 20px 1fr;\n}\r\n.grid-cols-\\[20px_1fr_20px\\] {\n  grid-template-columns: 20px 1fr 20px;\n}\r\n.grid-cols-\\[28px_1fr\\] {\n  grid-template-columns: 28px 1fr;\n}\r\n.grid-cols-\\[28px_1fr_28px\\] {\n  grid-template-columns: 28px 1fr 28px;\n}\r\n.grid-cols-\\[32px_1fr\\] {\n  grid-template-columns: 32px 1fr;\n}\r\n.grid-cols-\\[32px_1fr_32px\\] {\n  grid-template-columns: 32px 1fr 32px;\n}\r\n.grid-cols-\\[40\\%_1fr\\] {\n  grid-template-columns: 40% 1fr;\n}\r\n.grid-cols-\\[4px_1fr\\] {\n  grid-template-columns: 4px 1fr;\n}\r\n.grid-cols-\\[auto\\2c 1fr\\] {\n  grid-template-columns: auto 1fr;\n}\r\n.grid-rows-2 {\n  grid-template-rows: repeat(2, minmax(0, 1fr));\n}\r\n.flex-row {\n  flex-direction: row;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\r\n.flex-wrap {\n  flex-wrap: wrap;\n}\r\n.flex-nowrap {\n  flex-wrap: nowrap;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.items-baseline {\n  align-items: baseline;\n}\r\n.items-stretch {\n  align-items: stretch;\n}\r\n.justify-start {\n  justify-content: flex-start;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.justify-around {\n  justify-content: space-around;\n}\r\n.gap-0 {\n  gap: 0px;\n}\r\n.gap-0\\.5 {\n  gap: 0.125rem;\n}\r\n.gap-1 {\n  gap: 0.25rem;\n}\r\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\r\n.gap-10 {\n  gap: 2.5rem;\n}\r\n.gap-12 {\n  gap: 3rem;\n}\r\n.gap-14 {\n  gap: 3.5rem;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-3 {\n  gap: 0.75rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.gap-5 {\n  gap: 1.25rem;\n}\r\n.gap-6 {\n  gap: 1.5rem;\n}\r\n.gap-8 {\n  gap: 2rem;\n}\r\n.gap-x-0\\.5 {\n  -moz-column-gap: 0.125rem;\n       column-gap: 0.125rem;\n}\r\n.gap-x-1 {\n  -moz-column-gap: 0.25rem;\n       column-gap: 0.25rem;\n}\r\n.gap-x-1\\.5 {\n  -moz-column-gap: 0.375rem;\n       column-gap: 0.375rem;\n}\r\n.gap-x-10 {\n  -moz-column-gap: 2.5rem;\n       column-gap: 2.5rem;\n}\r\n.gap-x-16 {\n  -moz-column-gap: 4rem;\n       column-gap: 4rem;\n}\r\n.gap-x-2 {\n  -moz-column-gap: 0.5rem;\n       column-gap: 0.5rem;\n}\r\n.gap-x-3 {\n  -moz-column-gap: 0.75rem;\n       column-gap: 0.75rem;\n}\r\n.gap-x-4 {\n  -moz-column-gap: 1rem;\n       column-gap: 1rem;\n}\r\n.gap-x-40 {\n  -moz-column-gap: 10rem;\n       column-gap: 10rem;\n}\r\n.gap-x-5 {\n  -moz-column-gap: 1.25rem;\n       column-gap: 1.25rem;\n}\r\n.gap-x-6 {\n  -moz-column-gap: 1.5rem;\n       column-gap: 1.5rem;\n}\r\n.gap-x-8 {\n  -moz-column-gap: 2rem;\n       column-gap: 2rem;\n}\r\n.gap-y-0\\.5 {\n  row-gap: 0.125rem;\n}\r\n.gap-y-1 {\n  row-gap: 0.25rem;\n}\r\n.gap-y-10 {\n  row-gap: 2.5rem;\n}\r\n.gap-y-12 {\n  row-gap: 3rem;\n}\r\n.gap-y-16 {\n  row-gap: 4rem;\n}\r\n.gap-y-2 {\n  row-gap: 0.5rem;\n}\r\n.gap-y-3 {\n  row-gap: 0.75rem;\n}\r\n.gap-y-4 {\n  row-gap: 1rem;\n}\r\n.gap-y-6 {\n  row-gap: 1.5rem;\n}\r\n.gap-y-8 {\n  row-gap: 2rem;\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-10 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-0 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-10 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.divide-x > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-x-reverse: 0;\n  border-right-width: calc(1px * var(--tw-divide-x-reverse));\n  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));\n}\r\n.divide-y > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\r\n.self-start {\n  align-self: flex-start;\n}\r\n.self-end {\n  align-self: flex-end;\n}\r\n.self-stretch {\n  align-self: stretch;\n}\r\n.justify-self-end {\n  justify-self: end;\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.overflow-visible {\n  overflow: visible;\n}\r\n.overflow-x-auto {\n  overflow-x: auto;\n}\r\n.overflow-y-auto {\n  overflow-y: auto;\n}\r\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\r\n.overflow-y-hidden {\n  overflow-y: hidden;\n}\r\n.overflow-x-scroll {\n  overflow-x: scroll;\n}\r\n.overflow-y-scroll {\n  overflow-y: scroll;\n}\r\n.overscroll-none {\n  overscroll-behavior: none;\n}\r\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.text-ellipsis {\n  text-overflow: ellipsis;\n}\r\n.whitespace-nowrap {\n  white-space: nowrap;\n}\r\n.whitespace-pre-line {\n  white-space: pre-line;\n}\r\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\r\n.text-nowrap {\n  text-wrap: nowrap;\n}\r\n.text-pretty {\n  text-wrap: pretty;\n}\r\n.break-words {\n  overflow-wrap: break-word;\n}\r\n.rounded-\\[10px\\] {\n  border-radius: 10px;\n}\r\n.rounded-\\[16px\\] {\n  border-radius: 16px;\n}\r\n.rounded-\\[33px\\] {\n  border-radius: 33px;\n}\r\n.rounded-\\[3px\\] {\n  border-radius: 3px;\n}\r\n.rounded-\\[49px\\] {\n  border-radius: 49px;\n}\r\n.rounded-\\[4px\\] {\n  border-radius: 4px;\n}\r\n.rounded-\\[55px\\] {\n  border-radius: 55px;\n}\r\n.rounded-\\[66px\\] {\n  border-radius: 66px;\n}\r\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\r\n.rounded-circle {\n  border-radius: 9999px;\n}\r\n.rounded-full {\n  border-radius: 100%;\n}\r\n.rounded-large {\n  border-radius: 16px;\n}\r\n.rounded-lg {\n  border-radius: var(--radius);\n}\r\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\r\n.rounded-none {\n  border-radius: 0px;\n}\r\n.rounded-rounded {\n  border-radius: 8px;\n}\r\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.rounded-xl {\n  border-radius: 50px;\n}\r\n.rounded-xs {\n  border-radius: 4px;\n}\r\n.\\!rounded-r-none {\n  border-top-right-radius: 0px !important;\n  border-bottom-right-radius: 0px !important;\n}\r\n.rounded-b-lg {\n  border-bottom-right-radius: var(--radius);\n  border-bottom-left-radius: var(--radius);\n}\r\n.rounded-l-lg {\n  border-top-left-radius: var(--radius);\n  border-bottom-left-radius: var(--radius);\n}\r\n.rounded-l-none {\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\r\n.rounded-r-none {\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n}\r\n.rounded-r-sm {\n  border-top-right-radius: calc(var(--radius) - 4px);\n  border-bottom-right-radius: calc(var(--radius) - 4px);\n}\r\n.rounded-t-\\[10px\\] {\n  border-top-left-radius: 10px;\n  border-top-right-radius: 10px;\n}\r\n.rounded-t-md {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-top-right-radius: calc(var(--radius) - 2px);\n}\r\n.rounded-tl-sm {\n  border-top-left-radius: calc(var(--radius) - 4px);\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-0 {\n  border-width: 0px;\n}\r\n.border-2 {\n  border-width: 2px;\n}\r\n.border-\\[5px\\] {\n  border-width: 5px;\n}\r\n.border-y {\n  border-top-width: 1px;\n  border-bottom-width: 1px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-b-0 {\n  border-bottom-width: 0px;\n}\r\n.border-l {\n  border-left-width: 1px;\n}\r\n.border-l-2 {\n  border-left-width: 2px;\n}\r\n.border-r {\n  border-right-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-t-0 {\n  border-top-width: 0px;\n}\r\n.border-t-\\[1px\\] {\n  border-top-width: 1px;\n}\r\n.border-solid {\n  border-style: solid;\n}\r\n.border-dashed {\n  border-style: dashed;\n}\r\n.\\!border-none {\n  border-style: none !important;\n}\r\n.border-none {\n  border-style: none;\n}\r\n.border-\\[\\#000\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));\n}\r\n.border-\\[\\#EAECF0\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(234 236 240 / var(--tw-border-opacity, 1));\n}\r\n.border-black\\/30 {\n  border-color: rgb(0 0 0 / 0.3);\n}\r\n.border-current {\n  border-color: currentColor;\n}\r\n.border-error-light {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 205 202 / var(--tw-border-opacity, 1));\n}\r\n.border-error-main {\n  --tw-border-opacity: 1;\n  border-color: rgb(217 45 32 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(235 239 244 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(216 224 233 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(193 205 215 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-450 {\n  --tw-border-opacity: 1;\n  border-color: rgb(174 180 190 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(125 135 156 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-900 {\n  --tw-border-opacity: 1;\n  border-color: rgb(43 52 69 / var(--tw-border-opacity, 1));\n}\r\n.border-input {\n  border-color: hsl(var(--input));\n}\r\n.border-primary-light {\n  --tw-border-opacity: 1;\n  border-color: rgb(119 184 79 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-main {\n  --tw-border-opacity: 1;\n  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-main\\/50 {\n  border-color: rgb(89 183 31 / 0.5);\n}\r\n.border-transparent {\n  border-color: rgba(0,0,0,0);\n}\r\n.border-ui-border-base {\n  border-color: var(--border-base);\n}\r\n.border-ui-border-interactive {\n  border-color: var(--border-interactive);\n}\r\n.border-ui-contrast-border-base {\n  border-color: var(--contrast-border-base);\n}\r\n.border-ui-contrast-border-bot {\n  border-color: var(--contrast-border-bot);\n}\r\n.border-ui-tag-blue-border {\n  border-color: var(--tag-blue-border);\n}\r\n.border-ui-tag-green-border {\n  border-color: var(--tag-green-border);\n}\r\n.border-ui-tag-neutral-border {\n  border-color: var(--tag-neutral-border);\n}\r\n.border-ui-tag-orange-border {\n  border-color: var(--tag-orange-border);\n}\r\n.border-ui-tag-purple-border {\n  border-color: var(--tag-purple-border);\n}\r\n.border-ui-tag-red-border {\n  border-color: var(--tag-red-border);\n}\r\n.border-b-transparent {\n  border-bottom-color: rgba(0,0,0,0);\n}\r\n.border-b-ui-border-menu-bot {\n  border-bottom-color: var(--border-menu-bot);\n}\r\n.border-l-transparent {\n  border-left-color: rgba(0,0,0,0);\n}\r\n.border-r-ui-border-base {\n  border-right-color: var(--border-base);\n}\r\n.border-t-transparent {\n  border-top-color: rgba(0,0,0,0);\n}\r\n.border-t-ui-border-menu-top {\n  border-top-color: var(--border-menu-top);\n}\r\n.\\!bg-ui-bg-disabled {\n  background-color: var(--bg-disabled) !important;\n}\r\n.\\!bg-ui-bg-interactive {\n  background-color: var(--bg-interactive) !important;\n}\r\n.bg-\\[\\#CBAB7C\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(203 171 124 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#F0F4F8\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 244 248 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#eef1cf\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 241 207 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[rgba\\(3\\2c 7\\2c 18\\2c 0\\.5\\)\\] {\n  background-color: rgba(3,7,18,0.5);\n}\r\n.bg-accent {\n  background-color: hsl(var(--accent));\n}\r\n.bg-background {\n  background-color: hsl(var(--background));\n}\r\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.bg-black\\/70 {\n  background-color: rgb(0 0 0 / 0.7);\n}\r\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\r\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-border {\n  background-color: hsl(var(--border));\n}\r\n.bg-card {\n  background-color: hsl(var(--card));\n}\r\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\r\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(244 246 248 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-100\\/50 {\n  background-color: rgb(244 246 248 / 0.5);\n}\r\n.bg-gray-100\\/80 {\n  background-color: rgb(244 246 248 / 0.8);\n}\r\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(216 224 233 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 86 107 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-750 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(67 77 86 / var(--tw-bg-opacity, 1));\n}\r\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\r\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.bg-neutral-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));\n}\r\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\r\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\r\n.bg-primary-extraLight {\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 242 226 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-light {\n  --tw-bg-opacity: 1;\n  background-color: rgb(119 184 79 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-lighter {\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 251 230 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-main {\n  --tw-bg-opacity: 1;\n  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-main\\/10 {\n  background-color: rgb(89 183 31 / 0.1);\n}\r\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\r\n.bg-red {\n  --tw-bg-opacity: 1;\n  background-color: rgb(210 63 87 / var(--tw-bg-opacity, 1));\n}\r\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\r\n.bg-transparent {\n  background-color: rgba(0,0,0,0);\n}\r\n.bg-ui-bg-base {\n  background-color: var(--bg-base);\n}\r\n.bg-ui-bg-component {\n  background-color: var(--bg-component);\n}\r\n.bg-ui-bg-component-hover {\n  background-color: var(--bg-component-hover);\n}\r\n.bg-ui-bg-disabled {\n  background-color: var(--bg-disabled);\n}\r\n.bg-ui-bg-field {\n  background-color: var(--bg-field);\n}\r\n.bg-ui-bg-interactive {\n  background-color: var(--bg-interactive);\n}\r\n.bg-ui-bg-overlay {\n  background-color: var(--bg-overlay);\n}\r\n.bg-ui-bg-subtle {\n  background-color: var(--bg-subtle);\n}\r\n.bg-ui-bg-subtle-hover {\n  background-color: var(--bg-subtle-hover);\n}\r\n.bg-ui-bg-switch-off {\n  background-color: var(--bg-switch-off);\n}\r\n.bg-ui-border-base {\n  background-color: var(--border-base);\n}\r\n.bg-ui-border-menu-bot {\n  background-color: var(--border-menu-bot);\n}\r\n.bg-ui-border-menu-top {\n  background-color: var(--border-menu-top);\n}\r\n.bg-ui-button-danger {\n  background-color: var(--button-danger);\n}\r\n.bg-ui-button-inverted {\n  background-color: var(--button-inverted);\n}\r\n.bg-ui-button-neutral {\n  background-color: var(--button-neutral);\n}\r\n.bg-ui-button-neutral-hover {\n  background-color: var(--button-neutral-hover);\n}\r\n.bg-ui-button-transparent {\n  background-color: var(--button-transparent);\n}\r\n.bg-ui-contrast-bg-base {\n  background-color: var(--contrast-bg-base);\n}\r\n.bg-ui-contrast-bg-subtle {\n  background-color: var(--contrast-bg-subtle);\n}\r\n.bg-ui-contrast-border-base {\n  background-color: var(--contrast-border-base);\n}\r\n.bg-ui-contrast-border-top {\n  background-color: var(--contrast-border-top);\n}\r\n.bg-ui-contrast-fg-primary {\n  background-color: var(--contrast-fg-primary);\n}\r\n.bg-ui-fg-on-color {\n  background-color: var(--fg-on-color);\n}\r\n.bg-ui-tag-blue-bg {\n  background-color: var(--tag-blue-bg);\n}\r\n.bg-ui-tag-green-bg {\n  background-color: var(--tag-green-bg);\n}\r\n.bg-ui-tag-green-icon {\n  background-color: var(--tag-green-icon);\n}\r\n.bg-ui-tag-neutral-bg {\n  background-color: var(--tag-neutral-bg);\n}\r\n.bg-ui-tag-neutral-icon {\n  background-color: var(--tag-neutral-icon);\n}\r\n.bg-ui-tag-orange-bg {\n  background-color: var(--tag-orange-bg);\n}\r\n.bg-ui-tag-orange-icon {\n  background-color: var(--tag-orange-icon);\n}\r\n.bg-ui-tag-purple-bg {\n  background-color: var(--tag-purple-bg);\n}\r\n.bg-ui-tag-red-bg {\n  background-color: var(--tag-red-bg);\n}\r\n.bg-ui-tag-red-icon {\n  background-color: var(--tag-red-icon);\n}\r\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-white\\/80 {\n  background-color: rgb(255 255 255 / 0.8);\n}\r\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 248 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\r\n.bg-zinc-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-zinc-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));\n}\r\n.bg-zinc-200\\/80 {\n  background-color: rgb(228 228 231 / 0.8);\n}\r\n.bg-zinc-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(212 212 216 / var(--tw-bg-opacity, 1));\n}\r\n.bg-zinc-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));\n}\r\n.bg-opacity-30 {\n  --tw-bg-opacity: 0.3;\n}\r\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\r\n.bg-opacity-75 {\n  --tw-bg-opacity: 0.75;\n}\r\n.bg-\\[linear-gradient\\(0deg\\2c var\\(--border-strong\\)_1px\\2c transparent_1px\\)\\] {\n  background-image: linear-gradient(0deg,var(--border-strong) 1px,transparent 1px);\n}\r\n.bg-\\[linear-gradient\\(90deg\\2c var\\(--border-strong\\)_1px\\2c transparent_1px\\)\\] {\n  background-image: linear-gradient(90deg,var(--border-strong) 1px,transparent 1px);\n}\r\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-l {\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-t {\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\r\n.from-black {\n  --tw-gradient-from: #000 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary-main\\/10 {\n  --tw-gradient-from: rgb(89 183 31 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(89 183 31 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.to-primary-main\\/20 {\n  --tw-gradient-to: rgb(89 183 31 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-transparent {\n  --tw-gradient-to: rgba(0,0,0,0) var(--tw-gradient-to-position);\n}\r\n.bg-\\[length\\:1px_4px\\] {\n  background-size: 1px 4px;\n}\r\n.bg-\\[length\\:4px_1px\\] {\n  background-size: 4px 1px;\n}\r\n.fill-\\[\\#9ca3af\\] {\n  fill: #9ca3af;\n}\r\n.fill-current {\n  fill: currentColor;\n}\r\n.fill-ui-fg-muted {\n  fill: var(--fg-muted);\n}\r\n.fill-ui-fg-subtle {\n  fill: var(--fg-subtle);\n}\r\n.object-contain {\n  -o-object-fit: contain;\n     object-fit: contain;\n}\r\n.object-cover {\n  -o-object-fit: cover;\n     object-fit: cover;\n}\r\n.object-center {\n  -o-object-position: center;\n     object-position: center;\n}\r\n.\\!p-0 {\n  padding: 0px !important;\n}\r\n.\\!p-4 {\n  padding: 1rem !important;\n}\r\n.p-0 {\n  padding: 0px;\n}\r\n.p-0\\.5 {\n  padding: 0.125rem;\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-1\\.5 {\n  padding: 0.375rem;\n}\r\n.p-10 {\n  padding: 2.5rem;\n}\r\n.p-2 {\n  padding: 0.5rem;\n}\r\n.p-2\\.5 {\n  padding: 0.625rem;\n}\r\n.p-20 {\n  padding: 5rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-3\\.5 {\n  padding: 0.875rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-5 {\n  padding: 1.25rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.p-\\[1px\\] {\n  padding: 1px;\n}\r\n.p-\\[6px\\] {\n  padding: 6px;\n}\r\n.p-px {\n  padding: 1px;\n}\r\n.\\!py-0 {\n  padding-top: 0px !important;\n  padding-bottom: 0px !important;\n}\r\n.px-0 {\n  padding-left: 0px;\n  padding-right: 0px;\n}\r\n.px-0\\.5 {\n  padding-left: 0.125rem;\n  padding-right: 0.125rem;\n}\r\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\r\n.px-1\\.5 {\n  padding-left: 0.375rem;\n  padding-right: 0.375rem;\n}\r\n.px-10 {\n  padding-left: 2.5rem;\n  padding-right: 2.5rem;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-3\\.5 {\n  padding-left: 0.875rem;\n  padding-right: 0.875rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-5 {\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\r\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\r\n.px-\\[5px\\] {\n  padding-left: 5px;\n  padding-right: 5px;\n}\r\n.px-\\[6px\\] {\n  padding-left: 6px;\n  padding-right: 6px;\n}\r\n.py-0 {\n  padding-top: 0px;\n  padding-bottom: 0px;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-10 {\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\r\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\r\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\r\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-3\\.5 {\n  padding-top: 0.875rem;\n  padding-bottom: 0.875rem;\n}\r\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-5 {\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\r\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\r\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.py-\\[10px\\] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n}\r\n.py-\\[3px\\] {\n  padding-top: 3px;\n  padding-bottom: 3px;\n}\r\n.py-\\[5px\\] {\n  padding-top: 5px;\n  padding-bottom: 5px;\n}\r\n.py-\\[7px\\] {\n  padding-top: 7px;\n  padding-bottom: 7px;\n}\r\n.py-\\[9px\\] {\n  padding-top: 9px;\n  padding-bottom: 9px;\n}\r\n.py-px {\n  padding-top: 1px;\n  padding-bottom: 1px;\n}\r\n.\\!pb-0 {\n  padding-bottom: 0px !important;\n}\r\n.\\!pl-0 {\n  padding-left: 0px !important;\n}\r\n.\\!pr-0 {\n  padding-right: 0px !important;\n}\r\n.\\!pt-0 {\n  padding-top: 0px !important;\n}\r\n.pb-0 {\n  padding-bottom: 0px;\n}\r\n.pb-1 {\n  padding-bottom: 0.25rem;\n}\r\n.pb-10 {\n  padding-bottom: 2.5rem;\n}\r\n.pb-12 {\n  padding-bottom: 3rem;\n}\r\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\r\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\r\n.pb-4 {\n  padding-bottom: 1rem;\n}\r\n.pb-8 {\n  padding-bottom: 2rem;\n}\r\n.pb-\\[5px\\] {\n  padding-bottom: 5px;\n}\r\n.pb-\\[9px\\] {\n  padding-bottom: 9px;\n}\r\n.pl-0 {\n  padding-left: 0px;\n}\r\n.pl-10 {\n  padding-left: 2.5rem;\n}\r\n.pl-12 {\n  padding-left: 3rem;\n}\r\n.pl-2 {\n  padding-left: 0.5rem;\n}\r\n.pl-2\\.5 {\n  padding-left: 0.625rem;\n}\r\n.pl-4 {\n  padding-left: 1rem;\n}\r\n.pl-6 {\n  padding-left: 1.5rem;\n}\r\n.pl-7 {\n  padding-left: 1.75rem;\n}\r\n.pl-8 {\n  padding-left: 2rem;\n}\r\n.pl-\\[31px\\] {\n  padding-left: 31px;\n}\r\n.pl-\\[88px\\] {\n  padding-left: 88px;\n}\r\n.pr-1 {\n  padding-right: 0.25rem;\n}\r\n.pr-10 {\n  padding-right: 2.5rem;\n}\r\n.pr-12 {\n  padding-right: 3rem;\n}\r\n.pr-2 {\n  padding-right: 0.5rem;\n}\r\n.pr-2\\.5 {\n  padding-right: 0.625rem;\n}\r\n.pr-3 {\n  padding-right: 0.75rem;\n}\r\n.pr-4 {\n  padding-right: 1rem;\n}\r\n.pr-6 {\n  padding-right: 1.5rem;\n}\r\n.pr-7 {\n  padding-right: 1.75rem;\n}\r\n.pr-8 {\n  padding-right: 2rem;\n}\r\n.pr-\\[calc\\(15px\\+2px\\+8px\\)\\] {\n  padding-right: calc(15px + 2px + 8px);\n}\r\n.pt-0 {\n  padding-top: 0px;\n}\r\n.pt-1 {\n  padding-top: 0.25rem;\n}\r\n.pt-10 {\n  padding-top: 2.5rem;\n}\r\n.pt-2 {\n  padding-top: 0.5rem;\n}\r\n.pt-2\\.5 {\n  padding-top: 0.625rem;\n}\r\n.pt-20 {\n  padding-top: 5rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.pt-5 {\n  padding-top: 1.25rem;\n}\r\n.pt-6 {\n  padding-top: 1.5rem;\n}\r\n.pt-8 {\n  padding-top: 2rem;\n}\r\n.text-left {\n  text-align: left;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.text-right {\n  text-align: right;\n}\r\n.text-justify {\n  text-align: justify;\n}\r\n.text-start {\n  text-align: start;\n}\r\n.text-end {\n  text-align: end;\n}\r\n.align-middle {\n  vertical-align: middle;\n}\r\n.align-bottom {\n  vertical-align: bottom;\n}\r\n.font-mono {\n  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\r\n.font-sans {\n  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n.\\!text-sm {\n  font-size: 0.875rem !important;\n  line-height: 1.2 !important;\n}\r\n.text-2xl {\n  font-size: 2rem;\n  line-height: 1.2;\n}\r\n.text-2xs {\n  font-size: 0.625rem;\n  line-height: 1.2;\n}\r\n.text-3xl {\n  font-size: 2.25rem;\n  line-height: 1.2;\n}\r\n.text-3xs {\n  font-size: 0.5rem;\n  line-height: 1.2;\n}\r\n.text-4xl {\n  font-size: 2.5rem;\n  line-height: 1.2;\n}\r\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1.2;\n}\r\n.text-6xl {\n  font-size: 3.5rem;\n  line-height: 1.2;\n}\r\n.text-\\[0\\.8rem\\] {\n  font-size: 0.8rem;\n}\r\n.text-\\[10px\\] {\n  font-size: 10px;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.2;\n}\r\n.text-base18 {\n  font-size: 1.125rem;\n  line-height: 1.2;\n}\r\n.text-lg {\n  font-size: 1.5rem;\n  line-height: 1.2;\n}\r\n.text-md {\n  font-size: 1.25rem;\n  line-height: 1.2;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.2;\n}\r\n.text-xl {\n  font-size: 1.75rem;\n  line-height: 1.2;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1.2;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-normal {\n  font-weight: 400;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.uppercase {\n  text-transform: uppercase;\n}\r\n.lowercase {\n  text-transform: lowercase;\n}\r\n.capitalize {\n  text-transform: capitalize;\n}\r\n.italic {\n  font-style: italic;\n}\r\n.tabular-nums {\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\r\n.\\!leading-5 {\n  line-height: 1.25rem !important;\n}\r\n.\\!leading-6 {\n  line-height: 1.5rem !important;\n}\r\n.\\!leading-7 {\n  line-height: 1.75rem !important;\n}\r\n.\\!leading-\\[22px\\] {\n  line-height: 22px !important;\n}\r\n.\\!leading-normal {\n  line-height: 1.5 !important;\n}\r\n.leading-10 {\n  line-height: 2.5rem;\n}\r\n.leading-5 {\n  line-height: 1.25rem;\n}\r\n.leading-6 {\n  line-height: 1.5rem;\n}\r\n.leading-none {\n  line-height: 1;\n}\r\n.leading-snug {\n  line-height: 1.375;\n}\r\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\r\n.tracking-wider {\n  letter-spacing: 0.05em;\n}\r\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\r\n.\\!text-ui-contrast-fg-secondary {\n  color: var(--contrast-fg-secondary) !important;\n}\r\n.\\!text-ui-fg-on-color {\n  color: var(--fg-on-color) !important;\n}\r\n.text-\\[\\#064A60\\] {\n  --tw-text-opacity: 1;\n  color: rgb(6 74 96 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#224219\\] {\n  --tw-text-opacity: 1;\n  color: rgb(34 66 25 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#344054\\] {\n  --tw-text-opacity: 1;\n  color: rgb(52 64 84 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#475467\\] {\n  --tw-text-opacity: 1;\n  color: rgb(71 84 103 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#5F6E7C\\] {\n  --tw-text-opacity: 1;\n  color: rgb(95 110 124 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#667085\\] {\n  --tw-text-opacity: 1;\n  color: rgb(102 112 133 / var(--tw-text-opacity, 1));\n}\r\n.text-accent {\n  color: hsl(var(--accent));\n}\r\n.text-accent-foreground {\n  color: hsl(var(--accent-foreground));\n}\r\n.text-background {\n  color: hsl(var(--background));\n}\r\n.text-black {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\r\n.text-current {\n  color: currentColor;\n}\r\n.text-destructive {\n  color: hsl(var(--destructive));\n}\r\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\r\n.text-emerald-500 {\n  --tw-text-opacity: 1;\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\n}\r\n.text-error-dark {\n  --tw-text-opacity: 1;\n  color: rgb(180 35 24 / var(--tw-text-opacity, 1));\n}\r\n.text-error-main {\n  --tw-text-opacity: 1;\n  color: rgb(217 45 32 / var(--tw-text-opacity, 1));\n}\r\n.text-foreground {\n  color: hsl(var(--foreground));\n}\r\n.text-gray-200 {\n  --tw-text-opacity: 1;\n  color: rgb(235 239 244 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(216 224 233 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(193 205 215 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-450 {\n  --tw-text-opacity: 1;\n  color: rgb(174 180 190 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(99 115 129 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(125 135 156 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(75 86 107 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-750 {\n  --tw-text-opacity: 1;\n  color: rgb(67 77 86 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-760 {\n  --tw-text-opacity: 1;\n  color: rgb(75 75 75 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(33 43 54 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(43 52 69 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-950 {\n  --tw-text-opacity: 1;\n  color: rgb(55 63 80 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-base {\n  --tw-text-opacity: 1;\n  color: rgb(102 112 133 / var(--tw-text-opacity, 1));\n}\r\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\r\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\r\n.text-indigo-600 {\n  --tw-text-opacity: 1;\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\n}\r\n.text-muted {\n  color: hsl(var(--muted));\n}\r\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\r\n.text-neutral-500 {\n  --tw-text-opacity: 1;\n  color: rgb(115 115 115 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-600 {\n  --tw-text-opacity: 1;\n  color: rgb(82 82 82 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-950 {\n  --tw-text-opacity: 1;\n  color: rgb(10 10 10 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-800 {\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-950 {\n  --tw-text-opacity: 1;\n  color: rgb(67 20 7 / var(--tw-text-opacity, 1));\n}\r\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\r\n.text-primary-dark {\n  --tw-text-opacity: 1;\n  color: rgb(4 114 33 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\r\n.text-primary-main {\n  --tw-text-opacity: 1;\n  color: rgb(89 183 31 / var(--tw-text-opacity, 1));\n}\r\n.text-red {\n  --tw-text-opacity: 1;\n  color: rgb(210 63 87 / var(--tw-text-opacity, 1));\n}\r\n.text-rose-500 {\n  --tw-text-opacity: 1;\n  color: rgb(244 63 94 / var(--tw-text-opacity, 1));\n}\r\n.text-secondary {\n  color: hsl(var(--secondary));\n}\r\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\r\n.text-success-main {\n  --tw-text-opacity: 1;\n  color: rgb(7 148 85 / var(--tw-text-opacity, 1));\n}\r\n.text-ui-contrast-fg-primary {\n  color: var(--contrast-fg-primary);\n}\r\n.text-ui-contrast-fg-secondary {\n  color: var(--contrast-fg-secondary);\n}\r\n.text-ui-fg-base {\n  color: var(--fg-base);\n}\r\n.text-ui-fg-disabled {\n  color: var(--fg-disabled);\n}\r\n.text-ui-fg-error {\n  color: var(--fg-error);\n}\r\n.text-ui-fg-interactive {\n  color: var(--fg-interactive);\n}\r\n.text-ui-fg-muted {\n  color: var(--fg-muted);\n}\r\n.text-ui-fg-on-color {\n  color: var(--fg-on-color);\n}\r\n.text-ui-fg-on-inverted {\n  color: var(--fg-on-inverted);\n}\r\n.text-ui-fg-subtle {\n  color: var(--fg-subtle);\n}\r\n.text-ui-tag-blue-icon {\n  color: var(--tag-blue-icon);\n}\r\n.text-ui-tag-blue-text {\n  color: var(--tag-blue-text);\n}\r\n.text-ui-tag-green-icon {\n  color: var(--tag-green-icon);\n}\r\n.text-ui-tag-green-text {\n  color: var(--tag-green-text);\n}\r\n.text-ui-tag-neutral-icon {\n  color: var(--tag-neutral-icon);\n}\r\n.text-ui-tag-neutral-text {\n  color: var(--tag-neutral-text);\n}\r\n.text-ui-tag-orange-icon {\n  color: var(--tag-orange-icon);\n}\r\n.text-ui-tag-orange-text {\n  color: var(--tag-orange-text);\n}\r\n.text-ui-tag-purple-text {\n  color: var(--tag-purple-text);\n}\r\n.text-ui-tag-red-icon {\n  color: var(--tag-red-icon);\n}\r\n.text-ui-tag-red-text {\n  color: var(--tag-red-text);\n}\r\n.text-warning-main {\n  --tw-text-opacity: 1;\n  color: rgb(232 131 0 / var(--tw-text-opacity, 1));\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-300 {\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-400 {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-500 {\n  --tw-text-opacity: 1;\n  color: rgb(113 113 122 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-600 {\n  --tw-text-opacity: 1;\n  color: rgb(82 82 91 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-900 {\n  --tw-text-opacity: 1;\n  color: rgb(24 24 27 / var(--tw-text-opacity, 1));\n}\r\n.underline {\n  text-decoration-line: underline;\n}\r\n.line-through {\n  text-decoration-line: line-through;\n}\r\n.no-underline {\n  text-decoration-line: none;\n}\r\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\r\n.underline-offset-\\[4px\\] {\n  text-underline-offset: 4px;\n}\r\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.\\!placeholder-ui-fg-disabled::-moz-placeholder {\n  color: var(--fg-disabled) !important;\n}\r\n.\\!placeholder-ui-fg-disabled::placeholder {\n  color: var(--fg-disabled) !important;\n}\r\n.placeholder-ui-fg-muted::-moz-placeholder {\n  color: var(--fg-muted);\n}\r\n.placeholder-ui-fg-muted::placeholder {\n  color: var(--fg-muted);\n}\r\n.caret-ui-fg-base {\n  caret-color: var(--fg-base);\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-100 {\n  opacity: 1;\n}\r\n.opacity-25 {\n  opacity: 0.25;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.opacity-60 {\n  opacity: 0.6;\n}\r\n.opacity-70 {\n  opacity: 0.7;\n}\r\n.opacity-75 {\n  opacity: 0.75;\n}\r\n.opacity-90 {\n  opacity: 0.9;\n}\r\n.\\!shadow-borders-error {\n  --tw-shadow: var(--borders-error) !important;\n  --tw-shadow-colored: var(--borders-error) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.\\!shadow-buttons-neutral {\n  --tw-shadow: var(--buttons-neutral) !important;\n  --tw-shadow-colored: var(--buttons-neutral) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.shadow-\\[0_1px_1px_0\\] {\n  --tw-shadow: 0 1px 1px 0;\n  --tw-shadow-colored: 0 1px 1px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-\\[0_8px_24px_rgba\\(149\\2c 157\\2c 165\\2c 0\\.1\\)\\] {\n  --tw-shadow: 0 8px 24px rgba(149,157,165,0.1);\n  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-around-circle {\n  --tw-shadow: 0 4px 14px rgba(52, 52, 52, 0.1);\n  --tw-shadow-colored: 0 4px 14px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-borders-base {\n  --tw-shadow: var(--borders-base);\n  --tw-shadow-colored: var(--borders-base);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-borders-error {\n  --tw-shadow: var(--borders-error);\n  --tw-shadow-colored: var(--borders-error);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-borders-interactive-with-active {\n  --tw-shadow: var(--borders-interactive-with-active);\n  --tw-shadow-colored: var(--borders-interactive-with-active);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-buttons-danger {\n  --tw-shadow: var(--buttons-danger);\n  --tw-shadow-colored: var(--buttons-danger);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-buttons-inverted {\n  --tw-shadow: var(--buttons-inverted);\n  --tw-shadow-colored: var(--buttons-inverted);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-buttons-neutral {\n  --tw-shadow: var(--buttons-neutral);\n  --tw-shadow-colored: var(--buttons-neutral);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-details-contrast-on-bg-interactive {\n  --tw-shadow: var(--details-contrast-on-bg-interactive);\n  --tw-shadow-colored: var(--details-contrast-on-bg-interactive);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-details-switch-handle {\n  --tw-shadow: var(--details-switch-handle);\n  --tw-shadow-colored: var(--details-switch-handle);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-elevation-card-rest {\n  --tw-shadow: var(--elevation-card-rest);\n  --tw-shadow-colored: var(--elevation-card-rest);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-elevation-code-block {\n  --tw-shadow: var(--elevation-code-block);\n  --tw-shadow-colored: var(--elevation-code-block);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-elevation-flyout {\n  --tw-shadow: var(--elevation-flyout);\n  --tw-shadow-colored: var(--elevation-flyout);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-elevation-modal {\n  --tw-shadow: var(--elevation-modal);\n  --tw-shadow-colored: var(--elevation-modal);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-elevation-tooltip {\n  --tw-shadow: var(--elevation-tooltip);\n  --tw-shadow-colored: var(--elevation-tooltip);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-ui-border-base {\n  --tw-shadow-color: var(--border-base);\n  --tw-shadow: var(--tw-shadow-colored);\n}\r\n.\\!outline-none {\n  outline: 2px solid transparent !important;\n  outline-offset: 2px !important;\n}\r\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline {\n  outline-style: solid;\n}\r\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-muted {\n  --tw-ring-color: hsl(var(--muted));\n}\r\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-md {\n  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.grayscale {\n  --tw-grayscale: grayscale(100%);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.invert {\n  --tw-invert: invert(100%);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.\\!filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-2xl {\n  --tw-backdrop-blur: blur(40px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[height\\2c max-height\\2c opacity\\] {\n  transition-property: height,max-height,opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[max-height\\2c opacity\\] {\n  transition-property: max-height,opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-fg {\n  transition-property: color, background-color, border-color, box-shadow, opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.delay-200 {\n  transition-delay: 200ms;\n}\r\n.duration-150 {\n  transition-duration: 150ms;\n}\r\n.duration-200 {\n  transition-duration: 200ms;\n}\r\n.duration-300 {\n  transition-duration: 300ms;\n}\r\n.duration-500 {\n  transition-duration: 500ms;\n}\r\n.ease-in {\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-linear {\n  transition-timing-function: linear;\n}\r\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\r\n.duration-150 {\n  animation-duration: 150ms;\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.duration-500 {\n  animation-duration: 500ms;\n}\r\n.delay-200 {\n  animation-delay: 200ms;\n}\r\n.ease-in {\n  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-linear {\n  animation-timing-function: linear;\n}\r\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\r\n.duration-150 {\n  animation-duration: 150ms;\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.duration-500 {\n  animation-duration: 500ms;\n}\r\n.delay-200 {\n  animation-delay: 200ms;\n}\r\n.ease-in {\n  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-linear {\n  animation-timing-function: linear;\n}\r\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n/* Hide scrollbar for Chrome, Safari and Opera */\r\n.no-scrollbar::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n/* Hide scrollbar for IE, Edge and Firefox */\r\n.no-scrollbar {\r\n    -ms-overflow-style: none; /* IE and Edge */\r\n    scrollbar-width: none; /* Firefox */\r\n  }\r\n/* Show scrollbar */\r\n/* Chrome, Safari and Opera */\r\n.no-scrollbar::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n.no-scrollbar::-webkit-scrollbar-track {\r\n    background-color: transparent;\r\n  }\r\n.no-scrollbar {\r\n    -ms-overflow-style: none; /* IE and Edge */\r\n    scrollbar-width: none; /* Firefox */\r\n  }\r\ninput:not(:-moz-placeholder) ~ label {\n  font-size: 0.625rem;\n  line-height: 1.2;\n  font-weight: 400;\n  line-height: 1rem;\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\ninput:focus ~ label,\r\n  input:not(:placeholder-shown) ~ label {\n  font-size: 0.625rem;\n  line-height: 1.2;\n  font-weight: 400;\n  line-height: 1rem;\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\ninput:focus ~ label {\n  left: 0px;\n}\r\ninput:-webkit-autofill,\r\n  input:-webkit-autofill:hover,\r\n  input:-webkit-autofill:focus,\r\n  textarea:-webkit-autofill,\r\n  textarea:-webkit-autofill:hover,\r\n  textarea:-webkit-autofill:focus,\r\n  select:-webkit-autofill,\r\n  select:-webkit-autofill:hover,\r\n  select:-webkit-autofill:focus {\r\n    border: 1px solid #212121;\r\n    -webkit-text-fill-color: #212121;\r\n    -webkit-box-shadow: 0 0 0px 1000px #fff inset;\r\n    -webkit-transition: background-color 5000s ease-in-out 0s;\r\n    transition: background-color 5000s ease-in-out 0s;\r\n  }\r\ninput[type=\"search\"]::-webkit-search-decoration,\r\n  input[type=\"search\"]::-webkit-search-cancel-button,\r\n  input[type=\"search\"]::-webkit-search-results-button,\r\n  input[type=\"search\"]::-webkit-search-results-decoration {\r\n    -webkit-appearance: none;\r\n  }\r\n.\\[--duration\\:40s\\] {\n  --duration: 40s;\n}\r\n.\\[--gap\\:1rem\\] {\n  --gap: 1rem;\n}\r\n.\\[-moz-appearance\\:textfield\\] {\n  -moz-appearance: textfield;\n}\r\n.\\[animation-direction\\:reverse\\] {\n  animation-direction: reverse;\n}\r\n.\\[gap\\:var\\(--gap\\)\\] {\n  gap: var(--gap);\n}\r\n\r\nbody {\n  font-family: var(--font-body), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n\r\n/* Display/Hidden Scrollbar */\r\n\r\n.swiper {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.swiper-slide {\r\n  text-align: center;\r\n  font-size: 18px;\r\n  background: #fff;\r\n\r\n  /* Center slide text vertically */\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.swiper-slide img {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  -o-object-fit: cover;\r\n     object-fit: cover;\r\n}\r\n\r\n.swiper-v {\r\n  background: #eee;\r\n}\r\n\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  width: 40px !important;\r\n  height: 40px !important;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);\r\n  -webkit-backdrop-filter: blur(5px);\r\n          backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* Mobile Footer Accordion Improvements */\r\n.mobile-footer-accordion {\n  touch-action: manipulation;\n}\r\n\r\n.mobile-footer-accordion button {\n  min-height: 44px;\n  touch-action: manipulation;\r\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);\n}\r\n\r\n.mobile-footer-accordion button:active {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n\r\n/* Ensure smooth scrolling and touch optimization on mobile */\r\n@media (max-width: 767px) {\r\n  html {\r\n    scroll-behavior: smooth;\r\n    -webkit-overflow-scrolling: touch;\r\n  }\r\n\r\n  body {\r\n    -webkit-overflow-scrolling: touch;\r\n  }\r\n\r\n  /* Improve touch targets */\r\n  button,\r\n  a,\r\n  [role=\"button\"] {\r\n    min-height: 44px;\r\n    min-width: 44px;\r\n  }\r\n}\r\n.data-\\[state\\=checked\\]\\:txt-compact-small-plus[data-state=\"checked\"] {\n  font-size: 0.8125rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.\\[\\&\\>\\*\\]\\:txt-compact-small-plus>* {\n  font-size: 0.8125rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;\n}\r\n.\\[\\&\\>code\\]\\:code-body>code {\n  font-size: 0.75rem;\n  line-height: 1.125rem;\n  font-weight: 400;\n  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\n}\r\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\r\n.file\\:bg-transparent::file-selector-button {\n  background-color: rgba(0,0,0,0);\n}\r\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.2;\n}\r\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\r\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\r\n.placeholder\\:text-gray-700::-moz-placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(75 86 107 / var(--tw-text-opacity, 1));\n}\r\n.placeholder\\:text-gray-700::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(75 86 107 / var(--tw-text-opacity, 1));\n}\r\n.placeholder\\:text-muted-foreground::-moz-placeholder {\n  color: hsl(var(--muted-foreground));\n}\r\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\r\n.placeholder\\:text-ui-fg-on-color::-moz-placeholder {\n  color: var(--fg-on-color);\n}\r\n.placeholder\\:text-ui-fg-on-color::placeholder {\n  color: var(--fg-on-color);\n}\r\n.placeholder\\:text-zinc-500::-moz-placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(113 113 122 / var(--tw-text-opacity, 1));\n}\r\n.placeholder\\:text-zinc-500::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(113 113 122 / var(--tw-text-opacity, 1));\n}\r\n.placeholder\\:transition-colors::-moz-placeholder {\n  -moz-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.placeholder\\:transition-colors::placeholder {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.before\\:absolute::before {\n  content: var(--tw-content);\n  position: absolute;\n}\r\n.before\\:inset-0::before {\n  content: var(--tw-content);\n  inset: 0px;\n}\r\n.before\\:rounded-full::before {\n  content: var(--tw-content);\n  border-radius: 100%;\n}\r\n.before\\:shadow-details-switch-background::before {\n  content: var(--tw-content);\n  --tw-shadow: var(--details-switch-background);\n  --tw-shadow-colored: var(--details-switch-background);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.before\\:content-\\[\\'\\'\\]::before {\n  --tw-content: '';\n  content: var(--tw-content);\n}\r\n.after\\:pointer-events-none::after {\n  content: var(--tw-content);\n  pointer-events: none;\n}\r\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\r\n.after\\:inset-0::after {\n  content: var(--tw-content);\n  inset: 0px;\n}\r\n.after\\:inset-y-0::after {\n  content: var(--tw-content);\n  top: 0px;\n  bottom: 0px;\n}\r\n.after\\:right-0::after {\n  content: var(--tw-content);\n  right: 0px;\n}\r\n.after\\:hidden::after {\n  content: var(--tw-content);\n  display: none;\n}\r\n.after\\:h-full::after {\n  content: var(--tw-content);\n  height: 100%;\n}\r\n.after\\:w-px::after {\n  content: var(--tw-content);\n  width: 1px;\n}\r\n.after\\:rounded-full::after {\n  content: var(--tw-content);\n  border-radius: 100%;\n}\r\n.after\\:bg-transparent::after {\n  content: var(--tw-content);\n  background-color: rgba(0,0,0,0);\n}\r\n.after\\:bg-ui-border-base::after {\n  content: var(--tw-content);\n  background-color: var(--border-base);\n}\r\n.after\\:shadow-elevation-flyout::after {\n  content: var(--tw-content);\n  --tw-shadow: var(--elevation-flyout);\n  --tw-shadow-colored: var(--elevation-flyout);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.after\\:transition-fg::after {\n  content: var(--tw-content);\n  transition-property: color, background-color, border-color, box-shadow, opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.after\\:content-\\[\\'\\'\\]::after {\n  --tw-content: '';\n  content: var(--tw-content);\n}\r\n.first\\:mt-0:first-child {\n  margin-top: 0px;\n}\r\n.last\\:mb-0:last-child {\n  margin-bottom: 0px;\n}\r\n.last\\:border-b:last-child {\n  border-bottom-width: 1px;\n}\r\n.last-of-type\\:-mr-1:last-of-type {\n  margin-right: -0.25rem;\n}\r\n.last-of-type\\:border-b-0:last-of-type {\n  border-bottom-width: 0px;\n}\r\n.last-of-type\\:pr-4:last-of-type {\n  padding-right: 1rem;\n}\r\n.invalid\\:border-ui-border-error:invalid {\n  border-color: var(--border-error);\n}\r\n.invalid\\:\\!shadow-borders-error:invalid {\n  --tw-shadow: var(--borders-error) !important;\n  --tw-shadow-colored: var(--borders-error) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.invalid\\:shadow-borders-error:invalid {\n  --tw-shadow: var(--borders-error);\n  --tw-shadow-colored: var(--borders-error);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-within\\:relative:focus-within {\n  position: relative;\n}\r\n.focus-within\\:z-20:focus-within {\n  z-index: 20;\n}\r\n.focus-within\\:shadow-borders-interactive-with-active:focus-within {\n  --tw-shadow: var(--borders-interactive-with-active);\n  --tw-shadow-colored: var(--borders-interactive-with-active);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:scale-110:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-\\[1\\.02\\]:hover {\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:cursor-pointer:hover {\n  cursor: pointer;\n}\r\n.hover\\:border:hover {\n  border-width: 1px;\n}\r\n.hover\\:border-gray-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(193 205 215 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-primary-main:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-primary-main\\/30:hover {\n  border-color: rgb(89 183 31 / 0.3);\n}\r\n.hover\\:\\!bg-transparent:hover {\n  background-color: rgba(0,0,0,0) !important;\n}\r\n.hover\\:\\!bg-ui-bg-base:hover {\n  background-color: var(--bg-base) !important;\n}\r\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\r\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\r\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\r\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(244 246 248 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(33 43 54 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-muted:hover {\n  background-color: hsl(var(--muted));\n}\r\n.hover\\:bg-muted\\/30:hover {\n  background-color: hsl(var(--muted) / 0.3);\n}\r\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.hover\\:bg-primary:hover {\n  background-color: hsl(var(--primary));\n}\r\n.hover\\:bg-primary-dark:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(4 114 33 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-primary-lighter:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 251 230 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-primary-main:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-primary-main\\/10:hover {\n  background-color: rgb(89 183 31 / 0.1);\n}\r\n.hover\\:bg-primary-main\\/80:hover {\n  background-color: rgb(89 183 31 / 0.8);\n}\r\n.hover\\:bg-primary-main\\/90:hover {\n  background-color: rgb(89 183 31 / 0.9);\n}\r\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\r\n.hover\\:bg-transparent:hover {\n  background-color: rgba(0,0,0,0);\n}\r\n.hover\\:bg-ui-bg-base-hover:hover {\n  background-color: var(--bg-base-hover);\n}\r\n.hover\\:bg-ui-bg-field-hover:hover {\n  background-color: var(--bg-field-hover);\n}\r\n.hover\\:bg-ui-bg-subtle-hover:hover {\n  background-color: var(--bg-subtle-hover);\n}\r\n.hover\\:bg-ui-bg-switch-off-hover:hover {\n  background-color: var(--bg-switch-off-hover);\n}\r\n.hover\\:bg-ui-button-danger-hover:hover {\n  background-color: var(--button-danger-hover);\n}\r\n.hover\\:bg-ui-button-inverted-hover:hover {\n  background-color: var(--button-inverted-hover);\n}\r\n.hover\\:bg-ui-button-neutral-hover:hover {\n  background-color: var(--button-neutral-hover);\n}\r\n.hover\\:bg-ui-button-transparent-hover:hover {\n  background-color: var(--button-transparent-hover);\n}\r\n.hover\\:bg-ui-contrast-bg-base-hover:hover {\n  background-color: var(--contrast-bg-base-hover);\n}\r\n.hover\\:font-bold:hover {\n  font-weight: 700;\n}\r\n.hover\\:font-semibold:hover {\n  font-weight: 600;\n}\r\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\r\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(125 135 156 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-primary-foreground:hover {\n  color: hsl(var(--primary-foreground));\n}\r\n.hover\\:text-primary-main:hover {\n  --tw-text-opacity: 1;\n  color: rgb(89 183 31 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-ui-fg-base:hover {\n  color: var(--fg-base);\n}\r\n.hover\\:text-ui-fg-disabled:hover {\n  color: var(--fg-disabled);\n}\r\n.hover\\:text-ui-fg-subtle:hover {\n  color: var(--fg-subtle);\n}\r\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\r\n.hover\\:no-underline:hover {\n  text-decoration-line: none;\n}\r\n.hover\\:underline-offset-\\[12px\\]:hover {\n  text-underline-offset: 12px;\n}\r\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\r\n.hover\\:opacity-90:hover {\n  opacity: 0.9;\n}\r\n.hover\\:shadow-borders-interactive-with-active:hover {\n  --tw-shadow: var(--borders-interactive-with-active);\n  --tw-shadow-colored: var(--borders-interactive-with-active);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-elevation-card-hover:hover {\n  --tw-shadow: var(--elevation-card-hover);\n  --tw-shadow-colored: var(--elevation-card-hover);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus\\:z-\\[1\\]:focus {\n  z-index: 1;\n}\r\n.focus\\:border-gray-300:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(216 224 233 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:border-gray-700:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 86 107 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:border-primary-main:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\r\n.focus\\:bg-primary:focus {\n  background-color: hsl(var(--primary));\n}\r\n.focus\\:bg-transparent:focus {\n  background-color: rgba(0,0,0,0);\n}\r\n.focus\\:bg-ui-bg-component-hover:focus {\n  background-color: var(--bg-component-hover);\n}\r\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\r\n.focus\\:text-primary-foreground:focus {\n  color: hsl(var(--primary-foreground));\n}\r\n.focus\\:text-ui-fg-base:focus {\n  color: var(--fg-base);\n}\r\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus\\:ring-1:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-primary-main:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(89 183 31 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-primary-main\\/80:focus {\n  --tw-ring-color: rgb(89 183 31 / 0.8);\n}\r\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus\\:ring-white:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-opacity-75:focus {\n  --tw-ring-opacity: 0.75;\n}\r\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus\\:ring-offset-gray-300:focus {\n  --tw-ring-offset-color: #D8E0E9;\n}\r\n.focus-visible\\:border-none:focus-visible {\n  border-style: none;\n}\r\n.focus-visible\\:border-ui-border-interactive:focus-visible {\n  border-color: var(--border-interactive);\n}\r\n.focus-visible\\:bg-ui-bg-base:focus-visible {\n  background-color: var(--bg-base);\n}\r\n.focus-visible\\:bg-ui-bg-component-hover:focus-visible {\n  background-color: var(--bg-component-hover);\n}\r\n.focus-visible\\:bg-ui-bg-interactive:focus-visible {\n  background-color: var(--bg-interactive);\n}\r\n.focus-visible\\:text-ui-fg-base:focus-visible {\n  color: var(--fg-base);\n}\r\n.focus-visible\\:text-ui-fg-on-color:focus-visible {\n  color: var(--fg-on-color);\n}\r\n.focus-visible\\:\\!shadow-borders-focus:focus-visible {\n  --tw-shadow: var(--borders-focus) !important;\n  --tw-shadow-colored: var(--borders-focus) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.focus-visible\\:\\!shadow-buttons-inverted-focus:focus-visible {\n  --tw-shadow: var(--buttons-inverted-focus) !important;\n  --tw-shadow-colored: var(--buttons-inverted-focus) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.focus-visible\\:shadow-borders-focus:focus-visible {\n  --tw-shadow: var(--borders-focus);\n  --tw-shadow-colored: var(--borders-focus);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:shadow-borders-interactive-with-active:focus-visible {\n  --tw-shadow: var(--borders-interactive-with-active);\n  --tw-shadow-colored: var(--borders-interactive-with-active);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:shadow-borders-interactive-with-focus:focus-visible {\n  --tw-shadow: var(--borders-interactive-with-focus);\n  --tw-shadow-colored: var(--borders-interactive-with-focus);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:shadow-buttons-danger-focus:focus-visible {\n  --tw-shadow: var(--buttons-danger-focus);\n  --tw-shadow-colored: var(--buttons-danger-focus);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:shadow-buttons-neutral-focus:focus-visible {\n  --tw-shadow: var(--buttons-neutral-focus);\n  --tw-shadow-colored: var(--buttons-neutral-focus);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:shadow-details-switch-background-focus:focus-visible {\n  --tw-shadow: var(--details-switch-background-focus);\n  --tw-shadow-colored: var(--details-switch-background-focus);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus-visible\\:ring-0:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-1:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-primary-main:focus-visible {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(89 183 31 / var(--tw-ring-opacity, 1));\n}\r\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:hover\\:bg-ui-contrast-bg-base-hover:hover:focus-visible {\n  background-color: var(--contrast-bg-base-hover);\n}\r\n.active\\:bg-gray-200:active {\n  --tw-bg-opacity: 1;\n  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));\n}\r\n.active\\:bg-gray-50:active {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.active\\:bg-transparent:active {\n  background-color: rgba(0,0,0,0);\n}\r\n.active\\:bg-ui-bg-base-pressed:active {\n  background-color: var(--bg-base-pressed);\n}\r\n.active\\:bg-ui-bg-component-hover:active {\n  background-color: var(--bg-component-hover);\n}\r\n.active\\:bg-ui-bg-component-pressed:active {\n  background-color: var(--bg-component-pressed);\n}\r\n.active\\:bg-ui-button-danger-pressed:active {\n  background-color: var(--button-danger-pressed);\n}\r\n.active\\:bg-ui-button-inverted-pressed:active {\n  background-color: var(--button-inverted-pressed);\n}\r\n.active\\:bg-ui-button-neutral-pressed:active {\n  background-color: var(--button-neutral-pressed);\n}\r\n.active\\:bg-ui-button-transparent-pressed:active {\n  background-color: var(--button-transparent-pressed);\n}\r\n.active\\:bg-ui-contrast-bg-base-pressed:active {\n  background-color: var(--contrast-bg-base-pressed);\n}\r\n.active\\:text-primary-main:active {\n  --tw-text-opacity: 1;\n  color: rgb(89 183 31 / var(--tw-text-opacity, 1));\n}\r\n.active\\:text-ui-fg-base:active {\n  color: var(--fg-base);\n}\r\n.focus-visible\\:active\\:bg-ui-contrast-bg-base-pressed:active:focus-visible {\n  background-color: var(--contrast-bg-base-pressed);\n}\r\n.hover\\:enabled\\:bg-ui-bg-base-hover:enabled:hover {\n  background-color: var(--bg-base-hover);\n}\r\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\r\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n.disabled\\:border-ui-border-base:disabled {\n  border-color: var(--border-base);\n}\r\n.disabled\\:\\!bg-transparent:disabled {\n  background-color: rgba(0,0,0,0) !important;\n}\r\n.disabled\\:\\!bg-ui-bg-disabled:disabled {\n  background-color: var(--bg-disabled) !important;\n}\r\n.disabled\\:bg-transparent:disabled {\n  background-color: rgba(0,0,0,0);\n}\r\n.disabled\\:bg-ui-bg-disabled:disabled {\n  background-color: var(--bg-disabled);\n}\r\n.disabled\\:\\!text-ui-fg-disabled:disabled {\n  color: var(--fg-disabled) !important;\n}\r\n.disabled\\:text-orange-500:disabled {\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\r\n.disabled\\:text-ui-fg-disabled:disabled {\n  color: var(--fg-disabled);\n}\r\n.disabled\\:text-ui-fg-muted:disabled {\n  color: var(--fg-muted);\n}\r\n.disabled\\:placeholder-ui-fg-disabled:disabled::-moz-placeholder {\n  color: var(--fg-disabled);\n}\r\n.disabled\\:placeholder-ui-fg-disabled:disabled::placeholder {\n  color: var(--fg-disabled);\n}\r\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n.disabled\\:opacity-70:disabled {\n  opacity: 0.7;\n}\r\n.disabled\\:shadow-buttons-neutral:disabled {\n  --tw-shadow: var(--buttons-neutral);\n  --tw-shadow-colored: var(--buttons-neutral);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.disabled\\:after\\:hidden:disabled::after {\n  content: var(--tw-content);\n  display: none;\n}\r\n.group:hover .group-hover\\:rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.group:hover .group-hover\\:animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n.group\\/row:hover .group-hover\\/row\\:bg-ui-bg-base-hover {\n  background-color: var(--bg-base-hover);\n}\r\n.group:hover .group-hover\\:bg-primary-main\\/60 {\n  background-color: rgb(89 183 31 / 0.6);\n}\r\n.group:hover .group-hover\\:text-primary-main {\n  --tw-text-opacity: 1;\n  color: rgb(89 183 31 / var(--tw-text-opacity, 1));\n}\r\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\r\n.group:hover .group-hover\\:opacity-50 {\n  opacity: 0.5;\n}\r\n.group:hover .group-hover\\:shadow-elevation-card-hover {\n  --tw-shadow: var(--elevation-card-hover);\n  --tw-shadow-colored: var(--elevation-card-hover);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.group:hover .group-hover\\:\\[animation-play-state\\:paused\\] {\n  animation-play-state: paused;\n}\r\n.group:focus .group-focus\\:\\!shadow-borders-interactive-with-focus {\n  --tw-shadow: var(--borders-interactive-with-focus) !important;\n  --tw-shadow-colored: var(--borders-interactive-with-focus) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.group:focus-visible .group-focus-visible\\:\\!shadow-borders-interactive-with-focus {\n  --tw-shadow: var(--borders-interactive-with-focus) !important;\n  --tw-shadow-colored: var(--borders-interactive-with-focus) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.group:disabled .group-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.group:disabled .group-disabled\\:\\!bg-ui-bg-disabled {\n  background-color: var(--bg-disabled) !important;\n}\r\n.group:disabled .group-disabled\\:bg-ui-fg-disabled {\n  background-color: var(--fg-disabled);\n}\r\n.group\\/trigger:disabled .group-disabled\\/trigger\\:text-ui-fg-disabled {\n  color: var(--fg-disabled);\n}\r\n.group:disabled .group-disabled\\:text-ui-fg-disabled {\n  color: var(--fg-disabled);\n}\r\n.group:disabled .group-disabled\\:opacity-50 {\n  opacity: 0.5;\n}\r\n.group:disabled .group-disabled\\:\\!shadow-borders-base {\n  --tw-shadow: var(--borders-base) !important;\n  --tw-shadow-colored: var(--borders-base) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\r\n.aria-selected\\:bg-accent[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent));\n}\r\n.aria-selected\\:bg-accent\\/50[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.aria-selected\\:text-muted-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.aria-selected\\:opacity-100[aria-selected=\"true\"] {\n  opacity: 1;\n}\r\n.aria-\\[invalid\\=true\\]\\:border-ui-border-error[aria-invalid=\"true\"] {\n  border-color: var(--border-error);\n}\r\n.aria-\\[invalid\\=true\\]\\:\\!shadow-borders-error[aria-invalid=\"true\"] {\n  --tw-shadow: var(--borders-error) !important;\n  --tw-shadow-colored: var(--borders-error) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.aria-\\[invalid\\=true\\]\\:shadow-borders-error[aria-invalid=\"true\"] {\n  --tw-shadow: var(--borders-error);\n  --tw-shadow-colored: var(--borders-error);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled=\"true\"] {\n  pointer-events: none;\n}\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=checked\\]\\:translate-x-3\\.5[data-state=\"checked\"] {\n  --tw-translate-x: 0.875rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=checked\\]\\:translate-x-4[data-state=\"checked\"] {\n  --tw-translate-x: 1rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=unchecked\\]\\:translate-x-0\\.5[data-state=\"unchecked\"] {\n  --tw-translate-x: 0.125rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\r\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\r\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\r\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\r\n.data-\\[state\\=active\\]\\:border-0[data-state=\"active\"] {\n  border-width: 0px;\n}\r\n.data-\\[selected\\=true\\]\\:bg-accent[data-selected=\"true\"] {\n  background-color: hsl(var(--accent));\n}\r\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"] {\n  background-color: hsl(var(--background));\n}\r\n.data-\\[state\\=active\\]\\:bg-primary-light[data-state=\"active\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(119 184 79 / var(--tw-bg-opacity, 1));\n}\r\n.data-\\[state\\=active\\]\\:bg-transparent[data-state=\"active\"] {\n  background-color: rgba(0,0,0,0);\n}\r\n.data-\\[state\\=active\\]\\:bg-ui-bg-base[data-state=\"active\"] {\n  background-color: var(--bg-base);\n}\r\n.data-\\[state\\=checked\\]\\:bg-primary-main[data-state=\"checked\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));\n}\r\n.data-\\[state\\=checked\\]\\:bg-ui-bg-interactive[data-state=\"checked\"] {\n  background-color: var(--bg-interactive);\n}\r\n.data-\\[state\\=open\\]\\:\\!bg-ui-bg-component-hover[data-state=\"open\"] {\n  background-color: var(--bg-component-hover) !important;\n}\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=\"open\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\r\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\r\n.data-\\[state\\=active\\]\\:font-bold[data-state=\"active\"] {\n  font-weight: 700;\n}\r\n.data-\\[disabled\\]\\:text-ui-fg-disabled[data-disabled] {\n  color: var(--fg-disabled);\n}\r\n.data-\\[placeholder\\]\\:text-ui-fg-muted[data-placeholder] {\n  color: var(--fg-muted);\n}\r\n.data-\\[selected\\=true\\]\\:text-accent-foreground[data-selected=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.data-\\[state\\=active\\]\\:text-ui-fg-base[data-state=\"active\"] {\n  color: var(--fg-base);\n}\r\n.data-\\[state\\=active\\]\\:text-white[data-state=\"active\"] {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.data-\\[state\\=checked\\]\\:text-white[data-state=\"checked\"] {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.data-\\[disabled\\=true\\]\\:opacity-50[data-disabled=\"true\"] {\n  opacity: 0.5;\n}\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\r\n.data-\\[state\\=active\\]\\:shadow-elevation-card-rest[data-state=\"active\"] {\n  --tw-shadow: var(--elevation-card-rest);\n  --tw-shadow-colored: var(--elevation-card-rest);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.data-\\[state\\=checked\\]\\:shadow-borders-interactive-with-shadow[data-state=\"checked\"] {\n  --tw-shadow: var(--borders-interactive-with-shadow);\n  --tw-shadow-colored: var(--borders-interactive-with-shadow);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.data-\\[state\\=open\\]\\:\\!shadow-borders-interactive-with-active[data-state=\"open\"] {\n  --tw-shadow: var(--borders-interactive-with-active) !important;\n  --tw-shadow-colored: var(--borders-interactive-with-active) !important;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"] {\n  --tw-enter-scale: .9;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"] {\n  --tw-enter-translate-x: 13rem;\n}\r\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"] {\n  --tw-enter-translate-x: -13rem;\n}\r\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"] {\n  --tw-exit-translate-x: 13rem;\n}\r\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"] {\n  --tw-exit-translate-x: -13rem;\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-in-from-bottom-2[data-state=\"closed\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: 50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom-0[data-state=\"open\"] {\n  --tw-enter-translate-y: 0px;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: 50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"] {\n  --tw-enter-scale: .9;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"] {\n  --tw-enter-translate-x: 13rem;\n}\r\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"] {\n  --tw-enter-translate-x: -13rem;\n}\r\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"] {\n  --tw-exit-translate-x: 13rem;\n}\r\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"] {\n  --tw-exit-translate-x: -13rem;\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-in-from-bottom-2[data-state=\"closed\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: 50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom-0[data-state=\"open\"] {\n  --tw-enter-translate-y: 0px;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: 50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\r\n.data-\\[state\\=open\\]\\:hover\\:bg-accent:hover[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n.data-\\[state\\=open\\]\\:focus\\:bg-accent:focus[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group[data-state=\"checked\"] .group-data-\\[state\\=checked\\]\\:bg-ui-bg-interactive {\n  background-color: var(--bg-interactive);\n}\r\n.group[data-state=\"indeterminate\"] .group-data-\\[state\\=indeterminate\\]\\:bg-ui-bg-interactive {\n  background-color: var(--bg-interactive);\n}\r\n.group\\/trigger[data-state=\"active\"] .group-data-\\[state\\=active\\]\\/trigger\\:text-ui-fg-interactive {\n  color: var(--fg-interactive);\n}\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:text-ui-fg-interactive {\n  color: var(--fg-interactive);\n}\r\n.group[data-state=\"checked\"] .group-data-\\[state\\=checked\\]\\:shadow-borders-interactive-with-shadow {\n  --tw-shadow: var(--borders-interactive-with-shadow);\n  --tw-shadow-colored: var(--borders-interactive-with-shadow);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.group[data-state=\"indeterminate\"] .group-data-\\[state\\=indeterminate\\]\\:shadow-borders-interactive-with-shadow {\n  --tw-shadow: var(--borders-interactive-with-shadow);\n  --tw-shadow-colored: var(--borders-interactive-with-shadow);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.group:hover:enabled[data-state=\"unchecked\"] .group-hover\\:group-enabled\\:group-data-\\[state\\=unchecked\\]\\:bg-ui-bg-base-hover {\n  background-color: var(--bg-base-hover);\n}\r\n.radix-state-closed\\:pointer-events-none[data-state=\"closed\"] {\n  pointer-events: none;\n}\r\n.group[data-state=\"open\"] .group-radix-state-open\\:left-1\\/2 {\n  left: 50%;\n}\r\n.group[data-state=\"open\"] .group-radix-state-open\\:right-1\\/2 {\n  right: 50%;\n}\r\n.group[data-state=\"open\"] .group-radix-state-open\\:rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@media (prefers-reduced-motion: reduce) {\n\n  .motion-reduce\\:transition-none {\n    transition-property: none;\n  }\n}\r\n.dark\\:from-background:is(.dark *) {\n  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n@media not all and (min-width: 768px) {\n\n  .max-md\\:hidden {\n    display: none;\n  }\n\n  .max-md\\:\\!rounded-xs {\n    border-radius: 4px !important;\n  }\n\n  .max-md\\:pt-5 {\n    padding-top: 1.25rem;\n  }\n\n  .max-md\\:text-start {\n    text-align: start;\n  }\n}\r\n@media not all and (min-width: 640px) {\n\n  .max-sm\\:inset-x-2 {\n    left: 0.5rem;\n    right: 0.5rem;\n  }\n\n  .max-sm\\:w-\\[calc\\(100\\%-16px\\)\\] {\n    width: calc(100% - 16px);\n  }\n}\r\n@media (min-width: 400px) {\n\n  .xs\\:-col-start-1 {\n    grid-column-start: -1;\n  }\n\n  .xs\\:-col-start-10 {\n    grid-column-start: -10;\n  }\n\n  .xs\\:-col-start-11 {\n    grid-column-start: -11;\n  }\n\n  .xs\\:-col-start-12 {\n    grid-column-start: -12;\n  }\n\n  .xs\\:-col-start-13 {\n    grid-column-start: -13;\n  }\n\n  .xs\\:-col-start-2 {\n    grid-column-start: -2;\n  }\n\n  .xs\\:-col-start-3 {\n    grid-column-start: -3;\n  }\n\n  .xs\\:-col-start-4 {\n    grid-column-start: -4;\n  }\n\n  .xs\\:-col-start-5 {\n    grid-column-start: -5;\n  }\n\n  .xs\\:-col-start-6 {\n    grid-column-start: -6;\n  }\n\n  .xs\\:-col-start-7 {\n    grid-column-start: -7;\n  }\n\n  .xs\\:-col-start-8 {\n    grid-column-start: -8;\n  }\n\n  .xs\\:-col-start-9 {\n    grid-column-start: -9;\n  }\n\n  .xs\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .xs\\:col-start-10 {\n    grid-column-start: 10;\n  }\n\n  .xs\\:col-start-11 {\n    grid-column-start: 11;\n  }\n\n  .xs\\:col-start-12 {\n    grid-column-start: 12;\n  }\n\n  .xs\\:col-start-13 {\n    grid-column-start: 13;\n  }\n\n  .xs\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .xs\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .xs\\:col-start-4 {\n    grid-column-start: 4;\n  }\n\n  .xs\\:col-start-5 {\n    grid-column-start: 5;\n  }\n\n  .xs\\:col-start-6 {\n    grid-column-start: 6;\n  }\n\n  .xs\\:col-start-7 {\n    grid-column-start: 7;\n  }\n\n  .xs\\:col-start-8 {\n    grid-column-start: 8;\n  }\n\n  .xs\\:col-start-9 {\n    grid-column-start: 9;\n  }\n\n  .xs\\:-col-end-1 {\n    grid-column-end: -1;\n  }\n\n  .xs\\:-col-end-10 {\n    grid-column-end: -10;\n  }\n\n  .xs\\:-col-end-11 {\n    grid-column-end: -11;\n  }\n\n  .xs\\:-col-end-12 {\n    grid-column-end: -12;\n  }\n\n  .xs\\:-col-end-13 {\n    grid-column-end: -13;\n  }\n\n  .xs\\:-col-end-2 {\n    grid-column-end: -2;\n  }\n\n  .xs\\:-col-end-3 {\n    grid-column-end: -3;\n  }\n\n  .xs\\:-col-end-4 {\n    grid-column-end: -4;\n  }\n\n  .xs\\:-col-end-5 {\n    grid-column-end: -5;\n  }\n\n  .xs\\:-col-end-6 {\n    grid-column-end: -6;\n  }\n\n  .xs\\:-col-end-7 {\n    grid-column-end: -7;\n  }\n\n  .xs\\:-col-end-8 {\n    grid-column-end: -8;\n  }\n\n  .xs\\:-col-end-9 {\n    grid-column-end: -9;\n  }\n\n  .xs\\:col-end-1 {\n    grid-column-end: 1;\n  }\n\n  .xs\\:col-end-10 {\n    grid-column-end: 10;\n  }\n\n  .xs\\:col-end-11 {\n    grid-column-end: 11;\n  }\n\n  .xs\\:col-end-12 {\n    grid-column-end: 12;\n  }\n\n  .xs\\:col-end-13 {\n    grid-column-end: 13;\n  }\n\n  .xs\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .xs\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .xs\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .xs\\:col-end-5 {\n    grid-column-end: 5;\n  }\n\n  .xs\\:col-end-6 {\n    grid-column-end: 6;\n  }\n\n  .xs\\:col-end-7 {\n    grid-column-end: 7;\n  }\n\n  .xs\\:col-end-8 {\n    grid-column-end: 8;\n  }\n\n  .xs\\:col-end-9 {\n    grid-column-end: 9;\n  }\n}\r\n@media (min-width: 640px) {\n\n  .sm\\:-top-1 {\n    top: -0.25rem;\n  }\n\n  .sm\\:right-2 {\n    right: 0.5rem;\n  }\n\n  .sm\\:right-6 {\n    right: 1.5rem;\n  }\n\n  .sm\\:top-6 {\n    top: 1.5rem;\n  }\n\n  .sm\\:-col-start-1 {\n    grid-column-start: -1;\n  }\n\n  .sm\\:-col-start-10 {\n    grid-column-start: -10;\n  }\n\n  .sm\\:-col-start-11 {\n    grid-column-start: -11;\n  }\n\n  .sm\\:-col-start-12 {\n    grid-column-start: -12;\n  }\n\n  .sm\\:-col-start-13 {\n    grid-column-start: -13;\n  }\n\n  .sm\\:-col-start-2 {\n    grid-column-start: -2;\n  }\n\n  .sm\\:-col-start-3 {\n    grid-column-start: -3;\n  }\n\n  .sm\\:-col-start-4 {\n    grid-column-start: -4;\n  }\n\n  .sm\\:-col-start-5 {\n    grid-column-start: -5;\n  }\n\n  .sm\\:-col-start-6 {\n    grid-column-start: -6;\n  }\n\n  .sm\\:-col-start-7 {\n    grid-column-start: -7;\n  }\n\n  .sm\\:-col-start-8 {\n    grid-column-start: -8;\n  }\n\n  .sm\\:-col-start-9 {\n    grid-column-start: -9;\n  }\n\n  .sm\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .sm\\:col-start-10 {\n    grid-column-start: 10;\n  }\n\n  .sm\\:col-start-11 {\n    grid-column-start: 11;\n  }\n\n  .sm\\:col-start-12 {\n    grid-column-start: 12;\n  }\n\n  .sm\\:col-start-13 {\n    grid-column-start: 13;\n  }\n\n  .sm\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .sm\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .sm\\:col-start-4 {\n    grid-column-start: 4;\n  }\n\n  .sm\\:col-start-5 {\n    grid-column-start: 5;\n  }\n\n  .sm\\:col-start-6 {\n    grid-column-start: 6;\n  }\n\n  .sm\\:col-start-7 {\n    grid-column-start: 7;\n  }\n\n  .sm\\:col-start-8 {\n    grid-column-start: 8;\n  }\n\n  .sm\\:col-start-9 {\n    grid-column-start: 9;\n  }\n\n  .sm\\:-col-end-1 {\n    grid-column-end: -1;\n  }\n\n  .sm\\:-col-end-10 {\n    grid-column-end: -10;\n  }\n\n  .sm\\:-col-end-11 {\n    grid-column-end: -11;\n  }\n\n  .sm\\:-col-end-12 {\n    grid-column-end: -12;\n  }\n\n  .sm\\:-col-end-13 {\n    grid-column-end: -13;\n  }\n\n  .sm\\:-col-end-2 {\n    grid-column-end: -2;\n  }\n\n  .sm\\:-col-end-3 {\n    grid-column-end: -3;\n  }\n\n  .sm\\:-col-end-4 {\n    grid-column-end: -4;\n  }\n\n  .sm\\:-col-end-5 {\n    grid-column-end: -5;\n  }\n\n  .sm\\:-col-end-6 {\n    grid-column-end: -6;\n  }\n\n  .sm\\:-col-end-7 {\n    grid-column-end: -7;\n  }\n\n  .sm\\:-col-end-8 {\n    grid-column-end: -8;\n  }\n\n  .sm\\:-col-end-9 {\n    grid-column-end: -9;\n  }\n\n  .sm\\:col-end-1 {\n    grid-column-end: 1;\n  }\n\n  .sm\\:col-end-10 {\n    grid-column-end: 10;\n  }\n\n  .sm\\:col-end-11 {\n    grid-column-end: 11;\n  }\n\n  .sm\\:col-end-12 {\n    grid-column-end: 12;\n  }\n\n  .sm\\:col-end-13 {\n    grid-column-end: 13;\n  }\n\n  .sm\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .sm\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .sm\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .sm\\:col-end-5 {\n    grid-column-end: 5;\n  }\n\n  .sm\\:col-end-6 {\n    grid-column-end: 6;\n  }\n\n  .sm\\:col-end-7 {\n    grid-column-end: 7;\n  }\n\n  .sm\\:col-end-8 {\n    grid-column-end: 8;\n  }\n\n  .sm\\:col-end-9 {\n    grid-column-end: 9;\n  }\n\n  .sm\\:mx-2 {\n    margin-left: 0.5rem;\n    margin-right: 0.5rem;\n  }\n\n  .sm\\:my-10 {\n    margin-top: 2.5rem;\n    margin-bottom: 2.5rem;\n  }\n\n  .sm\\:my-20 {\n    margin-top: 5rem;\n    margin-bottom: 5rem;\n  }\n\n  .sm\\:mb-8 {\n    margin-bottom: 2rem;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:mt-12 {\n    margin-top: 3rem;\n  }\n\n  .sm\\:mt-16 {\n    margin-top: 4rem;\n  }\n\n  .sm\\:mt-20 {\n    margin-top: 5rem;\n  }\n\n  .sm\\:mt-6 {\n    margin-top: 1.5rem;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:inline-block {\n    display: inline-block;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n\n  .sm\\:h-18 {\n    height: 4.5rem;\n  }\n\n  .sm\\:h-full {\n    height: 100%;\n  }\n\n  .sm\\:w-1\\/3 {\n    width: 33.333333%;\n  }\n\n  .sm\\:w-48 {\n    width: 12rem;\n  }\n\n  .sm\\:w-\\[50vw\\] {\n    width: 50vw;\n  }\n\n  .sm\\:w-fit {\n    width: -moz-fit-content;\n    width: fit-content;\n  }\n\n  .sm\\:w-full {\n    width: 100%;\n  }\n\n  .sm\\:min-w-min {\n    min-width: -moz-min-content;\n    min-width: min-content;\n  }\n\n  .sm\\:max-w-\\[560px\\] {\n    max-width: 560px;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:items-end {\n    align-items: flex-end;\n  }\n\n  .sm\\:items-center {\n    align-items: center;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:justify-center {\n    justify-content: center;\n  }\n\n  .sm\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .sm\\:gap-12 {\n    gap: 3rem;\n  }\n\n  .sm\\:gap-2\\.5 {\n    gap: 0.625rem;\n  }\n\n  .sm\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .sm\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-3 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-10 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:overflow-hidden {\n    overflow: hidden;\n  }\n\n  .sm\\:p-0 {\n    padding: 0px;\n  }\n\n  .sm\\:p-10 {\n    padding: 2.5rem;\n  }\n\n  .sm\\:\\!py-0 {\n    padding-top: 0px !important;\n    padding-bottom: 0px !important;\n  }\n\n  .sm\\:px-0 {\n    padding-left: 0px;\n    padding-right: 0px;\n  }\n\n  .sm\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .sm\\:px-2 {\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n  }\n\n  .sm\\:px-20 {\n    padding-left: 5rem;\n    padding-right: 5rem;\n  }\n\n  .sm\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .sm\\:py-10 {\n    padding-top: 2.5rem;\n    padding-bottom: 2.5rem;\n  }\n\n  .sm\\:py-16 {\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\n\n  .sm\\:pr-0 {\n    padding-right: 0px;\n  }\n\n  .sm\\:pr-4 {\n    padding-right: 1rem;\n  }\n\n  .sm\\:pt-8 {\n    padding-top: 2rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-justify {\n    text-align: justify;\n  }\n\n  .sm\\:text-base {\n    font-size: 1rem;\n    line-height: 1.2;\n  }\n\n  .sm\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.2;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .md\\:absolute {\n    position: absolute;\n  }\n\n  .md\\:col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n\n  .md\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .md\\:col-span-5 {\n    grid-column: span 5 / span 5;\n  }\n\n  .md\\:col-span-7 {\n    grid-column: span 7 / span 7;\n  }\n\n  .md\\:col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n\n  .md\\:-col-start-1 {\n    grid-column-start: -1;\n  }\n\n  .md\\:-col-start-10 {\n    grid-column-start: -10;\n  }\n\n  .md\\:-col-start-11 {\n    grid-column-start: -11;\n  }\n\n  .md\\:-col-start-12 {\n    grid-column-start: -12;\n  }\n\n  .md\\:-col-start-13 {\n    grid-column-start: -13;\n  }\n\n  .md\\:-col-start-2 {\n    grid-column-start: -2;\n  }\n\n  .md\\:-col-start-3 {\n    grid-column-start: -3;\n  }\n\n  .md\\:-col-start-4 {\n    grid-column-start: -4;\n  }\n\n  .md\\:-col-start-5 {\n    grid-column-start: -5;\n  }\n\n  .md\\:-col-start-6 {\n    grid-column-start: -6;\n  }\n\n  .md\\:-col-start-7 {\n    grid-column-start: -7;\n  }\n\n  .md\\:-col-start-8 {\n    grid-column-start: -8;\n  }\n\n  .md\\:-col-start-9 {\n    grid-column-start: -9;\n  }\n\n  .md\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .md\\:col-start-10 {\n    grid-column-start: 10;\n  }\n\n  .md\\:col-start-11 {\n    grid-column-start: 11;\n  }\n\n  .md\\:col-start-12 {\n    grid-column-start: 12;\n  }\n\n  .md\\:col-start-13 {\n    grid-column-start: 13;\n  }\n\n  .md\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .md\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .md\\:col-start-4 {\n    grid-column-start: 4;\n  }\n\n  .md\\:col-start-5 {\n    grid-column-start: 5;\n  }\n\n  .md\\:col-start-6 {\n    grid-column-start: 6;\n  }\n\n  .md\\:col-start-7 {\n    grid-column-start: 7;\n  }\n\n  .md\\:col-start-8 {\n    grid-column-start: 8;\n  }\n\n  .md\\:col-start-9 {\n    grid-column-start: 9;\n  }\n\n  .md\\:-col-end-1 {\n    grid-column-end: -1;\n  }\n\n  .md\\:-col-end-10 {\n    grid-column-end: -10;\n  }\n\n  .md\\:-col-end-11 {\n    grid-column-end: -11;\n  }\n\n  .md\\:-col-end-12 {\n    grid-column-end: -12;\n  }\n\n  .md\\:-col-end-13 {\n    grid-column-end: -13;\n  }\n\n  .md\\:-col-end-2 {\n    grid-column-end: -2;\n  }\n\n  .md\\:-col-end-3 {\n    grid-column-end: -3;\n  }\n\n  .md\\:-col-end-4 {\n    grid-column-end: -4;\n  }\n\n  .md\\:-col-end-5 {\n    grid-column-end: -5;\n  }\n\n  .md\\:-col-end-6 {\n    grid-column-end: -6;\n  }\n\n  .md\\:-col-end-7 {\n    grid-column-end: -7;\n  }\n\n  .md\\:-col-end-8 {\n    grid-column-end: -8;\n  }\n\n  .md\\:-col-end-9 {\n    grid-column-end: -9;\n  }\n\n  .md\\:col-end-1 {\n    grid-column-end: 1;\n  }\n\n  .md\\:col-end-10 {\n    grid-column-end: 10;\n  }\n\n  .md\\:col-end-11 {\n    grid-column-end: 11;\n  }\n\n  .md\\:col-end-12 {\n    grid-column-end: 12;\n  }\n\n  .md\\:col-end-13 {\n    grid-column-end: 13;\n  }\n\n  .md\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .md\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .md\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .md\\:col-end-5 {\n    grid-column-end: 5;\n  }\n\n  .md\\:col-end-6 {\n    grid-column-end: 6;\n  }\n\n  .md\\:col-end-7 {\n    grid-column-end: 7;\n  }\n\n  .md\\:col-end-8 {\n    grid-column-end: 8;\n  }\n\n  .md\\:col-end-9 {\n    grid-column-end: 9;\n  }\n\n  .md\\:my-4 {\n    margin-top: 1rem;\n    margin-bottom: 1rem;\n  }\n\n  .md\\:my-\\[10vh\\] {\n    margin-top: 10vh;\n    margin-bottom: 10vh;\n  }\n\n  .md\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .md\\:mt-10 {\n    margin-top: 2.5rem;\n  }\n\n  .md\\:mt-15 {\n    margin-top: 3.75rem;\n  }\n\n  .md\\:mt-2 {\n    margin-top: 0.5rem;\n  }\n\n  .md\\:mt-32 {\n    margin-top: 8rem;\n  }\n\n  .md\\:line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:grid {\n    display: grid;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-10 {\n    height: 2.5rem;\n  }\n\n  .md\\:h-12 {\n    height: 3rem;\n  }\n\n  .md\\:h-14 {\n    height: 3.5rem;\n  }\n\n  .md\\:h-18 {\n    height: 4.5rem;\n  }\n\n  .md\\:h-20 {\n    height: 5rem;\n  }\n\n  .md\\:h-3 {\n    height: 0.75rem;\n  }\n\n  .md\\:h-40 {\n    height: 10rem;\n  }\n\n  .md\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .md\\:w-1\\/3 {\n    width: 33.333333%;\n  }\n\n  .md\\:w-1\\/4 {\n    width: 25%;\n  }\n\n  .md\\:w-10 {\n    width: 2.5rem;\n  }\n\n  .md\\:w-14 {\n    width: 3.5rem;\n  }\n\n  .md\\:w-18 {\n    width: 4.5rem;\n  }\n\n  .md\\:w-2\\/3 {\n    width: 66.666667%;\n  }\n\n  .md\\:w-2\\/4 {\n    width: 50%;\n  }\n\n  .md\\:w-20 {\n    width: 5rem;\n  }\n\n  .md\\:w-3 {\n    width: 0.75rem;\n  }\n\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    width: var(--radix-navigation-menu-viewport-width);\n  }\n\n  .md\\:w-auto {\n    width: auto;\n  }\n\n  .md\\:w-fit {\n    width: -moz-fit-content;\n    width: fit-content;\n  }\n\n  .md\\:w-full {\n    width: 100%;\n  }\n\n  .md\\:min-w-\\[420px\\] {\n    min-width: 420px;\n  }\n\n  .md\\:max-w-\\[40vw\\] {\n    max-width: 40vw;\n  }\n\n  .md\\:grid-cols-12 {\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:flex-wrap {\n    flex-wrap: wrap;\n  }\n\n  .md\\:flex-nowrap {\n    flex-wrap: nowrap;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .md\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .md\\:gap-10 {\n    gap: 2.5rem;\n  }\n\n  .md\\:gap-20 {\n    gap: 5rem;\n  }\n\n  .md\\:gap-3 {\n    gap: 0.75rem;\n  }\n\n  .md\\:gap-32 {\n    gap: 8rem;\n  }\n\n  .md\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .md\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .md\\:gap-x-10 {\n    -moz-column-gap: 2.5rem;\n         column-gap: 2.5rem;\n  }\n\n  .md\\:gap-x-12 {\n    -moz-column-gap: 3rem;\n         column-gap: 3rem;\n  }\n\n  .md\\:gap-y-4 {\n    row-gap: 1rem;\n  }\n\n  .md\\:bg-zinc-50 {\n    --tw-bg-opacity: 1;\n    background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n  }\n\n  .md\\:p-0 {\n    padding: 0px;\n  }\n\n  .md\\:p-10 {\n    padding: 2.5rem;\n  }\n\n  .md\\:p-4 {\n    padding: 1rem;\n  }\n\n  .md\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .md\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .md\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .md\\:px-16 {\n    padding-left: 4rem;\n    padding-right: 4rem;\n  }\n\n  .md\\:px-2 {\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n  }\n\n  .md\\:px-3 {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n\n  .md\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .md\\:py-10 {\n    padding-top: 2.5rem;\n    padding-bottom: 2.5rem;\n  }\n\n  .md\\:py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n\n  .md\\:pt-20 {\n    padding-top: 5rem;\n  }\n\n  .md\\:pt-\\[70px\\] {\n    padding-top: 70px;\n  }\n\n  .md\\:text-center {\n    text-align: center;\n  }\n\n  .md\\:text-2xl {\n    font-size: 2rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-2xs {\n    font-size: 0.625rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.5rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-base {\n    font-size: 1rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-lg {\n    font-size: 1.5rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-md {\n    font-size: 1.25rem;\n    line-height: 1.2;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.2;\n  }\n\n  .group:hover .md\\:group-hover\\:h-auto {\n    height: auto;\n  }\n\n  .group:hover .md\\:group-hover\\:opacity-100 {\n    opacity: 1;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n\n  .lg\\:-col-start-1 {\n    grid-column-start: -1;\n  }\n\n  .lg\\:-col-start-10 {\n    grid-column-start: -10;\n  }\n\n  .lg\\:-col-start-11 {\n    grid-column-start: -11;\n  }\n\n  .lg\\:-col-start-12 {\n    grid-column-start: -12;\n  }\n\n  .lg\\:-col-start-13 {\n    grid-column-start: -13;\n  }\n\n  .lg\\:-col-start-2 {\n    grid-column-start: -2;\n  }\n\n  .lg\\:-col-start-3 {\n    grid-column-start: -3;\n  }\n\n  .lg\\:-col-start-4 {\n    grid-column-start: -4;\n  }\n\n  .lg\\:-col-start-5 {\n    grid-column-start: -5;\n  }\n\n  .lg\\:-col-start-6 {\n    grid-column-start: -6;\n  }\n\n  .lg\\:-col-start-7 {\n    grid-column-start: -7;\n  }\n\n  .lg\\:-col-start-8 {\n    grid-column-start: -8;\n  }\n\n  .lg\\:-col-start-9 {\n    grid-column-start: -9;\n  }\n\n  .lg\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .lg\\:col-start-10 {\n    grid-column-start: 10;\n  }\n\n  .lg\\:col-start-11 {\n    grid-column-start: 11;\n  }\n\n  .lg\\:col-start-12 {\n    grid-column-start: 12;\n  }\n\n  .lg\\:col-start-13 {\n    grid-column-start: 13;\n  }\n\n  .lg\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .lg\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .lg\\:col-start-4 {\n    grid-column-start: 4;\n  }\n\n  .lg\\:col-start-5 {\n    grid-column-start: 5;\n  }\n\n  .lg\\:col-start-6 {\n    grid-column-start: 6;\n  }\n\n  .lg\\:col-start-7 {\n    grid-column-start: 7;\n  }\n\n  .lg\\:col-start-8 {\n    grid-column-start: 8;\n  }\n\n  .lg\\:col-start-9 {\n    grid-column-start: 9;\n  }\n\n  .lg\\:-col-end-1 {\n    grid-column-end: -1;\n  }\n\n  .lg\\:-col-end-10 {\n    grid-column-end: -10;\n  }\n\n  .lg\\:-col-end-11 {\n    grid-column-end: -11;\n  }\n\n  .lg\\:-col-end-12 {\n    grid-column-end: -12;\n  }\n\n  .lg\\:-col-end-13 {\n    grid-column-end: -13;\n  }\n\n  .lg\\:-col-end-2 {\n    grid-column-end: -2;\n  }\n\n  .lg\\:-col-end-3 {\n    grid-column-end: -3;\n  }\n\n  .lg\\:-col-end-4 {\n    grid-column-end: -4;\n  }\n\n  .lg\\:-col-end-5 {\n    grid-column-end: -5;\n  }\n\n  .lg\\:-col-end-6 {\n    grid-column-end: -6;\n  }\n\n  .lg\\:-col-end-7 {\n    grid-column-end: -7;\n  }\n\n  .lg\\:-col-end-8 {\n    grid-column-end: -8;\n  }\n\n  .lg\\:-col-end-9 {\n    grid-column-end: -9;\n  }\n\n  .lg\\:col-end-1 {\n    grid-column-end: 1;\n  }\n\n  .lg\\:col-end-10 {\n    grid-column-end: 10;\n  }\n\n  .lg\\:col-end-11 {\n    grid-column-end: 11;\n  }\n\n  .lg\\:col-end-12 {\n    grid-column-end: 12;\n  }\n\n  .lg\\:col-end-13 {\n    grid-column-end: 13;\n  }\n\n  .lg\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .lg\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .lg\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .lg\\:col-end-5 {\n    grid-column-end: 5;\n  }\n\n  .lg\\:col-end-6 {\n    grid-column-end: 6;\n  }\n\n  .lg\\:col-end-7 {\n    grid-column-end: 7;\n  }\n\n  .lg\\:col-end-8 {\n    grid-column-end: 8;\n  }\n\n  .lg\\:col-end-9 {\n    grid-column-end: 9;\n  }\n\n  .lg\\:my-20 {\n    margin-top: 5rem;\n    margin-bottom: 5rem;\n  }\n\n  .lg\\:mb-10 {\n    margin-bottom: 2.5rem;\n  }\n\n  .lg\\:ml-8 {\n    margin-left: 2rem;\n  }\n\n  .lg\\:mt-10 {\n    margin-top: 2.5rem;\n  }\n\n  .lg\\:mt-28 {\n    margin-top: 7rem;\n  }\n\n  .lg\\:block {\n    display: block;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:grid {\n    display: grid;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:h-10 {\n    height: 2.5rem;\n  }\n\n  .lg\\:h-12 {\n    height: 3rem;\n  }\n\n  .lg\\:h-14 {\n    height: 3.5rem;\n  }\n\n  .lg\\:h-18 {\n    height: 4.5rem;\n  }\n\n  .lg\\:h-24 {\n    height: 6rem;\n  }\n\n  .lg\\:h-28 {\n    height: 7rem;\n  }\n\n  .lg\\:h-30 {\n    height: 7.5rem;\n  }\n\n  .lg\\:h-34 {\n    height: 8.5rem;\n  }\n\n  .lg\\:h-40 {\n    height: 10rem;\n  }\n\n  .lg\\:w-10 {\n    width: 2.5rem;\n  }\n\n  .lg\\:w-14 {\n    width: 3.5rem;\n  }\n\n  .lg\\:w-18 {\n    width: 4.5rem;\n  }\n\n  .lg\\:w-28 {\n    width: 7rem;\n  }\n\n  .lg\\:w-30 {\n    width: 7.5rem;\n  }\n\n  .lg\\:w-34 {\n    width: 8.5rem;\n  }\n\n  .lg\\:w-40 {\n    width: 10rem;\n  }\n\n  .lg\\:w-full {\n    width: 100%;\n  }\n\n  .lg\\:max-w-\\[500px\\] {\n    max-width: 500px;\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:flex-col {\n    flex-direction: column;\n  }\n\n  .lg\\:gap-10 {\n    gap: 2.5rem;\n  }\n\n  .lg\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .lg\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .lg\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .lg\\:gap-y-6 {\n    row-gap: 1.5rem;\n  }\n\n  .lg\\:\\!p-6 {\n    padding: 1.5rem !important;\n  }\n\n  .lg\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .lg\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .lg\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .lg\\:py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n\n  .lg\\:py-20 {\n    padding-top: 5rem;\n    padding-bottom: 5rem;\n  }\n\n  .lg\\:pt-3 {\n    padding-top: 0.75rem;\n  }\n\n  .lg\\:text-2xl {\n    font-size: 2rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-2xs {\n    font-size: 0.625rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-3xl {\n    font-size: 2.25rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-4xl {\n    font-size: 2.5rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-6xl {\n    font-size: 3.5rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-base {\n    font-size: 1rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-base18 {\n    font-size: 1.125rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-lg {\n    font-size: 1.5rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-md {\n    font-size: 1.25rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.2;\n  }\n\n  .lg\\:text-xl {\n    font-size: 1.75rem;\n    line-height: 1.2;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .xl\\:col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n\n  .xl\\:col-span-8 {\n    grid-column: span 8 / span 8;\n  }\n\n  .xl\\:col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n\n  .xl\\:-col-start-1 {\n    grid-column-start: -1;\n  }\n\n  .xl\\:-col-start-10 {\n    grid-column-start: -10;\n  }\n\n  .xl\\:-col-start-11 {\n    grid-column-start: -11;\n  }\n\n  .xl\\:-col-start-12 {\n    grid-column-start: -12;\n  }\n\n  .xl\\:-col-start-13 {\n    grid-column-start: -13;\n  }\n\n  .xl\\:-col-start-2 {\n    grid-column-start: -2;\n  }\n\n  .xl\\:-col-start-3 {\n    grid-column-start: -3;\n  }\n\n  .xl\\:-col-start-4 {\n    grid-column-start: -4;\n  }\n\n  .xl\\:-col-start-5 {\n    grid-column-start: -5;\n  }\n\n  .xl\\:-col-start-6 {\n    grid-column-start: -6;\n  }\n\n  .xl\\:-col-start-7 {\n    grid-column-start: -7;\n  }\n\n  .xl\\:-col-start-8 {\n    grid-column-start: -8;\n  }\n\n  .xl\\:-col-start-9 {\n    grid-column-start: -9;\n  }\n\n  .xl\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .xl\\:col-start-10 {\n    grid-column-start: 10;\n  }\n\n  .xl\\:col-start-11 {\n    grid-column-start: 11;\n  }\n\n  .xl\\:col-start-12 {\n    grid-column-start: 12;\n  }\n\n  .xl\\:col-start-13 {\n    grid-column-start: 13;\n  }\n\n  .xl\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .xl\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .xl\\:col-start-4 {\n    grid-column-start: 4;\n  }\n\n  .xl\\:col-start-5 {\n    grid-column-start: 5;\n  }\n\n  .xl\\:col-start-6 {\n    grid-column-start: 6;\n  }\n\n  .xl\\:col-start-7 {\n    grid-column-start: 7;\n  }\n\n  .xl\\:col-start-8 {\n    grid-column-start: 8;\n  }\n\n  .xl\\:col-start-9 {\n    grid-column-start: 9;\n  }\n\n  .xl\\:-col-end-1 {\n    grid-column-end: -1;\n  }\n\n  .xl\\:-col-end-10 {\n    grid-column-end: -10;\n  }\n\n  .xl\\:-col-end-11 {\n    grid-column-end: -11;\n  }\n\n  .xl\\:-col-end-12 {\n    grid-column-end: -12;\n  }\n\n  .xl\\:-col-end-13 {\n    grid-column-end: -13;\n  }\n\n  .xl\\:-col-end-2 {\n    grid-column-end: -2;\n  }\n\n  .xl\\:-col-end-3 {\n    grid-column-end: -3;\n  }\n\n  .xl\\:-col-end-4 {\n    grid-column-end: -4;\n  }\n\n  .xl\\:-col-end-5 {\n    grid-column-end: -5;\n  }\n\n  .xl\\:-col-end-6 {\n    grid-column-end: -6;\n  }\n\n  .xl\\:-col-end-7 {\n    grid-column-end: -7;\n  }\n\n  .xl\\:-col-end-8 {\n    grid-column-end: -8;\n  }\n\n  .xl\\:-col-end-9 {\n    grid-column-end: -9;\n  }\n\n  .xl\\:col-end-1 {\n    grid-column-end: 1;\n  }\n\n  .xl\\:col-end-10 {\n    grid-column-end: 10;\n  }\n\n  .xl\\:col-end-11 {\n    grid-column-end: 11;\n  }\n\n  .xl\\:col-end-12 {\n    grid-column-end: 12;\n  }\n\n  .xl\\:col-end-13 {\n    grid-column-end: 13;\n  }\n\n  .xl\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .xl\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .xl\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .xl\\:col-end-5 {\n    grid-column-end: 5;\n  }\n\n  .xl\\:col-end-6 {\n    grid-column-end: 6;\n  }\n\n  .xl\\:col-end-7 {\n    grid-column-end: 7;\n  }\n\n  .xl\\:col-end-8 {\n    grid-column-end: 8;\n  }\n\n  .xl\\:col-end-9 {\n    grid-column-end: 9;\n  }\n\n  .xl\\:mb-20 {\n    margin-bottom: 5rem;\n  }\n\n  .xl\\:mt-32 {\n    margin-top: 8rem;\n  }\n\n  .xl\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .xl\\:gap-10 {\n    gap: 2.5rem;\n  }\n\n  .xl\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .xl\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .xl\\:text-2xl {\n    font-size: 2rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-3xl {\n    font-size: 2.25rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-4xl {\n    font-size: 2.5rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-6xl {\n    font-size: 3.5rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-7xl {\n    font-size: 4rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-base {\n    font-size: 1rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-base18 {\n    font-size: 1.125rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-lg {\n    font-size: 1.5rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-md {\n    font-size: 1.25rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-xl {\n    font-size: 1.75rem;\n    line-height: 1.2;\n  }\n\n  .xl\\:text-xs {\n    font-size: 0.75rem;\n    line-height: 1.2;\n  }\n}\r\n@media (min-width: 1440px) {\n\n  .\\32xl\\:w-1\\/4 {\n    width: 25%;\n  }\n\n  .\\32xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .\\32xl\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .\\32xl\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .\\32xl\\:px-20 {\n    padding-left: 5rem;\n    padding-right: 5rem;\n  }\n}\r\n@media (min-width: 1920px) {\n\n  .\\33xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\r\n.\\[\\&\\:\\:--webkit-search-cancel-button\\]\\:hidden::--webkit-search-cancel-button {\n  display: none;\n}\r\n.\\[\\&\\:\\:-webkit-inner-spin-button\\]\\:\\[-webkit-appearance\\:none\\]::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n}\r\n.\\[\\&\\:\\:-webkit-outer-spin-button\\]\\:\\[-webkit-appearance\\:none\\]::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n}\r\n.\\[\\&\\:\\:-webkit-search-cancel-button\\]\\:hidden::-webkit-search-cancel-button {\n  display: none;\n}\r\n.\\[\\&\\:\\:-webkit-search-decoration\\]\\:hidden::-webkit-search-decoration {\n  display: none;\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md:has(>.day-range-end) {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md:has(>.day-range-start) {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md:has([aria-selected]) {\n  border-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]) {\n  background-color: hsl(var(--accent));\n}\r\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\r\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside) {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end) {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\r\n.\\[\\&\\>\\*\\]\\:flex>* {\n  display: flex;\n}\r\n.\\[\\&\\>\\*\\]\\:items-center>* {\n  align-items: center;\n}\r\n.\\[\\&\\>\\*\\]\\:justify-center>* {\n  justify-content: center;\n}\r\n.\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\]>[role=checkbox] {\n  --tw-translate-y: 2px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&\\>a\\]\\:bg-transparent>a {\n  background-color: rgba(0,0,0,0);\n}\r\n.\\[\\&\\>button\\]\\:hidden>button {\n  display: none;\n}\r\n.\\[\\&\\>button\\]\\:bg-transparent>button {\n  background-color: rgba(0,0,0,0);\n}\r\n.\\[\\&\\>code\\]\\:mx-2>code {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.\\[\\&\\>code\\]\\:text-ui-contrast-fg-primary>code {\n  color: var(--contrast-fg-primary);\n}\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:h-3\\.5>svg {\n  height: 0.875rem;\n}\r\n.\\[\\&\\>svg\\]\\:w-3\\.5>svg {\n  width: 0.875rem;\n}\r\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&\\>svg\\]\\:fill-\\[\\#344054\\]>svg {\n  fill: #344054;\n}\r\n.\\[\\&\\>svg\\]\\:stroke-\\[\\#344054\\]>svg {\n  stroke: #344054;\n}\r\n.\\[\\&\\>svg\\]\\:stroke-white>svg {\n  stroke: #fff;\n}\r\n.\\[\\&\\>svg\\]\\:text-ui-fg-subtle>svg {\n  color: var(--fg-subtle);\n}\r\n.\\[\\&\\>svg\\]\\:hover\\:\\!fill-primary-lighter:hover>svg {\n  fill: #EEFBE6 !important;\n}\r\n.\\[\\&\\>svg\\]\\:hover\\:\\!stroke-primary-lighter:hover>svg {\n  stroke: #EEFBE6 !important;\n}\r\n.\\[\\&\\>svg\\]\\:hover\\:\\!stroke-primary-main:hover>svg {\n  stroke: #59B71F !important;\n}\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\r\n.\\[\\&\\>ul\\]\\:rounded-none>ul {\n  border-radius: 0px;\n}\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 [cmdk-group-heading] {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 [cmdk-group-heading] {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs [cmdk-group-heading] {\n  font-size: 0.75rem;\n  line-height: 1.2;\n}\r\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium [cmdk-group-heading] {\n  font-weight: 500;\n}\r\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground [cmdk-group-heading] {\n  color: hsl(var(--muted-foreground));\n}\r\n.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {\n  padding-top: 0px;\n}\r\n.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 [cmdk-group] {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 [cmdk-input-wrapper] svg {\n  height: 1.25rem;\n}\r\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 [cmdk-input-wrapper] svg {\n  width: 1.25rem;\n}\r\n.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 [cmdk-input] {\n  height: 3rem;\n}\r\n.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 [cmdk-item] {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 [cmdk-item] {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 [cmdk-item] svg {\n  height: 1.25rem;\n}\r\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 [cmdk-item] svg {\n  width: 1.25rem;\n}\r\n.\\[\\&_div\\]\\:h-2 div {\n  height: 0.5rem;\n}\r\n.\\[\\&_div\\]\\:w-2 div {\n  width: 0.5rem;\n}\r\n.\\[\\&_div\\]\\:rounded-sm div {\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-blue-icon div {\n  background-color: var(--tag-blue-icon);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-green-icon div {\n  background-color: var(--tag-green-icon);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-neutral-icon div {\n  background-color: var(--tag-neutral-icon);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-orange-icon div {\n  background-color: var(--tag-orange-icon);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-purple-icon div {\n  background-color: var(--tag-purple-icon);\n}\r\n.\\[\\&_div\\]\\:bg-ui-tag-red-icon div {\n  background-color: var(--tag-red-icon);\n}\r\n.\\[\\&_path\\]\\:shadow-details-contrast-on-bg-interactive path {\n  --tw-shadow: var(--details-contrast-on-bg-interactive);\n  --tw-shadow-colored: var(--details-contrast-on-bg-interactive);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\r\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-blue-icon svg {\n  color: var(--tag-blue-icon);\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-green-icon svg {\n  color: var(--tag-green-icon);\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-neutral-icon svg {\n  color: var(--tag-neutral-icon);\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-orange-icon svg {\n  color: var(--tag-orange-icon);\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-purple-icon svg {\n  color: var(--tag-purple-icon);\n}\r\n.\\[\\&_svg\\]\\:text-ui-tag-red-icon svg {\n  color: var(--tag-red-icon);\n}\r\n.\\[\\&_td\\:first-child\\]\\:pl-6 td:first-child {\n  padding-left: 1.5rem;\n}\r\n.\\[\\&_td\\:last-child\\]\\:pr-6 td:last-child {\n  padding-right: 1.5rem;\n}\r\n.\\[\\&_th\\:first-child\\]\\:pl-6 th:first-child {\n  padding-left: 1.5rem;\n}\r\n.\\[\\&_th\\:first-of-type\\]\\:w-\\[1\\%\\] th:first-of-type {\n  width: 1%;\n}\r\n.\\[\\&_th\\:first-of-type\\]\\:whitespace-nowrap th:first-of-type {\n  white-space: nowrap;\n}\r\n.\\[\\&_th\\:last-child\\]\\:pr-6 th:last-child {\n  padding-right: 1.5rem;\n}\r\n.\\[\\&_th\\:last-of-type\\]\\:w-\\[1\\%\\] th:last-of-type {\n  width: 1%;\n}\r\n.\\[\\&_th\\:last-of-type\\]\\:whitespace-nowrap th:last-of-type {\n  white-space: nowrap;\n}\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\r\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}\r\n.\\[\\&_tr\\]\\:bg-ui-bg-subtle tr {\n  background-color: var(--bg-subtle);\n}\r\n.\\[\\&_tr\\]\\:hover\\:bg-ui-bg-subtle:hover tr {\n  background-color: var(--bg-subtle);\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;AASA;;;;AAeA;;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;;AASA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAKA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmME;;;;;;;;;AAQF;;;;;;;;AAOA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAGA;;;;;;;;AAeA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAMA;;;;;AAKA;;;;;;;;;AAWA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAYA;;;;AAIA;;;;;;AAMA;;;;;AAMA;EACE;;;;;EAKA;;;;EAKA;;;;;;AAOF;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;EAEE;;;;;AAIF;;;;;;AAKA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;EAOA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAKF;EAEE;;;;;AAIF;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA"}}, {"offset": {"line": 9780, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}