{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-AOFGTNG6.mjs"], "sourcesContent": ["// src/components/table/data-table/data-table-order-by/data-table-order-by.tsx\nimport { DescendingSorting } from \"@medusajs/icons\";\nimport { DropdownMenu, IconButton } from \"@medusajs/ui\";\nimport { useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useSearchParams } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar initState = (params, prefix) => {\n  const param = prefix ? `${prefix}_order` : \"order\";\n  const sortParam = params.get(param);\n  if (!sortParam) {\n    return {\n      dir: \"asc\" /* ASC */\n    };\n  }\n  const dir = sortParam.startsWith(\"-\") ? \"desc\" /* DESC */ : \"asc\" /* ASC */;\n  const key = sortParam.replace(\"-\", \"\");\n  return {\n    key,\n    dir\n  };\n};\nvar DataTableOrderBy = ({\n  keys,\n  prefix\n}) => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [state, setState] = useState(initState(searchParams, prefix));\n  const param = prefix ? `${prefix}_order` : \"order\";\n  const { t } = useTranslation();\n  const handleDirChange = (dir) => {\n    setState((prev) => ({\n      ...prev,\n      dir\n    }));\n    updateOrderParam({\n      key: state.key,\n      dir\n    });\n  };\n  const handleKeyChange = (value) => {\n    setState((prev) => ({\n      ...prev,\n      key: value\n    }));\n    updateOrderParam({\n      key: value,\n      dir: state.dir\n    });\n  };\n  const updateOrderParam = (state2) => {\n    if (!state2.key) {\n      setSearchParams((prev) => {\n        prev.delete(param);\n        return prev;\n      });\n      return;\n    }\n    const orderParam = state2.dir === \"asc\" /* ASC */ ? state2.key : `-${state2.key}`;\n    setSearchParams((prev) => {\n      prev.set(param, orderParam);\n      return prev;\n    });\n  };\n  return /* @__PURE__ */ jsxs(DropdownMenu, { children: [\n    /* @__PURE__ */ jsx(DropdownMenu.Trigger, { asChild: true, children: /* @__PURE__ */ jsx(IconButton, { size: \"small\", children: /* @__PURE__ */ jsx(DescendingSorting, {}) }) }),\n    /* @__PURE__ */ jsxs(DropdownMenu.Content, { className: \"z-[1]\", align: \"end\", children: [\n      /* @__PURE__ */ jsx(\n        DropdownMenu.RadioGroup,\n        {\n          value: state.key,\n          onValueChange: handleKeyChange,\n          children: keys.map((key) => {\n            const stringKey = String(key.key);\n            return /* @__PURE__ */ jsx(\n              DropdownMenu.RadioItem,\n              {\n                value: stringKey,\n                onSelect: (event) => event.preventDefault(),\n                children: key.label\n              },\n              stringKey\n            );\n          })\n        }\n      ),\n      /* @__PURE__ */ jsx(DropdownMenu.Separator, {}),\n      /* @__PURE__ */ jsxs(\n        DropdownMenu.RadioGroup,\n        {\n          value: state.dir,\n          onValueChange: handleDirChange,\n          children: [\n            /* @__PURE__ */ jsxs(\n              DropdownMenu.RadioItem,\n              {\n                className: \"flex items-center justify-between\",\n                value: \"asc\",\n                onSelect: (event) => event.preventDefault(),\n                children: [\n                  t(\"general.ascending\"),\n                  /* @__PURE__ */ jsx(DropdownMenu.Label, { children: \"1 - 30\" })\n                ]\n              }\n            ),\n            /* @__PURE__ */ jsxs(\n              DropdownMenu.RadioItem,\n              {\n                className: \"flex items-center justify-between\",\n                value: \"desc\",\n                onSelect: (event) => event.preventDefault(),\n                children: [\n                  t(\"general.descending\"),\n                  /* @__PURE__ */ jsx(DropdownMenu.Label, { children: \"30 - 1\" })\n                ]\n              }\n            )\n          ]\n        }\n      )\n    ] })\n  ] });\n};\n\nexport {\n  DataTableOrderBy\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAGA,mBAAyB;AAGzB,yBAA0B;AAC1B,IAAI,YAAY,CAAC,QAAQ,WAAW;AAClC,QAAM,QAAQ,SAAS,GAAG,MAAM,WAAW;AAC3C,QAAM,YAAY,OAAO,IAAI,KAAK;AAClC,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,MACL,KAAK;AAAA;AAAA,IACP;AAAA,EACF;AACA,QAAM,MAAM,UAAU,WAAW,GAAG,IAAI,SAAoB;AAC5D,QAAM,MAAM,UAAU,QAAQ,KAAK,EAAE;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,cAAc,eAAe,IAAI,gBAAgB;AACxD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,UAAU,cAAc,MAAM,CAAC;AAClE,QAAM,QAAQ,SAAS,GAAG,MAAM,WAAW;AAC3C,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,kBAAkB,CAAC,QAAQ;AAC/B,aAAS,CAAC,UAAU;AAAA,MAClB,GAAG;AAAA,MACH;AAAA,IACF,EAAE;AACF,qBAAiB;AAAA,MACf,KAAK,MAAM;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,CAAC,UAAU;AACjC,aAAS,CAAC,UAAU;AAAA,MAClB,GAAG;AAAA,MACH,KAAK;AAAA,IACP,EAAE;AACF,qBAAiB;AAAA,MACf,KAAK;AAAA,MACL,KAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,CAAC,WAAW;AACnC,QAAI,CAAC,OAAO,KAAK;AACf,sBAAgB,CAAC,SAAS;AACxB,aAAK,OAAO,KAAK;AACjB,eAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF;AACA,UAAM,aAAa,OAAO,QAAQ,QAAkB,OAAO,MAAM,IAAI,OAAO,GAAG;AAC/E,oBAAgB,CAAC,SAAS;AACxB,WAAK,IAAI,OAAO,UAAU;AAC1B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,aAAuB,yBAAK,cAAc,EAAE,UAAU;AAAA,QACpC,wBAAI,aAAa,SAAS,EAAE,SAAS,MAAM,cAA0B,wBAAI,YAAY,EAAE,MAAM,SAAS,cAA0B,wBAAI,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QAC/J,yBAAK,aAAa,SAAS,EAAE,WAAW,SAAS,OAAO,OAAO,UAAU;AAAA,UACvE;AAAA,QACd,aAAa;AAAA,QACb;AAAA,UACE,OAAO,MAAM;AAAA,UACb,eAAe;AAAA,UACf,UAAU,KAAK,IAAI,CAAC,QAAQ;AAC1B,kBAAM,YAAY,OAAO,IAAI,GAAG;AAChC,uBAAuB;AAAA,cACrB,aAAa;AAAA,cACb;AAAA,gBACE,OAAO;AAAA,gBACP,UAAU,CAAC,UAAU,MAAM,eAAe;AAAA,gBAC1C,UAAU,IAAI;AAAA,cAChB;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,UACgB,wBAAI,aAAa,WAAW,CAAC,CAAC;AAAA,UAC9B;AAAA,QACd,aAAa;AAAA,QACb;AAAA,UACE,OAAO,MAAM;AAAA,UACb,eAAe;AAAA,UACf,UAAU;AAAA,gBACQ;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,UAAU,CAAC,UAAU,MAAM,eAAe;AAAA,gBAC1C,UAAU;AAAA,kBACR,EAAE,mBAAmB;AAAA,sBACL,wBAAI,aAAa,OAAO,EAAE,UAAU,SAAS,CAAC;AAAA,gBAChE;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,aAAa;AAAA,cACb;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,UAAU,CAAC,UAAU,MAAM,eAAe;AAAA,gBAC1C,UAAU;AAAA,kBACR,EAAE,oBAAoB;AAAA,sBACN,wBAAI,aAAa,OAAO,EAAE,UAAU,SAAS,CAAC;AAAA,gBAChE;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;", "names": []}