{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-W7625H47.mjs"], "sourcesContent": ["// src/hooks/table/filters/use-date-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useDateTableFilters = () => {\n  const { t } = useTranslation();\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  return dateFilters;\n};\n\nexport {\n  useDateTableFilters\n};\n"], "mappings": ";;;;;AAEA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,SAAO;AACT;", "names": []}