import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-PIR2H25N.mjs
var SHIPPING_PROFILE_QUERY_KEY = "shipping_profile";
var shippingProfileQueryKeys = queryKeysFactory(
  SHIPPING_PROFILE_QUERY_KEY
);
var useCreateShippingProfile = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.shippingProfile.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: shippingProfileQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useShippingProfile = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.shippingProfile.retrieve(id, query),
    queryKey: shippingProfileQueryKeys.detail(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useShippingProfiles = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.shippingProfile.list(query),
    queryKey: shippingProfileQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useUpdateShippingProfile = (id, options) => {
  const { data, ...rest } = useMutation({
    mutationFn: (payload) => sdk.admin.shippingProfile.update(id, payload),
    onSuccess: (data2, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: shippingProfileQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: shippingProfileQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data2, variables, context);
    },
    ...options
  });
  return { ...data, ...rest };
};
var useDeleteShippingProfile = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.shippingProfile.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: shippingProfileQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: shippingProfileQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  shippingProfileQueryKeys,
  useCreateShippingProfile,
  useShippingProfile,
  useShippingProfiles,
  useUpdateShippingProfile,
  useDeleteShippingProfile
};
//# sourceMappingURL=chunk-KRJAVNJS.js.map
