import {
  pricePreferencesQueryKeys
} from "./chunk-662EXSHO.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  FetchError
} from "./chunk-WYTGSSQF.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-V2LANK5S.mjs
var STORE_QUERY_KEY = "store";
var storeQueryKeys = queryKeysFactory(STORE_QUERY_KEY);
async function retrieveActiveStore(query) {
  var _a;
  const response = await sdk.admin.store.list(query);
  const activeStore = (_a = response.stores) == null ? void 0 : _a[0];
  if (!activeStore) {
    throw new FetchError("No active store found", "Not Found", 404);
  }
  return { store: activeStore };
}
var useStore = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => retrieveActiveStore(query),
    queryKey: storeQueryKeys.details(),
    ...options
  });
  return {
    ...data,
    ...rest
  };
};
var useUpdateStore = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.store.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.list()
      });
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.details()
      });
      queryClient.invalidateQueries({ queryKey: storeQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  storeQueryKeys,
  retrieveActiveStore,
  useStore,
  useUpdateStore
};
//# sourceMappingURL=chunk-RWC53K5F.js.map
