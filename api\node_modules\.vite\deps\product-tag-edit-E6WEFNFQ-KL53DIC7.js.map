{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-tag-edit-E6WEFNFQ.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useProductTag,\n  useUpdateProductTag\n} from \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/product-tags/product-tag-edit/product-tag-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/product-tags/product-tag-edit/components/product-tag-edit-form/product-tag-edit-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ProductTagEditSchema = z.object({\n  value: z.string().min(1)\n});\nvar ProductTagEditForm = ({ productTag }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      value: productTag.value\n    },\n    resolver: zodResolver(ProductTagEditSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateProductTag(productTag.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: ({ product_tag }) => {\n        toast.success(\n          t(\"productTags.edit.successToast\", {\n            value: product_tag.value\n          })\n        );\n        handleSuccess();\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex size-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex flex-1 flex-col overflow-auto\", children: /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"value\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"productTags.fields.value\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", type: \"button\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/product-tags/product-tag-edit/product-tag-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductTagEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product_tag, isPending, isError, error } = useProductTag(id);\n  const ready = !isPending && !!product_tag;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"productTags.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"productTags.edit.subtitle\") })\n    ] }),\n    ready && /* @__PURE__ */ jsx2(ProductTagEditForm, { productTag: product_tag })\n  ] });\n};\nexport {\n  ProductTagEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,yBAA0B;AA2D1B,IAAAA,sBAA2C;AA1D3C,IAAI,uBAAuB,EAAE,OAAO;AAAA,EAClC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AACzB,CAAC;AACD,IAAI,qBAAqB,CAAC,EAAE,WAAW,MAAM;AAC3C,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,WAAW;AAAA,IACpB;AAAA,IACA,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,oBAAoB,WAAW,EAAE;AACpE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,CAAC,EAAE,YAAY,MAAM;AAC9B,cAAM;AAAA,UACJA,GAAE,iCAAiC;AAAA,YACjC,OAAO,YAAY;AAAA,UACrB,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,sCAAsC,cAA0B;AAAA,UACjH,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,0BAA0B,EAAE,CAAC;AAAA,oBAC3D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,MAAM,UAAU,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvK,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,aAAa,WAAW,SAAS,MAAM,IAAI,cAAc,EAAE;AACnE,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,yBAAyB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC9H,oBAAAE,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUF,GAAE,2BAA2B,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC;AAAA,IACH,aAAyB,oBAAAE,KAAK,oBAAoB,EAAE,YAAY,YAAY,CAAC;AAAA,EAC/E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}