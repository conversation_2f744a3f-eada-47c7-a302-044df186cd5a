{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-YS65UGPC.mjs"], "sourcesContent": ["import {\n  customerGroupsQueryKeys\n} from \"./chunk-F6PXCY3N.mjs\";\nimport {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/price-lists.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar PRICE_LISTS_QUERY_KEY = \"price-lists\";\nvar priceListsQueryKeys = queryKeysFactory(PRICE_LISTS_QUERY_KEY);\nvar usePriceList = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.priceList.retrieve(id, query),\n    queryKey: priceListsQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar usePriceLists = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.priceList.list(query),\n    queryKey: priceListsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreatePriceList = (query, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.priceList.create(payload, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: customerGroupsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdatePriceList = (id, query, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.priceList.update(id, payload, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: priceListsQueryKeys.details()\n      });\n      queryClient.invalidateQueries({ queryKey: customerGroupsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeletePriceList = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.priceList.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useBatchPriceListPrices = (id, query, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.priceList.batchPrices(id, payload, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: priceListsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar usePriceListLinkProducts = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.priceList.linkProducts(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: priceListsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: priceListsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  priceListsQueryKeys,\n  usePriceList,\n  usePriceLists,\n  useCreatePriceList,\n  useUpdatePriceList,\n  useDeletePriceList,\n  useBatchPriceListPrices,\n  usePriceListLinkProducts\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB,iBAAiB,qBAAqB;AAChE,IAAI,eAAe,CAAC,IAAI,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,UAAU,SAAS,IAAI,KAAK;AAAA,IACrD,UAAU,oBAAoB,OAAO,EAAE;AAAA,IACvC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,gBAAgB,CAAC,OAAO,YAAY;AACtC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,UAAU,KAAK,KAAK;AAAA,IAC7C,UAAU,oBAAoB,KAAK,KAAK;AAAA,IACxC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,qBAAqB,CAAC,OAAO,YAAY;AAC3C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,OAAO,SAAS,KAAK;AAAA,IAClE,WAAW,CAAC,MAAM,WAAW,YAAY;AA1C7C;AA2CM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB,EAAE,UAAU,wBAAwB,IAAI,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,IAAI,OAAO,YAAY;AAC/C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,OAAO,IAAI,SAAS,KAAK;AAAA,IACtE,WAAW,CAAC,MAAM,WAAW,YAAY;AArD7C;AAsDM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,QAAQ;AAAA,MACxC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,wBAAwB,IAAI,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,IAAI,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,UAAU,OAAO,EAAE;AAAA,IAC/C,WAAW,CAAC,MAAM,WAAW,YAAY;AAnE7C;AAoEM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,0BAA0B,CAAC,IAAI,OAAO,YAAY;AACpD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,YAAY,IAAI,SAAS,KAAK;AAAA,IAC3E,WAAW,CAAC,MAAM,WAAW,YAAY;AA7E7C;AA8EM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,kBAAkB,MAAM,EAAE,CAAC;AACrE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,aAAa,IAAI,OAAO;AAAA,IACrE,WAAW,CAAC,MAAM,WAAW,YAAY;AA1F7C;AA2FM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB,EAAE,UAAU,kBAAkB,MAAM,EAAE,CAAC;AACrE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}