{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-organization-MZWNCGEA.mjs"], "sourcesContent": ["import {\n  CategoryCombobox\n} from \"./chunk-RZD5DU5K.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  PRODUCT_DETAIL_FIELDS\n} from \"./chunk-GSDEAUND.mjs\";\nimport {\n  FormExtensionZone,\n  useExtendableForm\n} from \"./chunk-L4KSRFES.mjs\";\nimport \"./chunk-BC3M3N6P.mjs\";\nimport \"./chunk-ONB3JEHR.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-D3YQN7HV.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-XKXNQ2KV.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProduct,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-organization/product-organization.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-organization/components/product-organization-form/product-organization-form.tsx\nimport { Button, toast } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ProductOrganizationSchema = zod.object({\n  type_id: zod.string().nullable(),\n  collection_id: zod.string().nullable(),\n  category_ids: zod.array(zod.string()),\n  tag_ids: zod.array(zod.string())\n});\nvar ProductOrganizationForm = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { getFormConfigs, getFormFields } = useExtension();\n  const configs = getFormConfigs(\"product\", \"organize\");\n  const fields = getFormFields(\"product\", \"organize\");\n  const collections = useComboboxData({\n    queryKey: [\"product_collections\"],\n    queryFn: (params) => sdk.admin.productCollection.list(params),\n    getOptions: (data) => data.collections.map((collection) => ({\n      label: collection.title,\n      value: collection.id\n    }))\n  });\n  const types = useComboboxData({\n    queryKey: [\"product_types\"],\n    queryFn: (params) => sdk.admin.productType.list(params),\n    getOptions: (data) => data.product_types.map((type) => ({\n      label: type.value,\n      value: type.id\n    }))\n  });\n  const tags = useComboboxData({\n    queryKey: [\"product_tags\"],\n    queryFn: (params) => sdk.admin.productTag.list(params),\n    getOptions: (data) => data.product_tags.map((tag) => ({\n      label: tag.value,\n      value: tag.id\n    }))\n  });\n  const form = useExtendableForm({\n    defaultValues: {\n      type_id: product.type_id ?? \"\",\n      collection_id: product.collection_id ?? \"\",\n      category_ids: product.categories?.map((c) => c.id) || [],\n      tag_ids: product.tags?.map((t2) => t2.id) || []\n    },\n    schema: ProductOrganizationSchema,\n    configs,\n    data: product\n  });\n  const { mutateAsync, isPending } = useUpdateProduct(product.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        type_id: data.type_id || null,\n        collection_id: data.collection_id || null,\n        categories: data.category_ids.map((c) => ({ id: c })),\n        tags: data.tag_ids?.map((t2) => ({ id: t2 }))\n      },\n      {\n        onSuccess: ({ product: product2 }) => {\n          toast.success(\n            t(\"products.organization.edit.toasts.success\", {\n              title: product2.title\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"type_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"products.fields.type.label\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: types.options,\n                  searchValue: types.searchValue,\n                  onSearchValueChange: types.onSearchValueChange,\n                  fetchNextPage: types.fetchNextPage\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"collection_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"products.fields.collection.label\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  multiple: false,\n                  options: collections.options,\n                  onSearchValueChange: collections.onSearchValueChange,\n                  searchValue: collections.searchValue\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"category_ids\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"products.fields.categories.label\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(CategoryCombobox, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"tag_ids\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"products.fields.tags.label\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  multiple: true,\n                  options: tags.options,\n                  onSearchValueChange: tags.onSearchValueChange,\n                  searchValue: tags.searchValue\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(FormExtensionZone, { fields, form })\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/products/product-organization/product-organization.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductOrganization = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product, isLoading, isError, error } = useProduct(id, {\n    fields: PRODUCT_DETAIL_FIELDS\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"products.organization.edit.header\") }) }) }),\n    !isLoading && product && /* @__PURE__ */ jsx2(ProductOrganizationForm, { product })\n  ] });\n};\nexport {\n  ProductOrganization as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,yBAA0B;AAyK1B,IAAAA,sBAA2C;AAxK3C,IAAI,4BAAgC,WAAO;AAAA,EACzC,SAAa,WAAO,EAAE,SAAS;AAAA,EAC/B,eAAmB,WAAO,EAAE,SAAS;AAAA,EACrC,cAAkB,UAAU,WAAO,CAAC;AAAA,EACpC,SAAa,UAAU,WAAO,CAAC;AACjC,CAAC;AACD,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AACF,MAAM;AAlGN;AAmGE,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,gBAAgB,cAAc,IAAI,aAAa;AACvD,QAAM,UAAU,eAAe,WAAW,UAAU;AACpD,QAAM,SAAS,cAAc,WAAW,UAAU;AAClD,QAAM,cAAc,gBAAgB;AAAA,IAClC,UAAU,CAAC,qBAAqB;AAAA,IAChC,SAAS,CAAC,WAAW,IAAI,MAAM,kBAAkB,KAAK,MAAM;AAAA,IAC5D,YAAY,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,gBAAgB;AAAA,MAC1D,OAAO,WAAW;AAAA,MAClB,OAAO,WAAW;AAAA,IACpB,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,UAAU,CAAC,eAAe;AAAA,IAC1B,SAAS,CAAC,WAAW,IAAI,MAAM,YAAY,KAAK,MAAM;AAAA,IACtD,YAAY,CAAC,SAAS,KAAK,cAAc,IAAI,CAAC,UAAU;AAAA,MACtD,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,gBAAgB;AAAA,IAC3B,UAAU,CAAC,cAAc;AAAA,IACzB,SAAS,CAAC,WAAW,IAAI,MAAM,WAAW,KAAK,MAAM;AAAA,IACrD,YAAY,CAAC,SAAS,KAAK,aAAa,IAAI,CAAC,SAAS;AAAA,MACpD,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,IACb,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,kBAAkB;AAAA,IAC7B,eAAe;AAAA,MACb,SAAS,QAAQ,WAAW;AAAA,MAC5B,eAAe,QAAQ,iBAAiB;AAAA,MACxC,gBAAc,aAAQ,eAAR,mBAAoB,IAAI,CAAC,MAAM,EAAE,QAAO,CAAC;AAAA,MACvD,WAAS,aAAQ,SAAR,mBAAc,IAAI,CAAC,OAAO,GAAG,QAAO,CAAC;AAAA,IAChD;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,QAAQ,EAAE;AAC9D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AA5IzD,QAAAC;AA6II,UAAM;AAAA,MACJ;AAAA,QACE,SAAS,KAAK,WAAW;AAAA,QACzB,eAAe,KAAK,iBAAiB;AAAA,QACrC,YAAY,KAAK,aAAa,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;AAAA,QACpD,OAAMA,MAAA,KAAK,YAAL,gBAAAA,IAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG;AAAA,MAC5C;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,SAAS,SAAS,MAAM;AACpC,gBAAM;AAAA,YACJ,EAAE,6CAA6C;AAAA,cAC7C,OAAO,SAAS;AAAA,YAClB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,gCAAgC,UAAU;AAAA,UACnH;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAU,EAAE,4BAA4B,EAAE,CAAC;AAAA,kBAC7E,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,MAAM;AAAA,kBACf,aAAa,MAAM;AAAA,kBACnB,qBAAqB,MAAM;AAAA,kBAC3B,eAAe,MAAM;AAAA,gBACvB;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAU,EAAE,kCAAkC,EAAE,CAAC;AAAA,kBACnF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,UAAU;AAAA,kBACV,SAAS,YAAY;AAAA,kBACrB,qBAAqB,YAAY;AAAA,kBACjC,aAAa,YAAY;AAAA,gBAC3B;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAU,EAAE,kCAAkC,EAAE,CAAC;AAAA,kBACnF,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,kBAAkB,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACnF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAU,EAAE,4BAA4B,EAAE,CAAC;AAAA,kBAC7E,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,UAAU;AAAA,kBACV,SAAS,KAAK;AAAA,kBACd,qBAAqB,KAAK;AAAA,kBAC1B,aAAa,KAAK;AAAA,gBACpB;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB,wBAAI,mBAAmB,EAAE,QAAQ,KAAK,CAAC;AAAA,IACzD,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,IAAI;AAAA,IAC5D,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAU,EAAE,mCAAmC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IAChN,CAAC,aAAa,eAA2B,oBAAAA,KAAK,yBAAyB,EAAE,QAAQ,CAAC;AAAA,EACpF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "_a", "jsxs2", "jsx2"]}