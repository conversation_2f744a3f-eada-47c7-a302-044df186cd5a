import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-MSDRGCRR.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var TextCell = ({ text, align = "left", maxWidth = 220 }) => {
  if (!text) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  const stringLength = text.toString().length;
  return (0, import_jsx_runtime.jsx)(ConditionalTooltip, { content: text, showTooltip: stringLength > 20, children: (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx("flex h-full w-full items-center gap-x-3 overflow-hidden", {
        "justify-start text-start": align === "left",
        "justify-center text-center": align === "center",
        "justify-end text-end": align === "right"
      }),
      style: {
        maxWidth
      },
      children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: text })
    }
  ) });
};
var TextHeader = ({ text, align = "left" }) => {
  return (0, import_jsx_runtime.jsx)("div", { className: clx("flex h-full w-full items-center", {
    "justify-start text-start": align === "left",
    "justify-center text-center": align === "center",
    "justify-end text-end": align === "right"
  }), children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: text }) });
};

export {
  TextCell,
  TextHeader
};
//# sourceMappingURL=chunk-7HUCBNCQ.js.map
