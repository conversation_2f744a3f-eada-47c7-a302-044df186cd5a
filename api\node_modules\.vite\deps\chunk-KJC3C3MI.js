import {
  Tooltip
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-OC7BQLYI.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ConditionalTooltip = ({
  children,
  showTooltip = false,
  ...props
}) => {
  if (showTooltip) {
    return (0, import_jsx_runtime.jsx)(Tooltip, { ...props, children });
  }
  return children;
};

export {
  ConditionalTooltip
};
//# sourceMappingURL=chunk-KJC3C3MI.js.map
