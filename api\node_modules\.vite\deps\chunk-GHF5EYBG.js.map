{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-DEQUVHHE.mjs"], "sourcesContent": ["// src/lib/client/client.ts\nimport Medusa from \"@medusajs/js-sdk\";\nvar backendUrl = __BACKEND_URL__ ?? \"/\";\nvar sdk = new Medusa({\n  baseUrl: backendUrl,\n  auth: {\n    type: \"session\"\n  }\n});\nif (typeof window !== \"undefined\") {\n  ;\n  window.__sdk = sdk;\n}\n\nexport {\n  sdk\n};\n"], "mappings": ";;;;;AAEA,IAAI,aAAa,mBAAmB;AACpC,IAAI,MAAM,IAAI,YAAO;AAAA,EACnB,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AACF,CAAC;AACD,IAAI,OAAO,WAAW,aAAa;AACjC;AACA,SAAO,QAAQ;AACjB;", "names": []}