import {
  Photo,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-MNXC6Q4F.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Thumbnail = ({ src, alt, size = "base" }) => {
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx(
        "bg-ui-bg-component border-ui-border-base flex items-center justify-center overflow-hidden rounded border",
        {
          "h-8 w-6": size === "base",
          "h-5 w-4": size === "small"
        }
      ),
      children: src ? (0, import_jsx_runtime.jsx)(
        "img",
        {
          src,
          alt,
          className: "h-full w-full object-cover object-center"
        }
      ) : (0, import_jsx_runtime.jsx)(Photo, { className: "text-ui-fg-subtle" })
    }
  );
};

export {
  Thumbnail
};
//# sourceMappingURL=chunk-XYBN3KRC.js.map
