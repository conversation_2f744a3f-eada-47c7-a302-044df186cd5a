{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-QL4XKIVL.mjs"], "sourcesContent": ["import {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/price-preferences.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar PRICE_PREFERENCES_QUERY_KEY = \"price-preferences\";\nvar pricePreferencesQueryKeys = queryKeysFactory(\n  PRICE_PREFERENCES_QUERY_KEY\n);\nvar usePricePreferences = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.pricePreference.list(query),\n    queryKey: pricePreferencesQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\nexport {\n  pricePreferencesQueryKeys,\n  usePricePreferences\n};\n"], "mappings": ";;;;;;;;;;;AAYA,IAAI,8BAA8B;AAClC,IAAI,4BAA4B;AAAA,EAC9B;AACF;AACA,IAAI,sBAAsB,CAAC,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,gBAAgB,KAAK,KAAK;AAAA,IACnD,UAAU,0BAA0B,KAAK,KAAK;AAAA,IAC9C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;", "names": []}