import {
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NNBHHXXN.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MoneyAmountCell = ({
  currencyCode,
  amount,
  align = "left",
  className
}) => {
  if (typeof amount === "undefined" || amount === null) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  const formatted = getStylizedAmount(amount, currencyCode);
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx(
        "flex h-full w-full items-center overflow-hidden",
        {
          "justify-start text-left": align === "left",
          "justify-end text-right": align === "right"
        },
        className
      ),
      children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: formatted })
    }
  );
};

export {
  MoneyAmountCell
};
//# sourceMappingURL=chunk-NO4BKKGC.js.map
