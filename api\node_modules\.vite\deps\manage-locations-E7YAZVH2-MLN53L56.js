import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import {
  useBatchInventoryItemLocationLevels,
  useInventoryItem
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  Checkbox,
  Heading,
  Text,
  clx,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/manage-locations-E7YAZVH2.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var LocationItem = ({
  selected,
  onSelect,
  location
}) => {
  var _a, _b, _c;
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "flex w-full cursor-pointer gap-x-2 rounded-lg border px-2 py-2",
        {
          "border-ui-border-interactive ": selected
        }
      ),
      onClick: () => onSelect(!selected),
      children: [
        (0, import_jsx_runtime.jsx)("div", { className: "h-5 w-5", children: (0, import_jsx_runtime.jsx)(
          Checkbox,
          {
            onClick: (e) => {
              e.stopPropagation();
              onSelect(!selected);
            },
            checked: selected
          }
        ) }),
        (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col", children: [
          (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: location.name }),
          (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: [
            (_a = location.address) == null ? void 0 : _a.address_1,
            (_b = location.address) == null ? void 0 : _b.city,
            (_c = location.address) == null ? void 0 : _c.country_code
          ].filter((el) => !!el).join(", ") })
        ] })
      ]
    }
  );
};
var EditInventoryItemAttributesSchema = z.object({
  locations: z.array(
    z.object({
      id: z.string(),
      location_id: z.string(),
      selected: z.boolean()
    })
  )
});
var getDefaultValues = (allLocations, existingLevels) => {
  return {
    locations: allLocations.map((location) => ({
      ...location,
      location_id: location.id,
      selected: existingLevels.has(location.id)
    }))
  };
};
var ManageLocationsForm = ({
  item,
  locations
}) => {
  const existingLocationLevels = (0, import_react.useMemo)(
    () => {
      var _a;
      return new Set(((_a = item.location_levels) == null ? void 0 : _a.map((l) => l.location_id)) ?? []);
    },
    item.location_levels
  );
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: getDefaultValues(locations, existingLocationLevels),
    resolver: t(EditInventoryItemAttributesSchema)
  });
  const { fields: locationFields, update: updateField } = useFieldArray({
    control: form.control,
    name: "locations"
  });
  (0, import_react.useEffect)(() => {
    form.setValue(
      "locations",
      getDefaultValues(locations, existingLocationLevels).locations
    );
  }, [existingLocationLevels, locations]);
  const { mutateAsync } = useBatchInventoryItemLocationLevels(item.id);
  const handleSubmit = form.handleSubmit(async ({ locations: locations2 }) => {
    const [selectedLocations, unselectedLocations] = locations2.reduce(
      (acc, location) => {
        if (!location.selected && !existingLocationLevels.has(location.location_id) || location.selected && existingLocationLevels.has(location.location_id)) {
          return acc;
        }
        if (location.selected) {
          acc[0].push(location.location_id);
        } else {
          acc[1].push(location.location_id);
        }
        return acc;
      },
      [[], []]
    );
    if (selectedLocations.length === 0 && unselectedLocations.length === 0) {
      return handleSuccess();
    }
    await mutateAsync(
      {
        create: selectedLocations.map((location_id) => ({
          location_id
        })),
        delete: unselectedLocations
      },
      {
        onSuccess: () => {
          toast.success(t2("inventory.toast.updateLocations"));
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime2.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-4 overflow-auto", children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-2 divide-y rounded-lg border", children: [
            (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 divide-x", children: [
              (0, import_jsx_runtime2.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: t2("fields.title") }),
              (0, import_jsx_runtime2.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: item.title ?? "-" })
            ] }),
            (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 divide-x", children: [
              (0, import_jsx_runtime2.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: t2("fields.sku") }),
              (0, import_jsx_runtime2.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: item.sku })
            ] })
          ] }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
            (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: t2("locations.domain") }),
            (0, import_jsx_runtime2.jsxs)("div", { className: "text-ui-fg-subtle flex w-full justify-between", children: [
              (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: t2("locations.selectLocations") }),
              (0, import_jsx_runtime2.jsxs)(Text, { size: "small", leading: "compact", children: [
                "(",
                t2("general.countOfTotalSelected", {
                  count: locationFields.filter((l) => l.selected).length,
                  total: locations.length
                }),
                ")"
              ] })
            ] })
          ] }),
          locationFields.map((location, idx) => {
            return (0, import_jsx_runtime2.jsx)(
              LocationItem,
              {
                selected: location.selected,
                location,
                onSelect: () => updateField(idx, {
                  ...location,
                  selected: !location.selected
                })
              },
              location.id
            );
          })
        ] }),
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(Button, { type: "submit", size: "small", isLoading: false, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ManageLocationsDrawer = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const {
    inventory_item: inventoryItem,
    isPending: isLoading,
    isError,
    error
  } = useInventoryItem(id);
  const { stock_locations, isLoading: loadingLocations } = useStockLocations();
  const ready = !isLoading && !loadingLocations && inventoryItem && stock_locations;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime3.jsx)(Heading, { children: t2("inventory.manageLocations") }) }),
    ready && (0, import_jsx_runtime3.jsx)(ManageLocationsForm, { item: inventoryItem, locations: stock_locations })
  ] });
};
export {
  ManageLocationsDrawer as Component
};
//# sourceMappingURL=manage-locations-E7YAZVH2-MLN53L56.js.map
