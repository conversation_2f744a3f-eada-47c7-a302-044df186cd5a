{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-CBSCX7RE.mjs"], "sourcesContent": ["import {\n  useDateTableFilters\n} from \"./chunk-W7625H47.mjs\";\n\n// src/hooks/table/filters/use-product-type-table-filters.tsx\nvar useProductTypeTableFilters = () => {\n  const dateFilters = useDateTableFilters();\n  return dateFilters;\n};\n\nexport {\n  useProductTypeTableFilters\n};\n"], "mappings": ";;;;;AAKA,IAAI,6BAA6B,MAAM;AACrC,QAAM,cAAc,oBAAoB;AACxC,SAAO;AACT;", "names": []}