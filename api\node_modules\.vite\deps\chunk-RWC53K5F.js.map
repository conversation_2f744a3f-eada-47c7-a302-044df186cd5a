{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-V2LANK5S.mjs"], "sourcesContent": ["import {\n  pricePreferencesQueryKeys\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/store.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nimport { FetchError } from \"@medusajs/js-sdk\";\nvar STORE_QUERY_KEY = \"store\";\nvar storeQueryKeys = queryKeysFactory(STORE_QUERY_KEY);\nasync function retrieveActiveStore(query) {\n  const response = await sdk.admin.store.list(query);\n  const activeStore = response.stores?.[0];\n  if (!activeStore) {\n    throw new FetchError(\"No active store found\", \"Not Found\", 404);\n  }\n  return { store: activeStore };\n}\nvar useStore = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => retrieveActiveStore(query),\n    queryKey: storeQueryKeys.details(),\n    ...options\n  });\n  return {\n    ...data,\n    ...rest\n  };\n};\nvar useUpdateStore = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.store.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.list()\n      });\n      queryClient.invalidateQueries({\n        queryKey: pricePreferencesQueryKeys.details()\n      });\n      queryClient.invalidateQueries({ queryKey: storeQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  storeQueryKeys,\n  retrieveActiveStore,\n  useStore,\n  useUpdateStore\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmBA,IAAI,kBAAkB;AACtB,IAAI,iBAAiB,iBAAiB,eAAe;AACrD,eAAe,oBAAoB,OAAO;AArB1C;AAsBE,QAAM,WAAW,MAAM,IAAI,MAAM,MAAM,KAAK,KAAK;AACjD,QAAM,eAAc,cAAS,WAAT,mBAAkB;AACtC,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,WAAW,yBAAyB,aAAa,GAAG;AAAA,EAChE;AACA,SAAO,EAAE,OAAO,YAAY;AAC9B;AACA,IAAI,WAAW,CAAC,OAAO,YAAY;AACjC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,oBAAoB,KAAK;AAAA,IACxC,UAAU,eAAe,QAAQ;AAAA,IACjC,GAAG;AAAA,EACL,CAAC;AACD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,IAAI,iBAAiB,CAAC,IAAI,YAAY;AACpC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,OAAO,IAAI,OAAO;AAAA,IAC3D,WAAW,CAAC,MAAM,WAAW,YAAY;AA3C7C;AA4CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,KAAK;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,QAAQ;AAAA,MAC9C,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,eAAe,QAAQ,EAAE,CAAC;AACpE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}