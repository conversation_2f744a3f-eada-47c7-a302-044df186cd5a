import {
  DashboardApp
} from "./chunk-OJUGDQLS.js";
import "./chunk-XQBAAEQQ.js";
import "./chunk-XBF43SLF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-GE5OJZGS.js";
import "./chunk-YM3FRBGU.js";
import "./chunk-7M4ICL3D.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-XGFC5LFP.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-5NX546NL.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-XYBN3KRC.js";
import "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-W5LXSWLX.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/app.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
import displayModule from "virtual:medusa/displays";
import formModule from "virtual:medusa/forms";
import menuItemModule from "virtual:medusa/menu-items";
import routeModule from "virtual:medusa/routes";
import widgetModule from "virtual:medusa/widgets";
var localPlugin = {
  widgetModule,
  routeModule,
  displayModule,
  formModule,
  menuItemModule
};
function App({ plugins = [] }) {
  const app = new DashboardApp({
    plugins: [localPlugin, ...plugins]
  });
  return (0, import_jsx_runtime.jsx)("div", { children: app.render() });
}
var app_default = App;
export {
  app_default as default
};
//# sourceMappingURL=@medusajs_dashboard.js.map
