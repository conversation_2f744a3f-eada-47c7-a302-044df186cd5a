import {
  instance
} from "./chunk-MPXR7HT5.js";

// node_modules/@medusajs/dashboard/dist/chunk-KD3INMVA.mjs
var promotionStatusMap = {
  [
    "ACTIVE"
    /* ACTIVE */
  ]: ["green", instance.t("statuses.active")],
  [
    "INACTIVE"
    /* INACTIVE */
  ]: ["red", instance.t("statuses.inactive")],
  [
    "DRAFT"
    /* DRAFT */
  ]: ["grey", instance.t("statuses.draft")],
  [
    "SCHEDULED"
    /* SCHEDULED */
  ]: [
    "orange",
    `${instance.t("promotions.fields.campaign")} ${instance.t("statuses.scheduled").toLowerCase()}`
  ],
  [
    "EXPIRED"
    /* EXPIRED */
  ]: [
    "red",
    `${instance.t("promotions.fields.campaign")} ${instance.t("statuses.expired").toLowerCase()}`
  ]
};
var getPromotionStatus = (promotion) => {
  const date = /* @__PURE__ */ new Date();
  const campaign = promotion.campaign;
  if (!campaign) {
    return promotionStatusMap[promotion.status.toUpperCase()];
  }
  if (campaign.starts_at && new Date(campaign.starts_at) > date) {
    return promotionStatusMap[
      "SCHEDULED"
      /* SCHEDULED */
    ];
  }
  const campaignBudget = campaign.budget;
  const overBudget = campaignBudget && campaignBudget.used > campaignBudget.limit;
  if (campaign.ends_at && new Date(campaign.ends_at) < date || overBudget) {
    return promotionStatusMap[
      "EXPIRED"
      /* EXPIRED */
    ];
  }
  return promotionStatusMap[promotion.status.toUpperCase()];
};

export {
  getPromotionStatus
};
//# sourceMappingURL=chunk-KFESVZ55.js.map
