"use client"

import { toast } from "@medusajs/ui"
import { isEqual } from "lodash"
import { Loader2 } from "lucide-react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import { addToCart } from "@lib/data/cart"
import {
  useProductInventory,
  useProductVariantSelection,
} from "@lib/hooks/use-product-detail"
import { formatDescription, formatTitle } from "@lib/util/format-description"
import { useCartBubble } from "contexts/cart-bubble-context"
import { useCartPosition } from "hooks/use-cart-position"
import useMediaQuery from "hooks/use-media-query"
import { PAGE_PATH } from "utils/path"
import { useProductStore } from "zustand-store/product-store"
import { useCartStateStore } from "zustand-store/useCartStore"

import { HttpTypes } from "@medusajs/types"
import { TPriceList } from "types/price-list"
import { TStoreProductWithCustomField } from "types/product"
import { T_Promotion } from "types/promotion"
import { T_Wishlist } from "types/wishlist"

import Divider from "@modules/common/components/divider"
import { Layout, LayoutColumn } from "components/Layout"
import { Button } from "components/ui/button"
import Typography from "components/ui/typography"
import { ProductQuantity } from "../product-quantity"

import { convertCurrencyToLocale } from "@lib/util/money"
import { getFirstVariant, hasValidVariants } from "@lib/util/product-helpers"
import OptionSelect from "@modules/products/components/product-actions/option-select"
import { Input } from "components/ui/input"
import SafeHTML from "components/ui/safe-html"
import Image from "next/image"
import ProductPrice from "../product-price"

type ProductActionsProps = {
  product: TStoreProductWithCustomField
  disabled?: boolean
  open?: boolean
  setOpen?: (open: boolean) => void
  promotions?: T_Promotion[]
  wishlist?: T_Wishlist | null
  priceList?: TPriceList | null
  customer?: HttpTypes.StoreCustomer | null
}

export default function ProductActions({
  product,
  disabled,
  open,
  setOpen,
}: ProductActionsProps) {
  const { t, i18n } = useTranslation("product_detail")
  const { push } = useRouter()
  const countryCode = useParams()?.countryCode as string
  const [isAdding, setIsAdding] = useState({
    isBuyNow: false,
    isAddToCart: false,
  })

  const [quantity, setQuantity] = useState(1)
  const [note, setNote] = useState("")

  // Responsive detection
  const isMobile = useMediaQuery("(max-width: 768px)")

  const {
    selectedVariant,
    setSelectedVariant,
    setOptionColor: setOptions,
  } = useProductStore()
  const { fetchCart } = useCartStateStore()

  // Update options function for mobile compatibility
  const updateOptions = (title: string, value: string) => {
    // Find the option by title and set its value
    const option = product.options?.find((opt) => opt.title === title)
    if (option) {
      setOptionValue(option.id, value)
    }
  }
  const { triggerBubble } = useCartBubble()
  const { getCartIconPosition, getElementPosition } = useCartPosition()

  const { options, setOptionValue, optionsAsKeymap } =
    useProductVariantSelection()

  // Check if product has valid variants
  const hasValidProductVariants = hasValidVariants(product)
  const firstVariant = getFirstVariant(product)

  const { isAddable, message } = useProductInventory(
    selectedVariant ?? firstVariant
  )

  // Preload promotions data to reduce wait time
  useEffect(() => {
    // Preload promotions in background
    import("@lib/data/promotions").then(({ getPromotions }) => {
      getPromotions({}).catch(() => {
        // Ignore errors - this is just preloading
      })
    })
  }, [])

  const getInventoryQuantity = (variant: any) => {
    if (!variant?.inventory?.[0]?.location_levels?.[0]) return 0
    return variant.inventory[0].location_levels[0].available_quantity
  }

  const inventoryQuantity = selectedVariant
    ? getInventoryQuantity(selectedVariant)
    : firstVariant
      ? getInventoryQuantity(firstVariant)
      : 0

  const isDisabled =
    !hasValidProductVariants || // Block if no valid variants
    !isAddable ||
    inventoryQuantity < quantity ||
    isAdding.isAddToCart ||
    isAdding.isBuyNow

  const stockStatus = useMemo(() => {
    if (!hasValidProductVariants) {
      return t("product_action.no_variants_available")
    }
    if (!selectedVariant && !firstVariant?.inventory) {
      return t("product_action.no_stock_location")
    }
    return inventoryQuantity > 0
      ? t("product_action.in_stock")
      : t("product_action.out_of_stock")
  }, [
    inventoryQuantity,
    selectedVariant,
    firstVariant,
    hasValidProductVariants,
    t,
  ])

  // Get translated title and description using ##en:##vi: format
  const translatedTitle = formatTitle(product.title, i18n.language)
  const translatedDescription = formatDescription(
    product.description,
    i18n.language
  )

  const handleAddToCart = async (
    isBuyNow: boolean,
    event?: React.MouseEvent
  ) => {
    let buttonPosition: { x: number; y: number } | null = null
    if (event && event.currentTarget) {
      buttonPosition = getElementPosition(event.currentTarget as HTMLElement)
    }

    if (!hasValidProductVariants) {
      toast.error(t("product_action.no_variants_error"))
      return
    }
    const variantToAdd = selectedVariant ?? firstVariant
    if (!variantToAdd) {
      toast.error(t("product_action.no_variant_selected"))
      return
    }
    try {
      setIsAdding({
        ...isAdding,
        [isBuyNow ? "isBuyNow" : "isAddToCart"]: true,
      })

      await addToCart({
        variantId: variantToAdd.id,
        quantity,
        countryCode,
        metadata: {
          note: note,
        },
      })

      // Apply preorder promotion if exists before fetching cart
      const promotionIntent = localStorage.getItem("preorder_promotion_intent")
      const pickTime = localStorage.getItem("preorder_pick_time")

      if (promotionIntent && pickTime) {
        try {
          const { updateCart, applyPromotions } = await import("@lib/data/cart")

          // Update cart metadata and apply promotion in one go
          await updateCart({
            metadata: {
              preorder_promotion_intent: promotionIntent,
              pick_time: pickTime,
            },
          })

          // Apply promotion immediately since cart now has items
          await applyPromotions([promotionIntent])

          // Clear localStorage after successful application
          localStorage.removeItem("preorder_promotion_intent")
          localStorage.removeItem("preorder_pick_time")
        } catch (error) {
          console.error("Error applying preorder promotion:", error)
        }
      }

      // Fetch the cart once at the end to update the UI
      await fetchCart(false)

      toast.success(t("product_action.add_to_cart_success"), {
        description: `${translatedTitle || product.title} ${t("product_action.add_to_cart_success_description")}`,
      })

      // Trigger bubble animation
      if (buttonPosition && !isBuyNow) {
        const cartPosition = getCartIconPosition()

        triggerBubble({
          startX: buttonPosition.x,
          startY: buttonPosition.y,
          endX: cartPosition.x,
          endY: cartPosition.y,
          productImage: product.thumbnail || undefined,
          productTitle: translatedTitle || product.title,
        })
      }

      if (isBuyNow) push(PAGE_PATH.CART)
      if (setOpen && !isBuyNow) setOpen(false)
    } catch (error) {
      console.error("ProductActions handleAddToCart error:", error)
      toast.error(t("product_action.added_to_cart_failed"), {
        description: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setIsAdding({ isBuyNow: false, isAddToCart: false })
    }
  }

  useEffect(() => {
    if (!open) {
      setSelectedVariant(null)
    }
  }, [open])

  // Effect to set the selected variant
  useEffect(() => {
    if (!product.variants || product.variants.length === 0) {
      return
    }

    const tmpVariant = product.variants.find((v) => {
      const variantOptions = optionsAsKeymap(v.options)
      return isEqual(variantOptions, options)
    })

    if (tmpVariant) {
      setSelectedVariant(tmpVariant)
    }
  }, [product.variants, options])

  useEffect(() => {
    if (product?.options) {
      const optionValues = product?.options?.flatMap((option) => option?.values)
      setOptionValue(product.options[0]?.id, optionValues[0]?.value)
    }
  }, [product?.options])

  useEffect(() => {
    if (product.options) {
      const colorOption = product.options.find(
        (option) => option.title.toLowerCase() === "color"
      )
      setOptions(colorOption || null)
    }
  }, [product.options])

  const totalPrice = useMemo(() => {
    const variant = selectedVariant ?? firstVariant
    const amount = variant?.calculated_price?.calculated_amount ?? 0
    return amount * quantity
  }, [selectedVariant, firstVariant, quantity])

  // Desktop view
  return (
    <>
      <Layout>
        <LayoutColumn start={1} end={{ base: 13, md: 7 }}>
          {product.thumbnail ? (
            <Image
              src={product.thumbnail}
              alt={product.title || "Product image"}
              className="h-full max-h-[534px] w-full rounded-md object-cover"
              width={0}
              height={0}
              sizes="100vw"
            />
          ) : (
            <div className="flex h-full max-h-[534px] w-full items-center justify-center rounded-md bg-gray-100">
              <span className="text-gray-400">
                {t("product_action.no_image_available")}
              </span>
            </div>
          )}
        </LayoutColumn>

        <LayoutColumn start={{ base: 1, md: 7 }} end={13}>
          <div className="flex flex-col gap-y-4 lg:gap-y-6">
            <Typography
              variant="h3"
              size="xl"
              className="font-semibold text-gray-750 max-md:pt-5"
            >
              {translatedTitle || product.title}
            </Typography>

            <ProductPrice
              product={product}
              variant={selectedVariant ?? firstVariant ?? undefined}
            />

            {(translatedDescription || product?.description) && (
              <SafeHTML
                html={translatedDescription || product?.description || ""}
                className="font-normal !leading-6 text-gray-700 max-md:pt-5"
              />
            )}

            <Divider />

            {(product.variants?.length ?? 0) > 1 && (
              <div className="flex flex-col gap-y-4">
                {(product.options || []).map((option) => (
                  <div key={option.id}>
                    <OptionSelect
                      option={option}
                      current={"Size " + option.values[0].value}
                      updateOption={setOptionValue}
                      title={option.title ?? ""}
                      data-testid="product-options"
                      disabled={
                        !!disabled || isAdding.isBuyNow || isAdding.isAddToCart
                      }
                    />
                  </div>
                ))}
                <Divider />
              </div>
            )}

            <div className="flex items-center justify-between">
              <ProductQuantity quantity={quantity} setQuantity={setQuantity} />

              <div className="flex items-center gap-x-2">
                <Typography
                  variant="p"
                  size="base"
                  className="font-semibold text-gray-600"
                >
                  {selectedVariant?.inventory_quantity}
                  {selectedVariant ? message : ""}
                </Typography>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Typography
                variant="p"
                size="base18"
                className="font-semibold text-gray-760"
              >
                <span className="font-normal">
                  {t("product_action.subtotal")}:
                </span>{" "}
                {convertCurrencyToLocale({
                  amount: totalPrice,
                  currency_code: null,
                })}
              </Typography>
              <Typography
                variant="p"
                size="base"
                className={`font-semibold ${
                  inventoryQuantity > 0 ? "text-primary-main" : "text-red"
                }`}
              >
                {stockStatus}
              </Typography>
            </div>

            <Input
              placeholder={t("product_action.note")}
              className="h-18 w-full"
              value={note}
              onChange={(e) => setNote(e.target.value)}
            />

            <div className="flex flex-col gap-x-4 gap-y-2 md:flex-row">
              <Button
                onClick={(e) => handleAddToCart(false, e)}
                disabled={isDisabled}
                className="w-full rounded-rounded bg-primary-main text-white max-md:!rounded-xs sm:w-48"
                data-testid="add-product-button"
              >
                {isAdding.isAddToCart ? (
                  <Loader2 className="animate-spin" size={14} />
                ) : (
                  <span className={isAdding.isAddToCart ? "invisible" : ""}>
                    {t("product_action.add_to_cart")}
                  </span>
                )}
              </Button>
            </div>
          </div>
        </LayoutColumn>
      </Layout>
    </>
  )
}
