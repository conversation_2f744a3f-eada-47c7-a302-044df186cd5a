"use client"

import { useTranslation } from "react-i18next"

import { TCartCustomField } from "types/cart"
import { CHECKOUT_STEP } from "utils/constant"

import { But<PERSON> } from "components/ui/button"
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "components/ui/card"

import CartTotals from "@modules/common/components/cart-totals"

type SummaryProps = {
  cart: TCartCustomField
}

export function getCheckoutStep(cart: TCartCustomField) {
  if (!cart?.shipping_address?.address_1 || !cart.email) {
    return CHECKOUT_STEP.ADDRESS
  } else if (cart?.shipping_methods?.length === 0) {
    return CHECKOUT_STEP.SHIPPING
  } else {
    return CHECKOUT_STEP.PAYMENT
  }
}

const Summary = ({ cart }: SummaryProps) => {
  const { t } = useTranslation("cart")
  const { setStep } = useCartStateStore()

  const handleProceedToCheckout = () => {
    // Set step to checkout to trigger CartDialog to show checkout content
    setStep("checkout")

    // Trigger a custom event to open CartDialog
    window.dispatchEvent(
      new CustomEvent("openCartDialog", {
        detail: { step: "checkout" },
      })
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-bold text-primary-main md:text-md">
          {t("your_order")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <CartTotals cart={cart} />
      </CardContent>
      <CardFooter>
        <Button
          className="h-10 w-full"
          onClick={handleProceedToCheckout}
          disabled={!cart?.items?.length}
          data-testid="checkout-button"
        >
          {t("checkout")}
        </Button>
      </CardFooter>
    </Card>
  )
}

export default Summary
