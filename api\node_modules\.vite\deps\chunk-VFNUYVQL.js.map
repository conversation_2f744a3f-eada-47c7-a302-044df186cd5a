{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-ENV6YVOM.mjs"], "sourcesContent": ["import {\n  shippingOptionsQueryKeys\n} from \"./chunk-GRT22PE5.mjs\";\nimport {\n  stockLocationsQueryKeys\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/fulfillment-sets.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar FULFILLMENT_SETS_QUERY_KEY = \"fulfillment_sets\";\nvar fulfillmentSetsQueryKeys = queryKeysFactory(\n  FULFILLMENT_SETS_QUERY_KEY\n);\nvar useDeleteFulfillmentSet = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.fulfillmentSet.delete(id),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentSetsQueryKeys.detail(id)\n      });\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentSetsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      await queryClient.invalidateQueries({\n        queryKey: shippingOptionsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateFulfillmentSetServiceZone = (fulfillmentSetId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.fulfillmentSet.createServiceZone(fulfillmentSetId, payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentSetsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateFulfillmentSetServiceZone = (fulfillmentSetId, serviceZoneId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.fulfillmentSet.updateServiceZone(\n      fulfillmentSetId,\n      serviceZoneId,\n      payload\n    ),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentSetsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteFulfillmentServiceZone = (fulfillmentSetId, serviceZoneId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.fulfillmentSet.deleteServiceZone(\n      fulfillmentSetId,\n      serviceZoneId\n    ),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentSetsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: shippingOptionsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useDeleteFulfillmentSet,\n  useCreateFulfillmentSetServiceZone,\n  useUpdateFulfillmentSetServiceZone,\n  useDeleteFulfillmentServiceZone\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,6BAA6B;AACjC,IAAI,2BAA2B;AAAA,EAC7B;AACF;AACA,IAAI,0BAA0B,CAAC,IAAI,YAAY;AAC7C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,eAAe,OAAO,EAAE;AAAA,IACpD,WAAW,OAAO,MAAM,WAAW,YAAY;AA5BnD;AA6BM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,OAAO,EAAE;AAAA,MAC9C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qCAAqC,CAAC,kBAAkB,YAAY;AACtE,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,eAAe,kBAAkB,kBAAkB,OAAO;AAAA,IAC7F,WAAW,OAAO,MAAM,WAAW,YAAY;AAjDnD;AAkDM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qCAAqC,CAAC,kBAAkB,eAAe,YAAY;AACrF,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,eAAe;AAAA,MAChD;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW,OAAO,MAAM,WAAW,YAAY;AApEnD;AAqEM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kCAAkC,CAAC,kBAAkB,eAAe,YAAY;AAClF,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,eAAe;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW,OAAO,MAAM,WAAW,YAAY;AAtFnD;AAuFM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}