import {
  getApi<PERSON>eyTypeFromPathname
} from "./chunk-R3MT5J4J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateApiKey
} from "./chunk-RX237AWS.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLocation
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Eye,
  EyeSlash,
  Heading,
  Input,
  Prompt,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/api-key-management-create-HWBQGG6O.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ApiKeyCreateSchema = objectType({
  title: stringType().min(1)
});
function getRedactedKey(key) {
  if (!key) {
    return "";
  }
  const firstThree = key.slice(0, 4);
  const lastTwo = key.slice(-2);
  return `${firstThree}${"•".repeat(key.length - 6)}${lastTwo}`;
}
var ApiKeyCreateForm = ({ keyType }) => {
  const [createdKey, setCreatedKey] = (0, import_react.useState)(null);
  const [showRedactedKey, setShowRedactedKey] = (0, import_react.useState)(true);
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      title: ""
    },
    resolver: t(ApiKeyCreateSchema)
  });
  const { mutateAsync, isPending } = useCreateApiKey();
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      // @ts-ignore
      { title: values.title, type: keyType },
      {
        onSuccess: ({ api_key }) => {
          toast.success(t2("apiKeyManagement.create.successToast"));
          switch (keyType) {
            case "publishable":
              handleSuccess(`/settings/publishable-api-keys/${api_key.id}`);
              break;
            case "secret":
              setCreatedKey(api_key);
              break;
          }
        },
        onError: (err) => {
          toast.error(err.message);
        }
      }
    );
  });
  const handleCopyToken = () => {
    if (!createdKey) {
      toast.error(t2("apiKeyManagement.create.copySecretTokenFailure"));
    }
    navigator.clipboard.writeText((createdKey == null ? void 0 : createdKey.token) ?? "");
    toast.success(t2("apiKeyManagement.create.copySecretTokenSuccess"));
  };
  const handleGoToSecretKey = () => {
    if (!createdKey) {
      return;
    }
    handleSuccess(`/settings/secret-api-keys/${createdKey.id}`);
  };
  return (0, import_jsx_runtime.jsxs)(import_react.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
      KeyboundForm,
      {
        className: "flex h-full flex-col overflow-hidden",
        onSubmit: handleSubmit,
        children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
            (0, import_jsx_runtime.jsxs)("div", { children: [
              (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: keyType === "publishable" ? t2("apiKeyManagement.create.createPublishableHeader") : t2("apiKeyManagement.create.createSecretHeader") }) }),
              (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: keyType === "publishable" ? t2("apiKeyManagement.create.createPublishableHint") : t2("apiKeyManagement.create.createSecretHint") }) })
            ] }),
            (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2 gap-4", children: (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "title",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ) })
          ] }) }) }),
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
            (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
          ] }) })
        ]
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Prompt, { variant: "confirmation", open: !!createdKey, children: (0, import_jsx_runtime.jsxs)(Prompt.Content, { className: "w-fit max-w-[42.5%]", children: [
      (0, import_jsx_runtime.jsxs)(Prompt.Header, { children: [
        (0, import_jsx_runtime.jsx)(Prompt.Title, { children: t2("apiKeyManagement.create.secretKeyCreatedHeader") }),
        (0, import_jsx_runtime.jsx)(Prompt.Description, { children: t2("apiKeyManagement.create.secretKeyCreatedHint") })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-3 px-6 py-4", children: [
        (0, import_jsx_runtime.jsxs)("div", { className: "shadow-borders-base bg-ui-bg-component grid h-8 grid-cols-[1fr_32px] items-center overflow-hidden rounded-md", children: [
          (0, import_jsx_runtime.jsx)("div", { className: "flex items-center px-2", children: (0, import_jsx_runtime.jsx)(Text, { family: "mono", size: "small", children: showRedactedKey ? getRedactedKey(createdKey == null ? void 0 : createdKey.token) : createdKey == null ? void 0 : createdKey.token }) }),
          (0, import_jsx_runtime.jsx)(
            "button",
            {
              className: "transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed text-ui-fg-muted active:text-ui-fg-subtle flex size-8 appearance-none items-center justify-center border-l",
              type: "button",
              onClick: () => setShowRedactedKey(!showRedactedKey),
              children: showRedactedKey ? (0, import_jsx_runtime.jsx)(EyeSlash, {}) : (0, import_jsx_runtime.jsx)(Eye, {})
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(
          Button,
          {
            size: "small",
            variant: "secondary",
            type: "button",
            className: "w-full",
            onClick: handleCopyToken,
            children: t2("apiKeyManagement.actions.copy")
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(Prompt.Footer, { className: "border-t py-4", children: (0, import_jsx_runtime.jsx)(Prompt.Action, { onClick: handleGoToSecretKey, children: t2("actions.continue") }) })
    ] }) })
  ] });
};
var ApiKeyManagementCreate = () => {
  const { pathname } = useLocation();
  const keyType = getApiKeyTypeFromPathname(pathname);
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(ApiKeyCreateForm, { keyType }) });
};
export {
  ApiKeyManagementCreate as Component
};
//# sourceMappingURL=api-key-management-create-HWBQGG6O-BE7KPMSR.js.map
