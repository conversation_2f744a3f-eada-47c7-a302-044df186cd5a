{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-QTCZFYFH.mjs"], "sourcesContent": ["import {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/workflow-executions.tsx\nimport { useQuery } from \"@tanstack/react-query\";\nvar WORKFLOW_EXECUTIONS_QUERY_KEY = \"workflow_executions\";\nvar workflowExecutionsQueryKeys = queryKeysFactory(\n  WORKFLOW_EXECUTIONS_QUERY_KEY\n);\nvar useWorkflowExecutions = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.workflowExecution.list(query),\n    queryKey: workflowExecutionsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useWorkflowExecution = (id, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.workflowExecution.retrieve(id),\n    queryKey: workflowExecutionsQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\nexport {\n  workflowExecutionsQueryKeys,\n  useWorkflowExecutions,\n  useWorkflowExecution\n};\n"], "mappings": ";;;;;;;;;;;AASA,IAAI,gCAAgC;AACpC,IAAI,8BAA8B;AAAA,EAChC;AACF;AACA,IAAI,wBAAwB,CAAC,OAAO,YAAY;AAC9C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,kBAAkB,KAAK,KAAK;AAAA,IACrD,UAAU,4BAA4B,KAAK,KAAK;AAAA,IAChD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,uBAAuB,CAAC,IAAI,YAAY;AAC1C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,kBAAkB,SAAS,EAAE;AAAA,IACtD,UAAU,4BAA4B,OAAO,EAAE;AAAA,IAC/C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;", "names": []}