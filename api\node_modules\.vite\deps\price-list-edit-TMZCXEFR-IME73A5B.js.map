{"version": 3, "sources": ["../../@medusajs/dashboard/dist/price-list-edit-TMZCXEFR.mjs"], "sourcesContent": ["import {\n  PriceListStatus,\n  PriceListType\n} from \"./chunk-XUQVQCAO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  usePriceList,\n  useUpdatePriceList\n} from \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/price-lists/price-list-edit/price-list-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/price-lists/price-list-edit/components/price-list-edit-form/edit-price-list-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  Input,\n  RadioGroup,\n  Select,\n  Textarea,\n  toast\n} from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PriceListEditSchema = z.object({\n  status: z.nativeEnum(PriceListStatus),\n  type: z.nativeEnum(PriceListType),\n  title: z.string().min(1),\n  description: z.string().min(1)\n});\nvar PriceListEditForm = ({ priceList }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      type: priceList.type,\n      title: priceList.title,\n      description: priceList.description,\n      status: priceList.status\n    },\n    resolver: zodResolver(PriceListEditSchema)\n  });\n  const { mutateAsync, isPending } = useUpdatePriceList(priceList.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(values, {\n      onSuccess: ({ price_list }) => {\n        toast.success(\n          t(\"priceLists.edit.successToast\", {\n            title: price_list.title\n          })\n        );\n        handleSuccess();\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-6 overflow-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"type\",\n              render: ({ field: { onChange, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsxs(\"div\", { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"priceLists.fields.type.label\") }),\n                    /* @__PURE__ */ jsx(Form.Hint, { children: t(\"priceLists.fields.type.hint\") })\n                  ] }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(RadioGroup, { ...field, onValueChange: onChange, children: [\n                    /* @__PURE__ */ jsx(\n                      RadioGroup.ChoiceBox,\n                      {\n                        value: \"sale\" /* SALE */,\n                        label: t(\"priceLists.fields.type.options.sale.label\"),\n                        description: t(\n                          \"priceLists.fields.type.options.sale.description\"\n                        )\n                      }\n                    ),\n                    /* @__PURE__ */ jsx(\n                      RadioGroup.ChoiceBox,\n                      {\n                        value: \"override\" /* OVERRIDE */,\n                        label: t(\n                          \"priceLists.fields.type.options.override.label\"\n                        ),\n                        description: t(\n                          \"priceLists.fields.type.options.override.description\"\n                        )\n                      }\n                    )\n                  ] }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"title\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"status\",\n                render: ({ field: { onChange, ref, ...field } }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"priceLists.fields.status.label\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: onChange, children: [\n                      /* @__PURE__ */ jsx(Select.Trigger, { ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                      /* @__PURE__ */ jsxs(Select.Content, { children: [\n                        /* @__PURE__ */ jsx(Select.Item, { value: \"active\" /* ACTIVE */, children: t(\"priceLists.fields.status.options.active\") }),\n                        /* @__PURE__ */ jsx(Select.Item, { value: \"draft\" /* DRAFT */, children: t(\"priceLists.fields.status.options.draft\") })\n                      ] })\n                    ] }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"description\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.description\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] })\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { className: \"shrink-0\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/price-lists/price-list-edit/price-list-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PriceListEdit = () => {\n  const { t } = useTranslation2();\n  const { id } = useParams();\n  const { price_list, isLoading, isError, error } = usePriceList(id);\n  const ready = !isLoading && price_list;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"priceLists.edit.header\") }) }),\n    ready && /* @__PURE__ */ jsx2(PriceListEditForm, { priceList: price_list })\n  ] });\n};\nexport {\n  PriceListEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,yBAA0B;AA+I1B,IAAAA,sBAA2C;AA9I3C,IAAI,sBAAsB,EAAE,OAAO;AAAA,EACjC,QAAQ,EAAE,WAAW,eAAe;AAAA,EACpC,MAAM,EAAE,WAAW,aAAa;AAAA,EAChC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC;AAC/B,CAAC;AACD,IAAI,oBAAoB,CAAC,EAAE,UAAU,MAAM;AACzC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,UAAU;AAAA,MAChB,OAAO,UAAU;AAAA,MACjB,aAAa,UAAU;AAAA,MACvB,QAAQ,UAAU;AAAA,IACpB;AAAA,IACA,UAAU,EAAY,mBAAmB;AAAA,EAC3C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,mBAAmB,UAAU,EAAE;AAClE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,CAAC,EAAE,WAAW,MAAM;AAC7B,cAAM;AAAA,UACJA,GAAE,gCAAgC;AAAA,YAChC,OAAO,WAAW;AAAA,UACpB,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,GAAG,MAAM,EAAE,MAAM;AAC7C,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,yBAAK,OAAO,EAAE,UAAU;AAAA,wBACtB,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,wBAC/D,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,6BAA6B,EAAE,CAAC;AAAA,kBAC/E,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,YAAY,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,wBAC5G;AAAA,sBACd,WAAW;AAAA,sBACX;AAAA,wBACE,OAAO;AAAA,wBACP,OAAOA,GAAE,2CAA2C;AAAA,wBACpD,aAAaA;AAAA,0BACX;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,wBACgB;AAAA,sBACd,WAAW;AAAA,sBACX;AAAA,wBACE,OAAO;AAAA,wBACP,OAAOA;AAAA,0BACL;AAAA,wBACF;AAAA,wBACA,aAAaA;AAAA,0BACX;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC,EAAE,CAAC;AAAA,sBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,gBAC1D;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,wBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,KAAK,GAAG,MAAM,EAAE,MAAM;AAClD,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gCAAgC,EAAE,CAAC;AAAA,wBACjE,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,0BACxG,wBAAI,OAAO,SAAS,EAAE,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,0BAC5E,yBAAK,OAAO,SAAS,EAAE,UAAU;AAAA,4BAC/B,wBAAI,OAAO,MAAM,EAAE,OAAO,UAAuB,UAAUA,GAAE,yCAAyC,EAAE,CAAC;AAAA,4BACzG,wBAAI,OAAO,MAAM,EAAE,OAAO,SAAqB,UAAUA,GAAE,wCAAwC,EAAE,CAAC;AAAA,sBACxH,EAAE,CAAC;AAAA,oBACL,EAAE,CAAC,EAAE,CAAC;AAAA,wBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,oBAAoB,EAAE,CAAC;AAAA,wBACrD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,WAAW,YAAY,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cACrJ,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI,aAAa,EAAE;AACjE,QAAM,QAAQ,CAAC,aAAa;AAC5B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC/H,aAAyB,oBAAAE,KAAK,mBAAmB,EAAE,WAAW,WAAW,CAAC;AAAA,EAC5E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}