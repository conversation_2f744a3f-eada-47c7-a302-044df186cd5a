{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-GXXQ33F7.mjs"], "sourcesContent": ["import {\n  useUser\n} from \"./chunk-2ZKVRTBW.mjs\";\n\n// src/components/common/user-link/user-link.tsx\nimport { Avatar, Text } from \"@medusajs/ui\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar UserLink = ({\n  id,\n  first_name,\n  last_name,\n  email,\n  type = \"user\"\n}) => {\n  const name = [first_name, last_name].filter(Boolean).join(\" \");\n  const fallback = name ? name.slice(0, 1) : email.slice(0, 1);\n  const link = type === \"user\" ? `/settings/users/${id}` : `/customers/${id}`;\n  return /* @__PURE__ */ jsxs(\n    Link,\n    {\n      to: link,\n      className: \"flex items-center gap-x-2 w-fit transition-fg hover:text-ui-fg-subtle outline-none focus-visible:shadow-borders-focus rounded-md\",\n      children: [\n        /* @__PURE__ */ jsx(Avatar, { size: \"2xsmall\", fallback: fallback.toUpperCase() }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"regular\", children: name || email })\n      ]\n    }\n  );\n};\nvar By = ({ id }) => {\n  const { user } = useUser(id);\n  if (!user) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(UserLink, { ...user });\n};\n\nexport {\n  UserLink,\n  By\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,yBAA0B;AAC1B,IAAI,WAAW,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AACT,MAAM;AACJ,QAAM,OAAO,CAAC,YAAY,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC7D,QAAM,WAAW,OAAO,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,CAAC;AAC3D,QAAM,OAAO,SAAS,SAAS,mBAAmB,EAAE,KAAK,cAAc,EAAE;AACzE,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,QAAQ,EAAE,MAAM,WAAW,UAAU,SAAS,YAAY,EAAE,CAAC;AAAA,YACjE,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,WAAW,UAAU,QAAQ,MAAM,CAAC;AAAA,MAC7G;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,KAAK,CAAC,EAAE,GAAG,MAAM;AACnB,QAAM,EAAE,KAAK,IAAI,QAAQ,EAAE;AAC3B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,UAAU,EAAE,GAAG,KAAK,CAAC;AAClD;", "names": []}