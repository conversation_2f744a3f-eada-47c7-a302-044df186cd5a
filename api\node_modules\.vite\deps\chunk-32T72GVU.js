import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";

// node_modules/@medusajs/dashboard/dist/chunk-C76H5USB.mjs
function useQueryParams(keys, prefix) {
  const [params] = useSearchParams();
  const result = {};
  keys.forEach((key) => {
    const prefixedKey = prefix ? `${prefix}_${key}` : key;
    const value = params.get(prefixedKey) || void 0;
    result[key] = value;
  });
  return result;
}

export {
  useQueryParams
};
//# sourceMappingURL=chunk-32T72GVU.js.map
