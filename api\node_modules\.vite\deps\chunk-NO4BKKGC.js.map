{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-NNBHHXXN.mjs"], "sourcesContent": ["import {\n  getStylizedAmount\n} from \"./chunk-PDWBYQOW.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\n\n// src/components/table/table-cells/common/money-amount-cell/money-amount-cell.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar MoneyAmountCell = ({\n  currencyCode,\n  amount,\n  align = \"left\",\n  className\n}) => {\n  if (typeof amount === \"undefined\" || amount === null) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  const formatted = getStylizedAmount(amount, currencyCode);\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\n        \"flex h-full w-full items-center overflow-hidden\",\n        {\n          \"justify-start text-left\": align === \"left\",\n          \"justify-end text-right\": align === \"right\"\n        },\n        className\n      ),\n      children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: formatted })\n    }\n  );\n};\n\nexport {\n  MoneyAmountCell\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AASA,yBAAoB;AACpB,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AACF,MAAM;AACJ,MAAI,OAAO,WAAW,eAAe,WAAW,MAAM;AACpD,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,QAAM,YAAY,kBAAkB,QAAQ,YAAY;AACxD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,2BAA2B,UAAU;AAAA,UACrC,0BAA0B,UAAU;AAAA,QACtC;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,UAAU,CAAC;AAAA,IACtF;AAAA,EACF;AACF;", "names": []}