{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PXZ7QYKX.mjs"], "sourcesContent": ["// src/lib/rma.ts\nfunction getReturnableQuantity(item) {\n  const {\n    delivered_quantity,\n    return_received_quantity,\n    return_dismissed_quantity,\n    return_requested_quantity\n  } = item.detail;\n  return delivered_quantity - (return_received_quantity + return_requested_quantity + return_dismissed_quantity);\n}\n\nexport {\n  getReturnableQuantity\n};\n"], "mappings": ";AACA,SAAS,sBAAsB,MAAM;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,KAAK;AACT,SAAO,sBAAsB,2BAA2B,4BAA4B;AACtF;", "names": []}