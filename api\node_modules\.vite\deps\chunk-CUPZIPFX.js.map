{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-774WSTCC.mjs"], "sourcesContent": ["// src/lib/query-key-factory.ts\nvar queryKeysFactory = (globalKey) => {\n  const queryKeyFactory = {\n    all: [globalKey],\n    lists: () => [...queryKeyFactory.all, \"list\"],\n    list: (query) => [...queryKeyFactory.lists(), query ? { query } : void 0].filter(\n      (k) => !!k\n    ),\n    details: () => [...queryKeyFactory.all, \"detail\"],\n    detail: (id, query) => [...queryKeyFactory.details(), id, query ? { query } : void 0].filter(\n      (k) => !!k\n    )\n  };\n  return queryKeyFactory;\n};\n\nexport {\n  queryKeysFactory\n};\n"], "mappings": ";AACA,IAAI,mBAAmB,CAAC,cAAc;AACpC,QAAM,kBAAkB;AAAA,IACtB,KAAK,CAAC,SAAS;AAAA,IACf,OAAO,MAAM,CAAC,GAAG,gBAAgB,KAAK,MAAM;AAAA,IAC5C,MAAM,CAAC,UAAU,CAAC,GAAG,gBAAgB,MAAM,GAAG,QAAQ,EAAE,MAAM,IAAI,MAAM,EAAE;AAAA,MACxE,CAAC,MAAM,CAAC,CAAC;AAAA,IACX;AAAA,IACA,SAAS,MAAM,CAAC,GAAG,gBAAgB,KAAK,QAAQ;AAAA,IAChD,QAAQ,CAAC,IAAI,UAAU,CAAC,GAAG,gBAAgB,QAAQ,GAAG,IAAI,QAAQ,EAAE,MAAM,IAAI,MAAM,EAAE;AAAA,MACpF,CAAC,MAAM,CAAC,CAAC;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;", "names": []}