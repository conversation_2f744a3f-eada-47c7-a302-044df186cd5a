"use client"

import { Check, Copy } from "lucide-react"
import Image from "next/image"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"

import { initiatePaymentSession } from "@lib/data/cart"

import { HttpTypes } from "@medusajs/types"
import ErrorMessage from "@modules/checkout/components/error-message"
import { Button } from "components/ui/button"
import { Card, CardContent, CardHeader } from "components/ui/card"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select"
import Typography from "components/ui/typography"

const Payment = ({
  cart,
  availablePaymentMethods,
}: {
  cart: HttpTypes.StoreCart | null
  availablePaymentMethods: any[]
}) => {
  console.log("🚀 ~ availablePaymentMethods:", availablePaymentMethods)
  const { t } = useTranslation("checkout")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    const initializePayment = async () => {
      if (!cart || (cart.payment_collection?.payment_sessions?.length ?? 0) > 0)
        return

      const timeoutId = setTimeout(async () => {
        setIsLoading(true)
        setError(null)
        try {
          const activeSession = cart.payment_collection?.payment_sessions?.find(
            (s: any) =>
              s.status === "pending" && s.provider_id === "pp_system_default"
          )
          console.log("🚀 ~ timeoutId_1 ~ activeSession:", activeSession)

          if (!activeSession) {
            await initiatePaymentSession(cart, {
              provider_id: "pp_system_default",
            })
              .then((res) => {
                console.log("🚀 ~ res:", res)
              })
              .catch((err) => console.log("🚀 ~ timeoutId ~ err:", err))
            console.log("🚀 ~ timeoutId_2 ~ activeSession:", activeSession)
          }
        } catch (err: any) {
          console.error("Payment session error:", err)
          console.error("Error details:", {
            message: err.message,
            status: err.status,
            response: err.response?.data,
          })

          let errorMessage = "Không thể khởi tạo phương thức thanh toán"

          if (err.message?.includes("Payment sessions are required")) {
            if (cart.total === 0) {
              errorMessage =
                "Không thể tạo payment session cho đơn hàng có tổng tiền = 0"
            } else {
              errorMessage =
                "Payment provider chưa được cấu hình đúng trong region"
            }
          } else if (err.status === 404) {
            errorMessage = "Payment provider 'pp_system' không tồn tại"
          } else if (err.status === 400) {
            errorMessage = "Dữ liệu cart không hợp lệ để tạo payment session"
          }

          setError(errorMessage)
        } finally {
          setIsLoading(false)
        }
      }, 200)

      return () => clearTimeout(timeoutId)
    }

    initializePayment()
  }, [cart?.id]) // Only depend on cart.id to reduce re-runs

  const handleCopy = () => {
    navigator.clipboard.writeText("250487").then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // Early return if no cart - show loading state
  if (!cart) {
    return (
      <Card className="border-none bg-white">
        <CardHeader className="!pb-0">
          <Typography className="text-md font-semibold">
            {t("steps.payment.payment_method")}
          </Typography>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Typography className="text-gray-500">
              Đang tải thông tin thanh toán...
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-none bg-white">
      <CardHeader className="!pb-0">
        <Typography className="text-md font-semibold">
          {t("steps.payment.payment_method")}
        </Typography>
      </CardHeader>
      <CardContent>
        <div className="mt-4 grid grid-cols-1 items-start gap-8 sm:mt-0 md:grid-cols-2">
          <Select>
            <SelectTrigger className="w-[180px]">
              <SelectValue
                placeholder={
                  t("steps.payment.bank_transfer") || "Chuyển khoản ngân hàng"
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="bank_transfer" className="text-primary-main">
                  {t("steps.payment.bank_transfer") || "Chuyển khoản ngân hàng"}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <div className="flex items-start gap-4">
            <Image
              src="/images/efruit-qr-code.png"
              alt="QR Code"
              width={300}
              height={300}
              className="h-auto w-32 rounded-lg"
            />
            <div className="flex flex-col">
              {[
                {
                  label: t("steps.payment.account_name") || "Tên tài khoản",
                  value:
                    t("steps.payment.bank_details.account_name") ||
                    "CÔNG TY TNHH EFRUIT",
                },
                {
                  label: t("steps.payment.bank_name") || "Ngân hàng",
                  value:
                    t("steps.payment.bank_details.bank_name") ||
                    "Ngân hàng TMCP Tiên Phong (TPBank)",
                },
                {
                  label: t("steps.payment.account_number") || "Số tài khoản",
                  value:
                    t("steps.payment.bank_details.account_number") || "250487",
                  copy: true,
                },
                {
                  label:
                    t("steps.payment.transfer_description") ||
                    "Nội dung chuyển khoản",
                  value:
                    t("steps.payment.bank_details.transfer_description") ||
                    "Thanh toán đơn hàng",
                },
              ].map(({ label, value, copy }, index) => (
                <div key={index} className={index !== 0 ? "mt-2" : ""}>
                  <Typography variant="p" className="font-semibold">
                    {label}
                  </Typography>
                  <div className="flex items-center gap-2">
                    <Typography variant="p" size="sm" className="text-gray-700">
                      {value}
                    </Typography>
                    {copy && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={handleCopy}
                        className="transition-all"
                      >
                        {copied ? (
                          <Check size={16} className="text-green-500" />
                        ) : (
                          <Copy size={16} />
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <ErrorMessage error={error} data-testid="payment-error-message" />
      </CardContent>
    </Card>
  )
}

export default Payment
