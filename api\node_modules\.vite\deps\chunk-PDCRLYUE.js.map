{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-7MM5L2ZM.mjs"], "sourcesContent": ["import {\n  useDeleteProductTag\n} from \"./chunk-CLRTJ6DI.mjs\";\n\n// src/routes/product-tags/common/hooks/use-delete-product-tag-action.tsx\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nvar useDeleteProductTagAction = ({\n  productTag\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteProductTag(productTag.id);\n  const handleDelete = async () => {\n    const confirmed = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"productTags.delete.confirmation\", {\n        value: productTag.value\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!confirmed) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"productTags.delete.successToast\", {\n            value: productTag.value\n          })\n        );\n        navigate(\"/settings/product-tags\", {\n          replace: true\n        });\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\nexport {\n  useDeleteProductTagAction\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,oBAAoB,WAAW,EAAE;AACzD,QAAM,eAAe,YAAY;AAC/B,UAAM,YAAY,MAAM,OAAO;AAAA,MAC7B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,mCAAmC;AAAA,QAChD,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,mCAAmC;AAAA,YACnC,OAAO,WAAW;AAAA,UACpB,CAAC;AAAA,QACH;AACA,iBAAS,0BAA0B;AAAA,UACjC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": []}