// node_modules/@medusajs/dashboard/dist/chunk-RPAL6FHW.mjs
var STEP_IN_PROGRESS_STATES = [
  "compensating",
  "invoking"
  /* INVOKING */
];
var STEP_SKIPPED_STATES = [
  "skipped",
  "skipped_failure"
  /* SKIPPED_FAILURE */
];
var STEP_OK_STATES = [
  "done"
  /* DONE */
];
var STEP_ERROR_STATES = [
  "failed",
  "reverted",
  "timeout",
  "dormant"
  /* DORMANT */
];
var STEP_INACTIVE_STATES = [
  "not_started"
  /* NOT_STARTED */
];
var TRANSACTION_ERROR_STATES = [
  "failed",
  "reverted"
  /* REVERTED */
];
var TRANSACTION_IN_PROGRESS_STATES = [
  "invoking",
  "waiting_to_compensate",
  "compensating"
  /* COMPENSATING */
];
var getTransactionStateColor = (state) => {
  let statusColor = "green";
  if (TRANSACTION_ERROR_STATES.includes(state)) {
    statusColor = "red";
  }
  if (TRANSACTION_IN_PROGRESS_STATES.includes(state)) {
    statusColor = "orange";
  }
  return statusColor;
};
var getTransactionState = (t, state) => {
  switch (state) {
    case "done":
      return t("workflowExecutions.state.done");
    case "failed":
      return t("workflowExecutions.state.failed");
    case "reverted":
      return t("workflowExecutions.state.reverted");
    case "invoking":
      return t("workflowExecutions.state.invoking");
    case "waiting_to_compensate":
      return t("workflowExecutions.transaction.state.waitingToCompensate");
    case "compensating":
      return t("workflowExecutions.state.compensating");
    case "not_started":
      return t("workflowExecutions.state.notStarted");
  }
};

export {
  STEP_IN_PROGRESS_STATES,
  STEP_SKIPPED_STATES,
  STEP_OK_STATES,
  STEP_ERROR_STATES,
  STEP_INACTIVE_STATES,
  getTransactionStateColor,
  getTransactionState
};
//# sourceMappingURL=chunk-MFHP2J6Q.js.map
