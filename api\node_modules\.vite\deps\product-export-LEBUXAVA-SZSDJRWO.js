import "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-MMLBNCGY.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-NIDIDWBA.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import {
  DataTableFilter
} from "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useExportProducts
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-export-LEBUXAVA.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ExportFilters = () => {
  const { t } = useTranslation();
  const filters = useProductTableFilters();
  return (0, import_jsx_runtime.jsxs)("div", { children: [
    (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t("products.export.filters.title") }),
    (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("products.export.filters.description") }),
    (0, import_jsx_runtime.jsx)("div", { className: "mt-4", children: (0, import_jsx_runtime.jsx)(DataTableFilter, { filters, readonly: true }) })
  ] });
};
var ProductExport = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t("products.export.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { className: "sr-only", children: t("products.export.description") })
    ] }),
    (0, import_jsx_runtime2.jsx)(ProductExportContent, {})
  ] });
};
var ProductExportContent = () => {
  const { t } = useTranslation();
  const { mutateAsync } = useExportProducts();
  const { handleSuccess } = useRouteModal();
  const handleExportRequest = async () => {
    await mutateAsync(
      {},
      {
        onSuccess: () => {
          toast.info(t("products.export.success.title"), {
            description: t("products.export.success.description")
          });
          handleSuccess();
        },
        onError: (err) => {
          toast.error(err.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime2.jsx)(ExportFilters, {}) }),
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime2.jsx)(Button, { onClick: handleExportRequest, size: "small", children: t("actions.export") })
    ] }) })
  ] });
};
export {
  ProductExport as Component
};
//# sourceMappingURL=product-export-LEBUXAVA-SZSDJRWO.js.map
