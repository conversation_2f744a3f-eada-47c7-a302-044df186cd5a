{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-C5P5PL3E.mjs"], "sourcesContent": ["// src/providers/extension-provider/use-extension.tsx\nimport { useContext } from \"react\";\n\n// src/providers/extension-provider/extension-context.tsx\nimport { createContext } from \"react\";\nvar ExtensionContext = createContext(\n  null\n);\n\n// src/providers/extension-provider/use-extension.tsx\nvar useExtension = () => {\n  const context = useContext(ExtensionContext);\n  if (!context) {\n    throw new Error(\"useExtension must be used within a ExtensionProvider\");\n  }\n  return context;\n};\n\n// src/providers/extension-provider/extension-provider.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ExtensionProvider = ({\n  api,\n  children\n}) => {\n  return /* @__PURE__ */ jsx(ExtensionContext.Provider, { value: api, children });\n};\n\nexport {\n  ExtensionProvider,\n  useExtension\n};\n"], "mappings": ";;;;;;;;;;;AACA,mBAA2B;AAG3B,IAAAA,gBAA8B;AAe9B,yBAAoB;AAdpB,IAAI,uBAAmB;AAAA,EACrB;AACF;AAGA,IAAI,eAAe,MAAM;AACvB,QAAM,cAAU,yBAAW,gBAAgB;AAC3C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACxE;AACA,SAAO;AACT;AAIA,IAAI,oBAAoB,CAAC;AAAA,EACvB;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,wBAAI,iBAAiB,UAAU,EAAE,OAAO,KAAK,SAAS,CAAC;AAChF;", "names": ["import_react"]}