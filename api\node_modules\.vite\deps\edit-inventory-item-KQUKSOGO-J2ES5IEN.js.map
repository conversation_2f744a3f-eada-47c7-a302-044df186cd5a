{"version": 3, "sources": ["../../@medusajs/dashboard/dist/edit-inventory-item-KQUKSOGO.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useInventoryItem,\n  useUpdateInventoryItem\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/inventory/inventory-detail/components/edit-inventory-item/edit-item-drawer.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/edit-inventory-item/components/edit-item-form.tsx\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditInventoryItemSchema = z.object({\n  title: z.string().optional(),\n  sku: z.string().min(1)\n});\nvar getDefaultValues = (item) => {\n  return {\n    title: item.title ?? void 0,\n    sku: item.sku ?? void 0\n  };\n};\nvar EditInventoryItemForm = ({ item }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: getDefaultValues(item),\n    resolver: zodResolver(EditInventoryItemSchema)\n  });\n  const { mutateAsync, isPending: isLoading } = useUpdateInventoryItem(item.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    mutateAsync(values, {\n      onSuccess: () => {\n        toast.success(t(\"inventory.toast.updateItem\"));\n        handleSuccess();\n      },\n      onError: (e) => toast.error(e.message)\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-8 overflow-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"title\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"sku\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.sku\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/inventory/inventory-detail/components/edit-inventory-item/edit-item-drawer.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar InventoryItemEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const {\n    inventory_item: inventoryItem,\n    isPending: isLoading,\n    isError,\n    error\n  } = useInventoryItem(id);\n  const ready = !isLoading && inventoryItem;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"inventory.editItemDetails\") }) }),\n    ready && /* @__PURE__ */ jsx2(EditInventoryItemForm, { item: inventoryItem })\n  ] });\n};\nexport {\n  InventoryItemEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,yBAA0B;AA0E1B,IAAAA,sBAA2C;AAzE3C,IAAI,0BAA0B,EAAE,OAAO;AAAA,EACrC,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;AACvB,CAAC;AACD,IAAI,mBAAmB,CAAC,SAAS;AAC/B,SAAO;AAAA,IACL,OAAO,KAAK,SAAS;AAAA,IACrB,KAAK,KAAK,OAAO;AAAA,EACnB;AACF;AACA,IAAI,wBAAwB,CAAC,EAAE,KAAK,MAAM;AACxC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe,iBAAiB,IAAI;AAAA,IACpC,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI,uBAAuB,KAAK,EAAE;AAC5E,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,gBAAY,QAAQ;AAAA,MAClB,WAAW,MAAM;AACf,cAAM,QAAQA,GAAE,4BAA4B,CAAC;AAC7C,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,MAAM,MAAM,MAAM,EAAE,OAAO;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,sBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,YAAY,EAAE,CAAC;AAAA,sBAC7C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACvG,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,EAAE;AACvB,QAAM,QAAQ,CAAC,aAAa;AAC5B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,2BAA2B,EAAE,CAAC,EAAE,CAAC;AAAA,IAClI,aAAyB,oBAAAE,KAAK,uBAAuB,EAAE,MAAM,cAAc,CAAC;AAAA,EAC9E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}