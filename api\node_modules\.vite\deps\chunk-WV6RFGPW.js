import {
  useCustomerGroupTableColumns
} from "./chunk-RYLAHPUE.js";
import {
  useCustomerGroupTableQuery
} from "./chunk-I2ZOQM4X.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import {
  useCustomerGroupTableFilters
} from "./chunk-XNFM7P3M.js";
import {
  StackedDrawer,
  StackedFocusModal
} from "./chunk-XMQMXYDG.js";
import {
  useCustomerGroups
} from "./chunk-FSXJE4G7.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Button,
  Checkbox,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-AKESWRB7.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 50;
var PREFIX = "cg";
var initRowSelection = (state) => {
  return state.reduce((acc, group) => {
    acc[group.id] = true;
    return acc;
  }, {});
};
var PriceListCustomerGroupRuleForm = ({
  state,
  setState,
  type
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(
    initRowSelection(state)
  );
  const [intermediate, setIntermediate] = (0, import_react.useState)(state);
  (0, import_react.useEffect)(() => {
    setRowSelection(initRowSelection(state));
    setIntermediate(state);
  }, [state]);
  const { searchParams, raw } = useCustomerGroupTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { customer_groups, count, isLoading, isError, error } = useCustomerGroups(
    { ...searchParams, fields: "id,name,customers.id" },
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (value) => {
    const state2 = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state2);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newCustomerGroups = (customer_groups == null ? void 0 : customer_groups.filter((cg) => newIds.includes(cg.id)).map((cg) => ({ id: cg.id, name: cg.name }))) || [];
    const filteredIntermediate = intermediate.filter(
      (cg) => !removedIds.includes(cg.id)
    );
    setIntermediate([...filteredIntermediate, ...newCustomerGroups]);
    setRowSelection(state2);
  };
  const handleSave = () => {
    setState(intermediate);
  };
  const filters = useCustomerGroupTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    data: customer_groups || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const Component = type === "focus" ? StackedFocusModal : StackedDrawer;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(Component.Body, { className: "min-h-0 p-0", children: (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count,
        isLoading,
        filters,
        orderBy: [
          { key: "name", label: t("fields.name") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        layout: "fill",
        pagination: true,
        search: true,
        prefix: PREFIX,
        queryObject: raw
      }
    ) }),
    (0, import_jsx_runtime.jsxs)(Component.Footer, { children: [
      (0, import_jsx_runtime.jsx)(Component.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", type: "button", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { type: "button", size: "small", onClick: handleSave, children: t("actions.save") })
    ] })
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCustomerGroupTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};

export {
  PriceListCustomerGroupRuleForm
};
//# sourceMappingURL=chunk-WV6RFGPW.js.map
