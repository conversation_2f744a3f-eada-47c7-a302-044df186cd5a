{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-XKXNQ2KV.mjs"], "sourcesContent": ["import {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\n\n// src/components/common/file-preview/file-preview.tsx\nimport { ArrowDownTray, Spinner } from \"@medusajs/icons\";\nimport { IconButton, Text } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar FilePreview = ({\n  filename,\n  url,\n  loading,\n  activity,\n  actions,\n  hideThumbnail\n}) => {\n  return /* @__PURE__ */ jsx(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component transition-fg rounded-md px-3 py-2\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-row items-center justify-between gap-2\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-row items-center gap-3\", children: [\n      !hideThumbnail && /* @__PURE__ */ jsx(FileThumbnail, {}),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col justify-center\", children: [\n        /* @__PURE__ */ jsx(\n          Text,\n          {\n            size: \"small\",\n            leading: \"compact\",\n            className: \"truncate max-w-[260px]\",\n            children: filename\n          }\n        ),\n        loading && !!activity && /* @__PURE__ */ jsx(\n          Text,\n          {\n            leading: \"compact\",\n            size: \"xsmall\",\n            className: \"text-ui-fg-interactive\",\n            children: activity\n          }\n        )\n      ] })\n    ] }),\n    loading && /* @__PURE__ */ jsx(Spinner, { className: \"animate-spin\" }),\n    !loading && actions && /* @__PURE__ */ jsx(ActionMenu, { groups: actions }),\n    !loading && url && /* @__PURE__ */ jsx(IconButton, { variant: \"transparent\", asChild: true, children: /* @__PURE__ */ jsx(\"a\", { href: url, download: filename ?? `${Date.now()}`, children: /* @__PURE__ */ jsx(ArrowDownTray, {}) }) })\n  ] }) });\n};\nvar FileThumbnail = () => {\n  return /* @__PURE__ */ jsxs(\n    \"svg\",\n    {\n      width: \"24\",\n      height: \"32\",\n      viewBox: \"0 0 24 32\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [\n        /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M20 31.75H4C1.92893 31.75 0.25 30.0711 0.25 28V4C0.25 1.92893 1.92893 0.25 4 0.25H15.9431C16.9377 0.25 17.8915 0.645088 18.5948 1.34835L22.6516 5.4052C23.3549 6.10847 23.75 7.06229 23.75 8.05685V28C23.75 30.0711 22.0711 31.75 20 31.75Z\",\n            fill: \"url(#paint0_linear_6594_388107)\",\n            stroke: \"url(#paint1_linear_6594_388107)\",\n            \"stroke-width\": \"0.5\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            opacity: \"0.4\",\n            d: \"M17.7857 12.8125V13.5357H10.3393V10.9643H15.9375C16.9569 10.9643 17.7857 11.7931 17.7857 12.8125ZM6.21429 16.9107V15.0893H8.78571V16.9107H6.21429ZM10.3393 16.9107V15.0893H17.7857V16.9107H10.3393ZM15.9375 21.0357H10.3393V18.4643H17.7857V19.1875C17.7857 20.2069 16.9569 21.0357 15.9375 21.0357ZM6.21429 19.1875V18.4643H8.78571V21.0357H8.0625C7.0431 21.0357 6.21429 20.2069 6.21429 19.1875ZM8.0625 10.9643H8.78571V13.5357H6.21429V12.8125C6.21429 11.7931 7.0431 10.9643 8.0625 10.9643Z\",\n            fill: \"url(#paint2_linear_6594_388107)\",\n            stroke: \"url(#paint3_linear_6594_388107)\",\n            \"stroke-width\": \"0.428571\"\n          }\n        ),\n        /* @__PURE__ */ jsxs(\"defs\", { children: [\n          /* @__PURE__ */ jsxs(\n            \"linearGradient\",\n            {\n              id: \"paint0_linear_6594_388107\",\n              x1: \"12\",\n              y1: \"0\",\n              x2: \"12\",\n              y2: \"32\",\n              gradientUnits: \"userSpaceOnUse\",\n              children: [\n                /* @__PURE__ */ jsx(\"stop\", { \"stop-color\": \"#F4F4F5\" }),\n                /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", \"stop-color\": \"#E4E4E7\" })\n              ]\n            }\n          ),\n          /* @__PURE__ */ jsxs(\n            \"linearGradient\",\n            {\n              id: \"paint1_linear_6594_388107\",\n              x1: \"12\",\n              y1: \"0\",\n              x2: \"12\",\n              y2: \"32\",\n              gradientUnits: \"userSpaceOnUse\",\n              children: [\n                /* @__PURE__ */ jsx(\"stop\", { \"stop-color\": \"#E4E4E7\" }),\n                /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", \"stop-color\": \"#D4D4D8\" })\n              ]\n            }\n          ),\n          /* @__PURE__ */ jsxs(\n            \"linearGradient\",\n            {\n              id: \"paint2_linear_6594_388107\",\n              x1: \"12\",\n              y1: \"10.75\",\n              x2: \"12\",\n              y2: \"21.25\",\n              gradientUnits: \"userSpaceOnUse\",\n              children: [\n                /* @__PURE__ */ jsx(\"stop\", { \"stop-color\": \"#52525B\" }),\n                /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", \"stop-color\": \"#A1A1AA\" })\n              ]\n            }\n          ),\n          /* @__PURE__ */ jsxs(\n            \"linearGradient\",\n            {\n              id: \"paint3_linear_6594_388107\",\n              x1: \"12\",\n              y1: \"10.75\",\n              x2: \"12\",\n              y2: \"21.25\",\n              gradientUnits: \"userSpaceOnUse\",\n              children: [\n                /* @__PURE__ */ jsx(\"stop\", { \"stop-color\": \"#18181B\" }),\n                /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", \"stop-color\": \"#52525B\" })\n              ]\n            }\n          )\n        ] })\n      ]\n    }\n  );\n};\n\nexport {\n  FilePreview\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,yBAA0B;AAC1B,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,wBAAI,OAAO,EAAE,WAAW,oFAAoF,cAA0B,yBAAK,OAAO,EAAE,WAAW,oDAAoD,UAAU;AAAA,QAClO,yBAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,MACrF,CAAC,qBAAiC,wBAAI,eAAe,CAAC,CAAC;AAAA,UACvC,yBAAK,OAAO,EAAE,WAAW,gCAAgC,UAAU;AAAA,YACjE;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,WAAW,CAAC,CAAC,gBAA4B;AAAA,UACvC;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,MAAM;AAAA,YACN,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,eAA2B,wBAAI,SAAS,EAAE,WAAW,eAAe,CAAC;AAAA,IACrE,CAAC,WAAW,eAA2B,wBAAI,YAAY,EAAE,QAAQ,QAAQ,CAAC;AAAA,IAC1E,CAAC,WAAW,WAAuB,wBAAI,YAAY,EAAE,SAAS,eAAe,SAAS,MAAM,cAA0B,wBAAI,KAAK,EAAE,MAAM,KAAK,UAAU,YAAY,GAAG,KAAK,IAAI,CAAC,IAAI,cAA0B,wBAAI,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,EAC1O,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,gBAAgB,MAAM;AACxB,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,YACQ;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,GAAG;AAAA,YACH,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,YACgB,yBAAK,QAAQ,EAAE,UAAU;AAAA,cACvB;AAAA,YACd;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,eAAe;AAAA,cACf,UAAU;AAAA,oBACQ,wBAAI,QAAQ,EAAE,cAAc,UAAU,CAAC;AAAA,oBACvC,wBAAI,QAAQ,EAAE,QAAQ,KAAK,cAAc,UAAU,CAAC;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,eAAe;AAAA,cACf,UAAU;AAAA,oBACQ,wBAAI,QAAQ,EAAE,cAAc,UAAU,CAAC;AAAA,oBACvC,wBAAI,QAAQ,EAAE,QAAQ,KAAK,cAAc,UAAU,CAAC;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,eAAe;AAAA,cACf,UAAU;AAAA,oBACQ,wBAAI,QAAQ,EAAE,cAAc,UAAU,CAAC;AAAA,oBACvC,wBAAI,QAAQ,EAAE,QAAQ,KAAK,cAAc,UAAU,CAAC;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,eAAe;AAAA,cACf,UAAU;AAAA,oBACQ,wBAAI,QAAQ,EAAE,cAAc,UAAU,CAAC;AAAA,oBACvC,wBAAI,QAAQ,EAAE,QAAQ,KAAK,cAAc,UAAU,CAAC;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": []}