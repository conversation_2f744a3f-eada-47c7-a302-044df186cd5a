import {
  useQueryParams
} from "./chunk-32T72GVU.js";

// node_modules/@medusajs/dashboard/dist/chunk-XMAWMECC.mjs
var useOrderTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    [
      "offset",
      "q",
      "created_at",
      "updated_at",
      "region_id",
      "sales_channel_id",
      "payment_status",
      "fulfillment_status",
      "order"
    ],
    prefix
  );
  const {
    offset,
    sales_channel_id,
    created_at,
    updated_at,
    fulfillment_status,
    payment_status,
    region_id,
    q,
    order
  } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    sales_channel_id: sales_channel_id == null ? void 0 : sales_channel_id.split(","),
    fulfillment_status: fulfillment_status == null ? void 0 : fulfillment_status.split(","),
    payment_status: payment_status == null ? void 0 : payment_status.split(","),
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    region_id: region_id == null ? void 0 : region_id.split(","),
    order: order ? order : "-display_id",
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};

export {
  useOrderTableQuery
};
//# sourceMappingURL=chunk-MKZD3R7Z.js.map
