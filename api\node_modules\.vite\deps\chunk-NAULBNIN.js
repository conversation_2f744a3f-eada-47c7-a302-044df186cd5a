import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-C5LYZZZ5.mjs
var CURRENCIES_QUERY_KEY = "currencies";
var currenciesQueryKeys = queryKeysFactory(CURRENCIES_QUERY_KEY);
var useCurrencies = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.currency.list(query),
    queryKey: currenciesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};

export {
  useCurrencies
};
//# sourceMappingURL=chunk-NAULBNIN.js.map
