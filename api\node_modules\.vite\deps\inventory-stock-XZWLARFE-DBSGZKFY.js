import {
  INVENTORY_ITEM_IDS_KEY
} from "./chunk-NKUOHIYZ.js";
import {
  DataGridTogglableNumberCell
} from "./chunk-BX5SNYTN.js";
import {
  DataGrid,
  DataGridReadonlyCell,
  createDataGridHelper
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-NV2N3EWM.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useBatchInventoryItemsLocationLevels,
  useInventoryItems
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/inventory-stock-XZWLARFE.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var helper = createDataGridHelper();
var useInventoryStockColumns = (locations = []) => {
  const { t: t2 } = useTranslation();
  return (0, import_react2.useMemo)(
    () => [
      helper.column({
        id: "title",
        name: "Title",
        header: "Title",
        cell: (context) => {
          const item = context.row.original;
          return (0, import_jsx_runtime.jsx)(DataGridReadonlyCell, { context, color: "normal", children: (0, import_jsx_runtime.jsx)("span", { title: item.title || void 0, children: item.title || "-" }) });
        },
        disableHiding: true
      }),
      helper.column({
        id: "sku",
        name: "SKU",
        header: "SKU",
        cell: (context) => {
          const item = context.row.original;
          return (0, import_jsx_runtime.jsx)(DataGridReadonlyCell, { context, color: "normal", children: (0, import_jsx_runtime.jsx)("span", { title: item.sku || void 0, children: item.sku || "-" }) });
        },
        disableHiding: true
      }),
      ...locations.map(
        (location) => helper.column({
          id: `location_${location.id}`,
          name: location.name,
          header: location.name,
          field: (context) => {
            const item = context.row.original;
            return `inventory_items.${item.id}.locations.${location.id}`;
          },
          type: "togglable-number",
          cell: (context) => {
            return (0, import_jsx_runtime.jsx)(
              DataGridTogglableNumberCell,
              {
                context,
                disabledToggleTooltip: t2(
                  "inventory.stock.disabledToggleTooltip"
                )
              }
            );
          }
        })
      )
    ],
    [locations, t2]
  );
};
var LocationQuantitySchema = z.object({
  id: z.string().optional(),
  quantity: z.union([z.number(), z.string()]),
  checked: z.boolean(),
  disabledToggle: z.boolean()
});
var InventoryLocationsSchema = z.record(LocationQuantitySchema);
var InventoryItemSchema = z.object({
  locations: InventoryLocationsSchema
});
var InventoryStockSchema = z.object({
  inventory_items: z.record(InventoryItemSchema)
});
var InventoryStockForm = ({
  items,
  locations
}) => {
  const { t: t2 } = useTranslation();
  const { setCloseOnEscape, handleSuccess } = useRouteModal();
  const initialValues = (0, import_react.useRef)(getDefaultValues(items, locations));
  console.log("initialValues", initialValues.current);
  const form = useForm({
    defaultValues: getDefaultValues(items, locations),
    resolver: t(InventoryStockSchema)
  });
  const columns = useInventoryStockColumns(locations);
  const { mutateAsync, isPending } = useBatchInventoryItemsLocationLevels();
  const onSubmit = form.handleSubmit(async (data) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
    const payload = {
      create: [],
      update: [],
      delete: [],
      force: true
    };
    for (const [inventory_item_id, item] of Object.entries(
      data.inventory_items
    )) {
      for (const [location_id, level] of Object.entries(item.locations)) {
        if (level.id) {
          const wasChecked = (_e = (_d = (_c = (_b = (_a = initialValues.current) == null ? void 0 : _a.inventory_items) == null ? void 0 : _b[inventory_item_id]) == null ? void 0 : _c.locations) == null ? void 0 : _d[location_id]) == null ? void 0 : _e.checked;
          if (wasChecked && !level.checked) {
            payload.delete.push(level.id);
          } else {
            const newQuantity = level.quantity !== "" ? castNumber(level.quantity) : 0;
            const originalQuantity = (_j = (_i = (_h = (_g = (_f = initialValues.current) == null ? void 0 : _f.inventory_items) == null ? void 0 : _g[inventory_item_id]) == null ? void 0 : _h.locations) == null ? void 0 : _i[location_id]) == null ? void 0 : _j.quantity;
            if (newQuantity !== originalQuantity) {
              payload.update.push({
                id: level.id,
                inventory_item_id,
                location_id,
                stocked_quantity: newQuantity
              });
            }
          }
        }
        if (!level.id && level.quantity !== "") {
          payload.create.push({
            inventory_item_id,
            location_id,
            stocked_quantity: castNumber(level.quantity)
          });
        }
      }
    }
    await mutateAsync(payload, {
      onSuccess: () => {
        toast.success(t2("inventory.stock.successToast"));
        handleSuccess();
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(KeyboundForm, { onSubmit, className: "flex size-full flex-col", children: [
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "size-full flex-1 overflow-y-auto", children: (0, import_jsx_runtime2.jsx)(
      DataGrid,
      {
        columns,
        data: items,
        state: form,
        onEditingChange: (editing) => {
          setCloseOnEscape(!editing);
        }
      }
    ) }),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-2", children: [
      (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", type: "button", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime2.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
function getDefaultValues(items, locations) {
  return {
    inventory_items: items.reduce((acc, item) => {
      const locationsMap = locations.reduce((locationAcc, location) => {
        var _a;
        const level = (_a = item.location_levels) == null ? void 0 : _a.find(
          (level2) => level2.location_id === location.id
        );
        locationAcc[location.id] = {
          id: level == null ? void 0 : level.id,
          quantity: typeof (level == null ? void 0 : level.stocked_quantity) === "number" ? level == null ? void 0 : level.stocked_quantity : "",
          checked: !!level,
          disabledToggle: ((level == null ? void 0 : level.incoming_quantity) || 0) > 0 || ((level == null ? void 0 : level.reserved_quantity) || 0) > 0
        };
        return locationAcc;
      }, {});
      acc[item.id] = { locations: locationsMap };
      return acc;
    }, {})
  };
}
var InventoryStock = () => {
  var _a;
  const { t: t2 } = useTranslation();
  const [searchParams] = useSearchParams();
  const inventoryItemIds = ((_a = searchParams.get(INVENTORY_ITEM_IDS_KEY)) == null ? void 0 : _a.split(",")) || void 0;
  const { inventory_items, isPending, isError, error } = useInventoryItems({
    id: inventoryItemIds
  });
  const {
    stock_locations,
    isPending: isPendingStockLocations,
    isError: isErrorStockLocations,
    error: errorStockLocations
  } = useStockLocations({
    limit: 9999,
    fields: "id,name"
  });
  const ready = !isPending && !!inventory_items && !isPendingStockLocations && !!stock_locations;
  if (isError) {
    throw error;
  }
  if (isErrorStockLocations) {
    throw errorStockLocations;
  }
  return (0, import_jsx_runtime3.jsxs)(RouteFocusModal, { children: [
    (0, import_jsx_runtime3.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime3.jsx)("span", { className: "sr-only", children: t2("inventory.stock.title") }) }),
    (0, import_jsx_runtime3.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime3.jsx)("span", { className: "sr-only", children: t2("inventory.stock.description") }) }),
    ready && (0, import_jsx_runtime3.jsx)(
      InventoryStockForm,
      {
        items: inventory_items,
        locations: stock_locations
      }
    )
  ] });
};
export {
  InventoryStock as Component
};
//# sourceMappingURL=inventory-stock-XZWLARFE-DBSGZKFY.js.map
