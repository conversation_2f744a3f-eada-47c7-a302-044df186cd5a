{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LSEYENCI.mjs"], "sourcesContent": ["// src/hooks/table/filters/use-promotion-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar usePromotionTableFilters = () => {\n  const { t } = useTranslation();\n  let filters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\", type: \"date\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\", type: \"date\" }\n  ];\n  return filters;\n};\n\nexport {\n  usePromotionTableFilters\n};\n"], "mappings": ";;;;;AAEA,IAAI,2BAA2B,MAAM;AACnC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,MAAI,UAAU;AAAA,IACZ,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,cAAc,MAAM,OAAO;AAAA,IAChE,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,cAAc,MAAM,OAAO;AAAA,EAClE;AACA,SAAO;AACT;", "names": []}