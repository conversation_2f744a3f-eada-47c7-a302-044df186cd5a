import {
  DateCell
} from "./chunk-OW6OIDUA.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-DT7QVGFJ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var EmailCell = ({ email }) => {
  if (!email) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: email }) });
};
var EmailHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t("fields.email") }) });
};
var NameCell = ({ firstName, lastName }) => {
  if (!firstName && !lastName) {
    return (0, import_jsx_runtime2.jsx)(PlaceholderCell, {});
  }
  const name = [firstName, lastName].filter(Boolean).join(" ");
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: name }) });
};
var NameHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: t("fields.name") }) });
};
var AccountCell = ({ hasAccount }) => {
  const { t } = useTranslation();
  const color = hasAccount ? "green" : "orange";
  const text = hasAccount ? t("customers.fields.registered") : t("customers.fields.guest");
  return (0, import_jsx_runtime3.jsx)(StatusCell, { color, children: text });
};
var AccountHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: t("fields.account") }) });
};
var FirstSeenCell = ({ createdAt }) => {
  if (!createdAt) {
    return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime4.jsx)(DateCell, { date: createdAt });
};
var FirstSeenHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime4.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: t("fields.createdAt") }) });
};
var columnHelper = createColumnHelper();
var useCustomerTableColumns = () => {
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("email", {
        header: () => (0, import_jsx_runtime5.jsx)(EmailHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime5.jsx)(EmailCell, { email: getValue() })
      }),
      columnHelper.display({
        id: "name",
        header: () => (0, import_jsx_runtime5.jsx)(NameHeader, {}),
        cell: ({
          row: {
            original: { first_name, last_name }
          }
        }) => (0, import_jsx_runtime5.jsx)(NameCell, { firstName: first_name, lastName: last_name })
      }),
      columnHelper.accessor("has_account", {
        header: () => (0, import_jsx_runtime5.jsx)(AccountHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime5.jsx)(AccountCell, { hasAccount: getValue() })
      }),
      columnHelper.accessor("created_at", {
        header: () => (0, import_jsx_runtime5.jsx)(FirstSeenHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime5.jsx)(FirstSeenCell, { createdAt: getValue() })
      })
    ],
    []
  );
};

export {
  useCustomerTableColumns
};
//# sourceMappingURL=chunk-AUEWJTBN.js.map
