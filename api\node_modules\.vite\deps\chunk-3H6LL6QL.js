import {
  productsQueryKeys
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-PNU5HPGY.mjs
var SALES_CHANNELS_QUERY_KEY = "sales-channels";
var salesChannelsQueryKeys = queryKeysFactory(SALES_CHANNELS_QUERY_KEY);
var useSalesChannel = (id, options) => {
  const { data, ...rest } = useQuery({
    queryKey: salesChannelsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.salesChannel.retrieve(id),
    ...options
  });
  return { ...data, ...rest };
};
var useSalesChannels = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.salesChannel.list(query),
    queryKey: salesChannelsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateSalesChannel = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.salesChannel.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateSalesChannel = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.salesChannel.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteSalesChannel = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.salesChannel.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteSalesChannelLazy = (options) => {
  return useMutation({
    mutationFn: (id) => sdk.admin.salesChannel.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.detail(variables)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useSalesChannelRemoveProducts = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.salesChannel.batchProducts(id, { remove: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.detail(id)
      });
      for (const product of variables || []) {
        queryClient.invalidateQueries({
          queryKey: productsQueryKeys.detail(product)
        });
      }
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useSalesChannelAddProducts = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.salesChannel.batchProducts(id, { add: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: salesChannelsQueryKeys.detail(id)
      });
      for (const product of variables || []) {
        queryClient.invalidateQueries({
          queryKey: productsQueryKeys.detail(product)
        });
      }
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  salesChannelsQueryKeys,
  useSalesChannel,
  useSalesChannels,
  useCreateSalesChannel,
  useUpdateSalesChannel,
  useDeleteSalesChannel,
  useDeleteSalesChannelLazy,
  useSalesChannelRemoveProducts,
  useSalesChannelAddProducts
};
//# sourceMappingURL=chunk-3H6LL6QL.js.map
