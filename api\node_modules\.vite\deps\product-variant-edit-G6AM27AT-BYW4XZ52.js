import {
  transformNullableFormData,
  transformNullableFormNumber
} from "./chunk-6JFXN7BR.js";
import {
  CountrySelect
} from "./chunk-U63OHBJU.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import {
  optionalInt
} from "./chunk-7LOZU53L.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-XGFC5LFP.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  productVariantQuery<PERSON>eys
} from "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProduct,
  useProductVariant,
  useUpdateProductVariant
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Input,
  Switch,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-variant-edit-G6AM27AT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ProductEditVariantSchema = z.object({
  title: z.string().min(1),
  material: z.string().optional(),
  sku: z.string().optional(),
  ean: z.string().optional(),
  upc: z.string().optional(),
  barcode: z.string().optional(),
  manage_inventory: z.boolean(),
  allow_backorder: z.boolean(),
  weight: optionalInt,
  height: optionalInt,
  width: optionalInt,
  length: optionalInt,
  mid_code: z.string().optional(),
  hs_code: z.string().optional(),
  origin_country: z.string().optional(),
  options: z.record(z.string())
});
var ProductEditVariantForm = ({
  variant,
  product
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const defaultOptions = (_a = product.options) == null ? void 0 : _a.reduce((acc, option) => {
    var _a2;
    const varOpt = (_a2 = variant.options) == null ? void 0 : _a2.find((o) => o.option_id === option.id);
    acc[option.title] = varOpt == null ? void 0 : varOpt.value;
    return acc;
  }, {});
  const form = useForm({
    defaultValues: {
      title: variant.title || "",
      material: variant.material || "",
      sku: variant.sku || "",
      ean: variant.ean || "",
      upc: variant.upc || "",
      barcode: variant.barcode || "",
      manage_inventory: variant.manage_inventory || false,
      allow_backorder: variant.allow_backorder || false,
      weight: variant.weight || "",
      height: variant.height || "",
      width: variant.width || "",
      length: variant.length || "",
      mid_code: variant.mid_code || "",
      hs_code: variant.hs_code || "",
      origin_country: variant.origin_country || "",
      options: defaultOptions
    },
    resolver: t(ProductEditVariantSchema)
  });
  const { mutateAsync, isPending } = useUpdateProductVariant(
    variant.product_id,
    variant.id
  );
  const handleSubmit = form.handleSubmit(async (data) => {
    const {
      title,
      weight,
      height,
      width,
      length,
      allow_backorder,
      manage_inventory,
      options,
      ...optional
    } = data;
    const nullableData = transformNullableFormData(optional);
    await mutateAsync(
      {
        id: variant.id,
        weight: transformNullableFormNumber(weight),
        height: transformNullableFormNumber(height),
        width: transformNullableFormNumber(width),
        length: transformNullableFormNumber(length),
        title,
        allow_backorder,
        manage_inventory,
        options,
        ...nullableData
      },
      {
        onSuccess: () => {
          handleSuccess("../");
          toast.success(t2("products.variant.edit.success"));
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex size-full flex-col gap-y-8 overflow-auto", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "title",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "material",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.material") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (_b = product.options) == null ? void 0 : _b.map((option) => {
              return (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `options.${option.title}`,
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: option.title }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Combobox,
                        {
                          value,
                          onChange: (v) => {
                            onChange(v);
                          },
                          ...field,
                          options: option.values.map((v) => ({
                            label: v.value,
                            value: v.value
                          }))
                        }
                      ) })
                    ] });
                  }
                },
                option.id
              );
            })
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
              (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t2("products.variant.inventory.header") }),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "sku",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.sku") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "ean",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.ean") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "upc",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.upc") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "barcode",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.barcode") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "manage_inventory",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.manageInventoryLabel") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            checked: value,
                            onCheckedChange: (checked) => onChange(!!checked),
                            ...field
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.manageInventoryHint") })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "allow_backorder",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.allowBackordersLabel") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            checked: value,
                            onCheckedChange: (checked) => onChange(!!checked),
                            ...field
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.allowBackordersHint") })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t2("products.attributes") }),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "weight",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.weight") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "width",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.width") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "length",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.length") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "height",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.height") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "mid_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.midCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "hs_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.hsCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "origin_country",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.countryOfOrigin") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ProductVariantEdit = () => {
  const initialData = useLoaderData();
  const { t: t2 } = useTranslation();
  const { id, variant_id } = useParams();
  const [URLSearchParms] = useSearchParams();
  const searchVariantId = URLSearchParms.get("variant_id");
  const { variant, isPending, isError, error } = useProductVariant(
    id,
    variant_id || searchVariantId,
    void 0,
    {
      initialData
    }
  );
  const {
    product,
    isPending: isProductPending,
    isError: isProductError,
    error: productError
  } = useProduct(
    variant == null ? void 0 : variant.product_id,
    {
      fields: "-variants"
    },
    {
      enabled: !!(variant == null ? void 0 : variant.product_id)
    }
  );
  const ready = !isPending && !!variant && !isProductPending && !!product;
  if (isError) {
    throw error;
  }
  if (isProductError) {
    throw productError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("products.variant.edit.header") }) }),
    ready && (0, import_jsx_runtime2.jsx)(ProductEditVariantForm, { variant, product })
  ] });
};
var queryFn = async (id, variantId) => {
  return await sdk.admin.product.retrieveVariant(id, variantId);
};
var editProductVariantQuery = (id, variantId) => ({
  queryKey: productVariantQueryKeys.detail(variantId),
  queryFn: async () => queryFn(id, variantId)
});
var editProductVariantLoader = async ({
  params,
  request
}) => {
  const id = params.id;
  const searchParams = new URL(request.url).searchParams;
  const searchVariantId = searchParams.get("variant_id");
  const variantId = params.variant_id || searchVariantId;
  const query = editProductVariantQuery(id, variantId || searchVariantId);
  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);
};
export {
  ProductVariantEdit as Component,
  editProductVariantLoader as loader
};
//# sourceMappingURL=product-variant-edit-G6AM27AT-BYW4XZ52.js.map
