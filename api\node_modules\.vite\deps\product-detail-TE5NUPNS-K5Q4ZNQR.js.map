{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-detail-TE5NUPNS.mjs"], "sourcesContent": ["import {\n  SidebarLink\n} from \"./chunk-LBIOZZPA.mjs\";\nimport \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  getFormattedCountry\n} from \"./chunk-OIAPXGI2.mjs\";\nimport {\n  PRODUCT_VARIANT_IDS_KEY\n} from \"./chunk-AM2BU2RH.mjs\";\nimport \"./chunk-YOYOJU5D.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useDataTableDateColumns\n} from \"./chunk-4BTG27L5.mjs\";\nimport {\n  DataTable,\n  useDataTableDateFilters\n} from \"./chunk-3IIOXMXN.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  PRODUCT_DETAIL_FIELDS\n} from \"./chunk-GSDEAUND.mjs\";\nimport \"./chunk-L4KSRFES.mjs\";\nimport \"./chunk-BC3M3N6P.mjs\";\nimport \"./chunk-ONB3JEHR.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-D3YQN7HV.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-QQ3CHZKV.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-XKXNQ2KV.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  productsQueryKeys,\n  useDeleteProduct,\n  useDeleteProductOption,\n  useDeleteVariantLazy,\n  useProduct,\n  useProductVariants,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { product } = useProduct(\n    id,\n    {\n      fields: PRODUCT_DETAIL_FIELDS\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id)\n    }\n  );\n  if (!product) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: product.title });\n};\n\n// src/routes/products/product-detail/loader.ts\nvar productDetailQuery = (id) => ({\n  queryKey: productsQueryKeys.detail(id, { fields: PRODUCT_DETAIL_FIELDS }),\n  queryFn: async () => sdk.admin.product.retrieve(id, { fields: PRODUCT_DETAIL_FIELDS })\n});\nvar productLoader = async ({ params }) => {\n  const id = params.id;\n  const query = productDetailQuery(id);\n  const response = await queryClient.ensureQueryData({\n    ...query,\n    staleTime: 9e4\n  });\n  return response;\n};\n\n// src/routes/products/product-detail/product-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/products/product-detail/components/product-attribute-section/product-attribute-section.tsx\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar ProductAttributeSection = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { getDisplays } = useExtension();\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"products.attributes\") }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"attributes\",\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.height\"), value: product.height }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.width\"), value: product.width }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.length\"), value: product.length }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.weight\"), value: product.weight }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.midCode\"), value: product.mid_code }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.hsCode\"), value: product.hs_code }),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"fields.countryOfOrigin\"),\n        value: getFormattedCountry(product.origin_country)\n      }\n    ),\n    getDisplays(\"product\", \"attributes\").map((Component2, i) => {\n      return /* @__PURE__ */ jsx2(Component2, { data: product }, i);\n    })\n  ] });\n};\n\n// src/routes/products/product-detail/components/product-general-section/product-general-section.tsx\nimport { PencilSquare as PencilSquare2, Trash } from \"@medusajs/icons\";\nimport { Container as Container2, Heading as Heading2, StatusBadge, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar productStatusColor = (status) => {\n  switch (status) {\n    case \"draft\":\n      return \"grey\";\n    case \"proposed\":\n      return \"orange\";\n    case \"published\":\n      return \"green\";\n    case \"rejected\":\n      return \"red\";\n    default:\n      return \"grey\";\n  }\n};\nvar ProductGeneralSection = ({\n  product\n}) => {\n  const { t } = useTranslation2();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { getDisplays } = useExtension();\n  const displays = getDisplays(\"product\", \"general\");\n  const { mutateAsync } = useDeleteProduct(product.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"products.deleteWarning\", {\n        title: product.title\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        navigate(\"..\");\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { children: product.title }),\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-4\", children: [\n        /* @__PURE__ */ jsx3(StatusBadge, { color: productStatusColor(product.status), children: t(`products.productStatus.${product.status}`) }),\n        /* @__PURE__ */ jsx3(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t(\"actions.edit\"),\n                    to: \"edit\",\n                    icon: /* @__PURE__ */ jsx3(PencilSquare2, {})\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t(\"actions.delete\"),\n                    onClick: handleDelete,\n                    icon: /* @__PURE__ */ jsx3(Trash, {})\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx3(SectionRow, { title: t(\"fields.description\"), value: product.description }),\n    /* @__PURE__ */ jsx3(SectionRow, { title: t(\"fields.subtitle\"), value: product.subtitle }),\n    /* @__PURE__ */ jsx3(SectionRow, { title: t(\"fields.handle\"), value: `/${product.handle}` }),\n    /* @__PURE__ */ jsx3(\n      SectionRow,\n      {\n        title: t(\"fields.discountable\"),\n        value: product.discountable ? t(\"fields.true\") : t(\"fields.false\")\n      }\n    ),\n    displays.map((Component2, index) => {\n      return /* @__PURE__ */ jsx3(Component2, { data: product }, index);\n    })\n  ] });\n};\n\n// src/routes/products/product-detail/components/product-media-section/product-media-section.tsx\nimport { PencilSquare as PencilSquare3, ThumbnailBadge } from \"@medusajs/icons\";\nimport {\n  Button,\n  Checkbox,\n  CommandBar,\n  Container as Container3,\n  Heading as Heading3,\n  Text,\n  Tooltip,\n  clx,\n  usePrompt as usePrompt2\n} from \"@medusajs/ui\";\nimport { useState } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ProductMediaSection = ({ product }) => {\n  const { t } = useTranslation3();\n  const prompt = usePrompt2();\n  const [selection, setSelection] = useState({});\n  const media = getMedia(product);\n  const handleCheckedChange = (id) => {\n    setSelection((prev) => {\n      if (prev[id]) {\n        const { [id]: _, ...rest } = prev;\n        return rest;\n      } else {\n        return { ...prev, [id]: true };\n      }\n    });\n  };\n  const { mutateAsync } = useUpdateProduct(product.id);\n  const handleDelete = async () => {\n    const ids = Object.keys(selection);\n    const includingThumbnail = ids.some(\n      (id) => media.find((m) => m.id === id)?.isThumbnail\n    );\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: includingThumbnail ? t(\"products.media.deleteWarningWithThumbnail\", {\n        count: ids.length\n      }) : t(\"products.media.deleteWarning\", {\n        count: ids.length\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    const mediaToKeep = product.images.filter((i) => !ids.includes(i.id)).map((i) => ({ url: i.url }));\n    await mutateAsync(\n      {\n        images: mediaToKeep,\n        thumbnail: includingThumbnail ? \"\" : void 0\n      },\n      {\n        onSuccess: () => {\n          setSelection({});\n        }\n      }\n    );\n  };\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Heading3, { level: \"h2\", children: t(\"products.media.label\") }),\n      /* @__PURE__ */ jsx4(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"media?view=edit\",\n                  icon: /* @__PURE__ */ jsx4(PencilSquare3, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    media.length > 0 ? /* @__PURE__ */ jsx4(\"div\", { className: \"grid grid-cols-[repeat(auto-fill,minmax(96px,1fr))] gap-4 px-6 py-4\", children: media.map((i, index) => {\n      const isSelected = selection[i.id];\n      return /* @__PURE__ */ jsxs3(\n        \"div\",\n        {\n          className: \"shadow-elevation-card-rest hover:shadow-elevation-card-hover transition-fg group relative aspect-square size-full cursor-pointer overflow-hidden rounded-[8px]\",\n          children: [\n            /* @__PURE__ */ jsx4(\n              \"div\",\n              {\n                className: clx(\n                  \"transition-fg invisible absolute right-2 top-2 opacity-0 group-hover:visible group-hover:opacity-100\",\n                  {\n                    \"visible opacity-100\": isSelected\n                  }\n                ),\n                children: /* @__PURE__ */ jsx4(\n                  Checkbox,\n                  {\n                    checked: selection[i.id] || false,\n                    onCheckedChange: () => handleCheckedChange(i.id)\n                  }\n                )\n              }\n            ),\n            i.isThumbnail && /* @__PURE__ */ jsx4(\"div\", { className: \"absolute left-2 top-2\", children: /* @__PURE__ */ jsx4(Tooltip, { content: t(\"fields.thumbnail\"), children: /* @__PURE__ */ jsx4(ThumbnailBadge, {}) }) }),\n            /* @__PURE__ */ jsx4(Link, { to: `media`, state: { curr: index }, children: /* @__PURE__ */ jsx4(\n              \"img\",\n              {\n                src: i.url,\n                alt: `${product.title} image`,\n                className: \"size-full object-cover\"\n              }\n            ) })\n          ]\n        },\n        i.id\n      );\n    }) }) : /* @__PURE__ */ jsxs3(\"div\", { className: \"flex flex-col items-center gap-y-4 pb-8 pt-6\", children: [\n      /* @__PURE__ */ jsxs3(\"div\", { className: \"flex flex-col items-center\", children: [\n        /* @__PURE__ */ jsx4(\n          Text,\n          {\n            size: \"small\",\n            leading: \"compact\",\n            weight: \"plus\",\n            className: \"text-ui-fg-subtle\",\n            children: t(\"products.media.emptyState.header\")\n          }\n        ),\n        /* @__PURE__ */ jsx4(Text, { size: \"small\", className: \"text-ui-fg-muted\", children: t(\"products.media.emptyState.description\") })\n      ] }),\n      /* @__PURE__ */ jsx4(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx4(Link, { to: \"media?view=edit\", children: t(\"products.media.emptyState.action\") }) })\n    ] }),\n    /* @__PURE__ */ jsx4(CommandBar, { open: !!Object.keys(selection).length, children: /* @__PURE__ */ jsxs3(CommandBar.Bar, { children: [\n      /* @__PURE__ */ jsx4(CommandBar.Value, { children: t(\"general.countSelected\", {\n        count: Object.keys(selection).length\n      }) }),\n      /* @__PURE__ */ jsx4(CommandBar.Seperator, {}),\n      /* @__PURE__ */ jsx4(\n        CommandBar.Command,\n        {\n          action: handleDelete,\n          label: t(\"actions.delete\"),\n          shortcut: \"d\"\n        }\n      )\n    ] }) })\n  ] });\n};\nvar getMedia = (product) => {\n  const { images = [], thumbnail } = product;\n  const media = images.map((image) => ({\n    id: image.id,\n    url: image.url,\n    isThumbnail: image.url === thumbnail\n  }));\n  if (thumbnail && !media.some((mediaItem) => mediaItem.url === thumbnail)) {\n    media.unshift({\n      id: \"img_thumbnail\",\n      url: thumbnail,\n      isThumbnail: true\n    });\n  }\n  return media;\n};\n\n// src/routes/products/product-detail/components/product-option-section/product-option-section.tsx\nimport { PencilSquare as PencilSquare4, Plus, Trash as Trash2 } from \"@medusajs/icons\";\nimport { Badge, Container as Container4, Heading as Heading4, usePrompt as usePrompt3 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar OptionActions = ({\n  product,\n  option\n}) => {\n  const { t } = useTranslation4();\n  const { mutateAsync } = useDeleteProductOption(product.id, option.id);\n  const prompt = usePrompt3();\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"products.options.deleteWarning\", {\n        title: option.title\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync();\n  };\n  return /* @__PURE__ */ jsx5(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              to: `options/${option.id}/edit`,\n              icon: /* @__PURE__ */ jsx5(PencilSquare4, {})\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              onClick: handleDelete,\n              icon: /* @__PURE__ */ jsx5(Trash2, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar ProductOptionSection = ({\n  product\n}) => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsxs4(Container4, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs4(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx5(Heading4, { level: \"h2\", children: t(\"products.options.header\") }),\n      /* @__PURE__ */ jsx5(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.create\"),\n                  to: \"options/create\",\n                  icon: /* @__PURE__ */ jsx5(Plus, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    product.options?.map((option) => {\n      return /* @__PURE__ */ jsx5(\n        SectionRow,\n        {\n          title: option.title,\n          value: option.values?.map((val) => {\n            return /* @__PURE__ */ jsx5(\n              Badge,\n              {\n                size: \"2xsmall\",\n                className: \"flex min-w-[20px] items-center justify-center\",\n                children: val.value\n              },\n              val.value\n            );\n          }),\n          actions: /* @__PURE__ */ jsx5(OptionActions, { product, option })\n        },\n        option.id\n      );\n    })\n  ] });\n};\n\n// src/routes/products/product-detail/components/product-organization-section/product-organization-section.tsx\nimport { PencilSquare as PencilSquare5 } from \"@medusajs/icons\";\nimport { Badge as Badge2, Container as Container5, Heading as Heading5, Tooltip as Tooltip2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation5 } from \"react-i18next\";\nimport { Link as Link2 } from \"react-router-dom\";\nimport { jsx as jsx6, jsxs as jsxs5 } from \"react/jsx-runtime\";\nvar ProductOrganizationSection = ({\n  product\n}) => {\n  const { t } = useTranslation5();\n  const { getDisplays } = useExtension();\n  return /* @__PURE__ */ jsxs5(Container5, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs5(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx6(Heading5, { level: \"h2\", children: t(\"products.organization.header\") }),\n      /* @__PURE__ */ jsx6(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"organization\",\n                  icon: /* @__PURE__ */ jsx6(PencilSquare5, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx6(\n      SectionRow,\n      {\n        title: t(\"fields.tags\"),\n        value: product.tags?.length ? product.tags.map((tag) => /* @__PURE__ */ jsx6(\n          OrganizationTag,\n          {\n            label: tag.value,\n            to: `/settings/product-tags/${tag.id}`\n          },\n          tag.id\n        )) : void 0\n      }\n    ),\n    /* @__PURE__ */ jsx6(\n      SectionRow,\n      {\n        title: t(\"fields.type\"),\n        value: product.type ? /* @__PURE__ */ jsx6(\n          OrganizationTag,\n          {\n            label: product.type.value,\n            to: `/settings/product-types/${product.type_id}`\n          }\n        ) : void 0\n      }\n    ),\n    /* @__PURE__ */ jsx6(\n      SectionRow,\n      {\n        title: t(\"fields.collection\"),\n        value: product.collection ? /* @__PURE__ */ jsx6(\n          OrganizationTag,\n          {\n            label: product.collection.title,\n            to: `/collections/${product.collection.id}`\n          }\n        ) : void 0\n      }\n    ),\n    /* @__PURE__ */ jsx6(\n      SectionRow,\n      {\n        title: t(\"fields.categories\"),\n        value: product.categories?.length ? product.categories.map((pcat) => /* @__PURE__ */ jsx6(\n          OrganizationTag,\n          {\n            label: pcat.name,\n            to: `/categories/${pcat.id}`\n          },\n          pcat.id\n        )) : void 0\n      }\n    ),\n    getDisplays(\"product\", \"organize\").map((Component2, i) => {\n      return /* @__PURE__ */ jsx6(Component2, { data: product }, i);\n    })\n  ] });\n};\nvar OrganizationTag = ({ label, to }) => {\n  return /* @__PURE__ */ jsx6(Tooltip2, { content: label, children: /* @__PURE__ */ jsx6(Badge2, { size: \"2xsmall\", className: \"block w-fit truncate\", asChild: true, children: /* @__PURE__ */ jsx6(Link2, { to, children: label }) }) });\n};\n\n// src/routes/products/product-detail/components/product-sales-channel-section/product-sales-channel-section.tsx\nimport { Channels, PencilSquare as PencilSquare6 } from \"@medusajs/icons\";\nimport { Container as Container6, Heading as Heading6, Text as Text2, Tooltip as Tooltip3 } from \"@medusajs/ui\";\nimport { Trans, useTranslation as useTranslation6 } from \"react-i18next\";\nimport { jsx as jsx7, jsxs as jsxs6 } from \"react/jsx-runtime\";\nvar ProductSalesChannelSection = ({\n  product\n}) => {\n  const { count } = useSalesChannels();\n  const { t } = useTranslation6();\n  const availableInSalesChannels = product.sales_channels?.map((sc) => ({\n    id: sc.id,\n    name: sc.name\n  })) ?? [];\n  const firstChannels = availableInSalesChannels.slice(0, 3);\n  const restChannels = availableInSalesChannels.slice(3);\n  return /* @__PURE__ */ jsxs6(Container6, { className: \"flex flex-col gap-y-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs6(\"div\", { className: \"flex items-center justify-between\", children: [\n      /* @__PURE__ */ jsx7(Heading6, { level: \"h2\", children: t(\"fields.sales_channels\") }),\n      /* @__PURE__ */ jsx7(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"sales-channels\",\n                  icon: /* @__PURE__ */ jsx7(PencilSquare6, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs6(\"div\", { className: \"grid grid-cols-[28px_1fr] items-center gap-x-3\", children: [\n      /* @__PURE__ */ jsx7(\"div\", { className: \"bg-ui-bg-base shadow-borders-base flex size-7 items-center justify-center rounded-md\", children: /* @__PURE__ */ jsx7(\"div\", { className: \"bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]\", children: /* @__PURE__ */ jsx7(Channels, { className: \"text-ui-fg-subtle\" }) }) }),\n      availableInSalesChannels.length > 0 ? /* @__PURE__ */ jsxs6(\"div\", { className: \"flex items-center gap-x-1\", children: [\n        /* @__PURE__ */ jsx7(Text2, { size: \"small\", leading: \"compact\", children: firstChannels.map((sc) => sc.name).join(\", \") }),\n        restChannels.length > 0 && /* @__PURE__ */ jsx7(\n          Tooltip3,\n          {\n            content: /* @__PURE__ */ jsx7(\"ul\", { children: restChannels.map((sc) => /* @__PURE__ */ jsx7(\"li\", { children: sc.name }, sc.id)) }),\n            children: /* @__PURE__ */ jsx7(\n              Text2,\n              {\n                size: \"small\",\n                leading: \"compact\",\n                className: \"text-ui-fg-subtle\",\n                children: `+${restChannels.length}`\n              }\n            )\n          }\n        )\n      ] }) : /* @__PURE__ */ jsx7(Text2, { size: \"small\", leading: \"compact\", className: \"text-ui-fg-subtle\", children: t(\"products.noSalesChannels\") })\n    ] }),\n    /* @__PURE__ */ jsx7(\"div\", { children: /* @__PURE__ */ jsx7(Text2, { className: \"text-ui-fg-subtle\", size: \"small\", leading: \"compact\", children: /* @__PURE__ */ jsx7(\n      Trans,\n      {\n        i18nKey: \"sales_channels.availableIn\",\n        values: {\n          x: availableInSalesChannels.length,\n          y: count ?? 0\n        },\n        components: [\n          /* @__PURE__ */ jsx7(\n            \"span\",\n            {\n              className: \"text-ui-fg-base txt-compact-medium-plus\"\n            },\n            \"x\"\n          ),\n          /* @__PURE__ */ jsx7(\n            \"span\",\n            {\n              className: \"text-ui-fg-base txt-compact-medium-plus\"\n            },\n            \"y\"\n          )\n        ]\n      }\n    ) }) })\n  ] });\n};\n\n// src/routes/products/product-detail/components/product-variant-section/product-variant-section.tsx\nimport { Buildings, Component, PencilSquare as PencilSquare7, Trash as Trash3 } from \"@medusajs/icons\";\nimport {\n  Badge as Badge3,\n  clx as clx2,\n  Container as Container7,\n  createDataTableColumnHelper,\n  createDataTableCommandHelper,\n  createDataTableFilterHelper,\n  Tooltip as Tooltip4,\n  usePrompt as usePrompt4\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useCallback, useMemo } from \"react\";\nimport { useTranslation as useTranslation7 } from \"react-i18next\";\nimport { useNavigate as useNavigate2, useSearchParams } from \"react-router-dom\";\nimport { jsx as jsx8, jsxs as jsxs7 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar PREFIX = \"pv\";\nvar ProductVariantSection = ({\n  product\n}) => {\n  const { t } = useTranslation7();\n  const {\n    q,\n    order,\n    offset,\n    allow_backorder,\n    manage_inventory,\n    created_at,\n    updated_at\n  } = useQueryParams(\n    [\n      \"q\",\n      \"order\",\n      \"offset\",\n      \"manage_inventory\",\n      \"allow_backorder\",\n      \"created_at\",\n      \"updated_at\"\n    ],\n    PREFIX\n  );\n  const columns = useColumns(product);\n  const filters = useFilters();\n  const commands = useCommands();\n  const { variants, count, isPending, isError, error } = useProductVariants(\n    product.id,\n    {\n      q,\n      order: order ? order : \"variant_rank\",\n      offset: offset ? parseInt(offset) : void 0,\n      limit: PAGE_SIZE,\n      allow_backorder: allow_backorder ? JSON.parse(allow_backorder) : void 0,\n      manage_inventory: manage_inventory ? JSON.parse(manage_inventory) : void 0,\n      created_at: created_at ? JSON.parse(created_at) : void 0,\n      updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n      fields: \"title,sku,*options,created_at,updated_at,*inventory_items.inventory.location_levels,inventory_quantity,manage_inventory\"\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx8(Container7, { className: \"divide-y p-0\", children: /* @__PURE__ */ jsx8(\n    DataTable,\n    {\n      data: variants,\n      columns,\n      filters,\n      rowCount: count,\n      getRowId: (row) => row.id,\n      rowHref: (row) => `/products/${product.id}/variants/${row.id}`,\n      pageSize: PAGE_SIZE,\n      isLoading: isPending,\n      heading: t(\"products.variants.header\"),\n      emptyState: {\n        empty: {\n          heading: t(\"products.variants.empty.heading\"),\n          description: t(\"products.variants.empty.description\")\n        },\n        filtered: {\n          heading: t(\"products.variants.filtered.heading\"),\n          description: t(\"products.variants.filtered.description\")\n        }\n      },\n      action: {\n        label: t(\"actions.create\"),\n        to: `variants/create`\n      },\n      actionMenu: {\n        groups: [\n          {\n            actions: [\n              {\n                label: t(\"products.editPrices\"),\n                to: `prices`,\n                icon: /* @__PURE__ */ jsx8(PencilSquare7, {})\n              },\n              {\n                label: t(\"inventory.stock.action\"),\n                to: `stock`,\n                icon: /* @__PURE__ */ jsx8(Buildings, {})\n              }\n            ]\n          }\n        ]\n      },\n      commands,\n      prefix: PREFIX\n    }\n  ) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = (product) => {\n  const { t } = useTranslation7();\n  const navigate = useNavigate2();\n  const { mutateAsync } = useDeleteVariantLazy(product.id);\n  const prompt = usePrompt4();\n  const [searchParams] = useSearchParams();\n  const tableSearchParams = useMemo(() => {\n    const filtered = new URLSearchParams();\n    for (const [key, value] of searchParams.entries()) {\n      if (key.startsWith(`${PREFIX}_`)) {\n        filtered.append(key, value);\n      }\n    }\n    return filtered;\n  }, [searchParams]);\n  const dateColumns = useDataTableDateColumns();\n  const handleDelete = useCallback(\n    async (id, title) => {\n      const res = await prompt({\n        title: t(\"general.areYouSure\"),\n        description: t(\"products.deleteVariantWarning\", {\n          title\n        }),\n        confirmText: t(\"actions.delete\"),\n        cancelText: t(\"actions.cancel\")\n      });\n      if (!res) {\n        return;\n      }\n      await mutateAsync({ variantId: id });\n    },\n    [mutateAsync, prompt, t]\n  );\n  const optionColumns = useMemo(() => {\n    if (!product?.options) {\n      return [];\n    }\n    return product.options.map((option) => {\n      return columnHelper.display({\n        id: option.id,\n        header: option.title,\n        cell: ({ row }) => {\n          const variantOpt = row.original.options?.find(\n            (opt) => opt.option_id === option.id\n          );\n          if (!variantOpt) {\n            return /* @__PURE__ */ jsx8(\"span\", { className: \"text-ui-fg-muted\", children: \"-\" });\n          }\n          return /* @__PURE__ */ jsx8(\"div\", { className: \"flex items-center\", children: /* @__PURE__ */ jsx8(Tooltip4, { content: variantOpt.value, children: /* @__PURE__ */ jsx8(\n            Badge3,\n            {\n              size: \"2xsmall\",\n              title: variantOpt.value,\n              className: \"inline-flex min-w-[20px] max-w-[140px] items-center justify-center overflow-hidden truncate\",\n              children: variantOpt.value\n            }\n          ) }) });\n        }\n      });\n    });\n  }, [product]);\n  const getActions = useCallback(\n    (ctx) => {\n      const variant = ctx.row.original;\n      const mainActions = [\n        {\n          icon: /* @__PURE__ */ jsx8(PencilSquare7, {}),\n          label: t(\"actions.edit\"),\n          onClick: (row) => {\n            navigate(\n              `edit-variant?variant_id=${row.row.original.id}&${tableSearchParams.toString()}`,\n              {\n                state: {\n                  restore_params: tableSearchParams.toString()\n                }\n              }\n            );\n          }\n        }\n      ];\n      const secondaryActions = [\n        {\n          icon: /* @__PURE__ */ jsx8(Trash3, {}),\n          label: t(\"actions.delete\"),\n          onClick: () => handleDelete(variant.id, variant.title)\n        }\n      ];\n      const inventoryItemsCount = variant.inventory_items?.length || 0;\n      switch (inventoryItemsCount) {\n        case 0:\n          break;\n        case 1: {\n          const inventoryItemLink = `/inventory/${variant.inventory_items[0].inventory.id}`;\n          mainActions.push({\n            label: t(\"products.variant.inventory.actions.inventoryItems\"),\n            onClick: () => {\n              navigate(inventoryItemLink);\n            },\n            icon: /* @__PURE__ */ jsx8(Buildings, {})\n          });\n          break;\n        }\n        default: {\n          const ids = variant.inventory_items?.map((i) => i.inventory?.id);\n          if (!ids || ids.length === 0) {\n            break;\n          }\n          const inventoryKitLink = `/inventory?${new URLSearchParams({\n            id: ids.join(\",\")\n          }).toString()}`;\n          mainActions.push({\n            label: t(\"products.variant.inventory.actions.inventoryKit\"),\n            onClick: () => {\n              navigate(inventoryKitLink);\n            },\n            icon: /* @__PURE__ */ jsx8(Component, {})\n          });\n        }\n      }\n      return [mainActions, secondaryActions];\n    },\n    [handleDelete, navigate, t, tableSearchParams]\n  );\n  const getInventory = useCallback(\n    (variant) => {\n      const castVariant = variant;\n      if (!variant.manage_inventory) {\n        return {\n          text: t(\"products.variant.inventory.notManaged\"),\n          hasInventoryKit: false,\n          notManaged: true\n        };\n      }\n      const quantity = variant.inventory_quantity;\n      const inventoryItems = castVariant.inventory_items?.map((i) => i.inventory).filter(Boolean);\n      const hasInventoryKit = inventoryItems.length > 1;\n      const locations = {};\n      inventoryItems.forEach((i) => {\n        i.location_levels?.forEach((l) => {\n          locations[l.id] = true;\n        });\n      });\n      const locationCount = Object.keys(locations).length;\n      const text = hasInventoryKit ? t(\"products.variant.tableItemAvailable\", {\n        availableCount: quantity\n      }) : t(\"products.variant.tableItem\", {\n        availableCount: quantity,\n        locationCount,\n        count: locationCount\n      });\n      return { text, hasInventoryKit, quantity, notManaged: false };\n    },\n    [t]\n  );\n  return useMemo(() => {\n    return [\n      columnHelper.accessor(\"title\", {\n        header: t(\"fields.title\"),\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      columnHelper.accessor(\"sku\", {\n        header: t(\"fields.sku\"),\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      ...optionColumns,\n      columnHelper.display({\n        id: \"inventory\",\n        header: t(\"fields.inventory\"),\n        cell: ({ row }) => {\n          const { text, hasInventoryKit, quantity, notManaged } = getInventory(\n            row.original\n          );\n          return /* @__PURE__ */ jsx8(Tooltip4, { content: text, children: /* @__PURE__ */ jsxs7(\"div\", { className: \"flex h-full w-full items-center gap-2 overflow-hidden\", children: [\n            hasInventoryKit && /* @__PURE__ */ jsx8(Component, {}),\n            /* @__PURE__ */ jsx8(\n              \"span\",\n              {\n                className: clx2(\"truncate\", {\n                  \"text-ui-fg-error\": !quantity && !notManaged\n                }),\n                children: text\n              }\n            )\n          ] }) });\n        },\n        maxSize: 250\n      }),\n      ...dateColumns,\n      columnHelper.action({\n        actions: getActions\n      })\n    ];\n  }, [t, optionColumns, dateColumns, getActions, getInventory]);\n};\nvar filterHelper = createDataTableFilterHelper();\nvar useFilters = () => {\n  const { t } = useTranslation7();\n  const dateFilters = useDataTableDateFilters();\n  return useMemo(() => {\n    return [\n      filterHelper.accessor(\"allow_backorder\", {\n        type: \"radio\",\n        label: t(\"fields.allowBackorder\"),\n        options: [\n          { label: t(\"filters.radio.yes\"), value: \"true\" },\n          { label: t(\"filters.radio.no\"), value: \"false\" }\n        ]\n      }),\n      filterHelper.accessor(\"manage_inventory\", {\n        type: \"radio\",\n        label: t(\"fields.manageInventory\"),\n        options: [\n          { label: t(\"filters.radio.yes\"), value: \"true\" },\n          { label: t(\"filters.radio.no\"), value: \"false\" }\n        ]\n      }),\n      ...dateFilters\n    ];\n  }, [t, dateFilters]);\n};\nvar commandHelper = createDataTableCommandHelper();\nvar useCommands = () => {\n  const { t } = useTranslation7();\n  const navigate = useNavigate2();\n  return [\n    commandHelper.command({\n      label: t(\"inventory.stock.action\"),\n      shortcut: \"i\",\n      action: async (selection) => {\n        navigate(\n          `stock?${PRODUCT_VARIANT_IDS_KEY}=${Object.keys(selection).join(\",\")}`\n        );\n      }\n    })\n  ];\n};\n\n// src/routes/products/product-detail/components/product-shipping-profile-section/product-shipping-profile-section.tsx\nimport { PencilSquare as PencilSquare8, ShoppingBag } from \"@medusajs/icons\";\nimport { Container as Container8, Heading as Heading7 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation8 } from \"react-i18next\";\nimport { jsx as jsx9, jsxs as jsxs8 } from \"react/jsx-runtime\";\nvar ProductShippingProfileSection = ({\n  product\n}) => {\n  const { t } = useTranslation8();\n  const shippingProfile = product.shipping_profile;\n  return /* @__PURE__ */ jsxs8(Container8, { className: \"p-0\", children: [\n    /* @__PURE__ */ jsxs8(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx9(Heading7, { level: \"h2\", children: t(\"products.shippingProfile.header\") }),\n      /* @__PURE__ */ jsx9(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"shipping-profile\",\n                  icon: /* @__PURE__ */ jsx9(PencilSquare8, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    shippingProfile && /* @__PURE__ */ jsx9(\n      SidebarLink,\n      {\n        to: `/settings/locations/shipping-profiles/${shippingProfile.id}`,\n        labelKey: shippingProfile.name,\n        descriptionKey: shippingProfile.type,\n        icon: /* @__PURE__ */ jsx9(ShoppingBag, {})\n      }\n    )\n  ] });\n};\n\n// src/routes/products/product-detail/product-detail.tsx\nimport { jsx as jsx10, jsxs as jsxs9 } from \"react/jsx-runtime\";\nvar ProductDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const { product, isLoading, isError, error } = useProduct(\n    id,\n    { fields: PRODUCT_DETAIL_FIELDS },\n    {\n      initialData\n    }\n  );\n  const { getWidgets } = useExtension();\n  const after = getWidgets(\"product.details.after\");\n  const before = getWidgets(\"product.details.before\");\n  const sideAfter = getWidgets(\"product.details.side.after\");\n  const sideBefore = getWidgets(\"product.details.side.before\");\n  if (isLoading || !product) {\n    return /* @__PURE__ */ jsx10(\n      TwoColumnPageSkeleton,\n      {\n        mainSections: 4,\n        sidebarSections: 3,\n        showJSON: true,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs9(\n    TwoColumnPage,\n    {\n      widgets: {\n        after,\n        before,\n        sideAfter,\n        sideBefore\n      },\n      showJSON: true,\n      showMetadata: true,\n      data: product,\n      children: [\n        /* @__PURE__ */ jsxs9(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx10(ProductGeneralSection, { product }),\n          /* @__PURE__ */ jsx10(ProductMediaSection, { product }),\n          /* @__PURE__ */ jsx10(ProductOptionSection, { product }),\n          /* @__PURE__ */ jsx10(ProductVariantSection, { product })\n        ] }),\n        /* @__PURE__ */ jsxs9(TwoColumnPage.Sidebar, { children: [\n          /* @__PURE__ */ jsx10(ProductSalesChannelSection, { product }),\n          /* @__PURE__ */ jsx10(ProductShippingProfileSection, { product }),\n          /* @__PURE__ */ jsx10(ProductOrganizationSection, { product }),\n          /* @__PURE__ */ jsx10(ProductAttributeSection, { product })\n        ] })\n      ]\n    }\n  );\n};\nexport {\n  ProductDetailBreadcrumb as Breadcrumb,\n  ProductDetail as Component,\n  productLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA,yBAAoB;AAyCpB,IAAAA,sBAAkC;AAkDlC,IAAAC,sBAA2C;AAuG3C,mBAAyB;AAGzB,IAAAC,sBAA2C;AA+J3C,IAAAC,sBAA2C;AAqG3C,IAAAC,sBAA2C;AA6F3C,IAAAC,sBAA2C;AA+F3C,IAAAC,gBAAqC;AAGrC,IAAAC,sBAA2C;AAyV3C,IAAAC,sBAA2C;AAuC3C,IAAAA,uBAA4C;AAvgC5C,IAAI,0BAA0B,CAAC,UAAU;AACvC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF;AACA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,QAAQ,MAAM,CAAC;AAChE;AAGA,IAAI,qBAAqB,CAAC,QAAQ;AAAA,EAChC,UAAU,kBAAkB,OAAO,IAAI,EAAE,QAAQ,sBAAsB,CAAC;AAAA,EACxE,SAAS,YAAY,IAAI,MAAM,QAAQ,SAAS,IAAI,EAAE,QAAQ,sBAAsB,CAAC;AACvF;AACA,IAAI,gBAAgB,OAAO,EAAE,OAAO,MAAM;AACxC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,mBAAmB,EAAE;AACnC,QAAM,WAAW,MAAM,YAAY,gBAAgB;AAAA,IACjD,GAAG;AAAA,IACH,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AAUA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,YAAY,IAAI,aAAa;AACrC,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,UACjE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,QAAQ,OAAO,CAAC;AAAA,QACrE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,QAAQ,MAAM,CAAC;AAAA,QACnE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,QAAQ,OAAO,CAAC;AAAA,QACrE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,QAAQ,OAAO,CAAC;AAAA,QACrE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,QAAQ,SAAS,CAAC;AAAA,QACxE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,QAAQ,QAAQ,CAAC;AAAA,QACtE,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,wBAAwB;AAAA,QACjC,OAAO,oBAAoB,QAAQ,cAAc;AAAA,MACnD;AAAA,IACF;AAAA,IACA,YAAY,WAAW,YAAY,EAAE,IAAI,CAAC,YAAY,MAAM;AAC1D,iBAAuB,oBAAAA,KAAK,YAAY,EAAE,MAAM,QAAQ,GAAG,CAAC;AAAA,IAC9D,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AAQA,IAAI,qBAAqB,CAAC,WAAW;AACnC,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,aAAa;AACrC,QAAM,WAAW,YAAY,WAAW,SAAS;AACjD,QAAM,EAAE,YAAY,IAAI,iBAAiB,QAAQ,EAAE;AACnD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,0BAA0B;AAAA,QACvC,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,iBAAS,IAAI;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,UAAU,QAAQ,MAAM,CAAC;AAAA,UAC1C,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,aAAa,EAAE,OAAO,mBAAmB,QAAQ,MAAM,GAAG,UAAU,EAAE,0BAA0B,QAAQ,MAAM,EAAE,EAAE,CAAC;AAAA,YACxH,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,cAAc;AAAA,oBACvB,IAAI;AAAA,oBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,kBAC9C;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,gBAAgB;AAAA,oBACzB,SAAS;AAAA,oBACT,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,kBACtC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,QAAQ,YAAY,CAAC;AAAA,QAC/E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,iBAAiB,GAAG,OAAO,QAAQ,SAAS,CAAC;AAAA,QACzE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,IAAI,QAAQ,MAAM,GAAG,CAAC;AAAA,QAC3E,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,qBAAqB;AAAA,QAC9B,OAAO,QAAQ,eAAe,EAAE,aAAa,IAAI,EAAE,cAAc;AAAA,MACnE;AAAA,IACF;AAAA,IACA,SAAS,IAAI,CAAC,YAAY,UAAU;AAClC,iBAAuB,oBAAAA,KAAK,YAAY,EAAE,MAAM,QAAQ,GAAG,KAAK;AAAA,IAClE,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AAmBA,IAAI,sBAAsB,CAAC,EAAE,QAAQ,MAAM;AACzC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAW;AAC1B,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,CAAC,CAAC;AAC7C,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,sBAAsB,CAAC,OAAO;AAClC,iBAAa,CAAC,SAAS;AACrB,UAAI,KAAK,EAAE,GAAG;AACZ,cAAM,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,eAAO,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,KAAK;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,EAAE,YAAY,IAAI,iBAAiB,QAAQ,EAAE;AACnD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,UAAM,qBAAqB,IAAI;AAAA,MAC7B,CAAC,OAAI;AA7TX;AA6Tc,2BAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAA7B,mBAAgC;AAAA;AAAA,IAC1C;AACA,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,qBAAqB,EAAE,6CAA6C;AAAA,QAC/E,OAAO,IAAI;AAAA,MACb,CAAC,IAAI,EAAE,gCAAgC;AAAA,QACrC,OAAO,IAAI;AAAA,MACb,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,cAAc,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AACjG,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ;AAAA,QACR,WAAW,qBAAqB,KAAK;AAAA,MACvC;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,uBAAa,CAAC,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,sBAAsB,EAAE,CAAC;AAAA,UACnE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,MAAM,SAAS,QAAoB,oBAAAA,KAAK,OAAO,EAAE,WAAW,uEAAuE,UAAU,MAAM,IAAI,CAAC,GAAG,UAAU;AACnK,YAAM,aAAa,UAAU,EAAE,EAAE;AACjC,iBAAuB,oBAAAD;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,gBACQ,oBAAAC;AAAA,cACd;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,uBAAuB;AAAA,kBACzB;AAAA,gBACF;AAAA,gBACA,cAA0B,oBAAAA;AAAA,kBACxB;AAAA,kBACA;AAAA,oBACE,SAAS,UAAU,EAAE,EAAE,KAAK;AAAA,oBAC5B,iBAAiB,MAAM,oBAAoB,EAAE,EAAE;AAAA,kBACjD;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,EAAE,mBAA+B,oBAAAA,KAAK,OAAO,EAAE,WAAW,yBAAyB,cAA0B,oBAAAA,KAAK,SAAS,EAAE,SAAS,EAAE,kBAAkB,GAAG,cAA0B,oBAAAA,KAAK,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,gBACpM,oBAAAA,KAAK,MAAM,EAAE,IAAI,SAAS,OAAO,EAAE,MAAM,MAAM,GAAG,cAA0B,oBAAAA;AAAA,cAC1F;AAAA,cACA;AAAA,gBACE,KAAK,EAAE;AAAA,gBACP,KAAK,GAAG,QAAQ,KAAK;AAAA,gBACrB,WAAW;AAAA,cACb;AAAA,YACF,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,QACA,EAAE;AAAA,MACJ;AAAA,IACF,CAAC,EAAE,CAAC,QAAoB,oBAAAD,MAAM,OAAO,EAAE,WAAW,gDAAgD,UAAU;AAAA,UAC1F,oBAAAA,MAAM,OAAO,EAAE,WAAW,8BAA8B,UAAU;AAAA,YAChE,oBAAAC;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,UAAU,EAAE,kCAAkC;AAAA,UAChD;AAAA,QACF;AAAA,YACgB,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,WAAW,oBAAoB,UAAU,EAAE,uCAAuC,EAAE,CAAC;AAAA,MACnI,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,mBAAmB,UAAU,EAAE,kCAAkC,EAAE,CAAC,EAAE,CAAC;AAAA,IACvM,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,MAAM,CAAC,CAAC,OAAO,KAAK,SAAS,EAAE,QAAQ,cAA0B,oBAAAD,MAAM,WAAW,KAAK,EAAE,UAAU;AAAA,UACpH,oBAAAC,KAAK,WAAW,OAAO,EAAE,UAAU,EAAE,yBAAyB;AAAA,QAC5E,OAAO,OAAO,KAAK,SAAS,EAAE;AAAA,MAChC,CAAC,EAAE,CAAC;AAAA,UACY,oBAAAA,KAAK,WAAW,WAAW,CAAC,CAAC;AAAA,UAC7B,oBAAAA;AAAA,QACd,WAAW;AAAA,QACX;AAAA,UACE,QAAQ;AAAA,UACR,OAAO,EAAE,gBAAgB;AAAA,UACzB,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,WAAW,CAAC,YAAY;AAC1B,QAAM,EAAE,SAAS,CAAC,GAAG,UAAU,IAAI;AACnC,QAAM,QAAQ,OAAO,IAAI,CAAC,WAAW;AAAA,IACnC,IAAI,MAAM;AAAA,IACV,KAAK,MAAM;AAAA,IACX,aAAa,MAAM,QAAQ;AAAA,EAC7B,EAAE;AACF,MAAI,aAAa,CAAC,MAAM,KAAK,CAAC,cAAc,UAAU,QAAQ,SAAS,GAAG;AACxE,UAAM,QAAQ;AAAA,MACZ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAOA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,uBAAuB,QAAQ,IAAI,OAAO,EAAE;AACpE,QAAM,SAAS,UAAW;AAC1B,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,kCAAkC;AAAA,QAC/C,OAAO,OAAO;AAAA,MAChB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAAA,EACpB;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,WAAW,OAAO,EAAE;AAAA,cACxB,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AA1fN;AA2fE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAD,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,yBAAyB,EAAE,CAAC;AAAA,UACtE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,gBAAgB;AAAA,kBACzB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,MAAM,CAAC,CAAC;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,KACH,aAAQ,YAAR,mBAAiB,IAAI,CAAC,WAAW;AAhhBrC,UAAAE;AAihBM,iBAAuB,oBAAAF;AAAA,QACrB;AAAA,QACA;AAAA,UACE,OAAO,OAAO;AAAA,UACd,QAAOE,MAAA,OAAO,WAAP,gBAAAA,IAAe,IAAI,CAAC,QAAQ;AACjC,uBAAuB,oBAAAF;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,WAAW;AAAA,gBACX,UAAU,IAAI;AAAA,cAChB;AAAA,cACA,IAAI;AAAA,YACN;AAAA,UACF;AAAA,UACA,aAAyB,oBAAAA,KAAK,eAAe,EAAE,SAAS,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAQA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AACF,MAAM;AAhjBN;AAijBE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,aAAa;AACrC,aAAuB,oBAAAG,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,8BAA8B,EAAE,CAAC;AAAA,UAC3E,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,aAAa;AAAA,QACtB,SAAO,aAAQ,SAAR,mBAAc,UAAS,QAAQ,KAAK,IAAI,CAAC,YAAwB,oBAAAA;AAAA,UACtE;AAAA,UACA;AAAA,YACE,OAAO,IAAI;AAAA,YACX,IAAI,0BAA0B,IAAI,EAAE;AAAA,UACtC;AAAA,UACA,IAAI;AAAA,QACN,CAAC,IAAI;AAAA,MACP;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,aAAa;AAAA,QACtB,OAAO,QAAQ,WAAuB,oBAAAA;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO,QAAQ,KAAK;AAAA,YACpB,IAAI,2BAA2B,QAAQ,OAAO;AAAA,UAChD;AAAA,QACF,IAAI;AAAA,MACN;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,mBAAmB;AAAA,QAC5B,OAAO,QAAQ,iBAA6B,oBAAAA;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,OAAO,QAAQ,WAAW;AAAA,YAC1B,IAAI,gBAAgB,QAAQ,WAAW,EAAE;AAAA,UAC3C;AAAA,QACF,IAAI;AAAA,MACN;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,mBAAmB;AAAA,QAC5B,SAAO,aAAQ,eAAR,mBAAoB,UAAS,QAAQ,WAAW,IAAI,CAAC,aAAyB,oBAAAA;AAAA,UACnF;AAAA,UACA;AAAA,YACE,OAAO,KAAK;AAAA,YACZ,IAAI,eAAe,KAAK,EAAE;AAAA,UAC5B;AAAA,UACA,KAAK;AAAA,QACP,CAAC,IAAI;AAAA,MACP;AAAA,IACF;AAAA,IACA,YAAY,WAAW,UAAU,EAAE,IAAI,CAAC,YAAY,MAAM;AACxD,iBAAuB,oBAAAA,KAAK,YAAY,EAAE,MAAM,QAAQ,GAAG,CAAC;AAAA,IAC9D,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AACA,IAAI,kBAAkB,CAAC,EAAE,OAAO,GAAG,MAAM;AACvC,aAAuB,oBAAAA,KAAK,SAAU,EAAE,SAAS,OAAO,cAA0B,oBAAAA,KAAK,OAAQ,EAAE,MAAM,WAAW,WAAW,wBAAwB,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAO,EAAE,IAAI,UAAU,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AACzO;AAOA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AACF,MAAM;AA7oBN;AA8oBE,QAAM,EAAE,MAAM,IAAI,iBAAiB;AACnC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,6BAA2B,aAAQ,mBAAR,mBAAwB,IAAI,CAAC,QAAQ;AAAA,IACpE,IAAI,GAAG;AAAA,IACP,MAAM,GAAG;AAAA,EACX,QAAO,CAAC;AACR,QAAM,gBAAgB,yBAAyB,MAAM,GAAG,CAAC;AACzD,QAAM,eAAe,yBAAyB,MAAM,CAAC;AACrD,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,mCAAmC,UAAU;AAAA,QACjF,oBAAAA,MAAM,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,UACvE,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,UACpE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,kDAAkD,UAAU;AAAA,UACpF,oBAAAC,KAAK,OAAO,EAAE,WAAW,wFAAwF,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,4EAA4E,cAA0B,oBAAAA,KAAK,UAAU,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,MACjV,yBAAyB,SAAS,QAAoB,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YACrG,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,cAAc,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,QAC1H,aAAa,SAAS,SAAqB,oBAAAA;AAAA,UACzC;AAAA,UACA;AAAA,YACE,aAAyB,oBAAAA,KAAK,MAAM,EAAE,UAAU,aAAa,IAAI,CAAC,WAAuB,oBAAAA,KAAK,MAAM,EAAE,UAAU,GAAG,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AAAA,YACpI,cAA0B,oBAAAA;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,UAAU,IAAI,aAAa,MAAM;AAAA,cACnC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC,QAAoB,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,qBAAqB,UAAU,EAAE,0BAA0B,EAAE,CAAC;AAAA,IACnJ,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,cAA0B,oBAAAA,KAAK,MAAO,EAAE,WAAW,qBAAqB,MAAM,SAAS,SAAS,WAAW,cAA0B,oBAAAA;AAAA,MACjK;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,GAAG,yBAAyB;AAAA,UAC5B,GAAG,SAAS;AAAA,QACd;AAAA,QACA,YAAY;AAAA,cACM,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,YACA;AAAA,UACF;AAAA,cACgB,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AAmBA,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,WAAW,OAAO;AAClC,QAAM,UAAU,WAAW;AAC3B,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACrD,QAAQ;AAAA,IACR;AAAA,MACE;AAAA,MACA,OAAO,QAAQ,QAAQ;AAAA,MACvB,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,MACpC,OAAO;AAAA,MACP,iBAAiB,kBAAkB,KAAK,MAAM,eAAe,IAAI;AAAA,MACjE,kBAAkB,mBAAmB,KAAK,MAAM,gBAAgB,IAAI;AAAA,MACpE,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,MAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,MAClD,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,WAAY,EAAE,WAAW,gBAAgB,cAA0B,oBAAAA;AAAA,IAC7F;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,UAAU,CAAC,QAAQ,IAAI;AAAA,MACvB,SAAS,CAAC,QAAQ,aAAa,QAAQ,EAAE,aAAa,IAAI,EAAE;AAAA,MAC5D,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS,EAAE,0BAA0B;AAAA,MACrC,YAAY;AAAA,QACV,OAAO;AAAA,UACL,SAAS,EAAE,iCAAiC;AAAA,UAC5C,aAAa,EAAE,qCAAqC;AAAA,QACtD;AAAA,QACA,UAAU;AAAA,UACR,SAAS,EAAE,oCAAoC;AAAA,UAC/C,aAAa,EAAE,wCAAwC;AAAA,QACzD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,EAAE,gBAAgB;AAAA,QACzB,IAAI;AAAA,MACN;AAAA,MACA,YAAY;AAAA,QACV,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,OAAO,EAAE,qBAAqB;AAAA,gBAC9B,IAAI;AAAA,gBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC9C;AAAA,cACA;AAAA,gBACE,OAAO,EAAE,wBAAwB;AAAA,gBACjC,IAAI;AAAA,gBACJ,UAAsB,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,cAC1C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,CAAC,YAAY;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,WAAW,YAAa;AAC9B,QAAM,EAAE,YAAY,IAAI,qBAAqB,QAAQ,EAAE;AACvD,QAAM,SAAS,UAAW;AAC1B,QAAM,CAAC,YAAY,IAAI,gBAAgB;AACvC,QAAM,wBAAoB,uBAAQ,MAAM;AACtC,UAAM,WAAW,IAAI,gBAAgB;AACrC,eAAW,CAAC,KAAK,KAAK,KAAK,aAAa,QAAQ,GAAG;AACjD,UAAI,IAAI,WAAW,GAAG,MAAM,GAAG,GAAG;AAChC,iBAAS,OAAO,KAAK,KAAK;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,cAAc,wBAAwB;AAC5C,QAAM,mBAAe;AAAA,IACnB,OAAO,IAAI,UAAU;AACnB,YAAM,MAAM,MAAM,OAAO;AAAA,QACvB,OAAO,EAAE,oBAAoB;AAAA,QAC7B,aAAa,EAAE,iCAAiC;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,QACD,aAAa,EAAE,gBAAgB;AAAA,QAC/B,YAAY,EAAE,gBAAgB;AAAA,MAChC,CAAC;AACD,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,YAAY,EAAE,WAAW,GAAG,CAAC;AAAA,IACrC;AAAA,IACA,CAAC,aAAa,QAAQ,CAAC;AAAA,EACzB;AACA,QAAM,oBAAgB,uBAAQ,MAAM;AAClC,QAAI,EAAC,mCAAS,UAAS;AACrB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,QAAQ,QAAQ,IAAI,CAAC,WAAW;AACrC,aAAO,aAAa,QAAQ;AAAA,QAC1B,IAAI,OAAO;AAAA,QACX,QAAQ,OAAO;AAAA,QACf,MAAM,CAAC,EAAE,IAAI,MAAM;AAz3B3B;AA03BU,gBAAM,cAAa,SAAI,SAAS,YAAb,mBAAsB;AAAA,YACvC,CAAC,QAAQ,IAAI,cAAc,OAAO;AAAA;AAEpC,cAAI,CAAC,YAAY;AACf,uBAAuB,oBAAAA,KAAK,QAAQ,EAAE,WAAW,oBAAoB,UAAU,IAAI,CAAC;AAAA,UACtF;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,qBAAqB,cAA0B,oBAAAA,KAAK,SAAU,EAAE,SAAS,WAAW,OAAO,cAA0B,oBAAAA;AAAA,YACnK;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO,WAAW;AAAA,cAClB,WAAW;AAAA,cACX,UAAU,WAAW;AAAA,YACvB;AAAA,UACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,iBAAa;AAAA,IACjB,CAAC,QAAQ;AA94Bb;AA+4BM,YAAM,UAAU,IAAI,IAAI;AACxB,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,UAC5C,OAAO,EAAE,cAAc;AAAA,UACvB,SAAS,CAAC,QAAQ;AAChB;AAAA,cACE,2BAA2B,IAAI,IAAI,SAAS,EAAE,IAAI,kBAAkB,SAAS,CAAC;AAAA,cAC9E;AAAA,gBACE,OAAO;AAAA,kBACL,gBAAgB,kBAAkB,SAAS;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,mBAAmB;AAAA,QACvB;AAAA,UACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,UACrC,OAAO,EAAE,gBAAgB;AAAA,UACzB,SAAS,MAAM,aAAa,QAAQ,IAAI,QAAQ,KAAK;AAAA,QACvD;AAAA,MACF;AACA,YAAM,wBAAsB,aAAQ,oBAAR,mBAAyB,WAAU;AAC/D,cAAQ,qBAAqB;AAAA,QAC3B,KAAK;AACH;AAAA,QACF,KAAK,GAAG;AACN,gBAAM,oBAAoB,cAAc,QAAQ,gBAAgB,CAAC,EAAE,UAAU,EAAE;AAC/E,sBAAY,KAAK;AAAA,YACf,OAAO,EAAE,mDAAmD;AAAA,YAC5D,SAAS,MAAM;AACb,uBAAS,iBAAiB;AAAA,YAC5B;AAAA,YACA,UAAsB,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,UAC1C,CAAC;AACD;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,OAAM,aAAQ,oBAAR,mBAAyB,IAAI,CAAC,MAAG;AAv7BvD,gBAAAL;AAu7B0D,oBAAAA,MAAA,EAAE,cAAF,gBAAAA,IAAa;AAAA;AAC7D,cAAI,CAAC,OAAO,IAAI,WAAW,GAAG;AAC5B;AAAA,UACF;AACA,gBAAM,mBAAmB,cAAc,IAAI,gBAAgB;AAAA,YACzD,IAAI,IAAI,KAAK,GAAG;AAAA,UAClB,CAAC,EAAE,SAAS,CAAC;AACb,sBAAY,KAAK;AAAA,YACf,OAAO,EAAE,iDAAiD;AAAA,YAC1D,SAAS,MAAM;AACb,uBAAS,gBAAgB;AAAA,YAC3B;AAAA,YACA,UAAsB,oBAAAK,KAAK,WAAW,CAAC,CAAC;AAAA,UAC1C,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO,CAAC,aAAa,gBAAgB;AAAA,IACvC;AAAA,IACA,CAAC,cAAc,UAAU,GAAG,iBAAiB;AAAA,EAC/C;AACA,QAAM,mBAAe;AAAA,IACnB,CAAC,YAAY;AA58BjB;AA68BM,YAAM,cAAc;AACpB,UAAI,CAAC,QAAQ,kBAAkB;AAC7B,eAAO;AAAA,UACL,MAAM,EAAE,uCAAuC;AAAA,UAC/C,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACd;AAAA,MACF;AACA,YAAM,WAAW,QAAQ;AACzB,YAAM,kBAAiB,iBAAY,oBAAZ,mBAA6B,IAAI,CAAC,MAAM,EAAE,WAAW,OAAO;AACnF,YAAM,kBAAkB,eAAe,SAAS;AAChD,YAAM,YAAY,CAAC;AACnB,qBAAe,QAAQ,CAAC,MAAM;AAz9BpC,YAAAL;AA09BQ,SAAAA,MAAA,EAAE,oBAAF,gBAAAA,IAAmB,QAAQ,CAAC,MAAM;AAChC,oBAAU,EAAE,EAAE,IAAI;AAAA,QACpB;AAAA,MACF,CAAC;AACD,YAAM,gBAAgB,OAAO,KAAK,SAAS,EAAE;AAC7C,YAAM,OAAO,kBAAkB,EAAE,uCAAuC;AAAA,QACtE,gBAAgB;AAAA,MAClB,CAAC,IAAI,EAAE,8BAA8B;AAAA,QACnC,gBAAgB;AAAA,QAChB;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AACD,aAAO,EAAE,MAAM,iBAAiB,UAAU,YAAY,MAAM;AAAA,IAC9D;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACA,aAAO,uBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,cAAc;AAAA,QACxB,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,aAAa,SAAS,OAAO;AAAA,QAC3B,QAAQ,EAAE,YAAY;AAAA,QACtB,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,EAAE,MAAM,iBAAiB,UAAU,WAAW,IAAI;AAAA,YACtD,IAAI;AAAA,UACN;AACA,qBAAuB,oBAAAK,KAAK,SAAU,EAAE,SAAS,MAAM,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,YAC5K,uBAAmC,oBAAAD,KAAK,WAAW,CAAC,CAAC;AAAA,gBACrC,oBAAAA;AAAA,cACd;AAAA,cACA;AAAA,gBACE,WAAW,IAAK,YAAY;AAAA,kBAC1B,oBAAoB,CAAC,YAAY,CAAC;AAAA,gBACpC,CAAC;AAAA,gBACD,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACR;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,OAAO;AAAA,QAClB,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,GAAG,eAAe,aAAa,YAAY,YAAY,CAAC;AAC9D;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,cAAc,wBAAwB;AAC5C,aAAO,uBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,SAAS,mBAAmB;AAAA,QACvC,MAAM;AAAA,QACN,OAAO,EAAE,uBAAuB;AAAA,QAChC,SAAS;AAAA,UACP,EAAE,OAAO,EAAE,mBAAmB,GAAG,OAAO,OAAO;AAAA,UAC/C,EAAE,OAAO,EAAE,kBAAkB,GAAG,OAAO,QAAQ;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,oBAAoB;AAAA,QACxC,MAAM;AAAA,QACN,OAAO,EAAE,wBAAwB;AAAA,QACjC,SAAS;AAAA,UACP,EAAE,OAAO,EAAE,mBAAmB,GAAG,OAAO,OAAO;AAAA,UAC/C,EAAE,OAAO,EAAE,kBAAkB,GAAG,OAAO,QAAQ;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF,GAAG,CAAC,GAAG,WAAW,CAAC;AACrB;AACA,IAAI,gBAAgB,6BAA6B;AACjD,IAAI,cAAc,MAAM;AACtB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,WAAW,YAAa;AAC9B,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,MACpB,OAAO,EAAE,wBAAwB;AAAA,MACjC,UAAU;AAAA,MACV,QAAQ,OAAO,cAAc;AAC3B;AAAA,UACE,SAAS,uBAAuB,IAAI,OAAO,KAAK,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,QACtE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAOA,IAAI,gCAAgC,CAAC;AAAA,EACnC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,kBAAkB,QAAQ;AAChC,aAAuB,oBAAAE,MAAM,WAAY,EAAE,WAAW,OAAO,UAAU;AAAA,QACrD,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iCAAiC,EAAE,CAAC;AAAA,UAC9E,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,uBAAmC,oBAAAA;AAAA,MACjC;AAAA,MACA;AAAA,QACE,IAAI,yCAAyC,gBAAgB,EAAE;AAAA,QAC/D,UAAU,gBAAgB;AAAA,QAC1B,gBAAgB,gBAAgB;AAAA,QAChC,UAAsB,oBAAAA,KAAK,aAAa,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI;AAAA,IAC7C;AAAA,IACA,EAAE,QAAQ,sBAAsB;AAAA,IAChC;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,QAAM,QAAQ,WAAW,uBAAuB;AAChD,QAAM,SAAS,WAAW,wBAAwB;AAClD,QAAM,YAAY,WAAW,4BAA4B;AACzD,QAAM,aAAa,WAAW,6BAA6B;AAC3D,MAAI,aAAa,CAAC,SAAS;AACzB,eAAuB,qBAAAC;AAAA,MACrB;AAAA,MACA;AAAA,QACE,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,qBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,qBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,qBAAAD,KAAM,uBAAuB,EAAE,QAAQ,CAAC;AAAA,cACxC,qBAAAA,KAAM,qBAAqB,EAAE,QAAQ,CAAC;AAAA,cACtC,qBAAAA,KAAM,sBAAsB,EAAE,QAAQ,CAAC;AAAA,cACvC,qBAAAA,KAAM,uBAAuB,EAAE,QAAQ,CAAC;AAAA,QAC1D,EAAE,CAAC;AAAA,YACa,qBAAAC,MAAM,cAAc,SAAS,EAAE,UAAU;AAAA,cACvC,qBAAAD,KAAM,4BAA4B,EAAE,QAAQ,CAAC;AAAA,cAC7C,qBAAAA,KAAM,+BAA+B,EAAE,QAAQ,CAAC;AAAA,cAChD,qBAAAA,KAAM,4BAA4B,EAAE,QAAQ,CAAC;AAAA,cAC7C,qBAAAA,KAAM,yBAAyB,EAAE,QAAQ,CAAC;AAAA,QAC5D,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsxs3", "jsx4", "jsx5", "jsxs4", "_a", "jsxs5", "jsx6", "jsxs6", "jsx7", "jsx8", "jsxs7", "jsxs8", "jsx9", "jsx10", "jsxs9"]}