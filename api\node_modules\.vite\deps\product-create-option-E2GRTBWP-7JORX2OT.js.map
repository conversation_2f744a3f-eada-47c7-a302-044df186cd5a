{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-create-option-E2GRTBWP.mjs"], "sourcesContent": ["import {\n  ChipInput\n} from \"./chunk-XDJ7OMBR.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateProductOption,\n  useProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-create-option/product-create-option.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-create-option/components/create-product-option-form/create-product-option-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateProductOptionSchema = z.object({\n  title: z.string().min(1),\n  values: z.array(z.string()).optional()\n});\nvar CreateProductOptionForm = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      title: \"\",\n      values: []\n    },\n    resolver: zodResolver(CreateProductOptionSchema)\n  });\n  const { mutateAsync, isPending } = useCreateProductOption(product.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    mutateAsync(values, {\n      onSuccess: () => {\n        toast.success(\n          t(\"products.options.create.successToast\", {\n            title: values.title\n          })\n        );\n        handleSuccess();\n      },\n      onError: async (err) => {\n        toast.error(err.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-4 overflow-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"title\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.fields.options.optionTitle\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Input,\n                    {\n                      ...field,\n                      placeholder: t(\n                        \"products.fields.options.optionTitlePlaceholder\"\n                      )\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"values\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.fields.options.variations\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    ChipInput,\n                    {\n                      ...field,\n                      placeholder: t(\n                        \"products.fields.options.variantionsPlaceholder\"\n                      )\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/products/product-create-option/product-create-option.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductCreateOption = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product, isLoading, isError, error } = useProduct(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"products.options.create.header\") }) }),\n    !isLoading && product && /* @__PURE__ */ jsx2(CreateProductOptionForm, { product })\n  ] });\n};\nexport {\n  ProductCreateOption as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,yBAA0B;AA+F1B,IAAAA,sBAA2C;AA9F3C,IAAI,4BAA4B,EAAE,OAAO;AAAA,EACvC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,SAAS;AACvC,CAAC;AACD,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,MACP,QAAQ,CAAC;AAAA,IACX;AAAA,IACA,UAAU,EAAY,yBAAyB;AAAA,EACjD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,uBAAuB,QAAQ,EAAE;AACpE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,gBAAY,QAAQ;AAAA,MAClB,WAAW,MAAM;AACf,cAAM;AAAA,UACJA,GAAE,wCAAwC;AAAA,YACxC,OAAO,OAAO;AAAA,UAChB,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,OAAO,QAAQ;AACtB,cAAM,MAAM,IAAI,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,qCAAqC,EAAE,CAAC;AAAA,sBACtE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,oCAAoC,EAAE,CAAC;AAAA,sBACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,EAAE;AAC5D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,gCAAgC,EAAE,CAAC,EAAE,CAAC;AAAA,IACvI,CAAC,aAAa,eAA2B,oBAAAE,KAAK,yBAAyB,EAAE,QAAQ,CAAC;AAAA,EACpF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}