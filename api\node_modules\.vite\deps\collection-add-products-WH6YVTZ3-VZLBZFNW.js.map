{"version": 3, "sources": ["../../@medusajs/dashboard/dist/collection-add-products-WH6YVTZ3.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport {\n  useCollection,\n  useUpdateCollectionProducts\n} from \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/collections/collection-add-products/collection-add-products.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/collections/collection-add-products/components/add-products-to-collection-form/add-products-to-collection-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddProductsToCollectionSchema = zod.object({\n  add: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"p\";\nvar AddProductsToCollectionForm = ({\n  collection\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      add: []\n    },\n    resolver: zodResolver(AddProductsToCollectionSchema)\n  });\n  const { setValue } = form;\n  const { mutateAsync, isPending: isMutating } = useUpdateCollectionProducts(\n    collection.id\n  );\n  const [rowSelection, setRowSelection] = useState({});\n  const updater = (newSelection) => {\n    const update = typeof newSelection === \"function\" ? newSelection(rowSelection) : newSelection;\n    setValue(\n      \"add\",\n      Object.keys(update).filter((k) => update[k]),\n      {\n        shouldDirty: true,\n        shouldTouch: true\n      }\n    );\n    setRowSelection(update);\n  };\n  useEffect(() => {\n    setValue(\n      \"add\",\n      Object.keys(rowSelection).filter((k) => rowSelection[k]),\n      {\n        shouldDirty: true,\n        shouldTouch: true\n      }\n    );\n  }, [rowSelection, setValue]);\n  const { searchParams, raw } = useProductTableQuery({\n    prefix: PREFIX,\n    pageSize: PAGE_SIZE\n  });\n  const { products, count, isLoading, isError, error } = useProducts(\n    {\n      fields: \"*variants,*sales_channels\",\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useProductTableFilters([\"collections\"]);\n  const { table } = useDataTable({\n    data: products ?? [],\n    columns,\n    count,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX,\n    getRowId: (row) => row.id,\n    enableRowSelection: true,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    },\n    enablePagination: true,\n    meta: {\n      collectionId: collection.id\n    }\n  });\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        add: values.add\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"collections.products.add.successToast\", {\n              count: values.add.length\n            })\n          );\n          handleSuccess();\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          form.formState.errors.add && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.add.message }),\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isMutating, children: t(\"actions.save\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            columns,\n            pageSize: PAGE_SIZE,\n            count,\n            queryObject: raw,\n            filters,\n            orderBy: [\n              { key: \"title\", label: t(\"fields.title\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            prefix: PREFIX,\n            isLoading,\n            layout: \"fill\",\n            pagination: true,\n            search: \"autofocus\"\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const base = useProductTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row, table }) => {\n          const { collectionId } = table.options.meta;\n          const isAdded = row.original.collection_id === collectionId;\n          const isSelected = row.getIsSelected() || isAdded;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isAdded,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isAdded) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"salesChannels.productAlreadyAdded\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n};\n\n// src/routes/collections/collection-add-products/collection-add-products.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CollectionAddProducts = () => {\n  const { id } = useParams();\n  const { collection, isLoading, isError, error } = useCollection(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && collection && /* @__PURE__ */ jsx2(AddProductsToCollectionForm, { collection }) });\n};\nexport {\n  CollectionAddProducts as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA,mBAA6C;AAI7C,yBAA0B;AA2L1B,IAAAA,sBAA4B;AA1L5B,IAAI,gCAAoC,WAAO;AAAA,EAC7C,KAAS,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AACpC,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,8BAA8B,CAAC;AAAA,EACjC;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,KAAK,CAAC;AAAA,IACR;AAAA,IACA,UAAU,EAAY,6BAA6B;AAAA,EACrD,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI;AAAA,IAC7C,WAAW;AAAA,EACb;AACA,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,UAAU,CAAC,iBAAiB;AAChC,UAAM,SAAS,OAAO,iBAAiB,aAAa,aAAa,YAAY,IAAI;AACjF;AAAA,MACE;AAAA,MACA,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MAC3C;AAAA,QACE,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AACA,oBAAgB,MAAM;AAAA,EACxB;AACA,8BAAU,MAAM;AACd;AAAA,MACE;AAAA,MACA,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;AAAA,MACvD;AAAA,QACE,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,QAAQ,CAAC;AAC3B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACrD;AAAA,MACE,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,uBAAuB,CAAC,aAAa,CAAC;AACtD,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,IAClB,MAAM;AAAA,MACJ,cAAc,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,KAAK,OAAO;AAAA,MACd;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,yCAAyC;AAAA,cACzC,OAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClJ,KAAK,UAAU,OAAO,WAAuB,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,IAAI,QAAQ,CAAC;AAAA,cACxG,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,YAAY,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACnH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,cAA0B;AAAA,UAC5G;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,aAAa;AAAA,YACb;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,SAAS,OAAOA,GAAE,cAAc,EAAE;AAAA,cACzC,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA,QAAQ;AAAA,YACR;AAAA,YACA,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AACxB,gBAAM,EAAE,aAAa,IAAI,MAAM,QAAQ;AACvC,gBAAM,UAAU,IAAI,SAAS,kBAAkB;AAC/C,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,SAAS;AACX,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,mCAAmC;AAAA,gBAC9C,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACF;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI,cAAc,EAAE;AAClE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,kBAA8B,oBAAAA,KAAK,6BAA6B,EAAE,WAAW,CAAC,EAAE,CAAC;AAC1J;", "names": ["import_jsx_runtime", "t", "jsx2"]}