{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-DXOEDXLQ.mjs"], "sourcesContent": ["import {\n  CreateCampaignFormFields\n} from \"./chunk-TDDYKNA2.mjs\";\nimport {\n  VisuallyHidden\n} from \"./chunk-F6ZOHZVB.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  useCreateCampaign\n} from \"./chunk-G2H6MAK7.mjs\";\n\n// src/routes/campaigns/campaign-create/components/create-campaign-form/create-campaign-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\n\n// src/routes/campaigns/common/constants.ts\nvar DEFAULT_CAMPAIGN_VALUES = {\n  name: \"\",\n  description: \"\",\n  campaign_identifier: \"\",\n  starts_at: null,\n  ends_at: null,\n  budget: {\n    type: \"usage\",\n    currency_code: null,\n    limit: null\n  }\n};\n\n// src/routes/campaigns/campaign-create/components/create-campaign-form/create-campaign-form.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateCampaignSchema = zod.object({\n  name: zod.string().min(1),\n  description: zod.string().optional(),\n  campaign_identifier: zod.string().min(1),\n  starts_at: zod.date().nullable(),\n  ends_at: zod.date().nullable(),\n  budget: zod.object({\n    limit: zod.number().min(0).nullish(),\n    type: zod.enum([\"spend\", \"usage\"]),\n    currency_code: zod.string().nullish()\n  })\n});\nvar CreateCampaignForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { mutateAsync, isPending } = useCreateCampaign();\n  const form = useForm({\n    defaultValues: DEFAULT_CAMPAIGN_VALUES,\n    resolver: zodResolver(CreateCampaignSchema)\n  });\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        name: data.name,\n        description: data.description,\n        campaign_identifier: data.campaign_identifier,\n        starts_at: data.starts_at,\n        ends_at: data.ends_at,\n        budget: {\n          type: data.budget.type,\n          limit: data.budget.limit ? data.budget.limit : void 0,\n          currency_code: data.budget.currency_code\n        }\n      },\n      {\n        onSuccess: ({ campaign }) => {\n          toast.success(\n            t(\"campaigns.create.successToast\", {\n              name: campaign.name\n            })\n          );\n          handleSuccess(`/campaigns/${campaign.id}`);\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex size-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteFocusModal.Header, { children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"campaigns.create.title\") }) }),\n          /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"campaigns.create.description\") }) })\n        ] }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex size-full flex-col items-center overflow-auto py-16\", children: /* @__PURE__ */ jsx(CreateCampaignFormFields, { form }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              size: \"small\",\n              variant: \"primary\",\n              type: \"submit\",\n              isLoading: isPending,\n              children: t(\"actions.create\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n};\n\nexport {\n  DEFAULT_CAMPAIGN_VALUES,\n  CreateCampaignSchema,\n  CreateCampaignForm\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,yBAA0B;AAd1B,IAAI,0BAA0B;AAAA,EAC5B,MAAM;AAAA,EACN,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,eAAe;AAAA,IACf,OAAO;AAAA,EACT;AACF;AAIA,IAAI,uBAA2B,WAAO;AAAA,EACpC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,aAAiB,WAAO,EAAE,SAAS;AAAA,EACnC,qBAAyB,WAAO,EAAE,IAAI,CAAC;AAAA,EACvC,WAAe,SAAK,EAAE,SAAS;AAAA,EAC/B,SAAa,SAAK,EAAE,SAAS;AAAA,EAC7B,QAAY,WAAO;AAAA,IACjB,OAAW,WAAO,EAAE,IAAI,CAAC,EAAE,QAAQ;AAAA,IACnC,MAAU,SAAK,CAAC,SAAS,OAAO,CAAC;AAAA,IACjC,eAAmB,WAAO,EAAE,QAAQ;AAAA,EACtC,CAAC;AACH,CAAC;AACD,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,aAAa,UAAU,IAAI,kBAAkB;AACrD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,IACf,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,KAAK;AAAA,QACX,aAAa,KAAK;AAAA,QAClB,qBAAqB,KAAK;AAAA,QAC1B,WAAW,KAAK;AAAA,QAChB,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,UACN,MAAM,KAAK,OAAO;AAAA,UAClB,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAQ;AAAA,UAC/C,eAAe,KAAK,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,SAAS,MAAM;AAC3B,gBAAM;AAAA,YACJA,GAAE,iCAAiC;AAAA,cACjC,MAAM,SAAS;AAAA,YACjB,CAAC;AAAA,UACH;AACA,wBAAc,cAAc,SAAS,EAAE,EAAE;AAAA,QAC3C;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,cACvC,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,cACtI,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC;AAAA,QACpK,EAAE,CAAC;AAAA,YACa,wBAAI,gBAAgB,MAAM,EAAE,WAAW,4DAA4D,cAA0B,wBAAI,0BAA0B,EAAE,KAAK,CAAC,EAAE,CAAC;AAAA,YACtK,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAUA,GAAE,gBAAgB;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["t"]}