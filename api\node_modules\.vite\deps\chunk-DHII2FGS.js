import {
  Input,
  Text
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-7OYLCEKK.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var HandleInput = (0, import_react.forwardRef)((props, ref) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r", children: (0, import_jsx_runtime.jsx)(
      Text,
      {
        className: "text-ui-fg-muted",
        size: "small",
        leading: "compact",
        weight: "plus",
        children: "/"
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Input, { ref, ...props, className: "pl-10" })
  ] });
});
HandleInput.displayName = "HandleInput";

export {
  HandleInput
};
//# sourceMappingURL=chunk-DHII2FGS.js.map
