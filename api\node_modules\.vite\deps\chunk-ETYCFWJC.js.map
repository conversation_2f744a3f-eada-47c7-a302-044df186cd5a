{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-X5VECN6S.mjs"], "sourcesContent": ["// src/components/common/chip-group/chip-group.tsx\nimport { XMarkMini } from \"@medusajs/icons\";\nimport { Button, clx } from \"@medusajs/ui\";\nimport { Children, createContext, useContext } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar GroupContext = createContext(null);\nvar useGroupContext = () => {\n  const context = useContext(GroupContext);\n  if (!context) {\n    throw new Error(\"useGroupContext must be used within a ChipGroup component\");\n  }\n  return context;\n};\nvar Group = ({\n  onClearAll,\n  onRemove,\n  variant = \"component\",\n  className,\n  children\n}) => {\n  const { t } = useTranslation();\n  const showClearAll = !!onClearAll && Children.count(children) > 0;\n  return /* @__PURE__ */ jsx(GroupContext.Provider, { value: { onRemove, variant }, children: /* @__PURE__ */ jsxs(\n    \"ul\",\n    {\n      role: \"application\",\n      className: clx(\"flex flex-wrap items-center gap-2\", className),\n      children: [\n        children,\n        showClearAll && /* @__PURE__ */ jsx(\"li\", { children: /* @__PURE__ */ jsx(\n          Button,\n          {\n            size: \"small\",\n            variant: \"transparent\",\n            type: \"button\",\n            onClick: onClearAll,\n            className: \"text-ui-fg-muted active:text-ui-fg-subtle\",\n            children: t(\"actions.clearAll\")\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar Chip = ({ index, className, children }) => {\n  const { onRemove, variant } = useGroupContext();\n  return /* @__PURE__ */ jsxs(\n    \"li\",\n    {\n      className: clx(\n        \"bg-ui-bg-component shadow-borders-base flex items-stretch divide-x overflow-hidden rounded-md\",\n        {\n          \"bg-ui-bg-component\": variant === \"component\",\n          \"bg-ui-bg-base-\": variant === \"base\"\n        },\n        className\n      ),\n      children: [\n        /* @__PURE__ */ jsx(\"span\", { className: \"txt-compact-small-plus text-ui-fg-subtle flex items-center justify-center px-2 py-1\", children }),\n        !!onRemove && /* @__PURE__ */ jsx(\n          \"button\",\n          {\n            onClick: () => onRemove(index),\n            type: \"button\",\n            className: clx(\n              \"text-ui-fg-muted active:text-ui-fg-subtle transition-fg flex items-center justify-center p-1\",\n              {\n                \"hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed\": variant === \"component\",\n                \"hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed\": variant === \"base\"\n              }\n            ),\n            children: /* @__PURE__ */ jsx(XMarkMini, {})\n          }\n        )\n      ]\n    }\n  );\n};\nvar ChipGroup = Object.assign(Group, { Chip });\n\nexport {\n  ChipGroup\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,mBAAoD;AAEpD,yBAA0B;AAC1B,IAAI,mBAAe,4BAAc,IAAI;AACrC,IAAI,kBAAkB,MAAM;AAC1B,QAAM,cAAU,yBAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,SAAO;AACT;AACA,IAAI,QAAQ,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,CAAC,CAAC,cAAc,sBAAS,MAAM,QAAQ,IAAI;AAChE,aAAuB,wBAAI,aAAa,UAAU,EAAE,OAAO,EAAE,UAAU,QAAQ,GAAG,cAA0B;AAAA,IAC1G;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,WAAW,IAAI,qCAAqC,SAAS;AAAA,MAC7D,UAAU;AAAA,QACR;AAAA,QACA,oBAAgC,wBAAI,MAAM,EAAE,cAA0B;AAAA,UACpE;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,YACX,UAAU,EAAE,kBAAkB;AAAA,UAChC;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,OAAO,CAAC,EAAE,OAAO,WAAW,SAAS,MAAM;AAC7C,QAAM,EAAE,UAAU,QAAQ,IAAI,gBAAgB;AAC9C,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,sBAAsB,YAAY;AAAA,UAClC,kBAAkB,YAAY;AAAA,QAChC;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,YACQ,wBAAI,QAAQ,EAAE,WAAW,uFAAuF,SAAS,CAAC;AAAA,QAC1I,CAAC,CAAC,gBAA4B;AAAA,UAC5B;AAAA,UACA;AAAA,YACE,SAAS,MAAM,SAAS,KAAK;AAAA,YAC7B,MAAM;AAAA,YACN,WAAW;AAAA,cACT;AAAA,cACA;AAAA,gBACE,oEAAoE,YAAY;AAAA,gBAChF,0DAA0D,YAAY;AAAA,cACxE;AAAA,YACF;AAAA,YACA,cAA0B,wBAAI,WAAW,CAAC,CAAC;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,YAAY,OAAO,OAAO,OAAO,EAAE,KAAK,CAAC;", "names": []}