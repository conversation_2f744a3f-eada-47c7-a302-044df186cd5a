import {
  CategoryTree
} from "./chunk-DM3HXBNN.js";
import {
  insertCategoryTreeItem
} from "./chunk-RGG2AVZE.js";
import {
  transformNullableFormData
} from "./chunk-6JFXN7BR.js";
import {
  HandleInput
} from "./chunk-DHII2FGS.js";
import "./chunk-7ALMK6OQ.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateProductCategory,
  useProductCategories
} from "./chunk-ZJNBJBHK.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Button,
  Heading,
  Input,
  ProgressTabs,
  Select,
  Text,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/category-create-NVRG7B6E.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var CreateCategoryDetails = ({ form }) => {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("categories.create.header") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("categories.create.hint") })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { autoComplete: "off", ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "handle",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, tooltip: t2("collections.handleTooltip"), children: t2("fields.handle") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(HandleInput, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "description",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.description") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "status",
          render: ({ field: { ref, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("categories.fields.status.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                  (0, import_jsx_runtime.jsx)(Select.Item, { value: "active", children: t2("categories.fields.status.active") }),
                  (0, import_jsx_runtime.jsx)(Select.Item, { value: "inactive", children: t2("categories.fields.status.inactive") })
                ] })
              ] }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "visibility",
          render: ({ field: { ref, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("categories.fields.visibility.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                  (0, import_jsx_runtime.jsx)(Select.Item, { value: "public", children: t2("categories.fields.visibility.public") }),
                  (0, import_jsx_runtime.jsx)(Select.Item, { value: "internal", children: t2("categories.fields.visibility.internal") })
                ] })
              ] }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] })
  ] }) });
};
var ID = "new-item";
var CreateCategoryNesting = ({
  form,
  shouldFreeze
}) => {
  const { t: t2 } = useTranslation();
  const [snapshot, setSnapshot] = (0, import_react2.useState)([]);
  const { product_categories, isPending, isError, error } = useProductCategories({
    parent_category_id: "null",
    limit: 9999,
    fields: "id,name,parent_category_id,rank,category_children,rank",
    include_descendants_tree: true
  });
  const parentCategoryId = useWatch({
    control: form.control,
    name: "parent_category_id"
  });
  const watchedRank = useWatch({
    control: form.control,
    name: "rank"
  });
  const watchedName = useWatch({
    control: form.control,
    name: "name"
  });
  const value = (0, import_react2.useMemo)(() => {
    const temp = {
      id: ID,
      name: watchedName,
      parent_category_id: parentCategoryId,
      rank: watchedRank,
      category_children: null
    };
    return insertCategoryTreeItem(product_categories ?? [], temp);
  }, [product_categories, watchedName, parentCategoryId, watchedRank]);
  const handleChange = ({
    parentId,
    index
  }, list) => {
    form.setValue("parent_category_id", parentId, {
      shouldDirty: true,
      shouldTouch: true
    });
    form.setValue("rank", index, {
      shouldDirty: true,
      shouldTouch: true
    });
    setSnapshot(list);
  };
  if (isError) {
    throw error;
  }
  const ready = !isPending && !!product_categories;
  return (0, import_jsx_runtime2.jsx)(
    CategoryTree,
    {
      value: shouldFreeze ? snapshot : value,
      enableDrag: (item) => item.id === ID,
      onChange: handleChange,
      renderValue: (item) => {
        if (item.id === ID) {
          return (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-3", children: [
            (0, import_jsx_runtime2.jsx)("span", { children: item.name }),
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", color: "blue", children: t2("categories.fields.new.label") })
          ] });
        }
        return item.name;
      },
      isLoading: !ready
    }
  );
};
var CreateCategoryDetailsSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  handle: z.string().optional(),
  status: z.enum(["active", "inactive"]),
  visibility: z.enum(["public", "internal"])
});
var CreateCategorySchema = z.object({
  rank: z.number().nullable(),
  parent_category_id: z.string().nullable()
}).merge(CreateCategoryDetailsSchema);
var CreateCategoryForm = ({
  parentCategoryId
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [activeTab, setActiveTab] = (0, import_react.useState)(
    "details"
    /* DETAILS */
  );
  const [validDetails, setValidDetails] = (0, import_react.useState)(false);
  const [shouldFreeze, setShouldFreeze] = (0, import_react.useState)(false);
  const form = useForm({
    defaultValues: {
      name: "",
      description: "",
      handle: "",
      status: "active",
      visibility: "public",
      rank: parentCategoryId ? 0 : null,
      parent_category_id: parentCategoryId
    },
    resolver: t(CreateCategorySchema)
  });
  const handleTabChange = (tab) => {
    if (tab === "organize") {
      const { name, handle, description, status, visibility } = form.getValues();
      const result = CreateCategoryDetailsSchema.safeParse({
        name,
        handle,
        description,
        status,
        visibility
      });
      if (!result.success) {
        result.error.errors.forEach((error) => {
          form.setError(error.path.join("."), {
            type: "manual",
            message: error.message
          });
        });
        return;
      }
      form.clearErrors();
      setValidDetails(true);
    }
    setActiveTab(tab);
  };
  const { mutateAsync, isPending } = useCreateProductCategory();
  const handleSubmit = form.handleSubmit((data) => {
    const { visibility, status, parent_category_id, rank, name, ...rest } = data;
    const parsedData = transformNullableFormData(rest, false);
    setShouldFreeze(true);
    mutateAsync(
      {
        name,
        ...parsedData,
        parent_category_id: parent_category_id ?? void 0,
        rank: rank ?? void 0,
        is_active: status === "active",
        is_internal: visibility === "internal"
      },
      {
        onSuccess: ({ product_category }) => {
          toast.success(
            t2("categories.create.successToast", {
              name: product_category.name
            })
          );
          handleSuccess(`/categories/${product_category.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
          setShouldFreeze(false);
        }
      }
    );
  });
  const nestingStatus = ((_a = form.getFieldState("parent_category_id")) == null ? void 0 : _a.isDirty) || ((_b = form.getFieldState("rank")) == null ? void 0 : _b.isDirty) || activeTab === "organize" ? "in-progress" : "not-started";
  const detailsStatus = validDetails ? "completed" : "in-progress";
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime3.jsx)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: (0, import_jsx_runtime3.jsxs)(
        ProgressTabs,
        {
          value: activeTab,
          onValueChange: (tab) => handleTabChange(tab),
          className: "flex size-full flex-col",
          children: [
            (0, import_jsx_runtime3.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime3.jsx)("div", { className: "flex w-full items-center justify-between", children: (0, import_jsx_runtime3.jsx)("div", { className: "-my-2 w-full max-w-[400px] border-l", children: (0, import_jsx_runtime3.jsxs)(ProgressTabs.List, { className: "grid w-full grid-cols-2", children: [
              (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "details",
                  status: detailsStatus,
                  className: "w-full min-w-0 overflow-hidden",
                  children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: t2("categories.create.tabs.details") })
                }
              ),
              (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "organize",
                  status: nestingStatus,
                  className: "w-full min-w-0 overflow-hidden",
                  children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: t2("categories.create.tabs.organize") })
                }
              )
            ] }) }) }) }),
            (0, import_jsx_runtime3.jsxs)(RouteFocusModal.Body, { className: "flex size-full flex-col overflow-auto", children: [
              (0, import_jsx_runtime3.jsx)(ProgressTabs.Content, { value: "details", children: (0, import_jsx_runtime3.jsx)(CreateCategoryDetails, { form }) }),
              (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Content,
                {
                  value: "organize",
                  className: "bg-ui-bg-subtle flex-1",
                  children: (0, import_jsx_runtime3.jsx)(CreateCategoryNesting, { form, shouldFreeze })
                }
              )
            ] }),
            (0, import_jsx_runtime3.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime3.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
              activeTab === "organize" ? (0, import_jsx_runtime3.jsx)(
                Button,
                {
                  size: "small",
                  variant: "primary",
                  type: "submit",
                  isLoading: isPending,
                  children: t2("actions.save")
                },
                "submit-btn"
              ) : (0, import_jsx_runtime3.jsx)(
                Button,
                {
                  size: "small",
                  variant: "primary",
                  type: "button",
                  onClick: () => handleTabChange(
                    "organize"
                    /* ORGANIZE */
                  ),
                  children: t2("actions.continue")
                },
                "continue-btn"
              )
            ] }) })
          ]
        }
      )
    }
  ) });
};
var CategoryCreate = () => {
  const [searchParams] = useSearchParams();
  const parentCategoryId = searchParams.get("parent_category_id");
  return (0, import_jsx_runtime4.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime4.jsx)(CreateCategoryForm, { parentCategoryId }) });
};
export {
  CategoryCreate as Component
};
//# sourceMappingURL=category-create-NVRG7B6E-KKXZYVGH.js.map
