import {
  GeoZoneForm
} from "./chunk-2XJFZ7O4.js";
import "./chunk-2VXAS6UY.js";
import {
  GEO_ZONE_STACKED_MODAL_ID
} from "./chunk-S4XCFSZC.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import "./chunk-ETYCFWJC.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  countries
} from "./chunk-XGFC5LFP.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useUpdateFulfillmentSetServiceZone
} from "./chunk-VFNUYVQL.js";
import "./chunk-BZZVTH5X.js";
import {
  useStockLocation
} from "./chunk-CXC4I63N.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  json,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-manage-areas-FBMF6VLN.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditeServiceZoneSchema = z.object({
  countries: z.array(z.object({ iso_2: z.string().min(2), display_name: z.string() })).min(1)
});
function EditServiceZoneAreasForm({
  fulfillmentSetId,
  locationId,
  zone
}) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      countries: zone.geo_zones.map((z2) => {
        const country = countries.find((c) => c.iso_2 === z2.country_code);
        return {
          iso_2: z2.country_code,
          display_name: (country == null ? void 0 : country.display_name) || z2.country_code.toUpperCase()
        };
      })
    },
    resolver: t(EditeServiceZoneSchema)
  });
  const { mutateAsync: editServiceZone, isPending: isLoading } = useUpdateFulfillmentSetServiceZone(fulfillmentSetId, zone.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await editServiceZone(
      {
        geo_zones: data.countries.map(({ iso_2 }) => ({
          country_code: iso_2,
          type: "country"
        }))
      },
      {
        onSuccess: () => {
          toast.success(
            t2("stockLocations.serviceZones.manageAreas.successToast", {
              name: zone.name
            })
          );
          handleSuccess(`/settings/locations/${locationId}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-auto", children: (0, import_jsx_runtime.jsxs)(StackedFocusModal, { id: GEO_ZONE_STACKED_MODAL_ID, children: [
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
            (0, import_jsx_runtime.jsx)(Heading, { children: t2("stockLocations.serviceZones.manageAreas.header", {
              name: zone.name
            }) }),
            (0, import_jsx_runtime.jsx)(GeoZoneForm, { form })
          ] }) }),
          (0, import_jsx_runtime.jsx)(GeoZoneForm.AreaDrawer, { form })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
}
var LocationServiceZoneManageAreas = () => {
  var _a, _b;
  const { location_id, fset_id, zone_id } = useParams();
  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {
    fields: "*fulfillment_sets.service_zones.geo_zones,fulfillment_sets.service_zones.name"
  });
  const zone = (_b = (_a = stock_location == null ? void 0 : stock_location.fulfillment_sets) == null ? void 0 : _a.find((f) => f.id === fset_id)) == null ? void 0 : _b.service_zones.find((z2) => z2.id === zone_id);
  if (!isPending && !isFetching && !zone) {
    throw json(
      { message: `Service zone with ID ${zone_id} was not found` },
      404
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: zone && (0, import_jsx_runtime2.jsx)(
    EditServiceZoneAreasForm,
    {
      zone,
      fulfillmentSetId: fset_id,
      locationId: location_id
    }
  ) });
};
export {
  LocationServiceZoneManageAreas as Component
};
//# sourceMappingURL=location-service-zone-manage-areas-FBMF6VLN-TJ5O72AL.js.map
