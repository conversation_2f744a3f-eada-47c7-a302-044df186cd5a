import {
  useDate
} from "./chunk-AGRADJYQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Tooltip,
  createDataTableColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-4BTG27L5.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var columnHelper = createDataTableColumnHelper();
var useDataTableDateColumns = () => {
  const { t } = useTranslation();
  const { getFullDate } = useDate();
  return (0, import_react.useMemo)(() => {
    return [
      columnHelper.accessor("created_at", {
        header: t("fields.createdAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Tooltip,
            {
              content: getFullDate({
                date: row.original.created_at,
                includeTime: true
              }),
              children: (0, import_jsx_runtime.jsx)("span", { children: getFullDate({ date: row.original.created_at }) })
            }
          );
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      }),
      columnHelper.accessor("updated_at", {
        header: t("fields.updatedAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Tooltip,
            {
              content: getFullDate({
                date: row.original.updated_at,
                includeTime: true
              }),
              children: (0, import_jsx_runtime.jsx)("span", { children: getFullDate({ date: row.original.updated_at }) })
            }
          );
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      })
    ];
  }, [t, getFullDate]);
};

export {
  useDataTableDateColumns
};
//# sourceMappingURL=chunk-WNW4SNUS.js.map
