{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LBIOZZPA.mjs"], "sourcesContent": ["import {\n  IconAvatar\n} from \"./chunk-EQTBJSBZ.mjs\";\n\n// src/components/common/sidebar-link/sidebar-link.tsx\nimport { Link } from \"react-router-dom\";\nimport { TriangleRightMini } from \"@medusajs/icons\";\nimport { Text } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SidebarLink = ({\n  to,\n  labelKey,\n  descriptionKey,\n  icon\n}) => {\n  return /* @__PURE__ */ jsx(Link, { to, className: \"group outline-none\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-2 px-2 pb-2\", children: /* @__PURE__ */ jsx(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component transition-fg hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed group-focus-visible:shadow-borders-interactive-with-active rounded-md px-4 py-2\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-4\", children: [\n    /* @__PURE__ */ jsx(IconAvatar, { children: icon }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-col\", children: [\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: labelKey }),\n      /* @__PURE__ */ jsx(\n        Text,\n        {\n          size: \"small\",\n          leading: \"compact\",\n          className: \"text-ui-fg-subtle\",\n          children: descriptionKey\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx(\"div\", { className: \"flex size-7 items-center justify-center\", children: /* @__PURE__ */ jsx(TriangleRightMini, { className: \"text-ui-fg-muted\" }) })\n  ] }) }) }) });\n};\n\nexport {\n  SidebarLink\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,yBAA0B;AAC1B,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,wBAAI,MAAM,EAAE,IAAI,WAAW,sBAAsB,cAA0B,wBAAI,OAAO,EAAE,WAAW,iCAAiC,cAA0B,wBAAI,OAAO,EAAE,WAAW,gNAAgN,cAA0B,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,QACne,wBAAI,YAAY,EAAE,UAAU,KAAK,CAAC;AAAA,QAClC,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,UACzD,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,SAAS,CAAC;AAAA,UACnF;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,OAAO,EAAE,WAAW,2CAA2C,cAA0B,wBAAI,mBAAmB,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC;AAAA,EAC1K,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACd;", "names": []}