{"version": 3, "sources": ["../../@medusajs/dashboard/dist/region-add-countries-IE5I4ECQ.mjs"], "sourcesContent": ["import {\n  useCountries,\n  useCountryTableColumns,\n  useCountryTableQuery\n} from \"./chunk-NOAFLTPV.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  countries\n} from \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useRegion,\n  useUpdateRegion\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/regions/region-add-countries/region-add-countries.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/regions/region-add-countries/components/add-countries-form/add-countries-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { Button, Checkbox, toast } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddCountriesSchema = zod.object({\n  countries: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"ac\";\nvar AddCountriesForm = ({ region }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const [rowSelection, setRowSelection] = useState({});\n  const form = useForm({\n    defaultValues: {\n      countries: []\n    },\n    resolver: zodResolver(AddCountriesSchema)\n  });\n  const { setValue } = form;\n  useEffect(() => {\n    const ids = Object.keys(rowSelection).filter((k) => rowSelection[k]);\n    setValue(\"countries\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n  }, [rowSelection, setValue]);\n  const { searchParams, raw } = useCountryTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { countries: countries2, count } = useCountries({\n    countries: countries.map((c, i) => ({\n      display_name: c.display_name,\n      name: c.name,\n      id: i,\n      iso_2: c.iso_2,\n      iso_3: c.iso_3,\n      num_code: c.num_code,\n      region_id: null,\n      region: {}\n    })),\n    ...searchParams\n  });\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: countries2 || [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: (row) => {\n      return region.countries?.findIndex((c) => c.iso_2 === row.original.iso_2) === -1;\n    },\n    getRowId: (row) => row.iso_2,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    },\n    prefix: PREFIX\n  });\n  const { mutateAsync, isPending: isLoading } = useUpdateRegion(region.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const payload = [\n      ...region.countries?.map((c) => c.iso_2) ?? [],\n      ...values.countries\n    ];\n    await mutateAsync(\n      {\n        countries: payload\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"regions.toast.countries\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", isLoading, type: \"submit\", children: t(\"actions.add\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            columns,\n            pageSize: PAGE_SIZE,\n            count,\n            search: \"autofocus\",\n            pagination: true,\n            layout: \"fill\",\n            orderBy: [\n              { key: \"display_name\", label: t(\"fields.name\") },\n              { key: \"iso_2\", label: t(\"fields.code\") }\n            ],\n            queryObject: raw,\n            prefix: PREFIX\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCountryTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreselected = !row.getCanSelect();\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: row.getIsSelected() || isPreselected,\n              disabled: isPreselected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [base]\n  );\n};\n\n// src/routes/regions/region-add-countries/region-add-countries.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar RegionAddCountries = () => {\n  const { id } = useParams();\n  const {\n    region,\n    isPending: isLoading,\n    isError,\n    error\n  } = useRegion(id, {\n    fields: \"*payment_providers\"\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && region && /* @__PURE__ */ jsx2(AddCountriesForm, { region }) });\n};\nexport {\n  RegionAddCountries as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,mBAA6C;AAK7C,yBAA0B;AAqJ1B,IAAAA,sBAA4B;AApJ5B,IAAI,qBAAyB,WAAO;AAAA,EAClC,WAAe,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAC1C,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,mBAAmB,CAAC,EAAE,OAAO,MAAM;AACrC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,WAAW,CAAC;AAAA,IACd;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,8BAAU,MAAM;AACd,UAAM,MAAM,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;AACnE,aAAS,aAAa,KAAK;AAAA,MACzB,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AAAA,EACH,GAAG,CAAC,cAAc,QAAQ,CAAC;AAC3B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,WAAW,YAAY,MAAM,IAAI,aAAa;AAAA,IACpD,WAAW,UAAU,IAAI,CAAC,GAAG,OAAO;AAAA,MAClC,cAAc,EAAE;AAAA,MAChB,MAAM,EAAE;AAAA,MACR,IAAI;AAAA,MACJ,OAAO,EAAE;AAAA,MACT,OAAO,EAAE;AAAA,MACT,UAAU,EAAE;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ,CAAC;AAAA,IACX,EAAE;AAAA,IACF,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB,CAAC,QAAQ;AArGjC;AAsGM,eAAO,YAAO,cAAP,mBAAkB,UAAU,CAAC,MAAM,EAAE,UAAU,IAAI,SAAS,YAAW;AAAA,IAChF;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI,gBAAgB,OAAO,EAAE;AACvE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AAjH3D;AAkHI,UAAM,UAAU;AAAA,MACd,KAAG,YAAO,cAAP,mBAAkB,IAAI,CAAC,MAAM,EAAE,WAAU,CAAC;AAAA,MAC7C,GAAG,OAAO;AAAA,IACZ;AACA,UAAM;AAAA,MACJ;AAAA,QACE,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,yBAAyB,CAAC;AAC1C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,WAAW,MAAM,UAAU,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,QACtG,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,mBAAmB,cAA0B;AAAA,UAClG;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,EAAE,KAAK,gBAAgB,OAAOA,GAAE,aAAa,EAAE;AAAA,cAC/C,EAAE,KAAK,SAAS,OAAOA,GAAE,aAAa,EAAE;AAAA,YAC1C;AAAA,YACA,aAAa;AAAA,YACb,QAAQ;AAAA,UACV;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc,KAAK;AAAA,cAChC,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,UAAU,IAAI;AAAA,IAChB,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,cAA0B,oBAAAA,KAAK,kBAAkB,EAAE,OAAO,CAAC,EAAE,CAAC;AACvI;", "names": ["import_jsx_runtime", "t", "jsx2"]}