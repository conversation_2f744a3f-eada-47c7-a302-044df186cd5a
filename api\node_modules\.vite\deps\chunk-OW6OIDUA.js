import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  useDate
} from "./chunk-AGRADJYQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Tooltip
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-3OHUAQUF.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DateCell = ({ date }) => {
  const { getFullDate } = useDate();
  if (!date) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)(
    Tooltip,
    {
      className: "z-10",
      content: (0, import_jsx_runtime.jsx)("span", { className: "text-pretty", children: `${getFullDate({
        date,
        includeTime: true
      })}` }),
      children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: getFullDate({ date, includeTime: false }) })
    }
  ) });
};
var DateHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t("fields.date") }) });
};

export {
  DateCell,
  DateHeader
};
//# sourceMappingURL=chunk-OW6OIDUA.js.map
