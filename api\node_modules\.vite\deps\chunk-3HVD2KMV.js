import {
  ordersQueryKeys
} from "./chunk-IBPOGPJN.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-CLRTJ6DI.mjs
var PRODUCT_VARIANT_QUERY_KEY = "product_variant";
var productVariantQueryKeys = queryKeysFactory(
  PRODUCT_VARIANT_QUERY_KEY
);
var useVariants = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.productVariant.list(query),
    queryKey: productVariantQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var REFUND_REASON_QUERY_KEY = "refund-reason";
var refundReasonQueryKeys = queryKeysFactory(REFUND_REASON_QUERY_KEY);
var useRefundReasons = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.refundReason.list(query),
    queryKey: [],
    ...options
  });
  return { ...data, ...rest };
};
var TAGS_QUERY_KEY = "tags";
var productTagsQueryKeys = queryKeysFactory(TAGS_QUERY_KEY);
var useProductTag = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: productTagsQueryKeys.detail(id, query),
    queryFn: async () => sdk.admin.productTag.retrieve(id),
    ...options
  });
  return { ...data, ...rest };
};
var useProductTags = (query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: productTagsQueryKeys.list(query),
    queryFn: async () => sdk.admin.productTag.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateProductTag = (query, options) => {
  return useMutation({
    mutationFn: async (data) => sdk.admin.productTag.create(data, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: productTagsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductTag = (id, query, options) => {
  return useMutation({
    mutationFn: async (data) => sdk.admin.productTag.update(id, data, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: productTagsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: productTagsQueryKeys.detail(data.product_tag.id, query)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteProductTag = (id, options) => {
  return useMutation({
    mutationFn: async () => sdk.admin.productTag.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: productTagsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: productTagsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var FULFILLMENTS_QUERY_KEY = "fulfillments";
var fulfillmentsQueryKeys = queryKeysFactory(FULFILLMENTS_QUERY_KEY);
var NOTIFICATION_QUERY_KEY = "notification";
var notificationQueryKeys = queryKeysFactory(NOTIFICATION_QUERY_KEY);
var useNotifications = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.notification.list(query),
    queryKey: notificationQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var PAYMENT_COLLECTION_QUERY_KEY = "payment-collection";
var paymentCollectionQueryKeys = queryKeysFactory(
  PAYMENT_COLLECTION_QUERY_KEY
);
var useMarkPaymentCollectionAsPaid = (orderId, paymentCollectionId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.paymentCollection.markAsPaid(paymentCollectionId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: paymentCollectionQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  productVariantQueryKeys,
  useVariants,
  useRefundReasons,
  productTagsQueryKeys,
  useProductTag,
  useProductTags,
  useCreateProductTag,
  useUpdateProductTag,
  useDeleteProductTag,
  notificationQueryKeys,
  useNotifications,
  useMarkPaymentCollectionAsPaid
};
//# sourceMappingURL=chunk-3HVD2KMV.js.map
