import {
  ConditionalPriceForm,
  ConditionalPriceSchema,
  ShippingOptionPriceProvider,
  buildShippingOptionPriceRules,
  useShippingOptionPriceColumns
} from "./chunk-XVMTVC52.js";
import {
  CONDITIONAL_PRICES_STACKED_MODAL_ID,
  ShippingOptionPriceType
} from "./chunk-S4XCFSZC.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import "./chunk-UDMOPZAP.js";
import {
  DataGrid
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import {
  SwitchBox
} from "./chunk-HK2C7UM6.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import {
  useCreateShippingOptions
} from "./chunk-BZZVTH5X.js";
import {
  useFulfillmentProviderOptions,
  useStockLocation
} from "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  useRegions
} from "./chunk-AKXAI3UV.js";
import {
  usePricePreferences
} from "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  json,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Input,
  ProgressTabs,
  RadioGroup,
  Select,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-shipping-option-create-7KOH5VVW.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var CreateShippingOptionDetailsForm = ({
  form,
  isReturn = false,
  zone,
  locationId,
  fulfillmentProviderOptions,
  selectedProviderId,
  type
}) => {
  const { t: t2 } = useTranslation();
  const isPickup = type === "pickup";
  const shippingProfiles = useComboboxData({
    queryFn: (params) => sdk.admin.shippingProfile.list(params),
    queryKey: ["shipping_profiles"],
    getOptions: (data) => data.shipping_profiles.map((profile) => ({
      label: profile.name,
      value: profile.id
    }))
  });
  const fulfillmentProviders = useComboboxData({
    queryFn: (params) => sdk.admin.fulfillmentProvider.list({
      ...params,
      stock_location_id: locationId
    }),
    queryKey: ["fulfillment_providers"],
    getOptions: (data) => data.fulfillment_providers.map((provider) => ({
      label: formatProvider(provider.id),
      value: provider.id
    }))
  });
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-6 py-16", children: [
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2(
        `stockLocations.shippingOptions.create.${isPickup ? "pickup" : isReturn ? "returns" : "shipping"}.header`,
        {
          zone: zone.name
        }
      ) }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2(
        `stockLocations.shippingOptions.create.${isReturn ? "returns" : isPickup ? "pickup" : "shipping"}.hint`
      ) })
    ] }),
    !isPickup && (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "price_type",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("stockLocations.shippingOptions.fields.priceType.label") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
              RadioGroup,
              {
                className: "grid grid-cols-1 gap-4 md:grid-cols-2",
                ...field,
                onValueChange: field.onChange,
                children: [
                  (0, import_jsx_runtime.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      className: "flex-1",
                      value: "flat",
                      label: t2(
                        "stockLocations.shippingOptions.fields.priceType.options.fixed.label"
                      ),
                      description: t2(
                        "stockLocations.shippingOptions.fields.priceType.options.fixed.hint"
                      )
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      className: "flex-1",
                      value: "calculated",
                      label: t2(
                        "stockLocations.shippingOptions.fields.priceType.options.calculated.label"
                      ),
                      description: t2(
                        "stockLocations.shippingOptions.fields.priceType.options.calculated.hint"
                      )
                    }
                  )
                ]
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "shipping_profile_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("stockLocations.shippingOptions.fields.profile") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: shippingProfiles.options,
                  searchValue: shippingProfiles.searchValue,
                  onSearchValueChange: shippingProfiles.onSearchValueChange,
                  disabled: shippingProfiles.disabled
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "provider_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(
                Form.Label,
                {
                  tooltip: t2(
                    "stockLocations.fulfillmentProviders.shippingOptionsTooltip"
                  ),
                  children: t2("stockLocations.shippingOptions.fields.provider")
                }
              ),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  onChange: (e) => {
                    field.onChange(e);
                    form.setValue("fulfillment_option_id", "");
                  },
                  options: fulfillmentProviders.options,
                  searchValue: fulfillmentProviders.searchValue,
                  onSearchValueChange: fulfillmentProviders.onSearchValueChange,
                  disabled: fulfillmentProviders.disabled
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "fulfillment_option_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2(
                "stockLocations.shippingOptions.fields.fulfillmentOption"
              ) }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_react2.createElement)(
                Select,
                {
                  ...field,
                  onValueChange: field.onChange,
                  disabled: !selectedProviderId,
                  key: selectedProviderId
                },
                (0, import_jsx_runtime.jsx)(Select.Trigger, { ref: field.ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                (0, import_jsx_runtime.jsx)(Select.Content, { children: fulfillmentProviderOptions == null ? void 0 : fulfillmentProviderOptions.filter((fo) => !!fo.is_return === isReturn).map((option) => (0, import_jsx_runtime.jsx)(Select.Item, { value: option.id, children: option.name || option.id }, option.id)) })
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)(Divider, {}),
    (0, import_jsx_runtime.jsx)(
      SwitchBox,
      {
        control: form.control,
        name: "enabled_in_store",
        label: t2("stockLocations.shippingOptions.fields.enableInStore.label"),
        description: t2(
          "stockLocations.shippingOptions.fields.enableInStore.hint"
        )
      }
    )
  ] }) });
};
var CreateShippingOptionsPricesForm = ({
  form,
  type
}) => {
  const isPickup = type === "pickup";
  const { getIsOpen, setIsOpen } = useStackedModal();
  const [selectedPrice, setSelectedPrice] = (0, import_react3.useState)(null);
  const onOpenConditionalPricesModal = (info) => {
    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, true);
    setSelectedPrice(info);
  };
  const onCloseConditionalPricesModal = () => {
    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, false);
    setSelectedPrice(null);
  };
  const {
    store,
    isLoading: isStoreLoading,
    isError: isStoreError,
    error: storeError
  } = useStore();
  const currencies = (0, import_react3.useMemo)(
    () => {
      var _a;
      return ((_a = store == null ? void 0 : store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)) || [];
    },
    [store]
  );
  const {
    regions,
    isLoading: isRegionsLoading,
    isError: isRegionsError,
    error: regionsError
  } = useRegions({
    fields: "id,name,currency_code",
    limit: 999
  });
  const { price_preferences: pricePreferences } = usePricePreferences({});
  const { setCloseOnEscape } = useRouteModal();
  const name = useWatch({ control: form.control, name: "name" });
  const columns = useShippingOptionPriceColumns({
    name,
    currencies,
    regions,
    pricePreferences
  });
  const isLoading = isStoreLoading || !store || isRegionsLoading || !regions;
  const data = (0, import_react3.useMemo)(
    () => [[...currencies || [], ...regions || []]],
    [currencies, regions]
  );
  (0, import_react3.useEffect)(() => {
    if (!isLoading && isPickup) {
      if (currencies.length > 0) {
        currencies.forEach((currency) => {
          form.setValue(`currency_prices.${currency}`, "0");
        });
      }
      if (regions.length > 0) {
        regions.forEach((region) => {
          form.setValue(`region_prices.${region.id}`, "0");
        });
      }
    }
  }, [isLoading, isPickup]);
  if (isStoreError) {
    throw storeError;
  }
  if (isRegionsError) {
    throw regionsError;
  }
  return (0, import_jsx_runtime2.jsx)(
    StackedFocusModal,
    {
      id: CONDITIONAL_PRICES_STACKED_MODAL_ID,
      onOpenChangeCallback: (open) => {
        if (!open) {
          setSelectedPrice(null);
        }
      },
      children: (0, import_jsx_runtime2.jsx)(
        ShippingOptionPriceProvider,
        {
          onOpenConditionalPricesModal,
          onCloseConditionalPricesModal,
          children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex size-full flex-col divide-y overflow-hidden", children: [
            (0, import_jsx_runtime2.jsx)(
              DataGrid,
              {
                isLoading,
                data,
                columns,
                state: form,
                onEditingChange: (editing) => setCloseOnEscape(!editing),
                disableInteractions: getIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID)
              }
            ),
            selectedPrice && (0, import_jsx_runtime2.jsx)(ConditionalPriceForm, { info: selectedPrice, variant: "create" })
          ] })
        }
      )
    }
  );
};
var CreateShippingOptionDetailsSchema = z.object({
  name: z.string().min(1),
  price_type: z.nativeEnum(ShippingOptionPriceType),
  enabled_in_store: z.boolean(),
  shipping_profile_id: z.string().min(1),
  provider_id: z.string().min(1),
  fulfillment_option_id: z.string().min(1)
});
var ShippingOptionConditionalPriceSchema = z.object({
  conditional_region_prices: z.record(
    z.string(),
    z.array(ConditionalPriceSchema).optional()
  ),
  conditional_currency_prices: z.record(
    z.string(),
    z.array(ConditionalPriceSchema).optional()
  )
});
var CreateShippingOptionSchema = z.object({
  region_prices: z.record(z.string(), z.string().optional()),
  currency_prices: z.record(z.string(), z.string().optional())
}).merge(CreateShippingOptionDetailsSchema).merge(ShippingOptionConditionalPriceSchema);
function CreateShippingOptionsForm({
  zone,
  isReturn,
  locationId,
  type
}) {
  var _a, _b;
  const [activeTab, setActiveTab] = (0, import_react.useState)(
    "details"
    /* DETAILS */
  );
  const [validDetails, setValidDetails] = (0, import_react.useState)(false);
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      price_type: "flat",
      enabled_in_store: true,
      shipping_profile_id: "",
      provider_id: "",
      fulfillment_option_id: "",
      region_prices: {},
      currency_prices: {},
      conditional_region_prices: {},
      conditional_currency_prices: {}
    },
    resolver: t(CreateShippingOptionSchema)
  });
  const selectedProviderId = useWatch({
    control: form.control,
    name: "provider_id"
  });
  const { fulfillment_options: fulfillmentProviderOptions } = useFulfillmentProviderOptions(selectedProviderId, {
    enabled: !!selectedProviderId
  });
  const isCalculatedPriceType = form.watch("price_type") === "calculated";
  const { mutateAsync, isPending: isLoading } = useCreateShippingOptions();
  const handleSubmit = form.handleSubmit(async (data) => {
    const currencyPrices = Object.entries(data.currency_prices).map(([code, value]) => {
      if (!value) {
        return void 0;
      }
      return {
        currency_code: code,
        amount: castNumber(value)
      };
    }).filter((p) => !!p);
    const regionPrices = Object.entries(data.region_prices).map(([region_id, value]) => {
      if (!value) {
        return void 0;
      }
      return {
        region_id,
        amount: castNumber(value)
      };
    }).filter((p) => !!p);
    const conditionalRegionPrices = Object.entries(
      data.conditional_region_prices
    ).flatMap(([region_id, value]) => {
      const prices = (value == null ? void 0 : value.map((rule) => ({
        region_id,
        amount: castNumber(rule.amount),
        rules: buildShippingOptionPriceRules(rule)
      }))) || [];
      return prices == null ? void 0 : prices.filter(Boolean);
    });
    const conditionalCurrencyPrices = Object.entries(
      data.conditional_currency_prices
    ).flatMap(([currency_code, value]) => {
      const prices = (value == null ? void 0 : value.map((rule) => ({
        currency_code,
        amount: castNumber(rule.amount),
        rules: buildShippingOptionPriceRules(rule)
      }))) || [];
      return prices == null ? void 0 : prices.filter(Boolean);
    });
    const allPrices = [
      ...currencyPrices,
      ...conditionalCurrencyPrices,
      ...regionPrices,
      ...conditionalRegionPrices
    ];
    const fulfillmentOptionData = fulfillmentProviderOptions == null ? void 0 : fulfillmentProviderOptions.find(
      (fo) => fo.id === data.fulfillment_option_id
    );
    await mutateAsync(
      {
        name: data.name,
        price_type: data.price_type,
        service_zone_id: zone.id,
        shipping_profile_id: data.shipping_profile_id,
        provider_id: data.provider_id,
        prices: allPrices,
        data: fulfillmentOptionData,
        rules: [
          {
            // eslint-disable-next-line
            value: isReturn ? "true" : "false",
            attribute: "is_return",
            operator: "eq"
          },
          {
            // eslint-disable-next-line
            value: data.enabled_in_store ? "true" : "false",
            attribute: "enabled_in_store",
            operator: "eq"
          }
        ],
        type: {
          // TODO: FETCH TYPES
          label: "Type label",
          description: "Type description",
          code: "type-code"
        }
      },
      {
        onSuccess: ({ shipping_option }) => {
          toast.success(
            t2(
              `stockLocations.shippingOptions.create.${isReturn ? "returns" : "shipping"}.successToast`,
              { name: shipping_option.name }
            )
          );
          handleSuccess(`/settings/locations/${locationId}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  const onTabChange = (tab) => {
    if (tab === "pricing") {
      form.clearErrors();
      const result = CreateShippingOptionDetailsSchema.safeParse({
        ...form.getValues()
      });
      if (!result.success) {
        const [firstError, ...rest] = result.error.errors;
        for (const error of rest) {
          const _path = error.path.join(".");
          form.setError(_path, {
            message: error.message,
            type: error.code
          });
        }
        form.setError(
          firstError.path.join("."),
          {
            message: firstError.message,
            type: firstError.code
          },
          {
            shouldFocus: true
          }
        );
        setValidDetails(false);
        return;
      }
      setValidDetails(true);
    }
    setActiveTab(tab);
  };
  const pricesStatus = ((_a = form.getFieldState("currency_prices")) == null ? void 0 : _a.isDirty) || ((_b = form.getFieldState("region_prices")) == null ? void 0 : _b.isDirty) || activeTab === "pricing" ? "in-progress" : "not-started";
  const detailsStatus = validDetails ? "completed" : "in-progress";
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime3.jsx)(
    KeyboundForm,
    {
      className: "flex h-full flex-col",
      onSubmit: handleSubmit,
      onKeyDown: (e) => {
        const isEnterKey = e.key === "Enter";
        const isModifierPressed = e.metaKey || e.ctrlKey;
        const shouldContinueToPricing = activeTab !== "pricing" && !isCalculatedPriceType;
        if (!isEnterKey) {
          return;
        }
        e.preventDefault();
        if (!isModifierPressed) {
          return;
        }
        if (shouldContinueToPricing) {
          e.stopPropagation();
          onTabChange(
            "pricing"
            /* PRICING */
          );
          return;
        }
        handleSubmit();
      },
      children: (0, import_jsx_runtime3.jsxs)(
        ProgressTabs,
        {
          value: activeTab,
          className: "flex h-full flex-col overflow-hidden",
          onValueChange: (tab) => onTabChange(tab),
          children: [
            (0, import_jsx_runtime3.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime3.jsxs)(ProgressTabs.List, { className: "border-ui-border-base -my-2 ml-2 min-w-0 flex-1 border-l", children: [
              (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "details",
                  status: detailsStatus,
                  className: "w-full max-w-[200px]",
                  children: (0, import_jsx_runtime3.jsx)("span", { className: "w-full cursor-auto overflow-hidden text-ellipsis whitespace-nowrap", children: t2("stockLocations.shippingOptions.create.tabs.details") })
                }
              ),
              !isCalculatedPriceType && (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "pricing",
                  status: pricesStatus,
                  className: "w-full max-w-[200px]",
                  children: (0, import_jsx_runtime3.jsx)("span", { className: "w-full overflow-hidden text-ellipsis whitespace-nowrap", children: t2("stockLocations.shippingOptions.create.tabs.prices") })
                }
              )
            ] }) }),
            (0, import_jsx_runtime3.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
              (0, import_jsx_runtime3.jsx)(
                ProgressTabs.Content,
                {
                  value: "details",
                  className: "size-full overflow-y-auto",
                  children: (0, import_jsx_runtime3.jsx)(
                    CreateShippingOptionDetailsForm,
                    {
                      form,
                      zone,
                      isReturn,
                      type,
                      locationId,
                      fulfillmentProviderOptions: fulfillmentProviderOptions || [],
                      selectedProviderId
                    }
                  )
                }
              ),
              (0, import_jsx_runtime3.jsx)(ProgressTabs.Content, { value: "pricing", className: "size-full", children: (0, import_jsx_runtime3.jsx)(CreateShippingOptionsPricesForm, { form, type }) })
            ] }),
            (0, import_jsx_runtime3.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime3.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
              activeTab === "pricing" || isCalculatedPriceType ? (0, import_jsx_runtime3.jsx)(
                Button,
                {
                  size: "small",
                  className: "whitespace-nowrap",
                  isLoading,
                  type: "submit",
                  children: t2("actions.save")
                },
                "submit-btn"
              ) : (0, import_jsx_runtime3.jsx)(
                Button,
                {
                  size: "small",
                  className: "whitespace-nowrap",
                  isLoading,
                  onClick: () => onTabChange(
                    "pricing"
                    /* PRICING */
                  ),
                  type: "button",
                  children: t2("actions.continue")
                },
                "continue-btn"
              )
            ] }) })
          ]
        }
      )
    }
  ) });
}
var LOC_CREATE_SHIPPING_OPTION_FIELDS = "*fulfillment_sets,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options";
function LocationServiceZoneShippingOptionCreate() {
  var _a, _b;
  const { location_id, fset_id, zone_id } = useParams();
  const [searchParams] = useSearchParams();
  const isReturn = searchParams.has("is_return");
  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {
    fields: LOC_CREATE_SHIPPING_OPTION_FIELDS
  });
  const fulfillmentSet = (_a = stock_location == null ? void 0 : stock_location.fulfillment_sets) == null ? void 0 : _a.find(
    (f) => f.id === fset_id
  );
  if (!isPending && !isFetching && !fulfillmentSet) {
    throw json(
      { message: `Fulfillment set with ID ${fset_id} was not found` },
      404
    );
  }
  const zone = (_b = fulfillmentSet == null ? void 0 : fulfillmentSet.service_zones) == null ? void 0 : _b.find((z2) => z2.id === zone_id);
  if (!isPending && !isFetching && !zone) {
    throw json(
      { message: `Service zone with ID ${zone_id} was not found` },
      404
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsx)(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: zone && (0, import_jsx_runtime4.jsx)(
    CreateShippingOptionsForm,
    {
      zone,
      isReturn,
      locationId: location_id,
      type: fulfillmentSet.type
    }
  ) });
}
export {
  LocationServiceZoneShippingOptionCreate as Component
};
//# sourceMappingURL=location-service-zone-shipping-option-create-7KOH5VVW-4OX76HPV.js.map
