{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-X2Y4KNQI.mjs"], "sourcesContent": ["import {\n  getProvinceByIso2,\n  isProvinceInCountry\n} from \"./chunk-THZJC662.mjs\";\nimport {\n  IconAvatar\n} from \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  getCountryByIso2\n} from \"./chunk-VDBOSWVE.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport {\n  useDeleteTaxRate\n} from \"./chunk-UAZEQNCO.mjs\";\nimport {\n  useDeleteTaxRegion\n} from \"./chunk-LDJKJLBJ.mjs\";\n\n// src/routes/tax-regions/common/components/tax-region-card/tax-region-card.tsx\nimport { Heading, Text, Tooltip, clx } from \"@medusajs/ui\";\nimport ReactCountryFlag from \"react-country-flag\";\nimport { ExclamationCircle, MapPin, Plus, Trash } from \"@medusajs/icons\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/tax-regions/common/hooks.ts\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nvar useDeleteTaxRegionAction = ({\n  taxRegion,\n  to = \"/settings/tax-regions\"\n}) => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteTaxRegion(taxRegion.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"taxRegions.delete.confirmation\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"taxRegions.delete.successToast\"));\n        navigate(to, { replace: true });\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\nvar useDeleteTaxRateAction = (taxRate) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteTaxRate(taxRate.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"taxRegions.taxRates.delete.confirmation\", {\n        name: taxRate.name\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"taxRegions.taxRates.delete.successToast\"));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\n// src/routes/tax-regions/common/components/tax-region-card/tax-region-card.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionCard = ({\n  taxRegion,\n  type = \"list\",\n  variant = \"country\",\n  asLink = true,\n  badge\n}) => {\n  const { t } = useTranslation2();\n  const { id, country_code, province_code } = taxRegion;\n  const country = getCountryByIso2(country_code);\n  const province = getProvinceByIso2(province_code);\n  let name = \"N/A\";\n  let misconfiguredSublevelTooltip = null;\n  if (province || province_code) {\n    name = province ? province : province_code.toUpperCase();\n  } else if (country || country_code) {\n    name = country ? country.display_name : country_code.toUpperCase();\n  }\n  if (country_code && province_code && !isProvinceInCountry(country_code, province_code)) {\n    name = province_code.toUpperCase();\n    misconfiguredSublevelTooltip = t(\n      \"taxRegions.fields.sublevels.tooltips.notPartOfCountry\",\n      {\n        country: country?.display_name,\n        province: province_code.toUpperCase()\n      }\n    );\n  }\n  const showCreateDefaultTaxRate = !taxRegion.tax_rates.filter((tr) => tr.is_default).length && type === \"header\";\n  const Component = /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"group-data-[link=true]:hover:bg-ui-bg-base-hover transition-fg flex flex-col justify-between gap-y-4 px-6 md:flex-row md:items-center md:gap-y-0\",\n        {\n          \"py-4\": type === \"header\",\n          \"py-3\": type === \"list\"\n        }\n      ),\n      children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-4\", children: [\n            /* @__PURE__ */ jsx(IconAvatar, { size: type === \"list\" ? \"small\" : \"large\", children: country_code && !province_code ? /* @__PURE__ */ jsx(\n              \"div\",\n              {\n                className: clx(\n                  \"flex size-fit items-center justify-center overflow-hidden rounded-[1px]\",\n                  {\n                    \"rounded-sm\": type === \"header\"\n                  }\n                ),\n                children: /* @__PURE__ */ jsx(\n                  ReactCountryFlag,\n                  {\n                    countryCode: country_code,\n                    svg: true,\n                    style: type === \"list\" ? { width: \"12px\", height: \"9px\" } : { width: \"16px\", height: \"12px\" },\n                    \"aria-label\": country?.display_name\n                  }\n                )\n              }\n            ) : /* @__PURE__ */ jsx(MapPin, { className: \"text-ui-fg-subtle\" }) }),\n            /* @__PURE__ */ jsx(\"div\", { children: type === \"list\" ? /* @__PURE__ */ jsx(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: name }) : /* @__PURE__ */ jsx(Heading, { children: name }) })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-fit items-center gap-x-2 md:hidden\", children: [\n            misconfiguredSublevelTooltip && /* @__PURE__ */ jsx(Tooltip, { content: misconfiguredSublevelTooltip, children: /* @__PURE__ */ jsx(ExclamationCircle, { className: \"text-ui-tag-orange-icon\" }) }),\n            badge,\n            /* @__PURE__ */ jsx(\n              TaxRegionCardActions,\n              {\n                taxRegion,\n                showCreateDefaultTaxRate\n              }\n            )\n          ] })\n        ] }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"hidden size-fit items-center gap-x-2 md:flex\", children: [\n          misconfiguredSublevelTooltip && /* @__PURE__ */ jsx(Tooltip, { content: misconfiguredSublevelTooltip, children: /* @__PURE__ */ jsx(ExclamationCircle, { className: \"text-ui-tag-orange-icon\" }) }),\n          badge,\n          /* @__PURE__ */ jsx(\n            TaxRegionCardActions,\n            {\n              taxRegion,\n              showCreateDefaultTaxRate\n            }\n          )\n        ] })\n      ]\n    }\n  );\n  if (asLink) {\n    return /* @__PURE__ */ jsx(\n      Link,\n      {\n        to: variant === \"country\" ? `${id}` : `provinces/${id}`,\n        \"data-link\": \"true\",\n        className: \"group block\",\n        children: Component\n      }\n    );\n  }\n  return Component;\n};\nvar TaxRegionCardActions = ({\n  taxRegion,\n  showCreateDefaultTaxRate\n}) => {\n  const { t } = useTranslation2();\n  const to = taxRegion.parent_id ? `/settings/tax-regions/${taxRegion.parent_id}` : void 0;\n  const handleDelete = useDeleteTaxRegionAction({ taxRegion, to });\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        ...showCreateDefaultTaxRate ? [\n          {\n            actions: [\n              {\n                icon: /* @__PURE__ */ jsx(Plus, {}),\n                label: t(\"taxRegions.fields.defaultTaxRate.action\"),\n                to: `tax-rates/create`\n              }\n            ]\n          }\n        ] : [],\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/components/localization/localized-table-pagination/localized-table-pagination.tsx\nimport { Table } from \"@medusajs/ui\";\nimport { forwardRef } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar LocalizedTablePagination = forwardRef((props, ref) => {\n  const { t } = useTranslation3();\n  const translations = {\n    of: t(\"general.of\"),\n    results: t(\"general.results\"),\n    pages: t(\"general.pages\"),\n    prev: t(\"general.prev\"),\n    next: t(\"general.next\")\n  };\n  return /* @__PURE__ */ jsx2(Table.Pagination, { ...props, translations, ref });\n});\nLocalizedTablePagination.displayName = \"LocalizedTablePagination\";\n\nexport {\n  LocalizedTablePagination,\n  useDeleteTaxRateAction,\n  TaxRegionCard\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA,yBAA0B;AA8I1B,mBAA2B;AAE3B,IAAAA,sBAA4B;AA3M5B,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AAAA,EACA,KAAK;AACP,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,mBAAmB,UAAU,EAAE;AACvD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,gCAAgC;AAAA,MAC/C,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,gCAAgC,CAAC;AACjD,iBAAS,IAAI,EAAE,SAAS,KAAK,CAAC;AAAA,MAChC;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAI,yBAAyB,CAAC,YAAY;AACxC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,iBAAiB,QAAQ,EAAE;AACnD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,2CAA2C;AAAA,QACxD,MAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,yCAAyC,CAAC;AAAA,MAC5D;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,IAAI,cAAc,cAAc,IAAI;AAC5C,QAAM,UAAU,iBAAiB,YAAY;AAC7C,QAAM,WAAW,kBAAkB,aAAa;AAChD,MAAI,OAAO;AACX,MAAI,+BAA+B;AACnC,MAAI,YAAY,eAAe;AAC7B,WAAO,WAAW,WAAW,cAAc,YAAY;AAAA,EACzD,WAAW,WAAW,cAAc;AAClC,WAAO,UAAU,QAAQ,eAAe,aAAa,YAAY;AAAA,EACnE;AACA,MAAI,gBAAgB,iBAAiB,CAAC,oBAAoB,cAAc,aAAa,GAAG;AACtF,WAAO,cAAc,YAAY;AACjC,mCAA+B;AAAA,MAC7B;AAAA,MACA;AAAA,QACE,SAAS,mCAAS;AAAA,QAClB,UAAU,cAAc,YAAY;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,QAAM,2BAA2B,CAAC,UAAU,UAAU,OAAO,CAAC,OAAO,GAAG,UAAU,EAAE,UAAU,SAAS;AACvG,QAAM,gBAA4B;AAAA,IAChC;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,QAAQ,SAAS;AAAA,UACjB,QAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,YACQ,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,cACtE,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,gBAC9D,wBAAI,YAAY,EAAE,MAAM,SAAS,SAAS,UAAU,SAAS,UAAU,gBAAgB,CAAC,oBAAgC;AAAA,cACtI;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,cAAc,SAAS;AAAA,kBACzB;AAAA,gBACF;AAAA,gBACA,cAA0B;AAAA,kBACxB;AAAA,kBACA;AAAA,oBACE,aAAa;AAAA,oBACb,KAAK;AAAA,oBACL,OAAO,SAAS,SAAS,EAAE,OAAO,QAAQ,QAAQ,MAAM,IAAI,EAAE,OAAO,QAAQ,QAAQ,OAAO;AAAA,oBAC5F,cAAc,mCAAS;AAAA,kBACzB;AAAA,gBACF;AAAA,cACF;AAAA,YACF,QAAoB,wBAAI,QAAQ,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC;AAAA,gBACrD,wBAAI,OAAO,EAAE,UAAU,SAAS,aAAyB,wBAAI,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,KAAK,CAAC,QAAoB,wBAAI,SAAS,EAAE,UAAU,KAAK,CAAC,EAAE,CAAC;AAAA,UAChN,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,gDAAgD,UAAU;AAAA,YACjG,oCAAgD,wBAAI,SAAS,EAAE,SAAS,8BAA8B,cAA0B,wBAAI,mBAAmB,EAAE,WAAW,0BAA0B,CAAC,EAAE,CAAC;AAAA,YAClM;AAAA,gBACgB;AAAA,cACd;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,YACa,yBAAK,OAAO,EAAE,WAAW,gDAAgD,UAAU;AAAA,UACjG,oCAAgD,wBAAI,SAAS,EAAE,SAAS,8BAA8B,cAA0B,wBAAI,mBAAmB,EAAE,WAAW,0BAA0B,CAAC,EAAE,CAAC;AAAA,UAClM;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ;AACV,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE,IAAI,YAAY,YAAY,GAAG,EAAE,KAAK,aAAa,EAAE;AAAA,QACrD,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,KAAK,UAAU,YAAY,yBAAyB,UAAU,SAAS,KAAK;AAClF,QAAM,eAAe,yBAAyB,EAAE,WAAW,GAAG,CAAC;AAC/D,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN,GAAG,2BAA2B;AAAA,UAC5B;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,UAAsB,wBAAI,MAAM,CAAC,CAAC;AAAA,gBAClC,OAAO,EAAE,yCAAyC;AAAA,gBAClD,IAAI;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,QACF,IAAI,CAAC;AAAA,QACL;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAI,+BAA2B,yBAAW,CAAC,OAAO,QAAQ;AACxD,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe;AAAA,IACnB,IAAI,EAAE,YAAY;AAAA,IAClB,SAAS,EAAE,iBAAiB;AAAA,IAC5B,OAAO,EAAE,eAAe;AAAA,IACxB,MAAM,EAAE,cAAc;AAAA,IACtB,MAAM,EAAE,cAAc;AAAA,EACxB;AACA,aAAuB,oBAAAC,KAAK,MAAM,YAAY,EAAE,GAAG,OAAO,cAAc,IAAI,CAAC;AAC/E,CAAC;AACD,yBAAyB,cAAc;", "names": ["import_jsx_runtime", "jsx2"]}