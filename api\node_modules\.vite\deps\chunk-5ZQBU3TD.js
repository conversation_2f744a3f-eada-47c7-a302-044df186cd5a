// node_modules/@medusajs/dashboard/dist/chunk-7DXVXBSA.mjs
var getCanceledOrderStatus = (t, status) => {
  if (status === "canceled") {
    return { label: t("orders.status.canceled"), color: "red" };
  }
  return null;
};
var getOrderPaymentStatus = (t, status) => {
  const [label, color] = {
    not_paid: [t("orders.payment.status.notPaid"), "red"],
    authorized: [t("orders.payment.status.authorized"), "orange"],
    partially_authorized: [
      t("orders.payment.status.partiallyAuthorized"),
      "red"
    ],
    awaiting: [t("orders.payment.status.awaiting"), "orange"],
    captured: [t("orders.payment.status.captured"), "green"],
    refunded: [t("orders.payment.status.refunded"), "green"],
    partially_refunded: [
      t("orders.payment.status.partiallyRefunded"),
      "orange"
    ],
    partially_captured: [
      t("orders.payment.status.partiallyCaptured"),
      "orange"
    ],
    canceled: [t("orders.payment.status.canceled"), "red"],
    requires_action: [t("orders.payment.status.requiresAction"), "orange"]
  }[status];
  return { label, color };
};
var getOrderFulfillmentStatus = (t, status) => {
  const [label, color] = {
    not_fulfilled: [t("orders.fulfillment.status.notFulfilled"), "red"],
    partially_fulfilled: [
      t("orders.fulfillment.status.partiallyFulfilled"),
      "orange"
    ],
    fulfilled: [t("orders.fulfillment.status.fulfilled"), "green"],
    partially_shipped: [
      t("orders.fulfillment.status.partiallyShipped"),
      "orange"
    ],
    shipped: [t("orders.fulfillment.status.shipped"), "green"],
    delivered: [t("orders.fulfillment.status.delivered"), "green"],
    partially_delivered: [
      t("orders.fulfillment.status.partiallyDelivered"),
      "orange"
    ],
    partially_returned: [
      t("orders.fulfillment.status.partiallyReturned"),
      "orange"
    ],
    returned: [t("orders.fulfillment.status.returned"), "green"],
    canceled: [t("orders.fulfillment.status.canceled"), "red"],
    requires_action: [t("orders.fulfillment.status.requiresAction"), "orange"]
  }[status];
  return { label, color };
};

export {
  getCanceledOrderStatus,
  getOrderPaymentStatus,
  getOrderFulfillmentStatus
};
//# sourceMappingURL=chunk-5ZQBU3TD.js.map
