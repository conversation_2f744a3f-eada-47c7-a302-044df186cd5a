{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-YOVJWH6O.mjs"], "sourcesContent": ["import {\n  DateCell\n} from \"./chunk-3OHUAQUF.mjs\";\nimport {\n  TextCell\n} from \"./chunk-MSDRGCRR.mjs\";\n\n// src/hooks/table/columns/use-product-tag-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useProductTagTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"value\", {\n        header: () => t(\"fields.value\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue() })\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: () => t(\"fields.createdAt\"),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx(DateCell, { date: getValue() });\n        }\n      }),\n      columnHelper.accessor(\"updated_at\", {\n        header: () => t(\"fields.updatedAt\"),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx(DateCell, { date: getValue() });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\n// src/hooks/table/columns/use-return-reason-table-columns.tsx\nimport { Badge } from \"@medusajs/ui\";\nimport { createColumnHelper as createColumnHelper2 } from \"@tanstack/react-table\";\nimport { useMemo as useMemo2 } from \"react\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar columnHelper2 = createColumnHelper2();\nvar useReturnReasonTableColumns = () => {\n  return useMemo2(\n    () => [\n      columnHelper2.accessor(\"value\", {\n        cell: ({ getValue }) => /* @__PURE__ */ jsx2(Badge, { size: \"2xsmall\", children: getValue() })\n      }),\n      columnHelper2.accessor(\"label\", {\n        cell: ({ row }) => {\n          const { label, description } = row.original;\n          return /* @__PURE__ */ jsx2(\"div\", { className: \" py-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full w-full flex-col justify-center\", children: [\n            /* @__PURE__ */ jsx2(\"span\", { className: \"truncate font-medium\", children: label }),\n            /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: description ? description : \"-\" })\n          ] }) });\n        }\n      })\n    ],\n    []\n  );\n};\n\n// src/hooks/table/columns/use-tax-rates-table-columns.tsx\nimport { createColumnHelper as createColumnHelper3 } from \"@tanstack/react-table\";\nimport { useMemo as useMemo3 } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/components/table/table-cells/taxes/type-cell/type-cell.tsx\nimport { Badge as Badge2 } from \"@medusajs/ui\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\n\n// src/hooks/table/columns/use-tax-rates-table-columns.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar columnHelper3 = createColumnHelper3();\n\nexport {\n  useProductTagTableColumns,\n  useReturnReasonTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AASA,mBAAwB;AAExB,yBAAoB;AA8BpB,IAAAA,gBAAoC;AACpC,IAAAC,sBAAkC;AAwBlC,IAAAC,gBAAoC;AAKpC,IAAAC,sBAA4B;AAG5B,IAAAA,sBAA4B;AA9D5B,IAAI,eAAe,mBAAmB;AACtC,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,MAAM,EAAE,cAAc;AAAA,QAC9B,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC5E,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,MAAM,EAAE,kBAAkB;AAAA,QAClC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,MAAM,EAAE,kBAAkB;AAAA,QAClC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAOA,IAAI,gBAAgB,mBAAoB;AACxC,IAAI,8BAA8B,MAAM;AACtC,aAAO,cAAAC;AAAA,IACL,MAAM;AAAA,MACJ,cAAc,SAAS,SAAS;AAAA,QAC9B,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAC,KAAK,OAAO,EAAE,MAAM,WAAW,UAAU,SAAS,EAAE,CAAC;AAAA,MAC/F,CAAC;AAAA,MACD,cAAc,SAAS,SAAS;AAAA,QAC9B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,EAAE,OAAO,YAAY,IAAI,IAAI;AACnC,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,SAAS,cAA0B,0BAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,gBAClJ,oBAAAA,KAAK,QAAQ,EAAE,WAAW,wBAAwB,UAAU,MAAM,CAAC;AAAA,gBACnE,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,cAAc,cAAc,IAAI,CAAC;AAAA,UACnG,EAAE,CAAC,EAAE,CAAC;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACF;AAaA,IAAI,gBAAgB,mBAAoB;", "names": ["import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "useMemo2", "jsx2"]}