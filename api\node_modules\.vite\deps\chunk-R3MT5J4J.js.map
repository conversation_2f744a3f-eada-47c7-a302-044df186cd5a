{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-G22WWLPG.mjs"], "sourcesContent": ["// src/routes/api-key-management/common/utils.ts\nfunction getApiKeyTypeFromPathname(pathname) {\n  const isSecretKey = pathname.startsWith(\"/settings/secret-api-keys\");\n  switch (isSecretKey) {\n    case true:\n      return \"secret\" /* SECRET */;\n    case false:\n      return \"publishable\" /* PUBLISHABLE */;\n  }\n}\nfunction getApiKeyStatusProps(revokedAt, t) {\n  if (!revokedAt) {\n    return {\n      color: \"green\",\n      label: t(\"apiKeyManagement.status.active\")\n    };\n  }\n  return {\n    color: \"red\",\n    label: t(\"apiKeyManagement.status.revoked\")\n  };\n}\nfunction getApiKeyTypeProps(type, t) {\n  if (type === \"publishable\" /* PUBLISHABLE */) {\n    return {\n      color: \"green\",\n      label: t(\"apiKeyManagement.type.publishable\")\n    };\n  }\n  return {\n    color: \"blue\",\n    label: t(\"apiKeyManagement.type.secret\")\n  };\n}\nvar prettifyRedactedToken = (token) => {\n  return token.replace(\"***\", `\\u2022\\u2022\\u2022`);\n};\n\nexport {\n  getApiKeyTypeFromPathname,\n  getApiKeyStatusProps,\n  getApiKeyTypeProps,\n  prettifyRedactedToken\n};\n"], "mappings": ";AACA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,cAAc,SAAS,WAAW,2BAA2B;AACnE,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF;AACA,SAAS,qBAAqB,WAAW,GAAG;AAC1C,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO,EAAE,gCAAgC;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO,EAAE,iCAAiC;AAAA,EAC5C;AACF;AACA,SAAS,mBAAmB,MAAM,GAAG;AACnC,MAAI,SAAS,eAAiC;AAC5C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO,EAAE,mCAAmC;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO,EAAE,8BAA8B;AAAA,EACzC;AACF;AACA,IAAI,wBAAwB,CAAC,UAAU;AACrC,SAAO,MAAM,QAAQ,OAAO,KAAoB;AAClD;", "names": []}