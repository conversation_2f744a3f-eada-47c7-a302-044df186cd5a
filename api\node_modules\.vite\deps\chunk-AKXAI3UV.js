import {
  pricePreferencesQueryKeys
} from "./chunk-662EXSHO.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-QZ6PT4QV.mjs
var REGIONS_QUERY_KEY = "regions";
var regionsQueryKeys = queryKeysFactory(REGIONS_QUERY_KEY);
var useRegion = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: regionsQueryKeys.detail(id, query),
    queryFn: async () => sdk.admin.region.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useRegions = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.region.list(query),
    queryKey: regionsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateRegion = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.region.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.list()
      });
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateRegion = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.region.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.details() });
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.list()
      });
      queryClient.invalidateQueries({
        queryKey: pricePreferencesQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteRegion = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.region.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: regionsQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  regionsQueryKeys,
  useRegion,
  useRegions,
  useCreateRegion,
  useUpdateRegion,
  useDeleteRegion
};
//# sourceMappingURL=chunk-AKXAI3UV.js.map
