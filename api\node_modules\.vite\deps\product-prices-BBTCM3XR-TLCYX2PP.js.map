{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-prices-BBTCM3XR.mjs"], "sourcesContent": ["import {\n  DataGrid,\n  createD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  createDataGridPriceColumns\n} from \"./chunk-GE4APTT2.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport {\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProduct,\n  useUpdateProductVariantsBatch\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-prices/product-prices.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-prices/pricing-edit.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button } from \"@medusajs/ui\";\nimport { useMemo as useMemo2 } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport * as zod from \"zod\";\n\n// src/routes/products/common/variant-pricing-form.tsx\nimport { useMemo } from \"react\";\nimport { useWatch } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VariantPricingForm = ({ form }) => {\n  const { store } = useStore();\n  const { regions } = useRegions({ limit: 9999 });\n  const { price_preferences: pricePreferences } = usePricePreferences({});\n  const { setCloseOnEscape } = useRouteModal();\n  const columns = useVariantPriceGridColumns({\n    currencies: store?.supported_currencies,\n    regions,\n    pricePreferences\n  });\n  const variants = useWatch({\n    control: form.control,\n    name: \"variants\"\n  });\n  return /* @__PURE__ */ jsx(\n    DataGrid,\n    {\n      columns,\n      data: variants,\n      state: form,\n      onEditingChange: (editing) => setCloseOnEscape(!editing)\n    }\n  );\n};\nvar columnHelper = createDataGridHelper();\nvar useVariantPriceGridColumns = ({\n  currencies = [],\n  regions = [],\n  pricePreferences = []\n}) => {\n  const { t } = useTranslation();\n  return useMemo(() => {\n    return [\n      columnHelper.column({\n        id: t(\"fields.title\"),\n        header: t(\"fields.title\"),\n        cell: (context) => {\n          const entity = context.row.original;\n          return /* @__PURE__ */ jsx(DataGrid.ReadonlyCell, { context, children: /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center gap-x-2 overflow-hidden\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: entity.title }) }) });\n        },\n        disableHiding: true\n      }),\n      ...createDataGridPriceColumns({\n        currencies: currencies.map((c) => c.currency_code),\n        regions,\n        pricePreferences,\n        getFieldName: (context, value) => {\n          if (context.column.id?.startsWith(\"currency_prices\")) {\n            return `variants.${context.row.index}.prices.${value}`;\n          }\n          return `variants.${context.row.index}.prices.${value}`;\n        },\n        t\n      })\n    ];\n  }, [t, currencies, regions, pricePreferences]);\n};\n\n// src/routes/products/product-prices/pricing-edit.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar UpdateVariantPricesSchema = zod.object({\n  variants: zod.array(\n    zod.object({\n      prices: zod.record(zod.string(), zod.string().or(zod.number()).optional()).optional()\n    })\n  )\n});\nvar PricingEdit = ({\n  product,\n  variantId\n}) => {\n  const { t } = useTranslation2();\n  const { handleSuccess } = useRouteModal();\n  const { mutateAsync, isPending } = useUpdateProductVariantsBatch(product.id);\n  const { regions } = useRegions({ limit: 9999 });\n  const regionsCurrencyMap = useMemo2(() => {\n    if (!regions?.length) {\n      return {};\n    }\n    return regions.reduce((acc, reg) => {\n      acc[reg.id] = reg.currency_code;\n      return acc;\n    }, {});\n  }, [regions]);\n  const variants = variantId ? product.variants?.filter((v) => v.id === variantId) : product.variants;\n  const form = useForm({\n    defaultValues: {\n      variants: variants?.map((variant) => ({\n        title: variant.title,\n        prices: variant.prices.reduce((acc, price) => {\n          if (price.rules?.region_id) {\n            acc[price.rules.region_id] = price.amount;\n          } else {\n            acc[price.currency_code] = price.amount;\n          }\n          return acc;\n        }, {})\n      }))\n    },\n    resolver: zodResolver(UpdateVariantPricesSchema, {})\n  });\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const reqData = values.variants.map((variant, ind) => ({\n      id: variants[ind].id,\n      prices: Object.entries(variant.prices || {}).filter(\n        ([_, value]) => value !== \"\" && typeof value !== \"undefined\"\n        // deleted cells\n      ).map(([currencyCodeOrRegionId, value]) => {\n        const regionId = currencyCodeOrRegionId.startsWith(\"reg_\") ? currencyCodeOrRegionId : void 0;\n        const currencyCode = currencyCodeOrRegionId.startsWith(\"reg_\") ? regionsCurrencyMap[regionId] : currencyCodeOrRegionId;\n        let existingId = void 0;\n        if (regionId) {\n          existingId = variants?.[ind]?.prices?.find(\n            (p) => p.rules[\"region_id\"] === regionId\n          )?.id;\n        } else {\n          existingId = variants?.[ind]?.prices?.find(\n            (p) => p.currency_code === currencyCode && Object.keys(p.rules ?? {}).length === 0\n          )?.id;\n        }\n        const amount = castNumber(value);\n        return {\n          id: existingId,\n          currency_code: currencyCode,\n          amount,\n          ...regionId ? { rules: { region_id: regionId } } : {}\n        };\n      })\n    }));\n    await mutateAsync(reqData, {\n      onSuccess: () => {\n        handleSuccess(\"..\");\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx2(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex size-full flex-col\", children: [\n    /* @__PURE__ */ jsx2(RouteFocusModal.Header, {}),\n    /* @__PURE__ */ jsx2(RouteFocusModal.Body, { className: \"flex flex-col overflow-hidden\", children: /* @__PURE__ */ jsx2(VariantPricingForm, { form }) }),\n    /* @__PURE__ */ jsx2(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx2(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx2(\n        Button,\n        {\n          type: \"submit\",\n          variant: \"primary\",\n          size: \"small\",\n          isLoading: isPending,\n          children: t(\"actions.save\")\n        }\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/products/product-prices/product-prices.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar ProductPrices = () => {\n  const { id, variant_id } = useParams();\n  const { product, isLoading, isError, error } = useProduct(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx3(RouteFocusModal, { children: !isLoading && product && /* @__PURE__ */ jsx3(PricingEdit, { product, variantId: variant_id }) });\n};\nexport {\n  ProductPrices as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,mBAAoC;AAMpC,IAAAA,gBAAwB;AAGxB,yBAAoB;AA4DpB,IAAAC,sBAAkC;AAgGlC,IAAAA,sBAA4B;AA3J5B,IAAI,qBAAqB,CAAC,EAAE,KAAK,MAAM;AACrC,QAAM,EAAE,MAAM,IAAI,SAAS;AAC3B,QAAM,EAAE,QAAQ,IAAI,WAAW,EAAE,OAAO,KAAK,CAAC;AAC9C,QAAM,EAAE,mBAAmB,iBAAiB,IAAI,oBAAoB,CAAC,CAAC;AACtE,QAAM,EAAE,iBAAiB,IAAI,cAAc;AAC3C,QAAM,UAAU,2BAA2B;AAAA,IACzC,YAAY,+BAAO;AAAA,IACnB;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,WAAW,SAAS;AAAA,IACxB,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB,CAAC,YAAY,iBAAiB,CAAC,OAAO;AAAA,IACzD;AAAA,EACF;AACF;AACA,IAAI,eAAe,qBAAqB;AACxC,IAAI,6BAA6B,CAAC;AAAA,EAChC,aAAa,CAAC;AAAA,EACd,UAAU,CAAC;AAAA,EACX,mBAAmB,CAAC;AACtB,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,aAAO,uBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,QAClB,IAAIA,GAAE,cAAc;AAAA,QACpB,QAAQA,GAAE,cAAc;AAAA,QACxB,MAAM,CAAC,YAAY;AACjB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,qBAAuB,wBAAI,SAAS,cAAc,EAAE,SAAS,cAA0B,wBAAI,OAAO,EAAE,WAAW,2DAA2D,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QACzQ;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,MACD,GAAG,2BAA2B;AAAA,QAC5B,YAAY,WAAW,IAAI,CAAC,MAAM,EAAE,aAAa;AAAA,QACjD;AAAA,QACA;AAAA,QACA,cAAc,CAAC,SAAS,UAAU;AApG1C;AAqGU,eAAI,aAAQ,OAAO,OAAf,mBAAmB,WAAW,oBAAoB;AACpD,mBAAO,YAAY,QAAQ,IAAI,KAAK,WAAW,KAAK;AAAA,UACtD;AACA,iBAAO,YAAY,QAAQ,IAAI,KAAK,WAAW,KAAK;AAAA,QACtD;AAAA,QACA,GAAAA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAACA,IAAG,YAAY,SAAS,gBAAgB,CAAC;AAC/C;AAIA,IAAI,4BAAgC,WAAO;AAAA,EACzC,UAAc;AAAA,IACR,WAAO;AAAA,MACT,QAAY,WAAW,WAAO,GAAO,WAAO,EAAE,GAAO,WAAO,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS;AAAA,IACtF,CAAC;AAAA,EACH;AACF,CAAC;AACD,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AACF,MAAM;AA5HN;AA6HE,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,aAAa,UAAU,IAAI,8BAA8B,QAAQ,EAAE;AAC3E,QAAM,EAAE,QAAQ,IAAI,WAAW,EAAE,OAAO,KAAK,CAAC;AAC9C,QAAM,yBAAqB,aAAAC,SAAS,MAAM;AACxC,QAAI,EAAC,mCAAS,SAAQ;AACpB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAClC,UAAI,IAAI,EAAE,IAAI,IAAI;AAClB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,WAAW,aAAY,aAAQ,aAAR,mBAAkB,OAAO,CAAC,MAAM,EAAE,OAAO,aAAa,QAAQ;AAC3F,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,UAAU,qCAAU,IAAI,CAAC,aAAa;AAAA,QACpC,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ,OAAO,OAAO,CAAC,KAAK,UAAU;AA/ItD,cAAAC;AAgJU,eAAIA,MAAA,MAAM,UAAN,gBAAAA,IAAa,WAAW;AAC1B,gBAAI,MAAM,MAAM,SAAS,IAAI,MAAM;AAAA,UACrC,OAAO;AACL,gBAAI,MAAM,aAAa,IAAI,MAAM;AAAA,UACnC;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAAA,MACP;AAAA,IACF;AAAA,IACA,UAAU,EAAY,2BAA2B,CAAC,CAAC;AAAA,EACrD,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,UAAU,OAAO,SAAS,IAAI,CAAC,SAAS,SAAS;AAAA,MACrD,IAAI,SAAS,GAAG,EAAE;AAAA,MAClB,QAAQ,OAAO,QAAQ,QAAQ,UAAU,CAAC,CAAC,EAAE;AAAA,QAC3C,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,MAAM,OAAO,UAAU;AAAA;AAAA,MAEnD,EAAE,IAAI,CAAC,CAAC,wBAAwB,KAAK,MAAM;AAjKjD,YAAAA,KAAA;AAkKQ,cAAM,WAAW,uBAAuB,WAAW,MAAM,IAAI,yBAAyB;AACtF,cAAM,eAAe,uBAAuB,WAAW,MAAM,IAAI,mBAAmB,QAAQ,IAAI;AAChG,YAAI,aAAa;AACjB,YAAI,UAAU;AACZ,wBAAa,YAAAA,MAAA,qCAAW,SAAX,gBAAAA,IAAiB,WAAjB,mBAAyB;AAAA,YACpC,CAAC,MAAM,EAAE,MAAM,WAAW,MAAM;AAAA,gBADrB,mBAEV;AAAA,QACL,OAAO;AACL,wBAAa,sDAAW,SAAX,mBAAiB,WAAjB,mBAAyB;AAAA,YACpC,CAAC,MAAM,EAAE,kBAAkB,gBAAgB,OAAO,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,WAAW;AAAA,gBADtE,mBAEV;AAAA,QACL;AACA,cAAM,SAAS,WAAW,KAAK;AAC/B,eAAO;AAAA,UACL,IAAI;AAAA,UACJ,eAAe;AAAA,UACf;AAAA,UACA,GAAG,WAAW,EAAE,OAAO,EAAE,WAAW,SAAS,EAAE,IAAI,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH,EAAE;AACF,UAAM,YAAY,SAAS;AAAA,MACzB,WAAW,MAAM;AACf,sBAAc,IAAI;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,oBAAAC,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,0BAAK,cAAc,EAAE,UAAU,cAAc,WAAW,2BAA2B,UAAU;AAAA,QAC/J,oBAAAA,KAAK,gBAAgB,QAAQ,CAAC,CAAC;AAAA,QAC/B,oBAAAA,KAAK,gBAAgB,MAAM,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA,KAAK,oBAAoB,EAAE,KAAK,CAAC,EAAE,CAAC;AAAA,QACvI,oBAAAA,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,0BAAK,OAAO,EAAE,WAAW,gDAAgD,UAAU;AAAA,UAC1I,oBAAAA,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUH,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7J,oBAAAG;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAUH,GAAE,cAAc;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,IAAI,WAAW,IAAI,UAAU;AACrC,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,EAAE;AAC5D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAI,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,eAA2B,oBAAAA,KAAK,aAAa,EAAE,SAAS,WAAW,WAAW,CAAC,EAAE,CAAC;AAC3J;", "names": ["import_react", "import_jsx_runtime", "t", "useMemo2", "_a", "jsx2", "jsx3"]}