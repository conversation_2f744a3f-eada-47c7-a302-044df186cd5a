import {
  InventoryItemGeneralSection
} from "./chunk-ITCUGY4F.js";
import {
  TextCell,
  TextHeader
} from "./chunk-7HUCBNCQ.js";
import {
  getFormattedCountry
} from "./chunk-WBEFJZV3.js";
import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-KTCGZHOS.js";
import "./chunk-EGRHWZRV.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import {
  useDate
} from "./chunk-AGRADJYQ.js";
import "./chunk-XGFC5LFP.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import {
  useDeleteReservationItem,
  useReservationItems
} from "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  inventoryItemsQueryKeys,
  useDeleteInventoryItemLevel,
  useInventoryItem,
  useInventoryItemLevels
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Tooltip,
  Trash,
  TriangleRightMini,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/inventory-detail-FV2MBKBO.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var INVENTORY_DETAIL_FIELDS = "*variants,*variants.product,*variants.options";
var InventoryDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { inventory_item } = useInventoryItem(
    id,
    {
      fields: INVENTORY_DETAIL_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!inventory_item) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: inventory_item.title ?? inventory_item.sku ?? id });
};
var InventoryItemAttributeSection = ({
  inventoryItem
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("products.attributes") }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "attributes",
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.height"), value: inventoryItem.height }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.width"), value: inventoryItem.width }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.length"), value: inventoryItem.length }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.weight"), value: inventoryItem.weight }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.midCode"), value: inventoryItem.mid_code }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.material"), value: inventoryItem.material }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.hsCode"), value: inventoryItem.hs_code }),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("fields.countryOfOrigin"),
        value: getFormattedCountry(inventoryItem.origin_country)
      }
    )
  ] });
};
var LocationActions = ({
  level
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteInventoryItemLevel(
    level.inventory_item_id,
    level.location_id
  );
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("inventory.deleteWarning"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync();
  };
  return (0, import_jsx_runtime3.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `locations/${level.location_id}`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete,
              disabled: level.reserved_quantity > 0 || level.stocked_quantity > 0
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useLocationListTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("stock_locations.0.name", {
        header: t("fields.location"),
        cell: ({ getValue }) => {
          const locationName = getValue();
          if (!locationName) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: locationName.toString() }) });
        }
      }),
      columnHelper.accessor("reserved_quantity", {
        header: t("inventory.reserved"),
        cell: ({ getValue }) => {
          const quantity = getValue();
          if (Number.isNaN(quantity)) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: quantity }) });
        }
      }),
      columnHelper.accessor("stocked_quantity", {
        header: t("fields.inStock"),
        cell: ({ getValue }) => {
          const stockedQuantity = getValue();
          if (Number.isNaN(stockedQuantity)) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: stockedQuantity }) });
        }
      }),
      columnHelper.accessor("available_quantity", {
        header: t("inventory.available"),
        cell: ({ getValue }) => {
          const availableQuantity = getValue();
          if (Number.isNaN(availableQuantity)) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: availableQuantity }) });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime4.jsx)(LocationActions, { level: row.original })
      })
    ],
    [t]
  );
};
var useLocationLevelTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(
    [
      "id",
      "location_id",
      "stocked_quantity",
      "reserved_quantity",
      "incoming_quantity",
      "available_quantity",
      "*stock_locations"
    ],
    prefix
  );
  const { reserved_quantity, stocked_quantity, available_quantity, ...params } = raw;
  const searchParams = {
    limit: pageSize,
    reserved_quantity: reserved_quantity ? JSON.parse(reserved_quantity) : void 0,
    stocked_quantity: stocked_quantity ? JSON.parse(stocked_quantity) : void 0,
    available_quantity: available_quantity ? JSON.parse(available_quantity) : void 0,
    ...params
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE = 20;
var ItemLocationListTable = ({
  inventory_item_id
}) => {
  const { searchParams, raw } = useLocationLevelTableQuery({
    pageSize: PAGE_SIZE
  });
  const {
    inventory_levels,
    count,
    isPending: isLoading,
    isError,
    error
  } = useInventoryItemLevels(inventory_item_id, {
    ...searchParams,
    fields: "*stock_locations"
  });
  const columns = useLocationListTableColumns();
  const { table } = useDataTable({
    data: inventory_levels ?? [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      pagination: true,
      queryObject: raw
    }
  );
};
var InventoryItemLocationLevelsSection = ({
  inventoryItem
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime6.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime6.jsx)(Heading, { children: t("inventory.locationLevels") }),
      (0, import_jsx_runtime6.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime6.jsx)(Link, { to: "locations", children: t("inventory.manageLocations") }) })
    ] }),
    (0, import_jsx_runtime6.jsx)(ItemLocationListTable, { inventory_item_id: inventoryItem.id })
  ] });
};
var CreatedAtCell = ({ date }) => {
  const { getFullDate } = useDate();
  if (!date) {
    return (0, import_jsx_runtime7.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime7.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime7.jsx)(
    Tooltip,
    {
      className: "z-10",
      content: (0, import_jsx_runtime7.jsx)("span", { className: "text-pretty", children: `${getFullDate({
        date,
        includeTime: true
      })}` }),
      children: (0, import_jsx_runtime7.jsx)("span", { className: "truncate", children: getFullDate({ date, includeTime: true }) })
    }
  ) });
};
var ReservationActions = ({
  reservation
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteReservationItem(reservation.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("inventory.deleteWarning"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("inventory.reservation.deleteSuccessToast"));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime8.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime8.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `/reservations/${reservation.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime8.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper2 = createColumnHelper();
var useReservationTableColumn = ({ sku }) => {
  const { t } = useTranslation();
  return (0, import_react3.useMemo)(
    () => [
      columnHelper2.display({
        id: "sku",
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("fields.sku") }),
        cell: () => {
          return (0, import_jsx_runtime9.jsx)(TextCell, { text: sku });
        }
      }),
      columnHelper2.accessor("line_item.order_id", {
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("inventory.reservation.orderID") }),
        cell: ({ getValue }) => {
          const orderId = getValue();
          if (!orderId) {
            return (0, import_jsx_runtime9.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime9.jsx)(TextCell, { text: orderId });
        }
      }),
      columnHelper2.accessor("description", {
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("fields.description") }),
        cell: ({ getValue }) => {
          const description = getValue();
          if (!description) {
            return (0, import_jsx_runtime9.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime9.jsx)(TextCell, { text: description });
        }
      }),
      columnHelper2.accessor("location.name", {
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("inventory.reservation.location") }),
        cell: ({ getValue }) => {
          const location = getValue();
          if (!location) {
            return (0, import_jsx_runtime9.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime9.jsx)(TextCell, { text: location });
        }
      }),
      columnHelper2.accessor("created_at", {
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("fields.createdAt") }),
        cell: ({ getValue }) => (0, import_jsx_runtime9.jsx)(CreatedAtCell, { date: getValue() })
      }),
      columnHelper2.accessor("quantity", {
        header: () => (0, import_jsx_runtime9.jsx)(TextHeader, { text: t("fields.quantity"), align: "right" }),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime9.jsx)(TextCell, { text: getValue(), align: "right" });
        }
      }),
      columnHelper2.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime9.jsx)(ReservationActions, { reservation: row.original })
      })
    ],
    [t]
  );
};
var useReservationsTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(
    [
      "id",
      "location_id",
      "inventory_item_id",
      "quantity",
      "line_item_id",
      "description",
      "created_by"
    ],
    prefix
  );
  const { quantity, ...params } = raw;
  const searchParams = {
    limit: pageSize,
    quantity: quantity ? JSON.parse(quantity) : void 0,
    ...params
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE2 = 20;
var ReservationItemTable = ({
  inventoryItem
}) => {
  const { searchParams, raw } = useReservationsTableQuery({
    pageSize: PAGE_SIZE2
  });
  const { reservations, count, isPending, isError, error } = useReservationItems({
    ...searchParams,
    inventory_item_id: [inventoryItem.id]
  });
  const { stock_locations } = useStockLocations({
    id: (reservations || []).map((r) => r.location_id)
  });
  const data = (0, import_react2.useMemo)(() => {
    const locationMap = new Map((stock_locations || []).map((l) => [l.id, l]));
    return (reservations || []).map((r) => ({
      ...r,
      location: locationMap.get(r.location_id)
    }));
  }, [reservations, stock_locations]);
  const columns = useReservationTableColumn({ sku: inventoryItem.sku });
  const { table } = useDataTable({
    data: data ?? [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE2
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime10.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE2,
      count,
      isLoading: isPending,
      pagination: true,
      queryObject: raw
    }
  );
};
var InventoryItemReservationsSection = ({
  inventoryItem
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime11.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime11.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime11.jsx)(Heading, { children: t("reservations.domain") }),
      (0, import_jsx_runtime11.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime11.jsx)(Link, { to: `/reservations/create?item_id=${inventoryItem.id}`, children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime11.jsx)(ReservationItemTable, { inventoryItem })
  ] });
};
var InventoryItemVariantsSection = ({
  variants
}) => {
  const { t } = useTranslation();
  if (!(variants == null ? void 0 : variants.length)) {
    return null;
  }
  return (0, import_jsx_runtime12.jsxs)(Container, { className: "p-0", children: [
    (0, import_jsx_runtime12.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime12.jsx)(Heading, { level: "h2", children: t("inventory.associatedVariants") }) }),
    (0, import_jsx_runtime12.jsx)("div", { className: "txt-small flex flex-col gap-2 px-2 pb-2", children: variants.map((variant) => {
      var _a;
      const link = variant.product ? `/products/${variant.product.id}/variants/${variant.id}` : null;
      const Inner = (0, import_jsx_runtime12.jsx)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component rounded-md px-4 py-2 transition-colors", children: (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center gap-3", children: [
        (0, import_jsx_runtime12.jsx)("div", { className: "shadow-elevation-card-rest rounded-md", children: (0, import_jsx_runtime12.jsx)(Thumbnail, { src: (_a = variant.product) == null ? void 0 : _a.thumbnail }) }),
        (0, import_jsx_runtime12.jsxs)("div", { className: "flex flex-1 flex-col", children: [
          (0, import_jsx_runtime12.jsx)("span", { className: "text-ui-fg-base font-medium", children: variant.title }),
          (0, import_jsx_runtime12.jsx)("span", { className: "text-ui-fg-subtle", children: variant.options.map((o) => o.value).join(" ⋅ ") })
        ] }),
        (0, import_jsx_runtime12.jsx)("div", { className: "size-7 flex items-center justify-center", children: (0, import_jsx_runtime12.jsx)(TriangleRightMini, { className: "text-ui-fg-muted" }) })
      ] }) });
      if (!link) {
        return (0, import_jsx_runtime12.jsx)("div", { children: Inner }, variant.id);
      }
      return (0, import_jsx_runtime12.jsx)(
        Link,
        {
          to: link,
          className: "outline-none focus-within:shadow-borders-interactive-with-focus rounded-md [&:hover>div]:bg-ui-bg-component-hover",
          children: Inner
        },
        variant.id
      );
    }) })
  ] });
};
var InventoryDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const {
    inventory_item,
    isPending: isLoading,
    isError,
    error
  } = useInventoryItem(
    id,
    {
      fields: INVENTORY_DETAIL_FIELDS
    },
    {
      initialData
    }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !inventory_item) {
    return (0, import_jsx_runtime13.jsx)(
      TwoColumnPageSkeleton,
      {
        showJSON: true,
        mainSections: 3,
        sidebarSections: 2,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime13.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("inventory_item.details.after"),
        before: getWidgets("inventory_item.details.before"),
        sideAfter: getWidgets("inventory_item.details.side.after"),
        sideBefore: getWidgets("inventory_item.details.side.before")
      },
      data: inventory_item,
      showJSON: true,
      showMetadata: true,
      children: [
        (0, import_jsx_runtime13.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime13.jsx)(InventoryItemGeneralSection, { inventoryItem: inventory_item }),
          (0, import_jsx_runtime13.jsx)(InventoryItemLocationLevelsSection, { inventoryItem: inventory_item }),
          (0, import_jsx_runtime13.jsx)(InventoryItemReservationsSection, { inventoryItem: inventory_item })
        ] }),
        (0, import_jsx_runtime13.jsxs)(TwoColumnPage.Sidebar, { children: [
          (0, import_jsx_runtime13.jsx)(
            InventoryItemVariantsSection,
            {
              variants: inventory_item.variants
            }
          ),
          (0, import_jsx_runtime13.jsx)(InventoryItemAttributeSection, { inventoryItem: inventory_item })
        ] })
      ]
    }
  );
};
var inventoryDetailQuery = (id) => ({
  queryKey: inventoryItemsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.inventoryItem.retrieve(id, {
    fields: INVENTORY_DETAIL_FIELDS
  })
});
var inventoryItemLoader = async ({ params }) => {
  const id = params.id;
  const query = inventoryDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  InventoryDetailBreadcrumb as Breadcrumb,
  InventoryDetail as Component,
  inventoryItemLoader as loader
};
//# sourceMappingURL=inventory-detail-FV2MBKBO-YTBZEE72.js.map
