{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PCFUZKDS.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/query/use-product-table-query.tsx\nvar DEFAULT_FIELDS = \"id,title,handle,status,*collection,*sales_channels,variants.id,thumbnail\";\nvar useProductTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\n      \"offset\",\n      \"order\",\n      \"q\",\n      \"created_at\",\n      \"updated_at\",\n      \"sales_channel_id\",\n      \"category_id\",\n      \"collection_id\",\n      \"is_giftcard\",\n      \"tag_id\",\n      \"type_id\",\n      \"status\",\n      \"id\"\n    ],\n    prefix\n  );\n  const {\n    offset,\n    sales_channel_id,\n    created_at,\n    updated_at,\n    category_id,\n    collection_id,\n    tag_id,\n    type_id,\n    is_giftcard,\n    status,\n    order,\n    q\n  } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    sales_channel_id: sales_channel_id?.split(\",\"),\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    category_id: category_id?.split(\",\"),\n    collection_id: collection_id?.split(\",\"),\n    is_giftcard: is_giftcard ? is_giftcard === \"true\" : void 0,\n    order,\n    tag_id: tag_id ? tag_id.split(\",\") : void 0,\n    type_id: type_id?.split(\",\"),\n    status: status?.split(\",\"),\n    q,\n    fields: DEFAULT_FIELDS\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useProductTableQuery\n};\n"], "mappings": ";;;;;AAKA,IAAI,iBAAiB;AACrB,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC,kBAAkB,qDAAkB,MAAM;AAAA,IAC1C,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,aAAa,2CAAa,MAAM;AAAA,IAChC,eAAe,+CAAe,MAAM;AAAA,IACpC,aAAa,cAAc,gBAAgB,SAAS;AAAA,IACpD;AAAA,IACA,QAAQ,SAAS,OAAO,MAAM,GAAG,IAAI;AAAA,IACrC,SAAS,mCAAS,MAAM;AAAA,IACxB,QAAQ,iCAAQ,MAAM;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,EACV;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}