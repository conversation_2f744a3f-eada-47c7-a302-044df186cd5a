import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-P3UUX2T6.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PlaceholderCell = () => {
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted", children: "-" }) });
};

export {
  PlaceholderCell
};
//# sourceMappingURL=chunk-S5JEKJNE.js.map
