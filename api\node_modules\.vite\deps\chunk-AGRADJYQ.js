import {
  languages
} from "./chunk-VHD2ND4K.js";
import {
  format,
  formatDistance,
  sub
} from "./chunk-7UAYECTW.js";
import {
  enUS
} from "./chunk-Y3NYV3NU.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-Q5PHSNDY.mjs
var useDate = () => {
  var _a;
  const { i18n } = useTranslation();
  const locale = ((_a = languages.find((l) => l.code === i18n.language)) == null ? void 0 : _a.date_locale) || enUS;
  const getFullDate = ({
    date,
    includeTime = false
  }) => {
    const ensuredDate = new Date(date);
    if (isNaN(ensuredDate.getTime())) {
      return "";
    }
    const timeFormat = includeTime ? "p" : "";
    return format(ensuredDate, `PP ${timeFormat}`, {
      locale
    });
  };
  function getRelativeDate(date) {
    const now = /* @__PURE__ */ new Date();
    return formatDistance(sub(new Date(date), { minutes: 0 }), now, {
      addSuffix: true,
      locale
    });
  }
  return {
    getFullDate,
    getRelativeDate
  };
};

export {
  useDate
};
//# sourceMappingURL=chunk-AGRADJYQ.js.map
