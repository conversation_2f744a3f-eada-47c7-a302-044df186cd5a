import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  ExclamationCircle,
  Text
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/no-match-YRNHGOT3.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NoMatch = () => {
  const { t } = useTranslation();
  const title = t("errorBoundary.notFoundTitle");
  const message = t("errorBoundary.noMatchMessage");
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full min-h-screen items-center justify-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-6", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col items-center gap-y-3", children: [
      (0, import_jsx_runtime.jsx)(ExclamationCircle, {}),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center justify-center gap-y-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title }),
        (0, import_jsx_runtime.jsx)(
          Text,
          {
            size: "small",
            className: "text-ui-fg-muted text-balance text-center",
            children: message
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime.jsx)(Button, { asChild: true, size: "small", variant: "secondary", children: (0, import_jsx_runtime.jsx)(Link, { to: "/", children: t("errorBoundary.backToDashboard") }) })
  ] }) });
};
export {
  NoMatch as Component
};
//# sourceMappingURL=no-match-YRNHGOT3-7CVEXUQA.js.map
