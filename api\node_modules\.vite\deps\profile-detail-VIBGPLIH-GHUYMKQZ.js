import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import {
  languages
} from "./chunk-VHD2ND4K.js";
import "./chunk-Y3NYV3NU.js";
import {
  useMe
} from "./chunk-7UV5UA6G.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare,
  Text
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/profile-detail-VIBGPLIH.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ProfileGeneralSection = ({ user }) => {
  var _a;
  const { i18n, t } = useTranslation();
  const name = [user.first_name, user.last_name].filter(Boolean).join(" ");
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t("profile.domain") }),
        (0, import_jsx_runtime.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("profile.manageYourProfileDetails") })
      ] }),
      (0, import_jsx_runtime.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "edit",
                  icon: (0, import_jsx_runtime.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.name") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: name || "-" })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.email") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: user.email })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("profile.fields.languageLabel") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: ((_a = languages.find((lang) => lang.code === i18n.language)) == null ? void 0 : _a.display_name) || "-" })
    ] })
  ] });
};
var ProfileDetail = () => {
  const { user, isPending: isLoading, isError, error } = useMe();
  const { getWidgets } = useExtension();
  if (isLoading || !user) {
    return (0, import_jsx_runtime2.jsx)(SingleColumnPageSkeleton, { sections: 1 });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("profile.details.after"),
        before: getWidgets("profile.details.before")
      },
      children: (0, import_jsx_runtime2.jsx)(ProfileGeneralSection, { user })
    }
  );
};
export {
  ProfileDetail as Component
};
//# sourceMappingURL=profile-detail-VIBGPLIH-GHUYMKQZ.js.map
