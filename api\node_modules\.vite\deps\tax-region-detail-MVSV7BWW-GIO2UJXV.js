import {
  TaxOverrideTable,
  TaxRateLine,
  useTaxOverrideTable
} from "./chunk-GJUNXSH3.js";
import "./chunk-VD6KBTYK.js";
import {
  TaxRegionTable,
  useTaxRegionTable
} from "./chunk-LMXFFZGF.js";
import {
  TaxRegionCard
} from "./chunk-HJV4BH76.js";
import {
  getCountryProvinceObjectByIso2
} from "./chunk-KL72CHML.js";
import {
  useTaxRateTableQuery
} from "./chunk-GDXEFZZY.js";
import {
  useTaxRegionTableQuery
} from "./chunk-GEC36FCE.js";
import "./chunk-XYHEMHQ5.js";
import "./chunk-QF476XOZ.js";
import "./chunk-73I4NEMG.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-32T72GVU.js";
import "./chunk-QX6SXRUW.js";
import {
  TaxRegionDetailBreadcrumb,
  taxRegionLoader
} from "./chunk-XQBAAEQQ.js";
import "./chunk-XGFC5LFP.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import {
  useTaxRates
} from "./chunk-Y5G57T2Y.js";
import {
  useTaxRegion,
  useTaxRegions
} from "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Badge,
  Button,
  Container,
  Heading,
  Text,
  Tooltip
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-detail-MVSV7BWW.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var TaxRegionDetailSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const defaultRates = taxRegion.tax_rates.filter((r) => r.is_default === true);
  const showBage = defaultRates.length === 0;
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsx)(
      TaxRegionCard,
      {
        taxRegion,
        type: "header",
        asLink: false,
        badge: showBage && (0, import_jsx_runtime.jsx)(Tooltip, { content: t("taxRegions.fields.noDefaultRate.tooltip"), children: (0, import_jsx_runtime.jsx)(Badge, { color: "orange", size: "2xsmall", className: "cursor-default", children: t("taxRegions.fields.noDefaultRate.label") }) })
      }
    ),
    defaultRates.map((rate) => {
      return (0, import_jsx_runtime.jsx)(TaxRateLine, { taxRate: rate }, rate.id);
    })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "p";
var TaxRegionProvinceSection = ({
  taxRegion,
  showSublevelRegions
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRegionTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { tax_regions, count, isPending, isError, error } = useTaxRegions(
    {
      ...searchParams,
      parent_id: taxRegion.id
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxRegionTable({
    count,
    data: tax_regions,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const provinceObject = getCountryProvinceObjectByIso2(taxRegion.country_code);
  if (!provinceObject && !showSublevelRegions && !taxRegion.children.length) {
    return null;
  }
  const type = (provinceObject == null ? void 0 : provinceObject.type) || "sublevel";
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime2.jsx)(
    TaxRegionTable,
    {
      variant: "province",
      action: { to: `provinces/create`, label: t("actions.create") },
      table,
      isPending,
      queryObject: raw,
      count,
      children: (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t(`taxRegions.${type}.header`) })
    }
  ) });
};
var PAGE_SIZE2 = 10;
var PREFIX2 = "o";
var TaxRegionOverrideSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRateTableQuery({
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  const { tax_rates, count, isPending, isError, error } = useTaxRates(
    {
      ...searchParams,
      tax_region_id: taxRegion.id,
      is_default: false
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxOverrideTable({
    count,
    data: tax_rates,
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime3.jsx)(
    TaxOverrideTable,
    {
      isPending,
      table,
      count,
      action: {
        label: t("actions.create"),
        to: "overrides/create"
      },
      queryObject: raw,
      prefix: PREFIX2,
      children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("taxRegions.taxOverrides.header") })
    }
  ) });
};
var TaxRegionSublevelAlert = ({
  taxRegion,
  showSublevelRegions,
  setShowSublevelRegions
}) => {
  const { t } = useTranslation();
  const [dismissed, setDismissed] = (0, import_react2.useState)(false);
  const provinceObject = getCountryProvinceObjectByIso2(taxRegion.country_code);
  if (provinceObject || showSublevelRegions || dismissed || taxRegion.children.length) {
    return null;
  }
  return (0, import_jsx_runtime4.jsx)(Alert, { dismissible: true, variant: "info", className: "bg-ui-bg-base", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col gap-y-3", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", asChild: true, children: (0, import_jsx_runtime4.jsx)("h2", { children: t("taxRegions.fields.sublevels.alert.header") }) }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", className: "text-pretty", children: t("taxRegions.fields.sublevels.alert.description") })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-3", children: [
      (0, import_jsx_runtime4.jsx)(
        Button,
        {
          variant: "secondary",
          size: "small",
          onClick: () => setShowSublevelRegions(true),
          children: t("taxRegions.fields.sublevels.alert.action")
        }
      ),
      (0, import_jsx_runtime4.jsx)(
        Button,
        {
          variant: "transparent",
          size: "small",
          onClick: () => setDismissed(true),
          children: t("actions.hide")
        }
      )
    ] })
  ] }) });
};
var TaxRegionDetail = () => {
  const { id } = useParams();
  const [showSublevelRegions, setShowSublevelRegions] = (0, import_react.useState)(false);
  const initialData = useLoaderData();
  const {
    tax_region: taxRegion,
    isLoading,
    isError,
    error
  } = useTaxRegion(id, void 0, { initialData });
  const { getWidgets } = useExtension();
  if (isLoading || !taxRegion) {
    return (0, import_jsx_runtime5.jsx)(SingleColumnPageSkeleton, { sections: 4, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsxs)(
    SingleColumnPage,
    {
      data: taxRegion,
      showJSON: true,
      widgets: {
        after: getWidgets("tax.details.after"),
        before: getWidgets("tax.details.before")
      },
      children: [
        (0, import_jsx_runtime5.jsx)(
          TaxRegionSublevelAlert,
          {
            taxRegion,
            showSublevelRegions,
            setShowSublevelRegions
          }
        ),
        (0, import_jsx_runtime5.jsx)(TaxRegionDetailSection, { taxRegion }),
        (0, import_jsx_runtime5.jsx)(
          TaxRegionProvinceSection,
          {
            taxRegion,
            showSublevelRegions
          }
        ),
        (0, import_jsx_runtime5.jsx)(TaxRegionOverrideSection, { taxRegion })
      ]
    }
  );
};
export {
  TaxRegionDetailBreadcrumb as Breadcrumb,
  TaxRegionDetail as Component,
  TaxRegionDetail,
  taxRegionLoader as loader
};
//# sourceMappingURL=tax-region-detail-MVSV7BWW-GIO2UJXV.js.map
