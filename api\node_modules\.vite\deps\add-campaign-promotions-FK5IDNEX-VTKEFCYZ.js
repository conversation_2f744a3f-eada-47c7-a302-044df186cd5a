import {
  usePromotionTableColumns,
  usePromotionTableQuery
} from "./chunk-KDZBYWB7.js";
import "./chunk-KFESVZ55.js";
import "./chunk-7HUCBNCQ.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import {
  usePromotionTableFilters
} from "./chunk-ZIXJCBL3.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-5NX546NL.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useAddOrRemoveCampaignPromotions,
  useCampaign,
  usePromotions
} from "./chunk-S32V3COL.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Hint,
  Tooltip,
  createColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/add-campaign-promotions-FK5IDNEX.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddCampaignPromotionsSchema = objectType({
  promotion_ids: arrayType(stringType()).min(1)
});
var PAGE_SIZE = 50;
var AddCampaignPromotionsForm = ({
  campaign
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: { promotion_ids: [] },
    resolver: t(AddCampaignPromotionsSchema)
  });
  const { setValue } = form;
  const { mutateAsync, isPending } = useAddOrRemoveCampaignPromotions(
    campaign.id
  );
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const updater = (fn) => {
    const state = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(state);
    setValue("promotion_ids", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(state);
  };
  const { searchParams, raw } = usePromotionTableQuery({ pageSize: PAGE_SIZE });
  const {
    promotions,
    count,
    isPending: isLoading
  } = usePromotions({ ...searchParams }, { placeholderData: keepPreviousData });
  const columns = useColumns();
  const filters = usePromotionTableFilters();
  const { table } = useDataTable({
    data: promotions ?? [],
    columns,
    enableRowSelection: (row) => {
      return row.original.campaign_id !== campaign.id;
    },
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    count,
    rowSelection: {
      state: rowSelection,
      updater
    },
    meta: {
      campaignId: campaign.id,
      currencyCode: (_a = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _a.currency_code,
      budgetType: (_b = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _b.type
    }
  });
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      { add: values.promotion_ids },
      {
        onSuccess: () => {
          toast.success(
            t2("campaigns.promotions.toast.success", {
              count: values.promotion_ids.length
            })
          );
          handleSuccess();
        },
        onError: (error) => toast.error(error.message)
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end gap-x-2", children: form.formState.errors.promotion_ids && (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: form.formState.errors.promotion_ids.message }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex size-full flex-col overflow-y-auto", children: (0, import_jsx_runtime.jsx)(
          _DataTable,
          {
            table,
            count,
            columns,
            pageSize: PAGE_SIZE,
            isLoading,
            filters,
            orderBy: [
              { key: "code", label: t2("fields.code") },
              { key: "type", label: t2("fields.type") },
              { key: "created_at", label: t2("fields.createdAt") },
              { key: "updated_at", label: t2("fields.updatedAt") }
            ],
            queryObject: raw,
            layout: "fill",
            pagination: true,
            search: "autofocus",
            noRecords: {
              message: t2("campaigns.promotions.add.list.noRecordsMessage")
            }
          }
        ) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = usePromotionTableColumns();
  const { t: t2 } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllRowsSelected(!!value)
            }
          );
        },
        cell: ({ row, table }) => {
          var _a, _b, _c;
          const { campaignId, currencyCode, budgetType } = table.options.meta;
          const isTypeSpend = budgetType === "spend";
          const isAdded = row.original.campaign_id === campaignId;
          const isAddedToADiffCampaign = !!row.original.campaign_id && row.original.campaign_id !== campaignId;
          const currencyMismatch = isTypeSpend && ((_a = row.original.application_method) == null ? void 0 : _a.currency_code) !== currencyCode;
          const isSelected = row.getIsSelected() || isAdded;
          const isIndeterminate = currencyMismatch || isAddedToADiffCampaign;
          const Component = (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: isIndeterminate ? "indeterminate" : isSelected,
              disabled: isAdded || isAddedToADiffCampaign || currencyMismatch,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
          if (isAddedToADiffCampaign) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("campaigns.promotions.alreadyAddedDiffCampaign", {
                  name: (_c = (_b = row.original) == null ? void 0 : _b.campaign) == null ? void 0 : _c.name
                }),
                side: "right",
                children: Component
              }
            );
          }
          if (currencyMismatch) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("campaigns.promotions.currencyMismatch"),
                side: "right",
                children: Component
              }
            );
          }
          if (isAdded) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("campaigns.promotions.alreadyAdded"),
                side: "right",
                children: Component
              }
            );
          }
          return Component;
        }
      }),
      ...base
    ],
    [t2, base]
  );
};
var AddCampaignPromotions = () => {
  const { id } = useParams();
  const { campaign, isError, error } = useCampaign(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: campaign && (0, import_jsx_runtime2.jsx)(AddCampaignPromotionsForm, { campaign }) });
};
export {
  AddCampaignPromotions as Component
};
//# sourceMappingURL=add-campaign-promotions-FK5IDNEX-VTKEFCYZ.js.map
