import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-DLZWPHHO.mjs
var useCustomerGroupTableFilters = () => {
  const { t } = useTranslation();
  let filters = [];
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at" },
    { label: t("fields.updatedAt"), key: "updated_at" }
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date"
  }));
  filters = [...filters, ...dateFilters];
  return filters;
};

export {
  useCustomerGroupTableFilters
};
//# sourceMappingURL=chunk-XNFM7P3M.js.map
