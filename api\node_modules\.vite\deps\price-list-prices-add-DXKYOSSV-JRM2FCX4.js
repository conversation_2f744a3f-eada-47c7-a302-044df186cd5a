import {
  PriceListCreateProductsSchema,
  usePriceListCurrencyData,
  usePriceListGridColumns
} from "./chunk-4YLIPT6L.js";
import {
  exctractPricesFromProducts,
  isProductRow
} from "./chunk-DC7EOWEK.js";
import "./chunk-EG6IR476.js";
import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-EUULPFXI.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  DataGrid
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-XYBN3KRC.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import {
  useBatchPriceListPrices,
  usePriceList
} from "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  ProgressTabs,
  Tooltip,
  createColumnHelper,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-prices-add-DXKYOSSV.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var PriceListPricesAddPricesForm = ({
  form,
  currencies,
  regions,
  pricePreferences
}) => {
  const ids = useWatch({
    control: form.control,
    name: "product_ids"
  });
  const existingProducts = useWatch({
    control: form.control,
    name: "products"
  });
  const { products, isLoading, isError, error } = useProducts({
    id: ids.map((id) => id.id),
    limit: ids.length,
    fields: "title,thumbnail,*variants"
  });
  const { setValue } = form;
  const { setCloseOnEscape } = useRouteModal();
  (0, import_react2.useEffect)(() => {
    if (!isLoading && products) {
      products.forEach((product) => {
        if (existingProducts[product.id] || !product.variants) {
          return;
        }
        setValue(`products.${product.id}.variants`, {
          ...product.variants.reduce((variants, variant) => {
            variants[variant.id] = {
              currency_prices: {},
              region_prices: {}
            };
            return variants;
          }, {})
        });
      });
    }
  }, [products, existingProducts, isLoading, setValue]);
  const columns = usePriceListGridColumns({
    currencies,
    regions,
    pricePreferences
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full flex-col divide-y overflow-hidden", children: (0, import_jsx_runtime.jsx)(
    DataGrid,
    {
      isLoading,
      columns,
      data: products,
      getSubRows: (row) => {
        if (isProductRow(row) && row.variants) {
          return row.variants;
        }
      },
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  ) });
};
var PAGE_SIZE = 50;
var PREFIX = "p";
function getInitialSelection(products) {
  return products.reduce((acc, curr) => {
    acc[curr.id] = true;
    return acc;
  }, {});
}
var PriceListPricesAddProductIdsForm = ({
  priceList,
  form
}) => {
  const { t: t2 } = useTranslation();
  const { control, setValue } = form;
  const variantIdMap = (0, import_react3.useMemo)(() => {
    return priceList.prices.reduce(
      (acc, curr) => {
        acc[curr.variant_id] = true;
        return acc;
      },
      {}
    );
  }, [priceList.prices]);
  const selectedIds = useWatch({
    control,
    name: "product_ids"
  });
  const productRecords = useWatch({
    control,
    name: "products"
  });
  const [rowSelection, setRowSelection] = (0, import_react3.useState)(
    getInitialSelection(selectedIds)
  );
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { products, count, isLoading, isError, error } = useProducts(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (fn) => {
    const state = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(state);
    const productRecordKeys = Object.keys(productRecords);
    const updatedRecords = productRecordKeys.reduce((acc, key) => {
      if (ids.includes(key)) {
        acc[key] = productRecords[key];
      }
      return acc;
    }, {});
    const update = ids.map((id) => ({ id }));
    setValue("product_ids", update, { shouldDirty: true, shouldTouch: true });
    setValue("products", updatedRecords, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(state);
  };
  const columns = useColumns();
  const filters = useProductTableFilters();
  const { table } = useDataTable({
    data: products || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: (row) => {
      var _a, _b;
      return !!((_a = row.original.variants) == null ? void 0 : _a.length) && !((_b = row.original.variants) == null ? void 0 : _b.some((v) => variantIdMap[v.id]));
    },
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    meta: {
      variantIdMap
    }
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col", children: (0, import_jsx_runtime2.jsx)(
    _DataTable,
    {
      table,
      columns,
      filters,
      pageSize: PAGE_SIZE,
      prefix: PREFIX,
      count,
      isLoading,
      layout: "fill",
      orderBy: [
        { key: "title", label: t2("fields.title") },
        { key: "status", label: t2("fields.status") },
        { key: "created_at", label: t2("fields.createdAt") },
        { key: "updated_at", label: t2("fields.updatedAt") }
      ],
      pagination: true,
      search: true,
      queryObject: raw
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTableColumns();
  return (0, import_react3.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row, table }) => {
          var _a;
          const { variantIdMap } = table.options.meta;
          const isPreselected = (_a = row.original.variants) == null ? void 0 : _a.some(
            (v) => variantIdMap[v.id]
          );
          const isDisabled = !row.getCanSelect() || isPreselected;
          const isChecked = row.getIsSelected() || isPreselected;
          const Component = (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: isChecked,
              disabled: isDisabled,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
          if (isPreselected) {
            return (0, import_jsx_runtime2.jsx)(Tooltip, { content: "This product is already in the price list", children: Component });
          }
          if (isDisabled) {
            return (0, import_jsx_runtime2.jsx)(Tooltip, { content: "This product has no variants", children: Component });
          }
          return Component;
        }
      }),
      ...base
    ],
    [base]
  );
};
var PriceListPricesAddSchema = z.object({
  product_ids: z.array(z.object({ id: z.string() })).min(1),
  products: PriceListCreateProductsSchema
});
var PriceListPricesAddProductIdsSchema = PriceListPricesAddSchema.pick(
  {
    product_ids: true
  }
);
var PriceListPricesAddProductsIdsFields = Object.keys(
  PriceListPricesAddProductIdsSchema.shape
);
var PriceListPricesAddProductsSchema = PriceListPricesAddSchema.pick({
  products: true
});
var PriceListPricesAddProductsFields = Object.keys(
  PriceListPricesAddProductsSchema.shape
);
var tabOrder = [
  "product",
  "price"
  /* PRICE */
];
var initialTabState = {
  [
    "product"
    /* PRODUCT */
  ]: "in-progress",
  [
    "price"
    /* PRICE */
  ]: "not-started"
};
var PriceListPricesAddForm = ({
  priceList,
  regions,
  currencies,
  pricePreferences
}) => {
  const [tab, setTab] = (0, import_react.useState)(
    "product"
    /* PRODUCT */
  );
  const [tabState, setTabState] = (0, import_react.useState)(initialTabState);
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      products: {},
      product_ids: []
    },
    resolver: t(PriceListPricesAddSchema)
  });
  const { mutateAsync, isPending } = useBatchPriceListPrices(priceList.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    const { products } = values;
    const prices = exctractPricesFromProducts(products, regions);
    await mutateAsync(
      {
        create: prices
      },
      {
        onSuccess: () => {
          toast.success(t2("priceLists.products.add.successToast"));
          handleSuccess();
        },
        onError: (e) => toast.error(e.message)
      }
    );
  });
  const partialFormValidation = (fields, schema) => {
    form.clearErrors(fields);
    const values = fields.reduce((acc, key) => {
      acc[key] = form.getValues(key);
      return acc;
    }, {});
    const validationResult = schema.safeParse(values);
    if (!validationResult.success) {
      validationResult.error.errors.forEach(({ path, message, code }) => {
        form.setError(path.join("."), {
          type: code,
          message
        });
      });
      return false;
    }
    return true;
  };
  const isTabDirty = (tab2) => {
    switch (tab2) {
      case "product": {
        const fields = PriceListPricesAddProductsIdsFields;
        return fields.some((field) => {
          return form.getFieldState(field).isDirty;
        });
      }
      case "price": {
        const fields = PriceListPricesAddProductsFields;
        return fields.some((field) => {
          return form.getFieldState(field).isDirty;
        });
      }
    }
  };
  const handleChangeTab = (update) => {
    if (tab === update) {
      return;
    }
    if (tabOrder.indexOf(update) < tabOrder.indexOf(tab)) {
      const isCurrentTabDirty = isTabDirty(tab);
      setTabState((prev) => ({
        ...prev,
        [tab]: isCurrentTabDirty ? prev[tab] : "not-started",
        [update]: "in-progress"
      }));
      setTab(update);
      return;
    }
    const tabs = tabOrder.slice(0, tabOrder.indexOf(update));
    for (const tab2 of tabs) {
      if (tab2 === "product") {
        if (!partialFormValidation(
          PriceListPricesAddProductsIdsFields,
          PriceListPricesAddProductIdsSchema
        )) {
          setTabState((prev) => ({
            ...prev,
            [tab2]: "in-progress"
          }));
          setTab(tab2);
          return;
        }
        setTabState((prev) => ({
          ...prev,
          [tab2]: "completed"
        }));
      }
    }
    setTabState((prev) => ({
      ...prev,
      [tab]: "completed",
      [update]: "in-progress"
    }));
    setTab(update);
  };
  const handleNextTab = (tab2) => {
    if (tabOrder.indexOf(tab2) + 1 >= tabOrder.length) {
      return;
    }
    const nextTab = tabOrder[tabOrder.indexOf(tab2) + 1];
    handleChangeTab(nextTab);
  };
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime3.jsx)(
    ProgressTabs,
    {
      value: tab,
      onValueChange: (tab2) => handleChangeTab(tab2),
      className: "flex h-full flex-col overflow-hidden",
      children: (0, import_jsx_runtime3.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
        (0, import_jsx_runtime3.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime3.jsx)("div", { className: "flex w-full items-center justify-between gap-x-4", children: (0, import_jsx_runtime3.jsx)("div", { className: "-my-2 w-full max-w-[600px] border-l", children: (0, import_jsx_runtime3.jsxs)(ProgressTabs.List, { className: "grid w-full grid-cols-3", children: [
          (0, import_jsx_runtime3.jsx)(
            ProgressTabs.Trigger,
            {
              status: tabState.product,
              value: "product",
              children: t2("priceLists.create.tabs.products")
            }
          ),
          (0, import_jsx_runtime3.jsx)(
            ProgressTabs.Trigger,
            {
              status: tabState.price,
              value: "price",
              children: t2("priceLists.create.tabs.prices")
            }
          )
        ] }) }) }) }),
        (0, import_jsx_runtime3.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
          (0, import_jsx_runtime3.jsx)(
            ProgressTabs.Content,
            {
              className: "size-full overflow-y-auto",
              value: "product",
              children: (0, import_jsx_runtime3.jsx)(
                PriceListPricesAddProductIdsForm,
                {
                  form,
                  priceList
                }
              )
            }
          ),
          (0, import_jsx_runtime3.jsx)(
            ProgressTabs.Content,
            {
              className: "size-full overflow-hidden",
              value: "price",
              children: (0, import_jsx_runtime3.jsx)(
                PriceListPricesAddPricesForm,
                {
                  form,
                  regions,
                  currencies,
                  pricePreferences
                }
              )
            }
          )
        ] }),
        (0, import_jsx_runtime3.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime3.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime3.jsx)(
            PrimaryButton,
            {
              tab,
              next: handleNextTab,
              isLoading: isPending
            }
          )
        ] }) })
      ] })
    }
  ) });
};
var PrimaryButton = ({ tab, next, isLoading }) => {
  const { t: t2 } = useTranslation();
  if (tab === "price") {
    return (0, import_jsx_runtime3.jsx)(
      Button,
      {
        type: "submit",
        variant: "primary",
        size: "small",
        isLoading,
        children: t2("actions.save")
      },
      "submit-button"
    );
  }
  return (0, import_jsx_runtime3.jsx)(
    Button,
    {
      type: "button",
      variant: "primary",
      size: "small",
      onClick: () => next(tab),
      children: t2("actions.continue")
    },
    "next-button"
  );
};
var PriceListProductsAdd = () => {
  const { id } = useParams();
  const { price_list, isPending, isError, error } = usePriceList(id);
  const { currencies, regions, pricePreferences, isReady } = usePriceListCurrencyData();
  const ready = isReady && !isPending && !!price_list;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime4.jsx)(
    PriceListPricesAddForm,
    {
      priceList: price_list,
      currencies,
      regions,
      pricePreferences
    }
  ) });
};
export {
  PriceListProductsAdd as Component
};
//# sourceMappingURL=price-list-prices-add-DXKYOSSV-JRM2FCX4.js.map
