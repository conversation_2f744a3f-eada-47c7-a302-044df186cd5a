import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import {
  useReservationItem,
  useUpdateReservationItem
} from "./chunk-OX66UHHF.js";
import {
  useInventoryItem
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  Text,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/edit-reservation-GEJHV6OI.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditReservationSchema = z.object({
  location_id: z.string(),
  description: z.string().optional(),
  quantity: z.number().min(1)
});
var AttributeGridRow = ({
  title,
  value
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 divide-x", children: [
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: title }),
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: value })
  ] });
};
var getDefaultValues = (reservation) => {
  return {
    quantity: reservation.quantity,
    location_id: reservation.location_id,
    description: reservation.description ?? void 0
  };
};
var EditReservationForm = ({
  reservation,
  item,
  locations
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: getDefaultValues(reservation),
    resolver: t(EditReservationSchema)
  });
  const { mutateAsync } = useUpdateReservationItem(reservation.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    mutateAsync(values, {
      onSuccess: () => {
        toast.success(t2("inventory.reservation.updateSuccessToast"));
        handleSuccess();
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  });
  const reservedQuantity = form.watch("quantity");
  const locationId = form.watch("location_id");
  const level = item.location_levels.find(
    (level2) => level2.location_id === locationId
  );
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-auto", children: [
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "location_id",
              render: ({ field: { onChange, value, ref, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("inventory.reservation.location") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                    Select,
                    {
                      value,
                      onValueChange: (v) => {
                        onChange(v);
                      },
                      ...field,
                      children: [
                        (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                        (0, import_jsx_runtime.jsx)(Select.Content, { children: (locations || []).map((r) => (0, import_jsx_runtime.jsx)(Select.Item, { value: r.id, children: r.name }, r.id)) })
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-4 divide-y rounded-lg border", children: [
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.title"),
                value: item.title ?? item.sku
              }
            ),
            (0, import_jsx_runtime.jsx)(AttributeGridRow, { title: t2("fields.sku"), value: item.sku }),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.inStock"),
                value: level.stocked_quantity
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("inventory.available"),
                value: level.stocked_quantity - (level.reserved_quantity - reservation.quantity) - reservedQuantity
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "quantity",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("inventory.reservation.reservedAmount") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      min: 0,
                      max: (level.available_quantity || 0) + (reservation.quantity || 0),
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "description",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.description") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: false, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ReservationEdit = () => {
  var _a;
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { reservation, isPending, isError, error } = useReservationItem(id);
  const { inventory_item: inventoryItem } = useInventoryItem(
    reservation == null ? void 0 : reservation.inventory_item_id,
    {
      enabled: !!reservation
    }
  );
  const { stock_locations } = useStockLocations(
    {
      id: (_a = inventoryItem == null ? void 0 : inventoryItem.location_levels) == null ? void 0 : _a.map(
        (l) => l.location_id
      )
    },
    {
      enabled: !!(inventoryItem == null ? void 0 : inventoryItem.location_levels)
    }
  );
  const ready = !isPending && reservation && inventoryItem && stock_locations;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("inventory.reservation.editItemDetails") }) }),
    ready && (0, import_jsx_runtime2.jsx)(
      EditReservationForm,
      {
        locations: stock_locations,
        reservation,
        item: inventoryItem
      }
    )
  ] });
};
export {
  ReservationEdit as Component
};
//# sourceMappingURL=edit-reservation-GEJHV6OI-HDVU2ZKJ.js.map
