{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-A2UMBW3V.mjs"], "sourcesContent": ["import {\n  useCustomerGroups\n} from \"./chunk-F6PXCY3N.mjs\";\n\n// src/hooks/table/filters/use-customer-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useCustomerTableFilters = (exclude) => {\n  const { t } = useTranslation();\n  const isGroupsExcluded = exclude?.includes(\"groups\");\n  const { customer_groups } = useCustomerGroups(\n    {\n      limit: 1e3\n    },\n    {\n      enabled: !isGroupsExcluded\n    }\n  );\n  let filters = [];\n  if (customer_groups && !isGroupsExcluded) {\n    const customerGroupFilter = {\n      key: \"groups\",\n      label: t(\"customers.groups.label\"),\n      type: \"select\",\n      multiple: true,\n      options: customer_groups.map((s) => ({\n        label: s.name,\n        value: s.id\n      }))\n    };\n    filters = [...filters, customerGroupFilter];\n  }\n  const hasAccountFilter = {\n    key: \"has_account\",\n    label: t(\"fields.account\"),\n    type: \"select\",\n    options: [\n      {\n        label: t(\"customers.registered\"),\n        value: \"true\"\n      },\n      {\n        label: t(\"customers.guest\"),\n        value: \"false\"\n      }\n    ]\n  };\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  filters = [...filters, hasAccountFilter, ...dateFilters];\n  return filters;\n};\n\nexport {\n  useCustomerTableFilters\n};\n"], "mappings": ";;;;;;;;AAMA,IAAI,0BAA0B,CAAC,YAAY;AACzC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,mBAAmB,mCAAS,SAAS;AAC3C,QAAM,EAAE,gBAAgB,IAAI;AAAA,IAC1B;AAAA,MACE,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,SAAS,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,UAAU,CAAC;AACf,MAAI,mBAAmB,CAAC,kBAAkB;AACxC,UAAM,sBAAsB;AAAA,MAC1B,KAAK;AAAA,MACL,OAAO,EAAE,wBAAwB;AAAA,MACjC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,gBAAgB,IAAI,CAAC,OAAO;AAAA,QACnC,OAAO,EAAE;AAAA,QACT,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,IACJ;AACA,cAAU,CAAC,GAAG,SAAS,mBAAmB;AAAA,EAC5C;AACA,QAAM,mBAAmB;AAAA,IACvB,KAAK;AAAA,IACL,OAAO,EAAE,gBAAgB;AAAA,IACzB,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,OAAO,EAAE,sBAAsB;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,iBAAiB;AAAA,QAC1B,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,YAAU,CAAC,GAAG,SAAS,kBAAkB,GAAG,WAAW;AACvD,SAAO;AACT;", "names": []}