{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-RZD5DU5K.mjs"], "sourcesContent": ["import {\n  useDebouncedSearch\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  TextSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  useProductCategories\n} from \"./chunk-ZJ3OFMHB.mjs\";\n\n// src/routes/products/common/components/category-combobox/category-combobox.tsx\nimport {\n  ArrowUturnLeft,\n  EllipseMiniSolid,\n  TriangleRightMini,\n  TrianglesMini,\n  XMarkMini\n} from \"@medusajs/icons\";\nimport { Divider, Text, clx } from \"@medusajs/ui\";\nimport { Popover as RadixPopover } from \"radix-ui\";\nimport {\n  Fragment,\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useMemo,\n  useRef,\n  useState\n} from \"react\";\nimport { Trans, useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TABLUAR_NUM_WIDTH = 8;\nvar TAG_BASE_WIDTH = 28;\nvar CategoryCombobox = forwardRef(({ value, onChange, className, ...props }, ref) => {\n  const innerRef = useRef(null);\n  useImperativeHandle(\n    ref,\n    () => innerRef.current,\n    []\n  );\n  const [open, setOpen] = useState(false);\n  const { i18n, t } = useTranslation();\n  const [level, setLevel] = useState([]);\n  const { searchValue, onSearchValueChange, query } = useDebouncedSearch();\n  const { product_categories, isPending, isError, error } = useProductCategories(\n    {\n      q: query,\n      parent_category_id: !searchValue ? getParentId(level) : void 0,\n      include_descendants_tree: !searchValue ? true : false\n    },\n    {\n      enabled: open\n    }\n  );\n  const [showLoading, setShowLoading] = useState(false);\n  useEffect(() => {\n    let timeoutId;\n    if (isPending) {\n      setShowLoading(true);\n    } else {\n      timeoutId = setTimeout(() => {\n        setShowLoading(false);\n      }, 150);\n    }\n    return () => {\n      clearTimeout(timeoutId);\n    };\n  }, [isPending]);\n  useEffect(() => {\n    if (searchValue) {\n      setLevel([]);\n    }\n  }, [searchValue]);\n  function handleLevelUp(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    setLevel(level.slice(0, level.length - 1));\n    innerRef.current?.focus();\n  }\n  function handleLevelDown(option) {\n    return (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n      setLevel([...level, { id: option.value, label: option.label }]);\n      innerRef.current?.focus();\n    };\n  }\n  const handleSelect = useCallback(\n    (option) => {\n      return (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (isSelected(value, option.value)) {\n          onChange(value.filter((v) => v !== option.value));\n        } else {\n          onChange([...value, option.value]);\n        }\n        innerRef.current?.focus();\n      };\n    },\n    [value, onChange]\n  );\n  function handleOpenChange(open2) {\n    if (!open2) {\n      onSearchValueChange(\"\");\n      setLevel([]);\n    }\n    if (open2) {\n      requestAnimationFrame(() => {\n        innerRef.current?.focus();\n      });\n    }\n    setOpen(open2);\n  }\n  const options = getOptions(product_categories || []);\n  const showTag = value.length > 0;\n  const showSelected = !open && value.length > 0;\n  const tagWidth = useMemo(() => {\n    const count = value.length;\n    const digits = count.toString().length;\n    return TAG_BASE_WIDTH + digits * TABLUAR_NUM_WIDTH;\n  }, [value]);\n  const showLevelUp = !searchValue && level.length > 0;\n  const [focusedIndex, setFocusedIndex] = useState(-1);\n  const handleKeyDown = useCallback(\n    (e) => {\n      if (!open) {\n        return;\n      }\n      const optionsLength = showLevelUp ? options.length + 1 : options.length;\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setFocusedIndex((prev) => {\n          const nextIndex = prev < optionsLength - 1 ? prev + 1 : prev;\n          return nextIndex;\n        });\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setFocusedIndex((prev) => {\n          return prev > 0 ? prev - 1 : prev;\n        });\n      } else if (e.key === \"ArrowRight\") {\n        const index = showLevelUp ? focusedIndex - 1 : focusedIndex;\n        const hasChildren = options[index]?.has_children;\n        if (!hasChildren || !!searchValue) {\n          return;\n        }\n        e.preventDefault();\n        setLevel([\n          ...level,\n          {\n            id: options[index].value,\n            label: options[index].label\n          }\n        ]);\n        setFocusedIndex(0);\n      } else if (e.key === \"Enter\" && focusedIndex !== -1) {\n        e.preventDefault();\n        if (showLevelUp && focusedIndex === 0) {\n          setLevel(level.slice(0, level.length - 1));\n          setFocusedIndex(0);\n          return;\n        }\n        const index = showLevelUp ? focusedIndex - 1 : focusedIndex;\n        handleSelect(options[index])(e);\n      }\n    },\n    [open, focusedIndex, options, level, handleSelect, searchValue, showLevelUp]\n  );\n  useEffect(() => {\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      window.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(RadixPopover.Root, { open, onOpenChange: handleOpenChange, children: [\n    /* @__PURE__ */ jsx(\n      RadixPopover.Anchor,\n      {\n        asChild: true,\n        onClick: () => {\n          if (!open) {\n            handleOpenChange(true);\n          }\n        },\n        children: /* @__PURE__ */ jsxs(\n          \"div\",\n          {\n            \"data-anchor\": true,\n            className: clx(\n              \"relative flex cursor-pointer items-center gap-x-2 overflow-hidden\",\n              \"h-8 w-full rounded-md\",\n              \"bg-ui-bg-field transition-fg shadow-borders-base\",\n              \"has-[input:focus]:shadow-borders-interactive-with-active\",\n              \"has-[:invalid]:shadow-borders-error has-[[aria-invalid=true]]:shadow-borders-error\",\n              \"has-[:disabled]:bg-ui-bg-disabled has-[:disabled]:text-ui-fg-disabled has-[:disabled]:cursor-not-allowed\",\n              {\n                // Fake the focus state as long as the popover is open,\n                // this prevents the styling from flickering when navigating\n                // between levels.\n                \"shadow-borders-interactive-with-active\": open\n              },\n              className\n            ),\n            style: {\n              \"--tag-width\": `${tagWidth}px`\n            },\n            children: [\n              showTag && /* @__PURE__ */ jsxs(\n                \"button\",\n                {\n                  type: \"button\",\n                  onClick: (e) => {\n                    e.preventDefault();\n                    onChange([]);\n                  },\n                  className: \"bg-ui-bg-base hover:bg-ui-bg-base-hover txt-compact-small-plus text-ui-fg-subtle focus-within:border-ui-fg-interactive transition-fg absolute left-0.5 top-0.5 flex h-[28px] items-center rounded-[4px] border py-[3px] pl-1.5 pr-1 outline-none\",\n                  children: [\n                    /* @__PURE__ */ jsx(\"span\", { className: \"tabular-nums\", children: value.length }),\n                    /* @__PURE__ */ jsx(XMarkMini, { className: \"text-ui-fg-muted\" })\n                  ]\n                }\n              ),\n              showSelected && /* @__PURE__ */ jsx(\"div\", { className: \"pointer-events-none absolute inset-y-0 left-[calc(var(--tag-width)+8px)] flex size-full items-center\", children: /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: t(\"general.selected\") }) }),\n              /* @__PURE__ */ jsx(\n                \"input\",\n                {\n                  ref: innerRef,\n                  value: searchValue,\n                  onChange: (e) => {\n                    onSearchValueChange(e.target.value);\n                  },\n                  className: clx(\n                    \"txt-compact-small size-full cursor-pointer appearance-none bg-transparent pr-8 outline-none\",\n                    \"hover:bg-ui-bg-field-hover\",\n                    \"focus:cursor-text\",\n                    \"placeholder:text-ui-fg-muted\",\n                    {\n                      \"pl-2\": !showTag,\n                      \"pl-[calc(var(--tag-width)+8px)]\": showTag\n                    }\n                  ),\n                  ...props\n                }\n              ),\n              /* @__PURE__ */ jsx(\n                \"button\",\n                {\n                  type: \"button\",\n                  onClick: () => handleOpenChange(true),\n                  className: \"text-ui-fg-muted transition-fg hover:bg-ui-bg-field-hover absolute right-0 flex size-8 items-center justify-center rounded-r outline-none\",\n                  children: /* @__PURE__ */ jsx(TrianglesMini, { className: \"text-ui-fg-muted\" })\n                }\n              )\n            ]\n          }\n        )\n      }\n    ),\n    /* @__PURE__ */ jsxs(\n      RadixPopover.Content,\n      {\n        sideOffset: 4,\n        role: \"listbox\",\n        className: clx(\n          \"shadow-elevation-flyout bg-ui-bg-base -left-2 z-50 w-[var(--radix-popper-anchor-width)] rounded-[8px]\",\n          \"max-h-[200px] overflow-y-auto\",\n          \"data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95\",\n          \"data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95\",\n          \"data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\"\n        ),\n        onInteractOutside: (e) => {\n          e.preventDefault();\n          const target = e.target;\n          if (target.closest(\"[data-anchor]\")) {\n            return;\n          }\n          handleOpenChange(false);\n        },\n        children: [\n          showLevelUp && /* @__PURE__ */ jsxs(Fragment, { children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"p-1\", children: /* @__PURE__ */ jsxs(\n              \"button\",\n              {\n                \"data-active\": focusedIndex === 0,\n                role: \"button\",\n                className: clx(\n                  \"transition-fg grid w-full appearance-none grid-cols-[20px_1fr] items-center justify-center gap-2 rounded-md px-2 py-1.5 text-left outline-none\",\n                  \"data-[active=true]:bg-ui-bg-field-hover\"\n                ),\n                type: \"button\",\n                onClick: handleLevelUp,\n                onMouseEnter: () => setFocusedIndex(0),\n                onMouseLeave: () => setFocusedIndex(-1),\n                tabIndex: -1,\n                children: [\n                  /* @__PURE__ */ jsx(ArrowUturnLeft, { className: \"text-ui-fg-muted\" }),\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: getParentLabel(level) })\n                ]\n              }\n            ) }),\n            /* @__PURE__ */ jsx(Divider, {})\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"p-1\", children: [\n            options.length > 0 && !showLoading && options.map((option, index) => /* @__PURE__ */ jsxs(\n              \"div\",\n              {\n                className: clx(\n                  \"transition-fg bg-ui-bg-base grid cursor-pointer grid-cols-1 items-center gap-2 overflow-hidden\",\n                  {\n                    \"grid-cols-[1fr_32px]\": option.has_children && !searchValue\n                  }\n                ),\n                children: [\n                  /* @__PURE__ */ jsxs(\n                    \"button\",\n                    {\n                      \"data-active\": showLevelUp ? focusedIndex === index + 1 : focusedIndex === index,\n                      type: \"button\",\n                      role: \"option\",\n                      className: clx(\n                        \"grid h-full w-full appearance-none grid-cols-[20px_1fr] items-center gap-2 overflow-hidden rounded-md px-2 py-1.5 text-left outline-none\",\n                        \"data-[active=true]:bg-ui-bg-field-hover\"\n                      ),\n                      onClick: handleSelect(option),\n                      onMouseEnter: () => setFocusedIndex(showLevelUp ? index + 1 : index),\n                      onMouseLeave: () => setFocusedIndex(-1),\n                      tabIndex: -1,\n                      children: [\n                        /* @__PURE__ */ jsx(\"div\", { className: \"flex size-5 items-center justify-center\", children: isSelected(value, option.value) && /* @__PURE__ */ jsx(EllipseMiniSolid, {}) }),\n                        /* @__PURE__ */ jsx(\n                          Text,\n                          {\n                            as: \"span\",\n                            size: \"small\",\n                            leading: \"compact\",\n                            className: \"w-full truncate\",\n                            children: option.label\n                          }\n                        )\n                      ]\n                    }\n                  ),\n                  option.has_children && !searchValue && /* @__PURE__ */ jsx(\n                    \"button\",\n                    {\n                      className: clx(\n                        \"text-ui-fg-muted flex size-8 appearance-none items-center justify-center rounded-md outline-none\",\n                        \"hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed\"\n                      ),\n                      type: \"button\",\n                      onClick: handleLevelDown(option),\n                      tabIndex: -1,\n                      children: /* @__PURE__ */ jsx(TriangleRightMini, {})\n                    }\n                  )\n                ]\n              },\n              option.value\n            )),\n            showLoading && Array.from({ length: 5 }).map((_, index) => /* @__PURE__ */ jsxs(\n              \"div\",\n              {\n                className: \"grid grid-cols-[20px_1fr_20px] gap-2 px-2 py-1.5\",\n                children: [\n                  /* @__PURE__ */ jsx(\"div\", {}),\n                  /* @__PURE__ */ jsx(TextSkeleton, { size: \"small\", leading: \"compact\" }),\n                  /* @__PURE__ */ jsx(\"div\", {})\n                ]\n              },\n              index\n            )),\n            options.length === 0 && !showLoading && /* @__PURE__ */ jsx(\"div\", { className: \"px-2 py-1.5\", children: /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: query ? /* @__PURE__ */ jsx(\n              Trans,\n              {\n                i18n,\n                i18nKey: \"general.noResultsTitle\",\n                tOptions: {\n                  query\n                },\n                components: [/* @__PURE__ */ jsx(\"span\", { className: \"font-medium\" }, \"query\")]\n              }\n            ) : t(\"general.noResultsTitle\") }) })\n          ] })\n        ]\n      }\n    )\n  ] });\n});\nCategoryCombobox.displayName = \"CategoryCombobox\";\nfunction getParentId(level) {\n  if (!level.length) {\n    return \"null\";\n  }\n  return level[level.length - 1].id;\n}\nfunction getParentLabel(level) {\n  if (!level.length) {\n    return null;\n  }\n  return level[level.length - 1].label;\n}\nfunction getOptions(categories) {\n  return categories.map((cat) => {\n    return {\n      value: cat.id,\n      label: cat.name,\n      has_children: cat.category_children?.length > 0\n    };\n  });\n}\nfunction isSelected(values, value) {\n  return values.includes(value);\n}\n\nexport {\n  CategoryCombobox\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,mBASO;AAEP,yBAA0B;AAC1B,IAAI,oBAAoB;AACxB,IAAI,iBAAiB;AACrB,IAAI,uBAAmB,yBAAW,CAAC,EAAE,OAAO,UAAU,WAAW,GAAG,MAAM,GAAG,QAAQ;AACnF,QAAM,eAAW,qBAAO,IAAI;AAC5B;AAAA,IACE;AAAA,IACA,MAAM,SAAS;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAS,KAAK;AACtC,QAAM,EAAE,MAAM,EAAE,IAAI,eAAe;AACnC,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,CAAC,CAAC;AACrC,QAAM,EAAE,aAAa,qBAAqB,MAAM,IAAI,mBAAmB;AACvE,QAAM,EAAE,oBAAoB,WAAW,SAAS,MAAM,IAAI;AAAA,IACxD;AAAA,MACE,GAAG;AAAA,MACH,oBAAoB,CAAC,cAAc,YAAY,KAAK,IAAI;AAAA,MACxD,0BAA0B,CAAC,cAAc,OAAO;AAAA,IAClD;AAAA,IACA;AAAA,MACE,SAAS;AAAA,IACX;AAAA,EACF;AACA,QAAM,CAAC,aAAa,cAAc,QAAI,uBAAS,KAAK;AACpD,8BAAU,MAAM;AACd,QAAI;AACJ,QAAI,WAAW;AACb,qBAAe,IAAI;AAAA,IACrB,OAAO;AACL,kBAAY,WAAW,MAAM;AAC3B,uBAAe,KAAK;AAAA,MACtB,GAAG,GAAG;AAAA,IACR;AACA,WAAO,MAAM;AACX,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,8BAAU,MAAM;AACd,QAAI,aAAa;AACf,eAAS,CAAC,CAAC;AAAA,IACb;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,WAAS,cAAc,GAAG;AA1E5B;AA2EI,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,aAAS,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,CAAC;AACzC,mBAAS,YAAT,mBAAkB;AAAA,EACpB;AACA,WAAS,gBAAgB,QAAQ;AAC/B,WAAO,CAAC,MAAM;AAjFlB;AAkFM,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,eAAS,CAAC,GAAG,OAAO,EAAE,IAAI,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC;AAC9D,qBAAS,YAAT,mBAAkB;AAAA,IACpB;AAAA,EACF;AACA,QAAM,mBAAe;AAAA,IACnB,CAAC,WAAW;AACV,aAAO,CAAC,MAAM;AA1FpB;AA2FQ,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,YAAI,WAAW,OAAO,OAAO,KAAK,GAAG;AACnC,mBAAS,MAAM,OAAO,CAAC,MAAM,MAAM,OAAO,KAAK,CAAC;AAAA,QAClD,OAAO;AACL,mBAAS,CAAC,GAAG,OAAO,OAAO,KAAK,CAAC;AAAA,QACnC;AACA,uBAAS,YAAT,mBAAkB;AAAA,MACpB;AAAA,IACF;AAAA,IACA,CAAC,OAAO,QAAQ;AAAA,EAClB;AACA,WAAS,iBAAiB,OAAO;AAC/B,QAAI,CAAC,OAAO;AACV,0BAAoB,EAAE;AACtB,eAAS,CAAC,CAAC;AAAA,IACb;AACA,QAAI,OAAO;AACT,4BAAsB,MAAM;AA7GlC;AA8GQ,uBAAS,YAAT,mBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,YAAQ,KAAK;AAAA,EACf;AACA,QAAM,UAAU,WAAW,sBAAsB,CAAC,CAAC;AACnD,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,eAAe,CAAC,QAAQ,MAAM,SAAS;AAC7C,QAAM,eAAW,sBAAQ,MAAM;AAC7B,UAAM,QAAQ,MAAM;AACpB,UAAM,SAAS,MAAM,SAAS,EAAE;AAChC,WAAO,iBAAiB,SAAS;AAAA,EACnC,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,cAAc,CAAC,eAAe,MAAM,SAAS;AACnD,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,EAAE;AACnD,QAAM,oBAAgB;AAAA,IACpB,CAAC,MAAM;AA9HX;AA+HM,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,YAAM,gBAAgB,cAAc,QAAQ,SAAS,IAAI,QAAQ;AACjE,UAAI,EAAE,QAAQ,aAAa;AACzB,UAAE,eAAe;AACjB,wBAAgB,CAAC,SAAS;AACxB,gBAAM,YAAY,OAAO,gBAAgB,IAAI,OAAO,IAAI;AACxD,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,WAAW,EAAE,QAAQ,WAAW;AAC9B,UAAE,eAAe;AACjB,wBAAgB,CAAC,SAAS;AACxB,iBAAO,OAAO,IAAI,OAAO,IAAI;AAAA,QAC/B,CAAC;AAAA,MACH,WAAW,EAAE,QAAQ,cAAc;AACjC,cAAM,QAAQ,cAAc,eAAe,IAAI;AAC/C,cAAM,eAAc,aAAQ,KAAK,MAAb,mBAAgB;AACpC,YAAI,CAAC,eAAe,CAAC,CAAC,aAAa;AACjC;AAAA,QACF;AACA,UAAE,eAAe;AACjB,iBAAS;AAAA,UACP,GAAG;AAAA,UACH;AAAA,YACE,IAAI,QAAQ,KAAK,EAAE;AAAA,YACnB,OAAO,QAAQ,KAAK,EAAE;AAAA,UACxB;AAAA,QACF,CAAC;AACD,wBAAgB,CAAC;AAAA,MACnB,WAAW,EAAE,QAAQ,WAAW,iBAAiB,IAAI;AACnD,UAAE,eAAe;AACjB,YAAI,eAAe,iBAAiB,GAAG;AACrC,mBAAS,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,CAAC;AACzC,0BAAgB,CAAC;AACjB;AAAA,QACF;AACA,cAAM,QAAQ,cAAc,eAAe,IAAI;AAC/C,qBAAa,QAAQ,KAAK,CAAC,EAAE,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,CAAC,MAAM,cAAc,SAAS,OAAO,cAAc,aAAa,WAAW;AAAA,EAC7E;AACA,8BAAU,MAAM;AACd,WAAO,iBAAiB,WAAW,aAAa;AAChD,WAAO,MAAM;AACX,aAAO,oBAAoB,WAAW,aAAa;AAAA,IACrD;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,aAAa,MAAM,EAAE,MAAM,cAAc,kBAAkB,UAAU;AAAA,QAC/E;AAAA,MACd,aAAa;AAAA,MACb;AAAA,QACE,SAAS;AAAA,QACT,SAAS,MAAM;AACb,cAAI,CAAC,MAAM;AACT,6BAAiB,IAAI;AAAA,UACvB;AAAA,QACF;AAAA,QACA,cAA0B;AAAA,UACxB;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,cACT;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAAA;AAAA;AAAA,gBAIE,0CAA0C;AAAA,cAC5C;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,eAAe,GAAG,QAAQ;AAAA,YAC5B;AAAA,YACA,UAAU;AAAA,cACR,eAA2B;AAAA,gBACzB;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,SAAS,CAAC,MAAM;AACd,sBAAE,eAAe;AACjB,6BAAS,CAAC,CAAC;AAAA,kBACb;AAAA,kBACA,WAAW;AAAA,kBACX,UAAU;AAAA,wBACQ,wBAAI,QAAQ,EAAE,WAAW,gBAAgB,UAAU,MAAM,OAAO,CAAC;AAAA,wBACjE,wBAAI,WAAW,EAAE,WAAW,mBAAmB,CAAC;AAAA,kBAClE;AAAA,gBACF;AAAA,cACF;AAAA,cACA,oBAAgC,wBAAI,OAAO,EAAE,WAAW,wGAAwG,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;AAAA,kBAC7P;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,UAAU,CAAC,MAAM;AACf,wCAAoB,EAAE,OAAO,KAAK;AAAA,kBACpC;AAAA,kBACA,WAAW;AAAA,oBACT;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,sBACE,QAAQ,CAAC;AAAA,sBACT,mCAAmC;AAAA,oBACrC;AAAA,kBACF;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF;AAAA,kBACgB;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,SAAS,MAAM,iBAAiB,IAAI;AAAA,kBACpC,WAAW;AAAA,kBACX,cAA0B,wBAAI,eAAe,EAAE,WAAW,mBAAmB,CAAC;AAAA,gBAChF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,QACgB;AAAA,MACd,aAAa;AAAA,MACb;AAAA,QACE,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,mBAAmB,CAAC,MAAM;AACxB,YAAE,eAAe;AACjB,gBAAM,SAAS,EAAE;AACjB,cAAI,OAAO,QAAQ,eAAe,GAAG;AACnC;AAAA,UACF;AACA,2BAAiB,KAAK;AAAA,QACxB;AAAA,QACA,UAAU;AAAA,UACR,mBAA+B,yBAAK,uBAAU,EAAE,UAAU;AAAA,gBACxC,wBAAI,OAAO,EAAE,WAAW,OAAO,cAA0B;AAAA,cACvE;AAAA,cACA;AAAA,gBACE,eAAe,iBAAiB;AAAA,gBAChC,MAAM;AAAA,gBACN,WAAW;AAAA,kBACT;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,cAAc,MAAM,gBAAgB,CAAC;AAAA,gBACrC,cAAc,MAAM,gBAAgB,EAAE;AAAA,gBACtC,UAAU;AAAA,gBACV,UAAU;AAAA,sBACQ,wBAAI,gBAAgB,EAAE,WAAW,mBAAmB,CAAC;AAAA,sBACrD,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,eAAe,KAAK,EAAE,CAAC;AAAA,gBAClG;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,wBAAI,SAAS,CAAC,CAAC;AAAA,UACjC,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,OAAO,UAAU;AAAA,YACxD,QAAQ,SAAS,KAAK,CAAC,eAAe,QAAQ,IAAI,CAAC,QAAQ,cAA0B;AAAA,cACnF;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,wBAAwB,OAAO,gBAAgB,CAAC;AAAA,kBAClD;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,sBACQ;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,eAAe,cAAc,iBAAiB,QAAQ,IAAI,iBAAiB;AAAA,sBAC3E,MAAM;AAAA,sBACN,MAAM;AAAA,sBACN,WAAW;AAAA,wBACT;AAAA,wBACA;AAAA,sBACF;AAAA,sBACA,SAAS,aAAa,MAAM;AAAA,sBAC5B,cAAc,MAAM,gBAAgB,cAAc,QAAQ,IAAI,KAAK;AAAA,sBACnE,cAAc,MAAM,gBAAgB,EAAE;AAAA,sBACtC,UAAU;AAAA,sBACV,UAAU;AAAA,4BACQ,wBAAI,OAAO,EAAE,WAAW,2CAA2C,UAAU,WAAW,OAAO,OAAO,KAAK,SAAqB,wBAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC;AAAA,4BAC3J;AAAA,0BACd;AAAA,0BACA;AAAA,4BACE,IAAI;AAAA,4BACJ,MAAM;AAAA,4BACN,SAAS;AAAA,4BACT,WAAW;AAAA,4BACX,UAAU,OAAO;AAAA,0BACnB;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,OAAO,gBAAgB,CAAC,mBAA+B;AAAA,oBACrD;AAAA,oBACA;AAAA,sBACE,WAAW;AAAA,wBACT;AAAA,wBACA;AAAA,sBACF;AAAA,sBACA,MAAM;AAAA,sBACN,SAAS,gBAAgB,MAAM;AAAA,sBAC/B,UAAU;AAAA,sBACV,cAA0B,wBAAI,mBAAmB,CAAC,CAAC;AAAA,oBACrD;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,OAAO;AAAA,YACT,CAAC;AAAA,YACD,eAAe,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,cAA0B;AAAA,cACzE;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,sBACQ,wBAAI,OAAO,CAAC,CAAC;AAAA,sBACb,wBAAI,cAAc,EAAE,MAAM,SAAS,SAAS,UAAU,CAAC;AAAA,sBACvD,wBAAI,OAAO,CAAC,CAAC;AAAA,gBAC/B;AAAA,cACF;AAAA,cACA;AAAA,YACF,CAAC;AAAA,YACD,QAAQ,WAAW,KAAK,CAAC,mBAA+B,wBAAI,OAAO,EAAE,WAAW,eAAe,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,YAAwB;AAAA,cACxM;AAAA,cACA;AAAA,gBACE;AAAA,gBACA,SAAS;AAAA,gBACT,UAAU;AAAA,kBACR;AAAA,gBACF;AAAA,gBACA,YAAY,KAAiB,wBAAI,QAAQ,EAAE,WAAW,cAAc,GAAG,OAAO,CAAC;AAAA,cACjF;AAAA,YACF,IAAI,EAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,UACtC,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL,CAAC;AACD,iBAAiB,cAAc;AAC/B,SAAS,YAAY,OAAO;AAC1B,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,MAAM,SAAS,CAAC,EAAE;AACjC;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,MAAM,SAAS,CAAC,EAAE;AACjC;AACA,SAAS,WAAW,YAAY;AAC9B,SAAO,WAAW,IAAI,CAAC,QAAQ;AAvZjC;AAwZI,WAAO;AAAA,MACL,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,gBAAc,SAAI,sBAAJ,mBAAuB,UAAS;AAAA,IAChD;AAAA,EACF,CAAC;AACH;AACA,SAAS,WAAW,QAAQ,OAAO;AACjC,SAAO,OAAO,SAAS,KAAK;AAC9B;", "names": []}