import {
  taxRegionsQueryKeys
} from "./chunk-PBNFBMP6.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-UAZEQNCO.mjs
var TAX_RATES_QUERY_KEY = "tax_rates";
var taxRatesQueryKeys = queryKeysFactory(TAX_RATES_QUERY_KEY);
var useTaxRate = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: taxRatesQueryKeys.detail(id),
    queryFn: async () => sdk.admin.taxRate.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useTaxRates = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.taxRate.list(query),
    queryKey: taxRatesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useUpdateTaxRate = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.taxRate.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: taxRatesQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateTaxRate = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.taxRate.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteTaxRate = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.taxRate.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: taxRatesQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useTaxRate,
  useTaxRates,
  useUpdateTaxRate,
  useCreateTaxRate,
  useDeleteTaxRate
};
//# sourceMappingURL=chunk-Y5G57T2Y.js.map
