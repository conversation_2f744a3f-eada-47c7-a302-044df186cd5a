import {
  InventoryItemGeneralSection
} from "./chunk-ITCUGY4F.js";
import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-KTCGZHOS.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import {
  useStockLocation
} from "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import {
  reservationItemsQueryKeys,
  useReservationItem
} from "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useInventoryItem
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/reservation-detail-QKUCAH7D.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ReservationDetailBreadcrumb = (props) => {
  var _a, _b;
  const { id } = props.params || {};
  const { reservation } = useReservationItem(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!reservation) {
    return null;
  }
  const display = ((_a = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _a.title) ?? ((_b = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _b.sku) ?? reservation.id;
  return (0, import_jsx_runtime.jsx)("span", { children: display });
};
var reservationDetailQuery = (id) => ({
  queryKey: reservationItemsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.reservation.retrieve(id)
});
var reservationItemLoader = async ({ params }) => {
  const id = params.id;
  const query = reservationDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ReservationGeneralSection = ({
  reservation
}) => {
  const { t } = useTranslation();
  const { inventory_item: inventoryItem, isPending: isLoadingInventoryItem } = useInventoryItem(reservation.inventory_item_id);
  const { stock_location: location, isPending: isLoadingLocation } = useStockLocation(reservation.location_id);
  if (isLoadingInventoryItem || !inventoryItem || isLoadingLocation || !location) {
    return (0, import_jsx_runtime2.jsx)("div", { children: "Loading..." });
  }
  const locationLevel = inventoryItem.location_levels.find(
    (l) => l.location_id === reservation.location_id
  );
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t("inventory.reservation.header", {
        itemName: inventoryItem.title ?? inventoryItem.sku
      }) }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: `edit`
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.lineItemId"),
        value: reservation.line_item_id
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.description"),
        value: reservation.description
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.location"),
        value: location == null ? void 0 : location.name
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.inStockAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.stocked_quantity
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.availableAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.available_quantity
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.reservedAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.reserved_quantity
      }
    )
  ] });
};
var ReservationDetail = () => {
  var _a, _b;
  const { id } = useParams();
  const initialData = useLoaderData();
  const { reservation, isLoading, isError, error } = useReservationItem(
    id,
    void 0,
    {
      initialData
    }
  );
  const { inventory_item } = useInventoryItem(
    (_a = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _a.id,
    void 0,
    { enabled: !!((_b = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _b.id) }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !reservation) {
    return (0, import_jsx_runtime3.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 1,
        sidebarSections: 1,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        before: getWidgets("reservation.details.before"),
        after: getWidgets("reservation.details.after"),
        sideBefore: getWidgets("reservation.details.side.before"),
        sideAfter: getWidgets("reservation.details.side.after")
      },
      data: reservation,
      showJSON: true,
      showMetadata: true,
      children: [
        (0, import_jsx_runtime3.jsx)(TwoColumnPage.Main, { children: (0, import_jsx_runtime3.jsx)(ReservationGeneralSection, { reservation }) }),
        (0, import_jsx_runtime3.jsx)(TwoColumnPage.Sidebar, { children: inventory_item && (0, import_jsx_runtime3.jsx)(InventoryItemGeneralSection, { inventoryItem: inventory_item }) })
      ]
    }
  );
};
export {
  ReservationDetailBreadcrumb as Breadcrumb,
  ReservationDetail as Component,
  reservationItemLoader as loader
};
//# sourceMappingURL=reservation-detail-QKUCAH7D-E5KYQLSY.js.map
