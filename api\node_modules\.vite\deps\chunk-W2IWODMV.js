import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-6WKBBTKM.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var LinkButton = ({
  className,
  variant = "interactive",
  ...props
}) => {
  return (0, import_jsx_runtime.jsx)(
    Link,
    {
      className: clx(
        "transition-fg txt-compact-small-plus rounded-[4px] outline-none",
        "focus-visible:shadow-borders-focus",
        {
          "text-ui-fg-interactive hover:text-ui-fg-interactive-hover": variant === "interactive",
          "text-ui-fg-base hover:text-ui-fg-subtle": variant === "primary"
        },
        className
      ),
      ...props
    }
  );
};

export {
  LinkButton
};
//# sourceMappingURL=chunk-W2IWODMV.js.map
