import "./chunk-EGRHWZRV.js";
import {
  MetadataForm
} from "./chunk-LNSD7AQU.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import "./chunk-XMQMXYDG.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-DPO7J5IQ.js";
import {
  useCustomerGroup,
  useUpdateCustomerGroup
} from "./chunk-FSXJE4G7.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-metadata-IRTI56TA.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CustomerGroupMetadata = () => {
  const { id } = useParams();
  const { customer_group, isPending, isError, error } = useCustomerGroup(id);
  const { mutateAsync, isPending: isMutating } = useUpdateCustomerGroup(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    MetadataForm,
    {
      metadata: customer_group == null ? void 0 : customer_group.metadata,
      hook: mutateAsync,
      isPending,
      isMutating
    }
  );
};
export {
  CustomerGroupMetadata as Component
};
//# sourceMappingURL=customer-group-metadata-IRTI56TA-PVYG6EOE.js.map
