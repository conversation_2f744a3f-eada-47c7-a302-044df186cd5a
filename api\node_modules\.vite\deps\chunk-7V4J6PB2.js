import {
  CreateCampaignFormFields
} from "./chunk-2T4FRC44.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  enumType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  useUpdatePromotion
} from "./chunk-S32V3COL.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Button,
  Heading,
  RadioGroup,
  Select,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-DRAV7IQC.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CampaignDetails = ({ campaign }) => {
  var _a, _b, _c, _d, _e, _f;
  const { t: t2 } = useTranslation();
  if (!campaign) {
    return;
  }
  return (0, import_jsx_runtime.jsxs)(import_react2.Fragment, { children: [
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { level: "h2", className: "mb-4", children: t2("campaigns.details") }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus font-", children: t2("campaigns.fields.identifier") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: campaign.campaign_identifier || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("fields.description") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: campaign.description || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("campaigns.fields.start_date") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_a = campaign.starts_at) == null ? void 0 : _a.toString()) || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("campaigns.fields.end_date") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_b = campaign.ends_at) == null ? void 0 : _b.toString()) || "-" }) })
      ] })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { level: "h2", className: "mb-4", children: t2("campaigns.budget.details") }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus font-", children: t2("campaigns.budget.fields.type") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_c = campaign.budget) == null ? void 0 : _c.type) || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("campaigns.budget.fields.currency") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_d = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _d.currency_code) || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("campaigns.budget.fields.limit") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_e = campaign.budget) == null ? void 0 : _e.limit) || "-" }) })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center py-1", children: [
        (0, import_jsx_runtime.jsx)(Text, { className: "txt-small-plus", children: t2("campaigns.budget.fields.used") }),
        (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-1", children: (0, import_jsx_runtime.jsx)(Text, { className: "txt-small", children: ((_f = campaign.budget) == null ? void 0 : _f.used) || "-" }) })
      ] })
    ] })
  ] });
};
var EditPromotionSchema = objectType({
  campaign_id: stringType().optional().nullable(),
  campaign_choice: enumType(["none", "existing"]).optional()
});
var AddCampaignPromotionFields = ({
  form,
  campaigns,
  withNewCampaign = true
}) => {
  const { t: t2 } = useTranslation();
  const watchCampaignId = useWatch({
    control: form.control,
    name: "campaign_id"
  });
  const watchCampaignChoice = useWatch({
    control: form.control,
    name: "campaign_choice"
  });
  const selectedCampaign = campaigns.find((c) => c.id === watchCampaignId);
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
    (0, import_jsx_runtime2.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "campaign_choice",
        render: ({ field }) => {
          return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("promotions.fields.campaign") }),
            (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsxs)(
              RadioGroup,
              {
                className: "grid grid-cols-1 gap-3",
                ...field,
                value: field.value,
                onValueChange: field.onChange,
                children: [
                  (0, import_jsx_runtime2.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      value: "none",
                      label: t2("promotions.form.campaign.none.title"),
                      description: t2("promotions.form.campaign.none.description")
                    }
                  ),
                  (0, import_jsx_runtime2.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      value: "existing",
                      label: t2("promotions.form.campaign.existing.title"),
                      description: t2(
                        "promotions.form.campaign.existing.description"
                      )
                    }
                  ),
                  withNewCampaign && (0, import_jsx_runtime2.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      value: "new",
                      label: t2("promotions.form.campaign.new.title"),
                      description: t2(
                        "promotions.form.campaign.new.description"
                      )
                    }
                  )
                ]
              }
            ) }),
            (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    watchCampaignChoice === "existing" && (0, import_jsx_runtime2.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "campaign_id",
        render: ({ field: { onChange, ref, ...field } }) => {
          return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("promotions.form.campaign.existing.title") }),
            (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsxs)(Select, { onValueChange: onChange, ...field, children: [
              (0, import_jsx_runtime2.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime2.jsx)(Select.Value, {}) }),
              (0, import_jsx_runtime2.jsxs)(Select.Content, { children: [
                !campaigns.length && (0, import_jsx_runtime2.jsxs)("div", { className: "flex h-[120px] flex-col items-center justify-center gap-2 p-2", children: [
                  (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: t2(
                    "promotions.form.campaign.existing.placeholder.title"
                  ) }),
                  (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-muted", children: t2(
                    "promotions.form.campaign.existing.placeholder.desc"
                  ) })
                ] }),
                campaigns.map((c) => {
                  var _a;
                  return (0, import_jsx_runtime2.jsx)(Select.Item, { value: c.id, children: (_a = c.name) == null ? void 0 : _a.toUpperCase() }, c.id);
                })
              ] })
            ] }) }),
            (0, import_jsx_runtime2.jsx)(
              Text,
              {
                size: "small",
                leading: "compact",
                className: "text-ui-fg-subtle",
                children: (0, import_jsx_runtime2.jsx)(
                  Trans,
                  {
                    t: t2,
                    i18nKey: "campaigns.fields.campaign_id.hint",
                    components: [(0, import_jsx_runtime2.jsx)("br", {}, "break")]
                  }
                )
              }
            ),
            (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    watchCampaignChoice === "new" && (0, import_jsx_runtime2.jsx)(CreateCampaignFormFields, { form, fieldScope: "campaign." }),
    (0, import_jsx_runtime2.jsx)(CampaignDetails, { campaign: selectedCampaign })
  ] });
};
var AddCampaignPromotionForm = ({
  promotion,
  campaigns
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { campaign } = promotion;
  const originalId = campaign == null ? void 0 : campaign.id;
  const form = useForm({
    defaultValues: {
      campaign_id: campaign == null ? void 0 : campaign.id,
      campaign_choice: (campaign == null ? void 0 : campaign.id) ? "existing" : "none"
    },
    resolver: t(EditPromotionSchema)
  });
  const { setValue } = form;
  const { mutateAsync, isPending } = useUpdatePromotion(promotion.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      { campaign_id: data.campaign_id },
      {
        onSuccess: () => {
          toast.success(t2("promotions.campaign.edit.successToast"));
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  const watchCampaignChoice = useWatch({
    control: form.control,
    name: "campaign_choice"
  });
  (0, import_react.useEffect)(() => {
    if (watchCampaignChoice === "none") {
      setValue("campaign_id", null);
    }
    if (watchCampaignChoice === "existing") {
      setValue("campaign_id", originalId);
    }
  }, [watchCampaignChoice, setValue, originalId]);
  return (0, import_jsx_runtime2.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Body, { className: "size-full overflow-auto", children: (0, import_jsx_runtime2.jsx)(
          AddCampaignPromotionFields,
          {
            form,
            campaigns,
            withNewCampaign: false
          }
        ) }),
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};

export {
  AddCampaignPromotionFields,
  AddCampaignPromotionForm
};
//# sourceMappingURL=chunk-7V4J6PB2.js.map
