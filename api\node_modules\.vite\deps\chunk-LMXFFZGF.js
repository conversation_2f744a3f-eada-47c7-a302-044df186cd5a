import {
  LocalizedTablePagination,
  TaxRegionCard
} from "./chunk-HJV4BH76.js";
import {
  DataTableOrderBy
} from "./chunk-CZLMELES.js";
import {
  NoRecords,
  NoResults
} from "./chunk-QL4D35SJ.js";
import {
  TableFooterSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  Link,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-SKR2YX52.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var TaxRegionTable = ({
  variant = "country",
  isPending,
  action,
  count = 0,
  table,
  queryObject,
  prefix,
  children
}) => {
  if (isPending) {
    return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col divide-y", children: [
      Array.from({ length: 3 }).map((_, index) => {
        return (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: "bg-ui-bg-field-component h-[52px] w-full animate-pulse"
          },
          index
        );
      }),
      (0, import_jsx_runtime.jsx)(TableFooterSkeleton, { layout: "fit" })
    ] });
  }
  const noQuery = Object.values(queryObject).filter((v) => Boolean(v)).length === 0;
  const noResults = !isPending && count === 0 && !noQuery;
  const noRecords = !isPending && count === 0 && noQuery;
  const { pageIndex, pageSize } = table.getState().pagination;
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col divide-y", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col justify-between gap-x-4 gap-y-3 px-6 py-4 md:flex-row md:items-center", children: [
      (0, import_jsx_runtime.jsx)("div", { children }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        !noRecords && (0, import_jsx_runtime.jsx)("div", { className: "flex w-full items-center gap-x-2 md:w-fit", children: (0, import_jsx_runtime.jsx)(
          DataTableOrderBy,
          {
            keys: ["updated_at", "created_at"],
            prefix
          }
        ) }),
        (0, import_jsx_runtime.jsx)(Link, { to: action.to, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: action.label }) })
      ] })
    ] }),
    noResults && (0, import_jsx_runtime.jsx)(NoResults, {}),
    noRecords && (0, import_jsx_runtime.jsx)(NoRecords, {}),
    !noRecords && !noResults ? !isPending ? table.getRowModel().rows.map((row) => {
      return (0, import_jsx_runtime.jsx)(
        TaxRegionCard,
        {
          variant,
          taxRegion: row.original,
          role: "row",
          "aria-rowindex": row.index
        },
        row.id
      );
    }) : Array.from({ length: 3 }).map((_, index) => {
      return (0, import_jsx_runtime.jsx)(
        "div",
        {
          className: "bg-ui-bg-field-component h-[60px] w-full animate-pulse"
        },
        index
      );
    }) : null,
    !noRecords && (0, import_jsx_runtime.jsx)(
      LocalizedTablePagination,
      {
        prefix,
        canNextPage: table.getCanNextPage(),
        canPreviousPage: table.getCanPreviousPage(),
        count,
        nextPage: table.nextPage,
        previousPage: table.previousPage,
        pageCount: table.getPageCount(),
        pageIndex,
        pageSize
      }
    )
  ] });
};
var useTaxRegionTable = ({
  data = [],
  count = 0,
  pageSize: _pageSize = 10,
  prefix
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const offsetKey = `${prefix ? `${prefix}_` : ""}offset`;
  const offset = searchParams.get(offsetKey);
  const [{ pageIndex, pageSize }, setPagination] = (0, import_react.useState)({
    pageIndex: offset ? Math.ceil(Number(offset) / _pageSize) : 0,
    pageSize: _pageSize
  });
  const pagination = (0, import_react.useMemo)(
    () => ({
      pageIndex,
      pageSize
    }),
    [pageIndex, pageSize]
  );
  (0, import_react.useEffect)(() => {
    const index = offset ? Math.ceil(Number(offset) / _pageSize) : 0;
    if (index === pageIndex) {
      return;
    }
    setPagination((prev) => ({
      ...prev,
      pageIndex: index
    }));
  }, [offset, _pageSize, pageIndex]);
  const onPaginationChange = (updater) => {
    const state = updater(pagination);
    const { pageIndex: pageIndex2, pageSize: pageSize2 } = state;
    setSearchParams((prev) => {
      if (!pageIndex2) {
        prev.delete(offsetKey);
        return prev;
      }
      const newSearch = new URLSearchParams(prev);
      newSearch.set(offsetKey, String(pageIndex2 * pageSize2));
      return newSearch;
    });
    setPagination(state);
    return state;
  };
  const table = useReactTable({
    data,
    columns: [],
    // We don't actually want to render any columns
    pageCount: Math.ceil(count / pageSize),
    state: {
      pagination
    },
    getCoreRowModel: getCoreRowModel(),
    onPaginationChange,
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true
  });
  return {
    table
  };
};

export {
  TaxRegionTable,
  useTaxRegionTable
};
//# sourceMappingURL=chunk-LMXFFZGF.js.map
