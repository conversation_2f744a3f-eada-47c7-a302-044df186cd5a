{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-7OYLCEKK.mjs"], "sourcesContent": ["// src/components/inputs/handle-input/handle-input.tsx\nimport { Input, Text } from \"@medusajs/ui\";\nimport { forwardRef } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar HandleInput = forwardRef((props, ref) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"relative\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r\", children: /* @__PURE__ */ jsx(\n      Text,\n      {\n        className: \"text-ui-fg-muted\",\n        size: \"small\",\n        leading: \"compact\",\n        weight: \"plus\",\n        children: \"/\"\n      }\n    ) }),\n    /* @__PURE__ */ jsx(Input, { ref, ...props, className: \"pl-10\" })\n  ] });\n});\nHandleInput.displayName = \"HandleInput\";\n\nexport {\n  HandleInput\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAEA,mBAA2B;AAC3B,yBAA0B;AAC1B,IAAI,kBAAc,yBAAW,CAAC,OAAO,QAAQ;AAC3C,aAAuB,yBAAK,OAAO,EAAE,WAAW,YAAY,UAAU;AAAA,QACpD,wBAAI,OAAO,EAAE,WAAW,gFAAgF,cAA0B;AAAA,MAChJ;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,OAAO,EAAE,KAAK,GAAG,OAAO,WAAW,QAAQ,CAAC;AAAA,EAClE,EAAE,CAAC;AACL,CAAC;AACD,YAAY,cAAc;", "names": []}