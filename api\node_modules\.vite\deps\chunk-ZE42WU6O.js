import {
  ArrowDownTray,
  Text,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-TYTNUPXB.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var FileUpload = ({
  label,
  hint,
  multiple = true,
  hasError,
  formats,
  onUploaded
}) => {
  const [isDragOver, setIsDragOver] = (0, import_react.useState)(false);
  const inputRef = (0, import_react.useRef)(null);
  const dropZoneRef = (0, import_react.useRef)(null);
  const handleOpenFileSelector = () => {
    var _a;
    (_a = inputRef.current) == null ? void 0 : _a.click();
  };
  const handleDragEnter = (event) => {
    var _a;
    event.preventDefault();
    event.stopPropagation();
    const files = (_a = event.dataTransfer) == null ? void 0 : _a.files;
    if (!files) {
      return;
    }
    setIsDragOver(true);
  };
  const handleDragLeave = (event) => {
    event.preventDefault();
    event.stopPropagation();
    if (!dropZoneRef.current || dropZoneRef.current.contains(event.relatedTarget)) {
      return;
    }
    setIsDragOver(false);
  };
  const handleUploaded = (files) => {
    if (!files) {
      return;
    }
    const fileList = Array.from(files);
    const fileObj = fileList.map((file) => {
      const id = Math.random().toString(36).substring(7);
      const previewUrl = URL.createObjectURL(file);
      return {
        id,
        url: previewUrl,
        file
      };
    });
    onUploaded(fileObj);
  };
  const handleDrop = (event) => {
    var _a;
    event.preventDefault();
    event.stopPropagation();
    setIsDragOver(false);
    handleUploaded((_a = event.dataTransfer) == null ? void 0 : _a.files);
  };
  const handleFileChange = async (event) => {
    handleUploaded(event.target.files);
  };
  return (0, import_jsx_runtime.jsxs)("div", { children: [
    (0, import_jsx_runtime.jsxs)(
      "button",
      {
        ref: dropZoneRef,
        type: "button",
        onClick: handleOpenFileSelector,
        onDrop: handleDrop,
        onDragOver: (e) => e.preventDefault(),
        onDragEnter: handleDragEnter,
        onDragLeave: handleDragLeave,
        className: clx(
          "bg-ui-bg-component border-ui-border-strong transition-fg group flex w-full flex-col items-center gap-y-2 rounded-lg border border-dashed p-8",
          "hover:border-ui-border-interactive focus:border-ui-border-interactive",
          "focus:shadow-borders-focus outline-none focus:border-solid",
          {
            "!border-ui-border-error": hasError,
            "!border-ui-border-interactive": isDragOver
          }
        ),
        children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle group-disabled:text-ui-fg-disabled flex items-center gap-x-2", children: [
            (0, import_jsx_runtime.jsx)(ArrowDownTray, {}),
            (0, import_jsx_runtime.jsx)(Text, { children: label })
          ] }),
          !!hint && (0, import_jsx_runtime.jsx)(
            Text,
            {
              size: "small",
              leading: "compact",
              className: "text-ui-fg-muted group-disabled:text-ui-fg-disabled",
              children: hint
            }
          )
        ]
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "input",
      {
        hidden: true,
        ref: inputRef,
        onChange: handleFileChange,
        type: "file",
        accept: formats.join(","),
        multiple
      }
    )
  ] });
};

export {
  FileUpload
};
//# sourceMappingURL=chunk-ZE42WU6O.js.map
