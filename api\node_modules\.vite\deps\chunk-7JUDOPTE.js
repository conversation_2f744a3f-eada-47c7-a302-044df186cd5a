import {
  returnsQuery<PERSON>eys
} from "./chunk-JR44VLTN.js";
import {
  ordersQueryKeys
} from "./chunk-IBPOGPJN.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-6LA2RGO4.mjs
var EXCHANGES_QUERY_KEY = "exchanges";
var exchangesQueryKeys = queryKeysFactory(EXCHANGES_QUERY_KEY);
var useExchange = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: async () => sdk.admin.exchange.retrieve(id, query),
    queryKey: exchangesQueryKeys.detail(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useExchanges = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: async () => sdk.admin.exchange.list(query),
    queryKey: exchangesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateExchange = (orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCancelExchange = (id, orderId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.exchange.cancel(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddExchangeInboundItems = (id, orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.addInboundItems(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateExchangeInboundItem = (id, orderId, options) => {
  return useMutation({
    mutationFn: ({
      actionId,
      ...payload
    }) => {
      return sdk.admin.exchange.updateInboundItem(id, actionId, payload);
    },
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRemoveExchangeInboundItem = (id, orderId, options) => {
  return useMutation({
    mutationFn: (actionId) => sdk.admin.exchange.removeInboundItem(id, actionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: returnsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddExchangeInboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.addInboundShipping(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateExchangeInboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: ({
      actionId,
      ...payload
    }) => sdk.admin.exchange.updateInboundShipping(id, actionId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteExchangeInboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: (actionId) => sdk.admin.exchange.deleteInboundShipping(id, actionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddExchangeOutboundItems = (id, orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.addOutboundItems(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateExchangeOutboundItems = (id, orderId, options) => {
  return useMutation({
    mutationFn: ({
      actionId,
      ...payload
    }) => {
      return sdk.admin.exchange.updateOutboundItem(id, actionId, payload);
    },
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRemoveExchangeOutboundItem = (id, orderId, options) => {
  return useMutation({
    mutationFn: (actionId) => sdk.admin.exchange.removeOutboundItem(id, actionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddExchangeOutboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.addOutboundShipping(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateExchangeOutboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: ({
      actionId,
      ...payload
    }) => sdk.admin.exchange.updateOutboundShipping(id, actionId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteExchangeOutboundShipping = (id, orderId, options) => {
  return useMutation({
    mutationFn: (actionId) => sdk.admin.exchange.deleteOutboundShipping(id, actionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useExchangeConfirmRequest = (id, orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.exchange.request(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: returnsQueryKeys.all
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCancelExchangeRequest = (id, orderId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.exchange.cancelRequest(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: exchangesQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useExchange,
  useExchanges,
  useCreateExchange,
  useCancelExchange,
  useAddExchangeInboundItems,
  useUpdateExchangeInboundItem,
  useRemoveExchangeInboundItem,
  useAddExchangeInboundShipping,
  useUpdateExchangeInboundShipping,
  useDeleteExchangeInboundShipping,
  useAddExchangeOutboundItems,
  useUpdateExchangeOutboundItems,
  useRemoveExchangeOutboundItem,
  useAddExchangeOutboundShipping,
  useUpdateExchangeOutboundShipping,
  useDeleteExchangeOutboundShipping,
  useExchangeConfirmRequest,
  useCancelExchangeRequest
};
//# sourceMappingURL=chunk-7JUDOPTE.js.map
