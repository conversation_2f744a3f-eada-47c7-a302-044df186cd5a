import {
  TaxRateRuleReferenceType
} from "./chunk-VD6KBTYK.js";
import {
  useProductTagTableColumns
} from "./chunk-LG77Y6Y7.js";
import {
  useProductTypeTableColumns
} from "./chunk-DGPYW6Y5.js";
import {
  useCustomerGroupTableColumns
} from "./chunk-RYLAHPUE.js";
import {
  useCollectionTableColumns,
  useCollectionTableQuery,
  useProductTagTableQuery
} from "./chunk-CHSDXWFK.js";
import {
  useProductTypeTableQuery
} from "./chunk-ITWRYKT3.js";
import {
  useCustomerGroupTableQuery
} from "./chunk-I2ZOQM4X.js";
import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import {
  useCollectionTableFilters,
  useProductTagTableFilters
} from "./chunk-3LRISSP5.js";
import {
  useProductTypeTableFilters
} from "./chunk-P5T2IZP5.js";
import {
  useCustomerGroupTableFilters
} from "./chunk-XNFM7P3M.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import {
  StackedDrawer,
  StackedFocusModal
} from "./chunk-XMQMXYDG.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  useProductTags
} from "./chunk-3HVD2KMV.js";
import {
  useProductTypes
} from "./chunk-HREJMEGI.js";
import {
  useCustomerGroups
} from "./chunk-FSXJE4G7.js";
import {
  useCollections
} from "./chunk-E4TFG45M.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  IconButton,
  Text,
  XMarkMini,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-XHI6RXEM.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
function initRowSelection(state) {
  return state.reduce((acc, reference) => {
    acc[reference.value] = true;
    return acc;
  }, {});
}
var TargetForm = ({
  referenceType,
  type,
  setState,
  state
}) => {
  const { t } = useTranslation();
  const Component = type === "focus" ? StackedFocusModal : StackedDrawer;
  const [intermediate, setIntermediate] = (0, import_react.useState)(state);
  const handleSave = () => {
    setState(intermediate);
  };
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(Component.Body, { className: "min-h-0 p-0", children: (0, import_jsx_runtime.jsx)(
      Table,
      {
        referenceType,
        initialRowState: initRowSelection(state),
        intermediate,
        setIntermediate
      }
    ) }),
    (0, import_jsx_runtime.jsxs)(Component.Footer, { children: [
      (0, import_jsx_runtime.jsx)(Component.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", type: "button", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { type: "button", size: "small", onClick: handleSave, children: t("actions.save") })
    ] })
  ] });
};
var Table = ({ referenceType, ...props }) => {
  switch (referenceType) {
    case TaxRateRuleReferenceType.CUSTOMER_GROUP:
      return (0, import_jsx_runtime.jsx)(CustomerGroupTable, { ...props });
    case "product":
      return (0, import_jsx_runtime.jsx)(ProductTable, { ...props });
    case TaxRateRuleReferenceType.PRODUCT_COLLECTION:
      return (0, import_jsx_runtime.jsx)(ProductCollectionTable, { ...props });
    case "product_type":
      return (0, import_jsx_runtime.jsx)(ProductTypeTable, { ...props });
    case TaxRateRuleReferenceType.PRODUCT_TAG:
      return (0, import_jsx_runtime.jsx)(ProductTagTable, { ...props });
    default:
      return null;
  }
};
var PAGE_SIZE = 50;
var PREFIX_CUSTOMER_GROUP = "cg";
var CustomerGroupTable = ({
  initialRowState,
  intermediate,
  setIntermediate
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialRowState);
  useCleanupSearchParams();
  const { searchParams, raw } = useCustomerGroupTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX_CUSTOMER_GROUP
  });
  const { customer_groups, count, isLoading, isError, error } = useCustomerGroups(searchParams, {
    placeholderData: keepPreviousData
  });
  const updater = (value) => {
    const state = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newCustomerGroups = (customer_groups == null ? void 0 : customer_groups.filter((cg) => newIds.includes(cg.id)).map((cg) => ({ value: cg.id, label: cg.name }))) || [];
    const filteredIntermediate = intermediate.filter(
      (cg) => !removedIds.includes(cg.value)
    );
    setIntermediate([...filteredIntermediate, ...newCustomerGroups]);
    setRowSelection(state);
  };
  const filters = useCustomerGroupTableFilters();
  const columns = useGroupColumns();
  const { table } = useDataTable({
    data: customer_groups || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX_CUSTOMER_GROUP
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      filters,
      orderBy: [
        { key: "name", label: t("fields.name") },
        { key: "created_at", label: t("fields.createdAt") },
        { key: "updated_at", label: t("fields.updatedAt") }
      ],
      layout: "fill",
      pagination: true,
      search: true,
      prefix: PREFIX_CUSTOMER_GROUP,
      queryObject: raw
    }
  );
};
var cgColumnHelper = createColumnHelper();
var useGroupColumns = () => {
  const base = useCustomerGroupTableColumns();
  return (0, import_react.useMemo)(
    () => [
      cgColumnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var PREFIX_PRODUCT = "p";
var ProductTable = ({
  initialRowState,
  intermediate,
  setIntermediate
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialRowState);
  useCleanupSearchParams();
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT
  });
  const { products, count, isLoading, isError, error } = useProducts(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (value) => {
    const state = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newProducts = (products == null ? void 0 : products.filter((p) => newIds.includes(p.id)).map((p) => ({
      value: p.id,
      label: p.title
    }))) || [];
    const filteredIntermediate = intermediate.filter(
      (p) => !removedIds.includes(p.value)
    );
    setIntermediate([...filteredIntermediate, ...newProducts]);
    setRowSelection(state);
  };
  const filters = useProductTableFilters();
  const columns = useProductColumns();
  const { table } = useDataTable({
    data: products || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      filters,
      orderBy: [
        { key: "title", label: t("fields.title") },
        { key: "created_at", label: t("fields.createdAt") },
        { key: "updated_at", label: t("fields.updatedAt") }
      ],
      layout: "fill",
      pagination: true,
      search: true,
      prefix: PREFIX_PRODUCT,
      queryObject: raw
    }
  );
};
var pColumnHelper = createColumnHelper();
var useProductColumns = () => {
  const base = useProductTableColumns();
  return (0, import_react.useMemo)(
    () => [
      pColumnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var PREFIX_PRODUCT_COLLECTION = "pc";
var ProductCollectionTable = ({
  initialRowState,
  intermediate,
  setIntermediate
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialRowState);
  useCleanupSearchParams();
  const { searchParams, raw } = useCollectionTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_COLLECTION
  });
  const { collections, count, isLoading, isError, error } = useCollections(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (value) => {
    const state = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newCollections = (collections == null ? void 0 : collections.filter((p) => newIds.includes(p.id)).map((p) => ({
      value: p.id,
      label: p.title
    }))) || [];
    const filteredIntermediate = intermediate.filter(
      (p) => !removedIds.includes(p.value)
    );
    setIntermediate([...filteredIntermediate, ...newCollections]);
    setRowSelection(state);
  };
  const filters = useCollectionTableFilters();
  const columns = useCollectionColumns();
  const { table } = useDataTable({
    data: collections || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_COLLECTION
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      filters,
      orderBy: [
        { key: "title", label: t("fields.title") },
        { key: "created_at", label: t("fields.createdAt") },
        { key: "updated_at", label: t("fields.updatedAt") }
      ],
      layout: "fill",
      pagination: true,
      search: true,
      prefix: PREFIX_PRODUCT_COLLECTION,
      queryObject: raw
    }
  );
};
var pcColumnHelper = createColumnHelper();
var useCollectionColumns = () => {
  const base = useCollectionTableColumns();
  return (0, import_react.useMemo)(
    () => [
      pcColumnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var PREFIX_PRODUCT_TYPE = "pt";
var ProductTypeTable = ({
  initialRowState,
  intermediate,
  setIntermediate
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialRowState);
  useCleanupSearchParams();
  const { searchParams, raw } = useProductTypeTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_TYPE
  });
  const { product_types, count, isLoading, isError, error } = useProductTypes(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (value) => {
    const state = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newTypes = (product_types == null ? void 0 : product_types.filter((p) => newIds.includes(p.id)).map((p) => ({
      value: p.id,
      label: p.value
    }))) || [];
    const filteredIntermediate = intermediate.filter(
      (p) => !removedIds.includes(p.value)
    );
    setIntermediate([...filteredIntermediate, ...newTypes]);
    setRowSelection(state);
  };
  const filters = useProductTypeTableFilters();
  const columns = useProductTypeColumns();
  const { table } = useDataTable({
    data: product_types || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_TYPE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      filters,
      orderBy: [
        { key: "value", label: t("fields.value") },
        { key: "created_at", label: t("fields.createdAt") },
        { key: "updated_at", label: t("fields.updatedAt") }
      ],
      layout: "fill",
      pagination: true,
      search: true,
      prefix: PREFIX_PRODUCT_TYPE,
      queryObject: raw
    }
  );
};
var ptColumnHelper = createColumnHelper();
var useProductTypeColumns = () => {
  const base = useProductTypeTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ptColumnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var PREFIX_PRODUCT_TAG = "ptag";
var ProductTagTable = ({
  initialRowState,
  intermediate,
  setIntermediate
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialRowState);
  useCleanupSearchParams();
  const { searchParams, raw } = useProductTagTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_TAG
  });
  const { product_tags, count, isLoading, isError, error } = useProductTags(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const updater = (value) => {
    const state = typeof value === "function" ? value(rowSelection) : value;
    const currentIds = Object.keys(rowSelection);
    const ids = Object.keys(state);
    const newIds = ids.filter((id) => !currentIds.includes(id));
    const removedIds = currentIds.filter((id) => !ids.includes(id));
    const newTags = (product_tags == null ? void 0 : product_tags.filter((p) => newIds.includes(p.id)).map((p) => ({
      value: p.id,
      label: p.value
    }))) || [];
    const filteredIntermediate = intermediate.filter(
      (p) => !removedIds.includes(p.value)
    );
    setIntermediate([...filteredIntermediate, ...newTags]);
    setRowSelection(state);
  };
  const filters = useProductTagTableFilters();
  const columns = useProductTagColumns();
  const { table } = useDataTable({
    data: product_tags || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater
    },
    pageSize: PAGE_SIZE,
    prefix: PREFIX_PRODUCT_TAG
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count,
      isLoading,
      filters,
      orderBy: [
        { key: "value", label: t("fields.value") },
        { key: "created_at", label: t("fields.createdAt") },
        { key: "updated_at", label: t("fields.updatedAt") }
      ],
      layout: "fill",
      pagination: true,
      search: true,
      prefix: PREFIX_PRODUCT_TAG,
      queryObject: raw
    }
  );
};
var ptagColumnHelper = createColumnHelper();
var useProductTagColumns = () => {
  const base = useProductTagTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ptagColumnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var useCleanupSearchParams = () => {
  const [_, setSearchParams] = useSearchParams();
  (0, import_react.useEffect)(() => {
    return () => {
      setSearchParams({});
    };
  }, []);
};
var TargetItem = ({ index, label, onRemove }) => {
  return (0, import_jsx_runtime2.jsxs)("div", { className: "bg-ui-bg-field-component shadow-borders-base flex items-center justify-between gap-2 rounded-md px-2 py-0.5", children: [
    (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: label }),
    (0, import_jsx_runtime2.jsx)(
      IconButton,
      {
        size: "small",
        variant: "transparent",
        type: "button",
        onClick: () => onRemove(index),
        children: (0, import_jsx_runtime2.jsx)(XMarkMini, {})
      }
    )
  ] });
};
var TaxRateRuleReferenceSchema = z.object({
  value: z.string(),
  label: z.string()
});
var TaxRateRuleTargetSchema = z.object({
  reference_type: z.nativeEnum(TaxRateRuleReferenceType),
  references: z.array(TaxRateRuleReferenceSchema)
});
var createTaxRulePayload = (target) => {
  return target.references.map((reference) => ({
    reference: target.reference_type,
    reference_id: reference.value
  }));
};

export {
  TargetForm,
  TargetItem,
  TaxRateRuleReferenceSchema,
  createTaxRulePayload
};
//# sourceMappingURL=chunk-GKFGUMRQ.js.map
