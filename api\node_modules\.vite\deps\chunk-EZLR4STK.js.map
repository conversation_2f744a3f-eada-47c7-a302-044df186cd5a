{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-6GU6IDUA.mjs"], "sourcesContent": ["// src/lib/cast-number.ts\nvar castNumber = (number) => {\n  return typeof number === \"string\" ? Number(number.replace(\",\", \".\")) : number;\n};\n\nexport {\n  castNumber\n};\n"], "mappings": ";AACA,IAAI,aAAa,CAAC,WAAW;AAC3B,SAAO,OAAO,WAAW,WAAW,OAAO,OAAO,QAAQ,KAAK,GAAG,CAAC,IAAI;AACzE;", "names": []}