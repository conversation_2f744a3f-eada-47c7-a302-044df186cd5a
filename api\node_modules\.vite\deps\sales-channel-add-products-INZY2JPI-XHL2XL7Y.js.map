{"version": 3, "sources": ["../../@medusajs/dashboard/dist/sales-channel-add-products-INZY2JPI.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport {\n  useSalesChannel,\n  useSalesChannelAddProducts\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/sales-channels/sales-channel-add-products/sales-channel-add-products.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/sales-channels/sales-channel-add-products/components/add-products-to-sales-channel-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddProductsToSalesChannelSchema = zod.object({\n  product_ids: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 50;\nvar AddProductsToSalesChannelForm = ({\n  salesChannel\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      product_ids: []\n    },\n    resolver: zodResolver(AddProductsToSalesChannelSchema)\n  });\n  const { setValue } = form;\n  const { mutateAsync, isPending } = useSalesChannelAddProducts(salesChannel.id);\n  const [rowSelection, setRowSelection] = useState({});\n  const updater = (fn) => {\n    const state = typeof fn === \"function\" ? fn(rowSelection) : fn;\n    const ids = Object.keys(state);\n    setValue(\"product_ids\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(state);\n  };\n  const { searchParams, raw } = useProductTableQuery({ pageSize: PAGE_SIZE });\n  const {\n    products,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useProducts(\n    {\n      fields: \"*variants,*sales_channels\",\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useProductTableFilters([\"sales_channel_id\"]);\n  const { table } = useDataTable({\n    data: products ?? [],\n    columns,\n    enableRowSelection: (row) => {\n      return !row.original.sales_channels?.map((sc) => sc.id).includes(salesChannel.id);\n    },\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE,\n    count,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    },\n    meta: {\n      salesChannelId: salesChannel.id\n    }\n  });\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(values.product_ids, {\n      onSuccess: () => {\n        toast.success(t(\"salesChannels.toast.update\"));\n        handleSuccess();\n      },\n      onError: (error2) => toast.error(error2.message)\n    });\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: form.formState.errors.product_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.product_ids.message }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex size-full flex-col overflow-y-auto\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            count,\n            columns,\n            pageSize: PAGE_SIZE,\n            isLoading,\n            filters,\n            orderBy: [\n              { key: \"title\", label: t(\"fields.title\") },\n              { key: \"status\", label: t(\"fields.status\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            queryObject: raw,\n            layout: \"fill\",\n            pagination: true,\n            search: \"autofocus\",\n            noRecords: {\n              message: t(\"salesChannels.products.add.list.noRecordsMessage\")\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTableColumns();\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row, table }) => {\n          const { salesChannelId } = table.options.meta;\n          const isAdded = row.original.sales_channels?.map((sc) => sc.id).includes(salesChannelId);\n          const isSelected = row.getIsSelected() || isAdded;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isAdded,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isAdded) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"salesChannels.productAlreadyAdded\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n};\n\n// src/routes/sales-channels/sales-channel-add-products/sales-channel-add-products.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar SalesChannelAddProducts = () => {\n  const { id } = useParams();\n  const {\n    sales_channel,\n    isPending: isLoading,\n    isError,\n    error\n  } = useSalesChannel(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && sales_channel && /* @__PURE__ */ jsx2(AddProductsToSalesChannelForm, { salesChannel: sales_channel }) });\n};\nexport {\n  SalesChannelAddProducts as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,mBAAkC;AAIlC,yBAA0B;AAuK1B,IAAAA,sBAA4B;AAtK5B,IAAI,kCAAsC,WAAO;AAAA,EAC/C,aAAiB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAC5C,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,gCAAgC,CAAC;AAAA,EACnC;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,aAAa,CAAC;AAAA,IAChB;AAAA,IACA,UAAU,EAAY,+BAA+B;AAAA,EACvD,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,aAAa,UAAU,IAAI,2BAA2B,aAAa,EAAE;AAC7E,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,QAAQ,OAAO,OAAO,aAAa,GAAG,YAAY,IAAI;AAC5D,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,aAAS,eAAe,KAAK;AAAA,MAC3B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,KAAK;AAAA,EACvB;AACA,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB,EAAE,UAAU,UAAU,CAAC;AAC1E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,uBAAuB,CAAC,kBAAkB,CAAC;AAC3D,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA,oBAAoB,CAAC,QAAQ;AAxIjC;AAyIM,aAAO,GAAC,SAAI,SAAS,mBAAb,mBAA6B,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,aAAa;AAAA,IAChF;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,gBAAgB,aAAa;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,YAAY,OAAO,aAAa;AAAA,MACpC,WAAW,MAAM;AACf,cAAM,QAAQA,GAAE,4BAA4B,CAAC;AAC7C,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,WAAW,MAAM,MAAM,OAAO,OAAO;AAAA,IACjD,CAAC;AAAA,EACH,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yCAAyC,UAAU,KAAK,UAAU,OAAO,mBAA+B,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACjR,wBAAI,gBAAgB,MAAM,EAAE,WAAW,2CAA2C,cAA0B;AAAA,UAC1H;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,SAAS,OAAOA,GAAE,cAAc,EAAE;AAAA,cACzC,EAAE,KAAK,UAAU,OAAOA,GAAE,eAAe,EAAE;AAAA,cAC3C,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,WAAW;AAAA,cACT,SAASA,GAAE,kDAAkD;AAAA,YAC/D;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AA3NlC;AA4NU,gBAAM,EAAE,eAAe,IAAI,MAAM,QAAQ;AACzC,gBAAM,WAAU,SAAI,SAAS,mBAAb,mBAA6B,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS;AACzE,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,SAAS;AACX,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,mCAAmC;AAAA,gBAC9C,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACF;AAIA,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,EAAE;AACtB,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,qBAAiC,oBAAAA,KAAK,+BAA+B,EAAE,cAAc,cAAc,CAAC,EAAE,CAAC;AAChL;", "names": ["import_jsx_runtime", "t", "jsx2"]}