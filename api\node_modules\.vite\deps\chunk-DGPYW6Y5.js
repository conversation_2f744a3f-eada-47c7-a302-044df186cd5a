import {
  DateCell
} from "./chunk-OW6OIDUA.js";
import {
  TextCell
} from "./chunk-7HUCBNCQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-SYQ6IA6C.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useProductTypeTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("value", {
        header: () => t("fields.value"),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: getValue() })
      }),
      columnHelper.accessor("created_at", {
        header: () => t("fields.createdAt"),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime.jsx)(DateCell, { date: getValue() });
        }
      }),
      columnHelper.accessor("updated_at", {
        header: () => t("fields.updatedAt"),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime.jsx)(DateCell, { date: getValue() });
        }
      })
    ],
    [t]
  );
};

export {
  useProductTypeTableColumns
};
//# sourceMappingURL=chunk-DGPYW6Y5.js.map
