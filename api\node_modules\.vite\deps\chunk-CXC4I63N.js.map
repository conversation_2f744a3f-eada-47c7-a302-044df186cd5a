{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-32IQRUVY.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/stock-locations.tsx\nimport {\n  useMutation,\n  useQuery as useQuery2\n} from \"@tanstack/react-query\";\n\n// src/hooks/api/fulfillment-providers.tsx\nimport { useQuery } from \"@tanstack/react-query\";\nvar FULFILLMENT_PROVIDERS_QUERY_KEY = \"fulfillment_providers\";\nvar fulfillmentProvidersQueryKeys = queryKeysFactory(\n  FULFILLMENT_PROVIDERS_QUERY_KEY\n);\nvar FULFILLMENT_PROVIDER_OPTIONS_QUERY_KEY = \"fulfillment_provider_options\";\nvar fulfillmentProviderOptionsQueryKeys = queryKeysFactory(\n  FULFILLMENT_PROVIDER_OPTIONS_QUERY_KEY\n);\nvar useFulfillmentProviders = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.fulfillmentProvider.list(query),\n    queryKey: fulfillmentProvidersQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useFulfillmentProviderOptions = (providerId, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.fulfillmentProvider.listFulfillmentOptions(providerId),\n    queryKey: fulfillmentProviderOptionsQueryKeys.list(providerId),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\n// src/hooks/api/stock-locations.tsx\nvar STOCK_LOCATIONS_QUERY_KEY = \"stock_locations\";\nvar stockLocationsQueryKeys = queryKeysFactory(\n  STOCK_LOCATIONS_QUERY_KEY\n);\nvar useStockLocation = (id, query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryFn: () => sdk.admin.stockLocation.retrieve(id, query),\n    queryKey: stockLocationsQueryKeys.detail(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useStockLocations = (query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryFn: () => sdk.admin.stockLocation.list(query),\n    queryKey: stockLocationsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateStockLocation = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.stockLocation.create(payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateStockLocation = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.stockLocation.update(id, payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.details()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateStockLocationSalesChannels = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.stockLocation.updateSalesChannels(id, payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.details()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteStockLocation = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.stockLocation.delete(id),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.lists()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateStockLocationFulfillmentSet = (locationId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.stockLocation.createFulfillmentSet(locationId, payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateStockLocationFulfillmentProviders = (id, options) => {\n  return useMutation({\n    mutationFn: async (payload) => await sdk.admin.stockLocation.updateFulfillmentProviders(id, payload),\n    onSuccess: async (data, variables, context) => {\n      await queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.details()\n      });\n      await queryClient.invalidateQueries({\n        queryKey: fulfillmentProvidersQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useFulfillmentProviders,\n  useFulfillmentProviderOptions,\n  stockLocationsQueryKeys,\n  useStockLocation,\n  useStockLocations,\n  useCreateStockLocation,\n  useUpdateStockLocation,\n  useUpdateStockLocationSalesChannels,\n  useDeleteStockLocation,\n  useCreateStockLocationFulfillmentSet,\n  useUpdateStockLocationFulfillmentProviders\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAkBA,IAAI,kCAAkC;AACtC,IAAI,gCAAgC;AAAA,EAClC;AACF;AACA,IAAI,yCAAyC;AAC7C,IAAI,sCAAsC;AAAA,EACxC;AACF;AACA,IAAI,0BAA0B,CAAC,OAAO,YAAY;AAChD,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,oBAAoB,KAAK,KAAK;AAAA,IACvD,UAAU,8BAA8B,KAAK,KAAK;AAAA,IAClD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,gCAAgC,CAAC,YAAY,YAAY;AAC3D,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,oBAAoB,uBAAuB,UAAU;AAAA,IAC9E,UAAU,oCAAoC,KAAK,UAAU;AAAA,IAC7D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AAGA,IAAI,4BAA4B;AAChC,IAAI,0BAA0B;AAAA,EAC5B;AACF;AACA,IAAI,mBAAmB,CAAC,IAAI,OAAO,YAAY;AAC7C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,SAAS,MAAM,IAAI,MAAM,cAAc,SAAS,IAAI,KAAK;AAAA,IACzD,UAAU,wBAAwB,OAAO,IAAI,KAAK;AAAA,IAClD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,OAAO,YAAY;AAC1C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,SAAS,MAAM,IAAI,MAAM,cAAc,KAAK,KAAK;AAAA,IACjD,UAAU,wBAAwB,KAAK,KAAK;AAAA,IAC5C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,yBAAyB,CAAC,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,OAAO,OAAO;AAAA,IAC/D,WAAW,OAAO,MAAM,WAAW,YAAY;AAnEnD;AAoEM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,OAAO,IAAI,OAAO;AAAA,IACnE,WAAW,OAAO,MAAM,WAAW,YAAY;AA/EnD;AAgFM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sCAAsC,CAAC,IAAI,YAAY;AACzD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,oBAAoB,IAAI,OAAO;AAAA,IAChF,WAAW,OAAO,MAAM,WAAW,YAAY;AA9FnD;AA+FM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,cAAc,OAAO,EAAE;AAAA,IACnD,WAAW,OAAO,MAAM,WAAW,YAAY;AA7GnD;AA8GM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,OAAO,EAAE;AAAA,MAC7C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,uCAAuC,CAAC,YAAY,YAAY;AAClE,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,cAAc,qBAAqB,YAAY,OAAO;AAAA,IACzF,WAAW,OAAO,MAAM,WAAW,YAAY;AA5HnD;AA6HM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,6CAA6C,CAAC,IAAI,YAAY;AAChE,SAAO,YAAY;AAAA,IACjB,YAAY,OAAO,YAAY,MAAM,IAAI,MAAM,cAAc,2BAA2B,IAAI,OAAO;AAAA,IACnG,WAAW,OAAO,MAAM,WAAW,YAAY;AAxInD;AAyIM,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,8BAA8B;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}