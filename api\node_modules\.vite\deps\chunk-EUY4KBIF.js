import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  DropdownMenu,
  EllipsisHorizontal,
  IconButton,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-3NJTXRIY.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ActionMenu = ({
  groups,
  variant = "transparent",
  children
}) => {
  const inner = children ?? (0, import_jsx_runtime.jsx)(IconButton, { size: "small", variant, children: (0, import_jsx_runtime.jsx)(EllipsisHorizontal, {}) });
  return (0, import_jsx_runtime.jsxs)(DropdownMenu, { children: [
    (0, import_jsx_runtime.jsx)(DropdownMenu.Trigger, { asChild: true, children: inner }),
    (0, import_jsx_runtime.jsx)(DropdownMenu.Content, { children: groups.map((group, index) => {
      if (!group.actions.length) {
        return null;
      }
      const isLast = index === groups.length - 1;
      return (0, import_jsx_runtime.jsxs)(DropdownMenu.Group, { children: [
        group.actions.map((action, index2) => {
          const Wrapper = action.disabledTooltip ? ({ children: children2 }) => (0, import_jsx_runtime.jsx)(
            ConditionalTooltip,
            {
              showTooltip: action.disabled,
              content: action.disabledTooltip,
              side: "right",
              children: (0, import_jsx_runtime.jsx)("div", { children: children2 })
            }
          ) : "div";
          if (action.onClick) {
            return (0, import_jsx_runtime.jsx)(Wrapper, { children: (0, import_jsx_runtime.jsxs)(
              DropdownMenu.Item,
              {
                disabled: action.disabled,
                onClick: (e) => {
                  e.stopPropagation();
                  action.onClick();
                },
                className: clx(
                  "[&_svg]:text-ui-fg-subtle flex items-center gap-x-2",
                  {
                    "[&_svg]:text-ui-fg-disabled": action.disabled
                  }
                ),
                children: [
                  action.icon,
                  (0, import_jsx_runtime.jsx)("span", { children: action.label })
                ]
              }
            ) }, index2);
          }
          return (0, import_jsx_runtime.jsx)(Wrapper, { children: (0, import_jsx_runtime.jsx)(
            DropdownMenu.Item,
            {
              className: clx(
                "[&_svg]:text-ui-fg-subtle flex items-center gap-x-2",
                {
                  "[&_svg]:text-ui-fg-disabled": action.disabled
                }
              ),
              asChild: true,
              disabled: action.disabled,
              children: (0, import_jsx_runtime.jsxs)(Link, { to: action.to, onClick: (e) => e.stopPropagation(), children: [
                action.icon,
                (0, import_jsx_runtime.jsx)("span", { children: action.label })
              ] })
            }
          ) }, index2);
        }),
        !isLast && (0, import_jsx_runtime.jsx)(DropdownMenu.Separator, {})
      ] }, index);
    }) })
  ] });
};

export {
  ActionMenu
};
//# sourceMappingURL=chunk-EUY4KBIF.js.map
