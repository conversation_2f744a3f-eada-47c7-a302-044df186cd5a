import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-QL4XKIVL.mjs
var PRICE_PREFERENCES_QUERY_KEY = "price-preferences";
var pricePreferencesQueryKeys = queryKeysFactory(
  PRICE_PREFERENCES_QUERY_KEY
);
var usePricePreferences = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.pricePreference.list(query),
    queryKey: pricePreferencesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};

export {
  pricePreferencesQueryKeys,
  usePricePreferences
};
//# sourceMappingURL=chunk-662EXSHO.js.map
