import {
  MetadataForm
} from "./chunk-LNSD7AQU.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import "./chunk-XMQMXYDG.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import {
  useOrder,
  useUpdateOrder
} from "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-metadata-4YNV2NWH.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var OrderMetadata = () => {
  const { id } = useParams();
  const { order, isPending, isError, error } = useOrder(id, {
    fields: "id,metadata"
  });
  const { mutateAsync, isPending: isMutating } = useUpdateOrder(order == null ? void 0 : order.id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    MetadataForm,
    {
      metadata: order == null ? void 0 : order.metadata,
      hook: mutateAsync,
      isPending,
      isMutating
    }
  );
};
export {
  OrderMetadata as Component
};
//# sourceMappingURL=order-metadata-4YNV2NWH-NLWDWNDZ.js.map
