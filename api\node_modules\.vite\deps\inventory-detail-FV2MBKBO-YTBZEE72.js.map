{"version": 3, "sources": ["../../@medusajs/dashboard/dist/inventory-detail-FV2MBKBO.mjs"], "sourcesContent": ["import {\n  InventoryItemGeneralSection\n} from \"./chunk-Q5DI5VYN.mjs\";\nimport {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  getFormattedCountry\n} from \"./chunk-OIAPXGI2.mjs\";\nimport \"./chunk-YOYOJU5D.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport {\n  useDate\n} from \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport {\n  Thumbnail\n} from \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport {\n  useDeleteReservationItem,\n  useReservationItems\n} from \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  inventoryItemsQueryKeys,\n  useDeleteInventoryItemLevel,\n  useInventoryItem,\n  useInventoryItemLevels\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/inventory/inventory-detail/constants.ts\nvar INVENTORY_DETAIL_FIELDS = \"*variants,*variants.product,*variants.options\";\n\n// src/routes/inventory/inventory-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar InventoryDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { inventory_item } = useInventoryItem(\n    id,\n    {\n      fields: INVENTORY_DETAIL_FIELDS\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id)\n    }\n  );\n  if (!inventory_item) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: inventory_item.title ?? inventory_item.sku ?? id });\n};\n\n// src/routes/inventory/inventory-detail/inventory-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/inventory-item-attributes/attributes-section.tsx\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar InventoryItemAttributeSection = ({\n  inventoryItem\n}) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"products.attributes\") }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"attributes\",\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.height\"), value: inventoryItem.height }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.width\"), value: inventoryItem.width }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.length\"), value: inventoryItem.length }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.weight\"), value: inventoryItem.weight }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.midCode\"), value: inventoryItem.mid_code }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.material\"), value: inventoryItem.material }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.hsCode\"), value: inventoryItem.hs_code }),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"fields.countryOfOrigin\"),\n        value: getFormattedCountry(inventoryItem.origin_country)\n      }\n    )\n  ] });\n};\n\n// src/routes/inventory/inventory-detail/components/inventory-item-location-levels.tsx\nimport { Button, Container as Container2, Heading as Heading2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/location-levels-table/use-location-list-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\n\n// src/routes/inventory/inventory-detail/components/location-levels-table/location-actions.tsx\nimport { PencilSquare as PencilSquare2, Trash } from \"@medusajs/icons\";\nimport { usePrompt } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar LocationActions = ({\n  level\n}) => {\n  const { t } = useTranslation2();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteInventoryItemLevel(\n    level.inventory_item_id,\n    level.location_id\n  );\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"inventory.deleteWarning\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync();\n  };\n  return /* @__PURE__ */ jsx3(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n              label: t(\"actions.edit\"),\n              to: `locations/${level.location_id}`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete,\n              disabled: level.reserved_quantity > 0 || level.stocked_quantity > 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/location-levels-table/use-location-list-table-columns.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useLocationListTableColumns = () => {\n  const { t } = useTranslation3();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"stock_locations.0.name\", {\n        header: t(\"fields.location\"),\n        cell: ({ getValue }) => {\n          const locationName = getValue();\n          if (!locationName) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: locationName.toString() }) });\n        }\n      }),\n      columnHelper.accessor(\"reserved_quantity\", {\n        header: t(\"inventory.reserved\"),\n        cell: ({ getValue }) => {\n          const quantity = getValue();\n          if (Number.isNaN(quantity)) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: quantity }) });\n        }\n      }),\n      columnHelper.accessor(\"stocked_quantity\", {\n        header: t(\"fields.inStock\"),\n        cell: ({ getValue }) => {\n          const stockedQuantity = getValue();\n          if (Number.isNaN(stockedQuantity)) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: stockedQuantity }) });\n        }\n      }),\n      columnHelper.accessor(\"available_quantity\", {\n        header: t(\"inventory.available\"),\n        cell: ({ getValue }) => {\n          const availableQuantity = getValue();\n          if (Number.isNaN(availableQuantity)) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: availableQuantity }) });\n        }\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx4(LocationActions, { level: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/location-levels-table/use-location-list-table-query.tsx\nvar useLocationLevelTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams(\n    [\n      \"id\",\n      \"location_id\",\n      \"stocked_quantity\",\n      \"reserved_quantity\",\n      \"incoming_quantity\",\n      \"available_quantity\",\n      \"*stock_locations\"\n    ],\n    prefix\n  );\n  const { reserved_quantity, stocked_quantity, available_quantity, ...params } = raw;\n  const searchParams = {\n    limit: pageSize,\n    reserved_quantity: reserved_quantity ? JSON.parse(reserved_quantity) : void 0,\n    stocked_quantity: stocked_quantity ? JSON.parse(stocked_quantity) : void 0,\n    available_quantity: available_quantity ? JSON.parse(available_quantity) : void 0,\n    ...params\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/inventory/inventory-detail/components/location-levels-table/location-list-table.tsx\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ItemLocationListTable = ({\n  inventory_item_id\n}) => {\n  const { searchParams, raw } = useLocationLevelTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const {\n    inventory_levels,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useInventoryItemLevels(inventory_item_id, {\n    ...searchParams,\n    fields: \"*stock_locations\"\n  });\n  const columns = useLocationListTableColumns();\n  const { table } = useDataTable({\n    data: inventory_levels ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx5(\n    _DataTable,\n    {\n      table,\n      columns,\n      pageSize: PAGE_SIZE,\n      count,\n      isLoading,\n      pagination: true,\n      queryObject: raw\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/inventory-item-location-levels.tsx\nimport { jsx as jsx6, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar InventoryItemLocationLevelsSection = ({\n  inventoryItem\n}) => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx6(Heading2, { children: t(\"inventory.locationLevels\") }),\n      /* @__PURE__ */ jsx6(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx6(Link, { to: \"locations\", children: t(\"inventory.manageLocations\") }) })\n    ] }),\n    /* @__PURE__ */ jsx6(ItemLocationListTable, { inventory_item_id: inventoryItem.id })\n  ] });\n};\n\n// src/routes/inventory/inventory-detail/components/inventory-item-reservations.tsx\nimport { Button as Button2, Container as Container3, Heading as Heading3 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation8 } from \"react-i18next\";\nimport { Link as Link2 } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/reservations-table/reservation-list-table.tsx\nimport { useMemo as useMemo3 } from \"react\";\n\n// src/routes/inventory/inventory-detail/components/reservations-table/use-reservation-list-table-columns.tsx\nimport { createColumnHelper as createColumnHelper2 } from \"@tanstack/react-table\";\nimport { useMemo as useMemo2 } from \"react\";\nimport { useTranslation as useTranslation7 } from \"react-i18next\";\n\n// src/components/table/table-cells/common/created-at-cell/created-at-cell.tsx\nimport { Tooltip } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation5 } from \"react-i18next\";\nimport { jsx as jsx7 } from \"react/jsx-runtime\";\nvar CreatedAtCell = ({ date }) => {\n  const { getFullDate } = useDate();\n  if (!date) {\n    return /* @__PURE__ */ jsx7(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx7(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx7(\n    Tooltip,\n    {\n      className: \"z-10\",\n      content: /* @__PURE__ */ jsx7(\"span\", { className: \"text-pretty\", children: `${getFullDate({\n        date,\n        includeTime: true\n      })}` }),\n      children: /* @__PURE__ */ jsx7(\"span\", { className: \"truncate\", children: getFullDate({ date, includeTime: true }) })\n    }\n  ) });\n};\n\n// src/routes/inventory/inventory-detail/components/reservations-table/reservation-actions.tsx\nimport { PencilSquare as PencilSquare3, Trash as Trash2 } from \"@medusajs/icons\";\nimport { toast, usePrompt as usePrompt2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation6 } from \"react-i18next\";\nimport { jsx as jsx8 } from \"react/jsx-runtime\";\nvar ReservationActions = ({\n  reservation\n}) => {\n  const { t } = useTranslation6();\n  const prompt = usePrompt2();\n  const { mutateAsync } = useDeleteReservationItem(reservation.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"inventory.deleteWarning\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"inventory.reservation.deleteSuccessToast\"));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx8(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx8(PencilSquare3, {}),\n              label: t(\"actions.edit\"),\n              to: `/reservations/${reservation.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx8(Trash2, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/reservations-table/use-reservation-list-table-columns.tsx\nimport { jsx as jsx9 } from \"react/jsx-runtime\";\nvar columnHelper2 = createColumnHelper2();\nvar useReservationTableColumn = ({ sku }) => {\n  const { t } = useTranslation7();\n  return useMemo2(\n    () => [\n      columnHelper2.display({\n        id: \"sku\",\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"fields.sku\") }),\n        cell: () => {\n          return /* @__PURE__ */ jsx9(TextCell, { text: sku });\n        }\n      }),\n      columnHelper2.accessor(\"line_item.order_id\", {\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"inventory.reservation.orderID\") }),\n        cell: ({ getValue }) => {\n          const orderId = getValue();\n          if (!orderId) {\n            return /* @__PURE__ */ jsx9(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx9(TextCell, { text: orderId });\n        }\n      }),\n      columnHelper2.accessor(\"description\", {\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"fields.description\") }),\n        cell: ({ getValue }) => {\n          const description = getValue();\n          if (!description) {\n            return /* @__PURE__ */ jsx9(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx9(TextCell, { text: description });\n        }\n      }),\n      columnHelper2.accessor(\"location.name\", {\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"inventory.reservation.location\") }),\n        cell: ({ getValue }) => {\n          const location = getValue();\n          if (!location) {\n            return /* @__PURE__ */ jsx9(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx9(TextCell, { text: location });\n        }\n      }),\n      columnHelper2.accessor(\"created_at\", {\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"fields.createdAt\") }),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx9(CreatedAtCell, { date: getValue() })\n      }),\n      columnHelper2.accessor(\"quantity\", {\n        header: () => /* @__PURE__ */ jsx9(TextHeader, { text: t(\"fields.quantity\"), align: \"right\" }),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx9(TextCell, { text: getValue(), align: \"right\" });\n        }\n      }),\n      columnHelper2.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx9(ReservationActions, { reservation: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/reservations-table/use-reservation-list-table-query.tsx\nvar useReservationsTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams(\n    [\n      \"id\",\n      \"location_id\",\n      \"inventory_item_id\",\n      \"quantity\",\n      \"line_item_id\",\n      \"description\",\n      \"created_by\"\n    ],\n    prefix\n  );\n  const { quantity, ...params } = raw;\n  const searchParams = {\n    limit: pageSize,\n    quantity: quantity ? JSON.parse(quantity) : void 0,\n    ...params\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/inventory/inventory-detail/components/reservations-table/reservation-list-table.tsx\nimport { jsx as jsx10 } from \"react/jsx-runtime\";\nvar PAGE_SIZE2 = 20;\nvar ReservationItemTable = ({\n  inventoryItem\n}) => {\n  const { searchParams, raw } = useReservationsTableQuery({\n    pageSize: PAGE_SIZE2\n  });\n  const { reservations, count, isPending, isError, error } = useReservationItems({\n    ...searchParams,\n    inventory_item_id: [inventoryItem.id]\n  });\n  const { stock_locations } = useStockLocations({\n    id: (reservations || []).map((r) => r.location_id)\n  });\n  const data = useMemo3(() => {\n    const locationMap = new Map((stock_locations || []).map((l) => [l.id, l]));\n    return (reservations || []).map((r) => ({\n      ...r,\n      location: locationMap.get(r.location_id)\n    }));\n  }, [reservations, stock_locations]);\n  const columns = useReservationTableColumn({ sku: inventoryItem.sku });\n  const { table } = useDataTable({\n    data: data ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE2\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx10(\n    _DataTable,\n    {\n      table,\n      columns,\n      pageSize: PAGE_SIZE2,\n      count,\n      isLoading: isPending,\n      pagination: true,\n      queryObject: raw\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/components/inventory-item-reservations.tsx\nimport { jsx as jsx11, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar InventoryItemReservationsSection = ({\n  inventoryItem\n}) => {\n  const { t } = useTranslation8();\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx11(Heading3, { children: t(\"reservations.domain\") }),\n      /* @__PURE__ */ jsx11(Button2, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx11(Link2, { to: `/reservations/create?item_id=${inventoryItem.id}`, children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx11(ReservationItemTable, { inventoryItem })\n  ] });\n};\n\n// src/routes/inventory/inventory-detail/components/inventory-item-variants/variants-section.tsx\nimport { TriangleRightMini } from \"@medusajs/icons\";\nimport { Container as Container4, Heading as Heading4 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation9 } from \"react-i18next\";\nimport { Link as Link3 } from \"react-router-dom\";\nimport { jsx as jsx12, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar InventoryItemVariantsSection = ({\n  variants\n}) => {\n  const { t } = useTranslation9();\n  if (!variants?.length) {\n    return null;\n  }\n  return /* @__PURE__ */ jsxs4(Container4, { className: \"p-0\", children: [\n    /* @__PURE__ */ jsx12(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: /* @__PURE__ */ jsx12(Heading4, { level: \"h2\", children: t(\"inventory.associatedVariants\") }) }),\n    /* @__PURE__ */ jsx12(\"div\", { className: \"txt-small flex flex-col gap-2 px-2 pb-2\", children: variants.map((variant) => {\n      const link = variant.product ? `/products/${variant.product.id}/variants/${variant.id}` : null;\n      const Inner = /* @__PURE__ */ jsx12(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component rounded-md px-4 py-2 transition-colors\", children: /* @__PURE__ */ jsxs4(\"div\", { className: \"flex items-center gap-3\", children: [\n        /* @__PURE__ */ jsx12(\"div\", { className: \"shadow-elevation-card-rest rounded-md\", children: /* @__PURE__ */ jsx12(Thumbnail, { src: variant.product?.thumbnail }) }),\n        /* @__PURE__ */ jsxs4(\"div\", { className: \"flex flex-1 flex-col\", children: [\n          /* @__PURE__ */ jsx12(\"span\", { className: \"text-ui-fg-base font-medium\", children: variant.title }),\n          /* @__PURE__ */ jsx12(\"span\", { className: \"text-ui-fg-subtle\", children: variant.options.map((o) => o.value).join(\" \\u22C5 \") })\n        ] }),\n        /* @__PURE__ */ jsx12(\"div\", { className: \"size-7 flex items-center justify-center\", children: /* @__PURE__ */ jsx12(TriangleRightMini, { className: \"text-ui-fg-muted\" }) })\n      ] }) });\n      if (!link) {\n        return /* @__PURE__ */ jsx12(\"div\", { children: Inner }, variant.id);\n      }\n      return /* @__PURE__ */ jsx12(\n        Link3,\n        {\n          to: link,\n          className: \"outline-none focus-within:shadow-borders-interactive-with-focus rounded-md [&:hover>div]:bg-ui-bg-component-hover\",\n          children: Inner\n        },\n        variant.id\n      );\n    }) })\n  ] });\n};\n\n// src/routes/inventory/inventory-detail/inventory-detail.tsx\nimport { jsx as jsx13, jsxs as jsxs5 } from \"react/jsx-runtime\";\nvar InventoryDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const {\n    inventory_item,\n    isPending: isLoading,\n    isError,\n    error\n  } = useInventoryItem(\n    id,\n    {\n      fields: INVENTORY_DETAIL_FIELDS\n    },\n    {\n      initialData\n    }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !inventory_item) {\n    return /* @__PURE__ */ jsx13(\n      TwoColumnPageSkeleton,\n      {\n        showJSON: true,\n        mainSections: 3,\n        sidebarSections: 2,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs5(\n    TwoColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"inventory_item.details.after\"),\n        before: getWidgets(\"inventory_item.details.before\"),\n        sideAfter: getWidgets(\"inventory_item.details.side.after\"),\n        sideBefore: getWidgets(\"inventory_item.details.side.before\")\n      },\n      data: inventory_item,\n      showJSON: true,\n      showMetadata: true,\n      children: [\n        /* @__PURE__ */ jsxs5(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx13(InventoryItemGeneralSection, { inventoryItem: inventory_item }),\n          /* @__PURE__ */ jsx13(InventoryItemLocationLevelsSection, { inventoryItem: inventory_item }),\n          /* @__PURE__ */ jsx13(InventoryItemReservationsSection, { inventoryItem: inventory_item })\n        ] }),\n        /* @__PURE__ */ jsxs5(TwoColumnPage.Sidebar, { children: [\n          /* @__PURE__ */ jsx13(\n            InventoryItemVariantsSection,\n            {\n              variants: inventory_item.variants\n            }\n          ),\n          /* @__PURE__ */ jsx13(InventoryItemAttributeSection, { inventoryItem: inventory_item })\n        ] })\n      ]\n    }\n  );\n};\n\n// src/routes/inventory/inventory-detail/loader.ts\nvar inventoryDetailQuery = (id) => ({\n  queryKey: inventoryItemsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.inventoryItem.retrieve(id, {\n    fields: INVENTORY_DETAIL_FIELDS\n  })\n});\nvar inventoryItemLoader = async ({ params }) => {\n  const id = params.id;\n  const query = inventoryDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  InventoryDetailBreadcrumb as Breadcrumb,\n  InventoryDetail as Component,\n  inventoryItemLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA,yBAAoB;AA0BpB,IAAAA,sBAAkC;AAiDlC,mBAAwB;AAOxB,IAAAC,sBAA4B;AAmD5B,IAAAA,sBAA4B;AAuF5B,IAAAA,sBAA4B;AA6C5B,IAAAA,sBAA2C;AAoB3C,IAAAC,gBAAoC;AAIpC,IAAAC,gBAAoC;AAMpC,IAAAC,sBAA4B;AAuB5B,IAAAC,sBAA4B;AAsD5B,IAAAA,sBAA4B;AA4F5B,IAAAA,uBAA6B;AAiD7B,IAAAA,uBAA4C;AAmB5C,IAAAC,uBAA4C;AAqC5C,IAAAA,uBAA4C;AA5jB5C,IAAI,0BAA0B;AAI9B,IAAI,4BAA4B,CAAC,UAAU;AACzC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,eAAe,IAAI;AAAA,IACzB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF;AACA,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,eAAe,SAAS,eAAe,OAAO,GAAG,CAAC;AACnG;AAUA,IAAI,gCAAgC,CAAC;AAAA,EACnC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,UACjE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,cAAc,OAAO,CAAC;AAAA,QAC3E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,cAAc,MAAM,CAAC;AAAA,QACzE,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,cAAc,OAAO,CAAC;AAAA,QAC3E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,cAAc,OAAO,CAAC;AAAA,QAC3E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,cAAc,SAAS,CAAC;AAAA,QAC9E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,iBAAiB,GAAG,OAAO,cAAc,SAAS,CAAC;AAAA,QAC/E,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,eAAe,GAAG,OAAO,cAAc,QAAQ,CAAC;AAAA,QAC5E,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,wBAAwB;AAAA,QACjC,OAAO,oBAAoB,cAAc,cAAc;AAAA,MACzD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAiBA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI;AAAA,IACtB,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACA,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,yBAAyB;AAAA,MACxC,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAAA,EACpB;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,aAAa,MAAM,WAAW;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,cACpC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAU,MAAM,oBAAoB,KAAK,MAAM,mBAAmB;AAAA,YACpE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,8BAA8B,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,0BAA0B;AAAA,QAC9C,QAAQ,EAAE,iBAAiB;AAAA,QAC3B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,eAAe,SAAS;AAC9B,cAAI,CAAC,cAAc;AACjB,uBAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,aAAa,SAAS,EAAE,CAAC,EAAE,CAAC;AAAA,QACvM;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,qBAAqB;AAAA,QACzC,QAAQ,EAAE,oBAAoB;AAAA,QAC9B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,WAAW,SAAS;AAC1B,cAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,SAAS,CAAC,EAAE,CAAC;AAAA,QACxL;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,oBAAoB;AAAA,QACxC,QAAQ,EAAE,gBAAgB;AAAA,QAC1B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,kBAAkB,SAAS;AACjC,cAAI,OAAO,MAAM,eAAe,GAAG;AACjC,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,gBAAgB,CAAC,EAAE,CAAC;AAAA,QAC/L;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,sBAAsB;AAAA,QAC1C,QAAQ,EAAE,qBAAqB;AAAA,QAC/B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,oBAAoB,SAAS;AACnC,cAAI,OAAO,MAAM,iBAAiB,GAAG;AACnC,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,kBAAkB,CAAC,EAAE,CAAC;AAAA,QACjM;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,iBAAiB,EAAE,OAAO,IAAI,SAAS,CAAC;AAAA,MAClF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,6BAA6B,CAAC;AAAA,EAChC,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM;AAAA,IACV;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,mBAAmB,kBAAkB,oBAAoB,GAAG,OAAO,IAAI;AAC/E,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,mBAAmB,oBAAoB,KAAK,MAAM,iBAAiB,IAAI;AAAA,IACvE,kBAAkB,mBAAmB,KAAK,MAAM,gBAAgB,IAAI;AAAA,IACpE,oBAAoB,qBAAqB,KAAK,MAAM,kBAAkB,IAAI;AAAA,IAC1E,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,cAAc,IAAI,IAAI,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,uBAAuB,mBAAmB;AAAA,IAC5C,GAAG;AAAA,IACH,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,UAAU,4BAA4B;AAC5C,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,oBAAoB,CAAC;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAI,qCAAqC,CAAC;AAAA,EACxC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,UAAU,EAAE,0BAA0B,EAAE,CAAC;AAAA,UAC1D,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,aAAa,UAAU,EAAE,2BAA2B,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1L,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,uBAAuB,EAAE,mBAAmB,cAAc,GAAG,CAAC;AAAA,EACrF,EAAE,CAAC;AACL;AAmBA,IAAI,gBAAgB,CAAC,EAAE,KAAK,MAAM;AAChC,QAAM,EAAE,YAAY,IAAI,QAAQ;AAChC,MAAI,CAAC,MAAM;AACT,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA;AAAA,IAC3H;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,aAAyB,oBAAAA,KAAK,QAAQ,EAAE,WAAW,eAAe,UAAU,GAAG,YAAY;AAAA,QACzF;AAAA,QACA,aAAa;AAAA,MACf,CAAC,CAAC,GAAG,CAAC;AAAA,MACN,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,YAAY,EAAE,MAAM,aAAa,KAAK,CAAC,EAAE,CAAC;AAAA,IACtH;AAAA,EACF,EAAE,CAAC;AACL;AAOA,IAAI,qBAAqB,CAAC;AAAA,EACxB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAW;AAC1B,QAAM,EAAE,YAAY,IAAI,yBAAyB,YAAY,EAAE;AAC/D,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,yBAAyB;AAAA,MACxC,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,0CAA0C,CAAC;AAAA,MAC7D;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,iBAAiB,YAAY,EAAE;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,cACrC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,gBAAgB,mBAAoB;AACxC,IAAI,4BAA4B,CAAC,EAAE,IAAI,MAAM;AAC3C,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO,cAAAC;AAAA,IACL,MAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,QACpB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAC,KAAK,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;AAAA,QACxE,MAAM,MAAM;AACV,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,IAAI,CAAC;AAAA,QACrD;AAAA,MACF,CAAC;AAAA,MACD,cAAc,SAAS,sBAAsB;AAAA,QAC3C,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,+BAA+B,EAAE,CAAC;AAAA,QAC3F,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,UAAU,SAAS;AACzB,cAAI,CAAC,SAAS;AACZ,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,QAAQ,CAAC;AAAA,QACzD;AAAA,MACF,CAAC;AAAA,MACD,cAAc,SAAS,eAAe;AAAA,QACpC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;AAAA,QAChF,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,cAAc,SAAS;AAC7B,cAAI,CAAC,aAAa;AAChB,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,YAAY,CAAC;AAAA,QAC7D;AAAA,MACF,CAAC;AAAA,MACD,cAAc,SAAS,iBAAiB;AAAA,QACtC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,gCAAgC,EAAE,CAAC;AAAA,QAC5F,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,WAAW,SAAS;AAC1B,cAAI,CAAC,UAAU;AACb,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,SAAS,CAAC;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,MACD,cAAc,SAAS,cAAc;AAAA,QACnC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;AAAA,QAC9E,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,eAAe,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAClF,CAAC;AAAA,MACD,cAAc,SAAS,YAAY;AAAA,QACjC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,iBAAiB,GAAG,OAAO,QAAQ,CAAC;AAAA,QAC7F,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,SAAS,GAAG,OAAO,QAAQ,CAAC;AAAA,QAC5E;AAAA,MACF,CAAC;AAAA,MACD,cAAc,QAAQ;AAAA,QACpB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,oBAAoB,EAAE,aAAa,IAAI,SAAS,CAAC;AAAA,MAC3F,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,4BAA4B,CAAC;AAAA,EAC/B,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM;AAAA,IACV;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,UAAU,GAAG,OAAO,IAAI;AAChC,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,UAAU,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,IAC5C,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,aAAa;AACjB,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,cAAc,IAAI,IAAI,0BAA0B;AAAA,IACtD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,cAAc,OAAO,WAAW,SAAS,MAAM,IAAI,oBAAoB;AAAA,IAC7E,GAAG;AAAA,IACH,mBAAmB,CAAC,cAAc,EAAE;AAAA,EACtC,CAAC;AACD,QAAM,EAAE,gBAAgB,IAAI,kBAAkB;AAAA,IAC5C,KAAK,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,WAAW;AAAA,EACnD,CAAC;AACD,QAAM,WAAO,cAAAC,SAAS,MAAM;AAC1B,UAAM,cAAc,IAAI,KAAK,mBAAmB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACzE,YAAQ,gBAAgB,CAAC,GAAG,IAAI,CAAC,OAAO;AAAA,MACtC,GAAG;AAAA,MACH,UAAU,YAAY,IAAI,EAAE,WAAW;AAAA,IACzC,EAAE;AAAA,EACJ,GAAG,CAAC,cAAc,eAAe,CAAC;AAClC,QAAM,UAAU,0BAA0B,EAAE,KAAK,cAAc,IAAI,CAAC;AACpE,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,QAAQ,CAAC;AAAA,IACf;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,qBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAI,mCAAmC,CAAC;AAAA,EACtC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,qBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,qBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,qBAAAC,KAAM,SAAU,EAAE,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,UACtD,qBAAAA,KAAM,QAAS,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,qBAAAA,KAAM,MAAO,EAAE,IAAI,gCAAgC,cAAc,EAAE,IAAI,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1N,EAAE,CAAC;AAAA,QACa,qBAAAA,KAAM,sBAAsB,EAAE,cAAc,CAAC;AAAA,EAC/D,EAAE,CAAC;AACL;AAQA,IAAI,+BAA+B,CAAC;AAAA,EAClC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,EAAC,qCAAU,SAAQ;AACrB,WAAO;AAAA,EACT;AACA,aAAuB,qBAAAC,MAAM,WAAY,EAAE,WAAW,OAAO,UAAU;AAAA,QACrD,qBAAAC,KAAM,OAAO,EAAE,WAAW,+CAA+C,cAA0B,qBAAAA,KAAM,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC;AAAA,QAClL,qBAAAA,KAAM,OAAO,EAAE,WAAW,2CAA2C,UAAU,SAAS,IAAI,CAAC,YAAY;AAnoB7H;AAooBM,YAAM,OAAO,QAAQ,UAAU,aAAa,QAAQ,QAAQ,EAAE,aAAa,QAAQ,EAAE,KAAK;AAC1F,YAAM,YAAwB,qBAAAA,KAAM,OAAO,EAAE,WAAW,wFAAwF,cAA0B,qBAAAD,MAAM,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,YACvN,qBAAAC,KAAM,OAAO,EAAE,WAAW,yCAAyC,cAA0B,qBAAAA,KAAM,WAAW,EAAE,MAAK,aAAQ,YAAR,mBAAiB,UAAU,CAAC,EAAE,CAAC;AAAA,YACpJ,qBAAAD,MAAM,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,cAC1D,qBAAAC,KAAM,QAAQ,EAAE,WAAW,+BAA+B,UAAU,QAAQ,MAAM,CAAC;AAAA,cACnF,qBAAAA,KAAM,QAAQ,EAAE,WAAW,qBAAqB,UAAU,QAAQ,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,KAAU,EAAE,CAAC;AAAA,QAClI,EAAE,CAAC;AAAA,YACa,qBAAAA,KAAM,OAAO,EAAE,WAAW,2CAA2C,cAA0B,qBAAAA,KAAM,mBAAmB,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC;AAAA,MAC9K,EAAE,CAAC,EAAE,CAAC;AACN,UAAI,CAAC,MAAM;AACT,mBAAuB,qBAAAA,KAAM,OAAO,EAAE,UAAU,MAAM,GAAG,QAAQ,EAAE;AAAA,MACrE;AACA,iBAAuB,qBAAAA;AAAA,QACrB;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,CAAC,EAAE,CAAC;AAAA,EACN,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,gBAAgB;AAChC,eAAuB,qBAAAC;AAAA,MACrB;AAAA,MACA;AAAA,QACE,UAAU;AAAA,QACV,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,qBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,8BAA8B;AAAA,QAChD,QAAQ,WAAW,+BAA+B;AAAA,QAClD,WAAW,WAAW,mCAAmC;AAAA,QACzD,YAAY,WAAW,oCAAoC;AAAA,MAC7D;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,YACQ,qBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,qBAAAD,KAAM,6BAA6B,EAAE,eAAe,eAAe,CAAC;AAAA,cACpE,qBAAAA,KAAM,oCAAoC,EAAE,eAAe,eAAe,CAAC;AAAA,cAC3E,qBAAAA,KAAM,kCAAkC,EAAE,eAAe,eAAe,CAAC;AAAA,QAC3F,EAAE,CAAC;AAAA,YACa,qBAAAC,MAAM,cAAc,SAAS,EAAE,UAAU;AAAA,cACvC,qBAAAD;AAAA,YACd;AAAA,YACA;AAAA,cACE,UAAU,eAAe;AAAA,YAC3B;AAAA,UACF;AAAA,cACgB,qBAAAA,KAAM,+BAA+B,EAAE,eAAe,eAAe,CAAC;AAAA,QACxF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,uBAAuB,CAAC,QAAQ;AAAA,EAClC,UAAU,wBAAwB,OAAO,EAAE;AAAA,EAC3C,SAAS,YAAY,IAAI,MAAM,cAAc,SAAS,IAAI;AAAA,IACxD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,sBAAsB,OAAO,EAAE,OAAO,MAAM;AAC9C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,qBAAqB,EAAE;AACrC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_react", "import_react", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4", "jsx5", "jsxs2", "jsx6", "jsx7", "jsx8", "useMemo2", "jsx9", "useMemo3", "jsx10", "jsxs3", "jsx11", "jsxs4", "jsx12", "jsx13", "jsxs5"]}