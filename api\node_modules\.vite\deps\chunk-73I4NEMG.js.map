{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-EQTBJSBZ.mjs"], "sourcesContent": ["// src/components/common/icon-avatar/icon-avatar.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar IconAvatar = ({\n  size = \"small\",\n  children,\n  className\n}) => {\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\n        \"shadow-borders-base flex size-7 items-center justify-center\",\n        \"[&>div]:bg-ui-bg-field [&>div]:text-ui-fg-subtle [&>div]:flex [&>div]:size-6 [&>div]:items-center [&>div]:justify-center\",\n        {\n          \"size-7 rounded-md [&>div]:size-6 [&>div]:rounded-[4px]\": size === \"small\",\n          \"size-10 rounded-lg [&>div]:size-9 [&>div]:rounded-[6px]\": size === \"large\",\n          \"size-12 rounded-xl [&>div]:size-11 [&>div]:rounded-[10px]\": size === \"xlarge\"\n        },\n        className\n      ),\n      children: /* @__PURE__ */ jsx(\"div\", { children })\n    }\n  );\n};\n\nexport {\n  IconAvatar\n};\n"], "mappings": ";;;;;;;;;;;AAEA,yBAAoB;AACpB,IAAI,aAAa,CAAC;AAAA,EAChB,OAAO;AAAA,EACP;AAAA,EACA;AACF,MAAM;AACJ,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,UACE,0DAA0D,SAAS;AAAA,UACnE,2DAA2D,SAAS;AAAA,UACpE,6DAA6D,SAAS;AAAA,QACxE;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAA0B,wBAAI,OAAO,EAAE,SAAS,CAAC;AAAA,IACnD;AAAA,EACF;AACF;", "names": []}