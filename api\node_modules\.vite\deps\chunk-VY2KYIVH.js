import {
  TextCell,
  TextHeader
} from "./chunk-7HUCBNCQ.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NEZX6265.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useCurrenciesTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("code", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.code") }),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: getValue().toUpperCase() })
      }),
      columnHelper.accessor("name", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.name") }),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: getValue() })
      })
    ],
    [t]
  );
};
var useCurrenciesTableQuery = ({
  pageSize = 10,
  prefix
}) => {
  const raw = useQueryParams(["order", "q", "offset"], prefix);
  const { offset, ...rest } = raw;
  const searchParams = {
    limit: pageSize,
    offset: offset ? parseInt(offset) : 0,
    ...rest
  };
  return { searchParams, raw };
};

export {
  useCurrenciesTableColumns,
  useCurrenciesTableQuery
};
//# sourceMappingURL=chunk-VY2KYIVH.js.map
