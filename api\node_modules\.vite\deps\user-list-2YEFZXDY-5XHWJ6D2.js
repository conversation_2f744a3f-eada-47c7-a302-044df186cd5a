import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useDataTableDateColumns
} from "./chunk-WNW4SNUS.js";
import {
  DataTable,
  useDataTableDateFilters
} from "./chunk-EPUS4TBC.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useUsers
} from "./chunk-7UV5UA6G.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  createDataTableColumnHelper
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/user-list-2YEFZXDY.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var UserListTable = () => {
  const { q, order, offset } = useQueryParams(["q", "order", "offset"]);
  const { users, count, isPending, isError, error } = useUsers(
    {
      q,
      order,
      offset: offset ? parseInt(offset) : 0,
      limit: PAGE_SIZE
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useFilters();
  const { t } = useTranslation();
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime.jsx)(
    DataTable,
    {
      data: users,
      columns,
      filters,
      getRowId: (row) => row.id,
      rowCount: count,
      pageSize: PAGE_SIZE,
      heading: t("users.domain"),
      rowHref: (row) => `${row.id}`,
      isLoading: isPending,
      action: {
        label: t("users.invite"),
        to: "invite"
      },
      emptyState: {
        empty: {
          heading: t("users.list.empty.heading"),
          description: t("users.list.empty.description")
        },
        filtered: {
          heading: t("users.list.filtered.heading"),
          description: t("users.list.filtered.description")
        }
      }
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dateColumns = useDataTableDateColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("email", {
        header: t("fields.email"),
        cell: ({ row }) => {
          return row.original.email;
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("first_name", {
        header: t("fields.firstName"),
        cell: ({ row }) => {
          return row.original.first_name || "-";
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("last_name", {
        header: t("fields.lastName"),
        cell: ({ row }) => {
          return row.original.last_name || "-";
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      ...dateColumns,
      columnHelper.action({
        actions: [
          {
            label: t("actions.edit"),
            icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
            onClick: (ctx) => {
              navigate(`${ctx.row.original.id}/edit`);
            }
          }
        ]
      })
    ],
    [t, navigate, dateColumns]
  );
};
var useFilters = () => {
  const dateFilters = useDataTableDateFilters();
  return (0, import_react.useMemo)(() => {
    return dateFilters;
  }, [dateFilters]);
};
var UserList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("user.list.after"),
        before: getWidgets("user.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(UserListTable, {})
    }
  );
};
export {
  UserList as Component
};
//# sourceMappingURL=user-list-2YEFZXDY-5XHWJ6D2.js.map
