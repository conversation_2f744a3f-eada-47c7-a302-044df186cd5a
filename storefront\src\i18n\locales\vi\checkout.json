{"cart": "Giỏ hàng", "edit": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "proceed_to_checkout": "<PERSON><PERSON><PERSON><PERSON> hành đặt hàng", "subtotal": "<PERSON><PERSON><PERSON> tạm t<PERSON>h", "brewing_note": "<PERSON><PERSON><PERSON>", "empty_cart_message": "tr<PERSON><PERSON>", "item(s)": "s<PERSON><PERSON> p<PERSON>m", "steps": {"delivery_address": {"title": "Th<PERSON>ng tin giao hàng", "billing_address": "Địa chỉ gửi hoá đơn", "shipping_address": "Đ<PERSON>a chỉ giao hàng", "continue_to_delivery": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> giao hàng", "contact": "<PERSON><PERSON><PERSON>", "greeting": "Chào {{name}}, bạn có muốn sử dụng một trong các địa chỉ đã lưu không?", "choose_address": "<PERSON><PERSON><PERSON> địa chỉ", "fullname": "Họ và tên", "form": {"first_name": "Họ", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "Họ và tên", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "country_code": "Mã quốc gia", "country": "Quốc gia", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "province": "Tỉnh/Thành phố", "district": "Quận/Huyện", "ward": "Phường/Xã", "address": "Đ<PERSON>a chỉ cụ thể (số n<PERSON>, tên đường...)", "generate_invoice": "Thông tin xuất hóa đơn", "company": "<PERSON><PERSON>ng ty", "company_name": "<PERSON><PERSON>n công ty", "company_tax_code": "<PERSON><PERSON> số thuế", "company_address": "Địa chỉ công ty", "postal_code": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "same_as_billing": "Sử dụng địa chỉ thanh toán", "email": "Email", "save_address": "Ghi nhớ", "note": "<PERSON>hi chú khi giao hàng"}}, "shipping_method": {"title": "<PERSON><PERSON><PERSON><PERSON> thức vận chuy<PERSON>n", "delivery": "<PERSON><PERSON><PERSON>", "pickup": "<PERSON>g đi", "continue_to_payment": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h toán", "method": "<PERSON><PERSON><PERSON><PERSON> thức", "no_shipping_methods": "<PERSON><PERSON><PERSON><PERSON> có ph<PERSON><PERSON><PERSON> thức vận chuyển", "error": "Lỗi khi thiết lập ph<PERSON><PERSON><PERSON> thức vận chuyển"}, "payment": {"title": "<PERSON><PERSON> toán", "method": "<PERSON><PERSON><PERSON><PERSON> thức", "details": "<PERSON> ti<PERSON>", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "payment_details": "<PERSON> tiết thanh toán", "another_step_will_appear": "<PERSON><PERSON><PERSON> bước sẽ xuất hiện", "gift_card": "Thẻ quà tặng", "enter_card_details": "<PERSON><PERSON><PERSON><PERSON> thông tin thẻ", "continue_to_review": "<PERSON><PERSON><PERSON><PERSON> tục xem lại", "bank_transfer": "<PERSON><PERSON><PERSON><PERSON>", "account_name": "<PERSON><PERSON><PERSON> tà<PERSON>", "bank_name": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "account_number": "Số tài <PERSON>n", "transfer_description": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n", "bank_details": {"account_name": "CT TNHH TMDV EFRUIT", "bank_name": "<PERSON>ân hàng TECHCOMBANK", "account_number": "250487", "transfer_description": "eF - <PERSON><PERSON><PERSON> anh/chị (Cty)"}}, "review": {"title": "<PERSON><PERSON>", "complete_order": "Đặt hàng", "memberPointPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch tích điểm thành viên", "deliveryPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch giao hàng", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "returnPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON><PERSON>, đ<PERSON><PERSON> / tr<PERSON> sản phẩm", "paymentOnline": "<PERSON><PERSON><PERSON> s<PERSON>ch thanh to<PERSON> khi mua hàng online", "content": "Bằng cách nhấp vào nút Hoàn tất đơn hàng, bạn xác nhận rằng bạn đã đọc, hiểu và chấp nhận <deliveryPolicy><PERSON><PERSON>h sách giao hàng</deliveryPolicy>, <privacyPolicy><PERSON><PERSON><PERSON> sách bảo mật</privacyPolicy> và <returnPolicy>Ch<PERSON>h sách bảo hành đổi trả sản phẩm</returnPolicy> và xác nhận rằng bạn đã đọc <paymentOnline>Ch<PERSON>h sách thanh toán khi mua hàng online</paymentOnline>."}}, "summary": {"title": "<PERSON><PERSON><PERSON> hàng", "back_to_cart": "Quay lại giỏ hàng", "discount": "G<PERSON>ảm giá", "discount_code": "Mã giảm giá", "discount_code_placeholder": "Mã giảm giá", "discount_code_button": "<PERSON><PERSON>", "discount_code_error": "Mã giảm giá không hợp lệ", "discount_code_success": "<PERSON>ã giảm giá đã đư<PERSON>c áp dụng", "discount_code_view": "<PERSON>em mã gi<PERSON>m giá của bạn", "discount_code_remove_success": "Mã giảm giá đã đư<PERSON><PERSON> x<PERSON>a", "discount_code_remove_error": "<PERSON><PERSON><PERSON><PERSON> thể xóa mã giảm giá", "subtotal": "<PERSON><PERSON><PERSON> tạm t<PERSON>h", "shipping": "<PERSON><PERSON> giao hàng", "tax": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> cộng"}, "note": "<PERSON><PERSON><PERSON>", "order_note": "<PERSON><PERSON> ch<PERSON> sản phẩm", "product_note": "<PERSON><PERSON> chú đơn hàng", "delivery_time": "<PERSON><PERSON><PERSON><PERSON> gian giao", "cart_dialog_description": "<PERSON><PERSON><PERSON> là hộp thoại giỏ hàng của bạn. <PERSON>ạn có thể xem lại các mặt hàng, tiến hành thanh toán hoặc tiếp tục mua sắm."}