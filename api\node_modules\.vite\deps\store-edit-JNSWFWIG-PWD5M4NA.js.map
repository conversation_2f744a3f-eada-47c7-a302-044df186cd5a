{"version": 3, "sources": ["../../@medusajs/dashboard/dist/store-edit-JNSWFWIG.mjs"], "sourcesContent": ["import {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStore,\n  useUpdateStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/store/store-edit/store-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/store/store-edit/components/edit-store-form/edit-store-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Select, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditStoreSchema = z.object({\n  name: z.string().min(1),\n  default_currency_code: z.string().optional(),\n  default_region_id: z.string().optional(),\n  default_sales_channel_id: z.string().optional(),\n  default_location_id: z.string().optional()\n});\nvar EditStoreForm = ({ store }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: store.name,\n      default_region_id: store.default_region_id || void 0,\n      default_currency_code: store.supported_currencies?.find((c) => c.is_default)?.currency_code || void 0,\n      default_sales_channel_id: store.default_sales_channel_id || void 0,\n      default_location_id: store.default_location_id || void 0\n    },\n    resolver: zodResolver(EditStoreSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateStore(store.id);\n  const regionsCombobox = useComboboxData({\n    queryKey: [\"regions\", \"default_region_id\"],\n    queryFn: (params) => sdk.admin.region.list({ ...params, fields: \"id,name\" }),\n    defaultValue: store.default_region_id || void 0,\n    getOptions: (data) => data.regions.map((r) => ({ label: r.name, value: r.id }))\n  });\n  const salesChannelsCombobox = useComboboxData({\n    queryFn: (params) => sdk.admin.salesChannel.list({ ...params, fields: \"id,name\" }),\n    getOptions: (data) => data.sales_channels.map((sc) => ({ label: sc.name, value: sc.id })),\n    queryKey: [\"sales_channels\", \"default_sales_channel_id\"],\n    defaultValue: store.default_sales_channel_id || void 0\n  });\n  const locationsCombobox = useComboboxData({\n    queryFn: (params) => sdk.admin.stockLocation.list({ ...params, fields: \"id,name\" }),\n    getOptions: (data) => data.stock_locations.map((l) => ({ label: l.name, value: l.id })),\n    queryKey: [\"stock_locations\", \"default_location_id\"],\n    defaultValue: store.default_location_id || void 0\n  });\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const { default_currency_code, ...rest } = values;\n    const normalizedMutation = {\n      ...rest,\n      supported_currencies: store.supported_currencies?.map((c) => ({\n        ...c,\n        is_default: c.currency_code === default_currency_code\n      }))\n    };\n    await mutateAsync(normalizedMutation, {\n      onSuccess: () => {\n        toast.success(t(\"store.toast.update\"));\n        handleSuccess();\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-8\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"name\",\n          render: ({ field }) => /* @__PURE__ */ jsxs(Form.Item, { children: [\n            /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { placeholder: \"ACME\", ...field }) }),\n            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n          ] })\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"default_currency_code\",\n          render: ({ field: { onChange, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"store.defaultCurrency\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: onChange, children: [\n                /* @__PURE__ */ jsx(Select.Trigger, { ref: field.ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                /* @__PURE__ */ jsx(Select.Content, { children: store.supported_currencies?.map((currency) => /* @__PURE__ */ jsx(\n                  Select.Item,\n                  {\n                    value: currency.currency_code,\n                    children: currency.currency_code.toUpperCase()\n                  },\n                  currency.currency_code\n                )) })\n              ] }) })\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"default_region_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"store.defaultRegion\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: regionsCombobox.options,\n                  searchValue: regionsCombobox.searchValue,\n                  onSearchValueChange: regionsCombobox.onSearchValueChange,\n                  disabled: regionsCombobox.disabled\n                }\n              ) })\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"default_sales_channel_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"store.defaultSalesChannel\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: salesChannelsCombobox.options,\n                  searchValue: salesChannelsCombobox.searchValue,\n                  onSearchValueChange: salesChannelsCombobox.onSearchValueChange,\n                  disabled: salesChannelsCombobox.disabled\n                }\n              ) })\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"default_location_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"store.defaultLocation\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: locationsCombobox.options,\n                  searchValue: locationsCombobox.searchValue,\n                  onSearchValueChange: locationsCombobox.onSearchValueChange,\n                  disabled: locationsCombobox.disabled\n                }\n              ) })\n            ] });\n          }\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", isLoading: isPending, type: \"submit\", children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/store/store-edit/store-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar StoreEdit = () => {\n  const { t } = useTranslation2();\n  const { store, isPending: isLoading, isError, error } = useStore();\n  if (isError) {\n    throw error;\n  }\n  const ready = !!store && !isLoading;\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"store.edit.header\") }) }),\n    ready && /* @__PURE__ */ jsx2(EditStoreForm, { store })\n  ] });\n};\nexport {\n  StoreEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,yBAA0B;AA2K1B,IAAAA,sBAA2C;AA1K3C,IAAI,kBAAkB,EAAE,OAAO;AAAA,EAC7B,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,uBAAuB,EAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,mBAAmB,EAAE,OAAO,EAAE,SAAS;AAAA,EACvC,0BAA0B,EAAE,OAAO,EAAE,SAAS;AAAA,EAC9C,qBAAqB,EAAE,OAAO,EAAE,SAAS;AAC3C,CAAC;AACD,IAAI,gBAAgB,CAAC,EAAE,MAAM,MAAM;AA/CnC;AAgDE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,MAAM;AAAA,MACZ,mBAAmB,MAAM,qBAAqB;AAAA,MAC9C,yBAAuB,iBAAM,yBAAN,mBAA4B,KAAK,CAAC,MAAM,EAAE,gBAA1C,mBAAuD,kBAAiB;AAAA,MAC/F,0BAA0B,MAAM,4BAA4B;AAAA,MAC5D,qBAAqB,MAAM,uBAAuB;AAAA,IACpD;AAAA,IACA,UAAU,EAAY,eAAe;AAAA,EACvC,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,eAAe,MAAM,EAAE;AAC1D,QAAM,kBAAkB,gBAAgB;AAAA,IACtC,UAAU,CAAC,WAAW,mBAAmB;AAAA,IACzC,SAAS,CAAC,WAAW,IAAI,MAAM,OAAO,KAAK,EAAE,GAAG,QAAQ,QAAQ,UAAU,CAAC;AAAA,IAC3E,cAAc,MAAM,qBAAqB;AAAA,IACzC,YAAY,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE;AAAA,EAChF,CAAC;AACD,QAAM,wBAAwB,gBAAgB;AAAA,IAC5C,SAAS,CAAC,WAAW,IAAI,MAAM,aAAa,KAAK,EAAE,GAAG,QAAQ,QAAQ,UAAU,CAAC;AAAA,IACjF,YAAY,CAAC,SAAS,KAAK,eAAe,IAAI,CAAC,QAAQ,EAAE,OAAO,GAAG,MAAM,OAAO,GAAG,GAAG,EAAE;AAAA,IACxF,UAAU,CAAC,kBAAkB,0BAA0B;AAAA,IACvD,cAAc,MAAM,4BAA4B;AAAA,EAClD,CAAC;AACD,QAAM,oBAAoB,gBAAgB;AAAA,IACxC,SAAS,CAAC,WAAW,IAAI,MAAM,cAAc,KAAK,EAAE,GAAG,QAAQ,QAAQ,UAAU,CAAC;AAAA,IAClF,YAAY,CAAC,SAAS,KAAK,gBAAgB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE;AAAA,IACtF,UAAU,CAAC,mBAAmB,qBAAqB;AAAA,IACnD,cAAc,MAAM,uBAAuB;AAAA,EAC7C,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AA/E3D,QAAAC;AAgFI,UAAM,EAAE,uBAAuB,GAAG,KAAK,IAAI;AAC3C,UAAM,qBAAqB;AAAA,MACzB,GAAG;AAAA,MACH,uBAAsBA,MAAA,MAAM,yBAAN,gBAAAA,IAA4B,IAAI,CAAC,OAAO;AAAA,QAC5D,GAAG;AAAA,QACH,YAAY,EAAE,kBAAkB;AAAA,MAClC;AAAA,IACF;AACA,UAAM,YAAY,oBAAoB;AAAA,MACpC,WAAW,MAAM;AACf,cAAM,QAAQD,GAAE,oBAAoB,CAAC;AACrC,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,UAAsB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gBACjD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,gBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,aAAa,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,gBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3C,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,GAAG,MAAM,EAAE,MAAM;AArHzD,gBAAAC;AAsHY,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUD,GAAE,uBAAuB,EAAE,CAAC;AAAA,kBACxD,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,oBACxG,wBAAI,OAAO,SAAS,EAAE,KAAK,MAAM,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,oBACvF,wBAAI,OAAO,SAAS,EAAE,WAAUC,MAAA,MAAM,yBAAN,gBAAAA,IAA4B,IAAI,CAAC,iBAA6B;AAAA,kBAC5G,OAAO;AAAA,kBACP;AAAA,oBACE,OAAO,SAAS;AAAA,oBAChB,UAAU,SAAS,cAAc,YAAY;AAAA,kBAC/C;AAAA,kBACA,SAAS;AAAA,gBACX,GAAG,CAAC;AAAA,cACN,EAAE,CAAC,EAAE,CAAC;AAAA,YACR,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUD,GAAE,qBAAqB,EAAE,CAAC;AAAA,kBACtD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,gBAAgB;AAAA,kBACzB,aAAa,gBAAgB;AAAA,kBAC7B,qBAAqB,gBAAgB;AAAA,kBACrC,UAAU,gBAAgB;AAAA,gBAC5B;AAAA,cACF,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,kBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,sBAAsB;AAAA,kBAC/B,aAAa,sBAAsB;AAAA,kBACnC,qBAAqB,sBAAsB;AAAA,kBAC3C,UAAU,sBAAsB;AAAA,gBAClC;AAAA,cACF,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,kBACxD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,kBAAkB;AAAA,kBAC3B,aAAa,kBAAkB;AAAA,kBAC/B,qBAAqB,kBAAkB;AAAA,kBACvC,UAAU,kBAAkB;AAAA,gBAC9B;AAAA,cACF,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,WAAW,WAAW,MAAM,UAAU,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,YAAY,MAAM;AACpB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,OAAO,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AACjE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,QAAM,QAAQ,CAAC,CAAC,SAAS,CAAC;AAC1B,aAAuB,oBAAAE,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUH,GAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1H,aAAyB,oBAAAG,KAAK,eAAe,EAAE,MAAM,CAAC;AAAA,EACxD,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "_a", "jsxs2", "jsx2"]}