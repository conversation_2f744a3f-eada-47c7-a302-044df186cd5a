import {
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-EQTBJSBZ.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var IconAvatar = ({
  size = "small",
  children,
  className
}) => {
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx(
        "shadow-borders-base flex size-7 items-center justify-center",
        "[&>div]:bg-ui-bg-field [&>div]:text-ui-fg-subtle [&>div]:flex [&>div]:size-6 [&>div]:items-center [&>div]:justify-center",
        {
          "size-7 rounded-md [&>div]:size-6 [&>div]:rounded-[4px]": size === "small",
          "size-10 rounded-lg [&>div]:size-9 [&>div]:rounded-[6px]": size === "large",
          "size-12 rounded-xl [&>div]:size-11 [&>div]:rounded-[10px]": size === "xlarge"
        },
        className
      ),
      children: (0, import_jsx_runtime.jsx)("div", { children })
    }
  );
};

export {
  IconAvatar
};
//# sourceMappingURL=chunk-73I4NEMG.js.map
