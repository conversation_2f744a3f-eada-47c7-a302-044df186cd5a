import {
  TaxRegionDetailBreadcrumb,
  taxRegionLoader
} from "./chunk-XQBAAEQQ.js";
import {
  isFetchError
} from "./chunk-XBF43SLF.js";
import {
  useReturnReasons
} from "./chunk-GE5OJZGS.js";
import {
  ProgressBar
} from "./chunk-YM3FRBGU.js";
import {
  AnimatePresence
} from "./chunk-7M4ICL3D.js";
import {
  require_debounce
} from "./chunk-RQF55WOK.js";
import {
  matchSorter
} from "./chunk-IA4ROPJA.js";
import {
  I18n
} from "./chunk-5NX546NL.js";
import {
  t as t2
} from "./chunk-GYCHI7LC.js";
import {
  ZodBoolean,
  ZodEffects,
  ZodNullable,
  ZodNumber,
  ZodOptional,
  ZodString,
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import {
  ExtensionProvider,
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Skeleton
} from "./chunk-TP2BI5T3.js";
import {
  FilePreview
} from "./chunk-W5LXSWLX.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  we
} from "./chunk-26EN2TVL.js";
import {
  languages
} from "./chunk-VHD2ND4K.js";
import {
  formatDistance
} from "./chunk-7UAYECTW.js";
import {
  t
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  notificationQueryKeys,
  useNotifications,
  useProductTags,
  useVariants
} from "./chunk-3HVD2KMV.js";
import {
  useLogout
} from "./chunk-H3VZFY73.js";
import {
  useApiKeys
} from "./chunk-RX237AWS.js";
import {
  useTaxRegions
} from "./chunk-PBNFBMP6.js";
import {
  useProductTypes
} from "./chunk-HREJMEGI.js";
import {
  useShippingProfiles
} from "./chunk-KRJAVNJS.js";
import {
  useMe,
  useUsers
} from "./chunk-7UV5UA6G.js";
import {
  usePriceLists
} from "./chunk-XBIMCDU6.js";
import {
  useCustomerGroups,
  useCustomers
} from "./chunk-FSXJE4G7.js";
import {
  useCollections
} from "./chunk-E4TFG45M.js";
import {
  useCampaigns,
  usePromotions
} from "./chunk-S32V3COL.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import {
  useOrders
} from "./chunk-IBPOGPJN.js";
import {
  useProductCategories
} from "./chunk-ZJNBJBHK.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import {
  useRegions
} from "./chunk-AKXAI3UV.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import {
  useInventoryItems,
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  __publicField as __publicField2
} from "./chunk-XMKQFEQ4.js";
import {
  Link,
  NavLink,
  Navigate,
  Outlet,
  RouterProvider,
  createBrowserRouter,
  useLocation,
  useMatches,
  useNavigate,
  useNavigation,
  useRouteError
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowUturnLeft,
  Avatar,
  Badge,
  BellAlert,
  BellAlertDone,
  BookOpen,
  BuildingStorefront,
  Buildings,
  Button,
  ChevronDownMini,
  CircleHalfSolid,
  CogSixTooth,
  CurrencyDollar,
  Divider,
  Drawer,
  DropdownMenu,
  EllipsisHorizontal,
  ExclamationCircle,
  Heading,
  I18nProvider,
  IconButton,
  InformationCircleSolid,
  InlineTip,
  Input,
  Kbd,
  Keyboard,
  MagnifyingGlass,
  MinusMini,
  OpenRectArrowOut,
  Plus,
  ReceiptPercent,
  ShoppingCart,
  SidebarLeft,
  Spinner,
  SquaresPlus,
  Switch,
  Tag,
  Text,
  TimelineVertical,
  Toaster,
  TooltipProvider,
  TriangleDownMini,
  TriangleRightMini,
  User,
  Users,
  XMark,
  clx,
  dist_exports2 as dist_exports,
  dist_exports4 as dist_exports2,
  toast
} from "./chunk-RJPG7Y6U.js";
import {
  QueryClientProvider,
  keepPreviousData,
  useInfiniteQuery
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __commonJS,
  __publicField,
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/react-fast-compare/index.js
var require_react_fast_compare = __commonJS({
  "node_modules/react-fast-compare/index.js"(exports, module) {
    var hasElementType = typeof Element !== "undefined";
    var hasMap = typeof Map === "function";
    var hasSet = typeof Set === "function";
    var hasArrayBuffer = typeof ArrayBuffer === "function" && !!ArrayBuffer.isView;
    function equal(a, b) {
      if (a === b) return true;
      if (a && b && typeof a == "object" && typeof b == "object") {
        if (a.constructor !== b.constructor) return false;
        var length, i, keys;
        if (Array.isArray(a)) {
          length = a.length;
          if (length != b.length) return false;
          for (i = length; i-- !== 0; )
            if (!equal(a[i], b[i])) return false;
          return true;
        }
        var it;
        if (hasMap && a instanceof Map && b instanceof Map) {
          if (a.size !== b.size) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0])) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!equal(i.value[1], b.get(i.value[0]))) return false;
          return true;
        }
        if (hasSet && a instanceof Set && b instanceof Set) {
          if (a.size !== b.size) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0])) return false;
          return true;
        }
        if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
          length = a.length;
          if (length != b.length) return false;
          for (i = length; i-- !== 0; )
            if (a[i] !== b[i]) return false;
          return true;
        }
        if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
        if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === "function" && typeof b.valueOf === "function") return a.valueOf() === b.valueOf();
        if (a.toString !== Object.prototype.toString && typeof a.toString === "function" && typeof b.toString === "function") return a.toString() === b.toString();
        keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length) return false;
        for (i = length; i-- !== 0; )
          if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
        if (hasElementType && a instanceof Element) return false;
        for (i = length; i-- !== 0; ) {
          if ((keys[i] === "_owner" || keys[i] === "__v" || keys[i] === "__o") && a.$$typeof) {
            continue;
          }
          if (!equal(a[keys[i]], b[keys[i]])) return false;
        }
        return true;
      }
      return a !== a && b !== b;
    }
    module.exports = function isEqual(a, b) {
      try {
        return equal(a, b);
      } catch (error) {
        if ((error.message || "").match(/stack|recursion/i)) {
          console.warn("react-fast-compare cannot handle circular refs");
          return false;
        }
        throw error;
      }
    };
  }
});

// node_modules/invariant/browser.js
var require_browser = __commonJS({
  "node_modules/invariant/browser.js"(exports, module) {
    "use strict";
    var invariant2 = function(condition, format, a, b, c, d, e, f) {
      if (true) {
        if (format === void 0) {
          throw new Error("invariant requires an error message argument");
        }
      }
      if (!condition) {
        var error;
        if (format === void 0) {
          error = new Error(
            "Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings."
          );
        } else {
          var args = [a, b, c, d, e, f];
          var argIndex = 0;
          error = new Error(
            format.replace(/%s/g, function() {
              return args[argIndex++];
            })
          );
          error.name = "Invariant Violation";
        }
        error.framesToPop = 1;
        throw error;
      }
    };
    module.exports = invariant2;
  }
});

// node_modules/shallowequal/index.js
var require_shallowequal = __commonJS({
  "node_modules/shallowequal/index.js"(exports, module) {
    module.exports = function shallowEqual2(objA, objB, compare, compareContext) {
      var ret = compare ? compare.call(compareContext, objA, objB) : void 0;
      if (ret !== void 0) {
        return !!ret;
      }
      if (objA === objB) {
        return true;
      }
      if (typeof objA !== "object" || !objA || typeof objB !== "object" || !objB) {
        return false;
      }
      var keysA = Object.keys(objA);
      var keysB = Object.keys(objB);
      if (keysA.length !== keysB.length) {
        return false;
      }
      var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);
      for (var idx = 0; idx < keysA.length; idx++) {
        var key = keysA[idx];
        if (!bHasOwnProperty(key)) {
          return false;
        }
        var valueA = objA[key];
        var valueB = objB[key];
        ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;
        if (ret === false || ret === void 0 && valueA !== valueB) {
          return false;
        }
      }
      return true;
    };
  }
});

// node_modules/@medusajs/admin-shared/dist/index.mjs
var PRODUCT_CUSTOM_FIELD_MODEL = "product";
var PRODUCT_CUSTOM_FIELD_FORM_ZONES = [
  "create",
  "edit",
  "organize",
  "attributes"
];
var PRODUCT_CUSTOM_FIELD_CREATE_FORM_TABS = [
  "general",
  "organize"
];
var PRODUCT_CUSTOM_FIELD_FORM_TABS = [
  ...PRODUCT_CUSTOM_FIELD_CREATE_FORM_TABS
];
var PRODUCT_CUSTOM_FIELD_DISPLAY_ZONES = [
  "general",
  "organize",
  "attributes"
];
var PRODUCT_CUSTOM_FIELD_LINK_PATHS = [
  `${PRODUCT_CUSTOM_FIELD_MODEL}.$link`
];
var PRODUCT_CUSTOM_FIELD_FORM_CONFIG_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_FORM_ZONES.map(
    (form) => `${PRODUCT_CUSTOM_FIELD_MODEL}.${form}.$config`
  )
];
var PRODUCT_CUSTOM_FIELD_FORM_FIELD_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_FORM_ZONES.flatMap((form) => {
    return form === "create" ? PRODUCT_CUSTOM_FIELD_CREATE_FORM_TABS.map(
      (tab) => `${PRODUCT_CUSTOM_FIELD_MODEL}.${form}.${tab}.$field`
    ) : [`${PRODUCT_CUSTOM_FIELD_MODEL}.${form}.$field`];
  })
];
var PRODUCT_CUSTOM_FIELD_DISPLAY_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_DISPLAY_ZONES.map(
    (id) => `${PRODUCT_CUSTOM_FIELD_MODEL}.${id}.$display`
  )
];
var CUSTOM_FIELD_CONTAINER_ZONES = [
  ...PRODUCT_CUSTOM_FIELD_DISPLAY_ZONES
];
var CUSTOM_FIELD_FORM_ZONES = [
  ...PRODUCT_CUSTOM_FIELD_FORM_ZONES
];
var CUSTOM_FIELD_FORM_TABS = [
  ...PRODUCT_CUSTOM_FIELD_FORM_TABS
];
var CUSTOM_FIELD_FORM_CONFIG_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_FORM_CONFIG_PATHS
];
var CUSTOM_FIELD_FORM_FIELD_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_FORM_FIELD_PATHS
];
var CUSTOM_FIELD_DISPLAY_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_DISPLAY_PATHS
];
var CUSTOM_FIELD_LINK_PATHS = [
  ...PRODUCT_CUSTOM_FIELD_LINK_PATHS
];
var NESTED_ROUTE_POSITIONS = [
  "/orders",
  "/products",
  "/inventory",
  "/customers",
  "/promotions",
  "/price-lists"
];
var ORDER_INJECTION_ZONES = [
  "order.details.before",
  "order.details.after",
  "order.details.side.before",
  "order.details.side.after",
  "order.list.before",
  "order.list.after"
];
var CUSTOMER_INJECTION_ZONES = [
  "customer.details.before",
  "customer.details.after",
  "customer.details.side.before",
  "customer.details.side.after",
  "customer.list.before",
  "customer.list.after"
];
var CUSTOMER_GROUP_INJECTION_ZONES = [
  "customer_group.details.before",
  "customer_group.details.after",
  "customer_group.list.before",
  "customer_group.list.after"
];
var PRODUCT_INJECTION_ZONES = [
  "product.details.before",
  "product.details.after",
  "product.list.before",
  "product.list.after",
  "product.details.side.before",
  "product.details.side.after"
];
var PRODUCT_VARIANT_INJECTION_ZONES = [
  "product_variant.details.before",
  "product_variant.details.after",
  "product_variant.details.side.before",
  "product_variant.details.side.after"
];
var PRODUCT_COLLECTION_INJECTION_ZONES = [
  "product_collection.details.before",
  "product_collection.details.after",
  "product_collection.list.before",
  "product_collection.list.after"
];
var PRODUCT_CATEGORY_INJECTION_ZONES = [
  "product_category.details.before",
  "product_category.details.after",
  "product_category.details.side.before",
  "product_category.details.side.after",
  "product_category.list.before",
  "product_category.list.after"
];
var PRODUCT_TYPE_INJECTION_ZONES = [
  "product_type.details.before",
  "product_type.details.after",
  "product_type.list.before",
  "product_type.list.after"
];
var PRODUCT_TAG_INJECTION_ZONES = [
  "product_tag.details.before",
  "product_tag.details.after",
  "product_tag.list.before",
  "product_tag.list.after"
];
var PRICE_LIST_INJECTION_ZONES = [
  "price_list.details.before",
  "price_list.details.after",
  "price_list.details.side.before",
  "price_list.details.side.after",
  "price_list.list.before",
  "price_list.list.after"
];
var PROMOTION_INJECTION_ZONES = [
  "promotion.details.before",
  "promotion.details.after",
  "promotion.details.side.before",
  "promotion.details.side.after",
  "promotion.list.before",
  "promotion.list.after"
];
var CAMPAIGN_INJECTION_ZONES = [
  "campaign.details.before",
  "campaign.details.after",
  "campaign.details.side.before",
  "campaign.details.side.after",
  "campaign.list.before",
  "campaign.list.after"
];
var USER_INJECTION_ZONES = [
  "user.details.before",
  "user.details.after",
  "user.list.before",
  "user.list.after"
];
var STORE_INJECTION_ZONES = [
  "store.details.before",
  "store.details.after"
];
var PROFILE_INJECTION_ZONES = [
  "profile.details.before",
  "profile.details.after"
];
var REGION_INJECTION_ZONES = [
  "region.details.before",
  "region.details.after",
  "region.list.before",
  "region.list.after"
];
var SHIPPING_PROFILE_INJECTION_ZONES = [
  "shipping_profile.details.before",
  "shipping_profile.details.after",
  "shipping_profile.list.before",
  "shipping_profile.list.after"
];
var LOCATION_INJECTION_ZONES = [
  "location.details.before",
  "location.details.after",
  "location.details.side.before",
  "location.details.side.after",
  "location.list.before",
  "location.list.after",
  "location.list.side.before",
  "location.list.side.after"
];
var LOGIN_INJECTION_ZONES = ["login.before", "login.after"];
var SALES_CHANNEL_INJECTION_ZONES = [
  "sales_channel.details.before",
  "sales_channel.details.after",
  "sales_channel.list.before",
  "sales_channel.list.after"
];
var RESERVATION_INJECTION_ZONES = [
  "reservation.details.before",
  "reservation.details.after",
  "reservation.details.side.before",
  "reservation.details.side.after",
  "reservation.list.before",
  "reservation.list.after"
];
var API_KEY_INJECTION_ZONES = [
  "api_key.details.before",
  "api_key.details.after",
  "api_key.list.before",
  "api_key.list.after"
];
var WORKFLOW_INJECTION_ZONES = [
  "workflow.details.before",
  "workflow.details.after",
  "workflow.list.before",
  "workflow.list.after"
];
var TAX_INJECTION_ZONES = [
  "tax.details.before",
  "tax.details.after",
  "tax.list.before",
  "tax.list.after"
];
var RETURN_REASON_INJECTION_ZONES = [
  "return_reason.list.before",
  "return_reason.list.after"
];
var INVENTORY_ITEM_INJECTION_ZONES = [
  "inventory_item.details.before",
  "inventory_item.details.after",
  "inventory_item.details.side.before",
  "inventory_item.details.side.after",
  "inventory_item.list.before",
  "inventory_item.list.after"
];
var INJECTION_ZONES = [
  ...ORDER_INJECTION_ZONES,
  ...CUSTOMER_INJECTION_ZONES,
  ...CUSTOMER_GROUP_INJECTION_ZONES,
  ...PRODUCT_INJECTION_ZONES,
  ...PRODUCT_VARIANT_INJECTION_ZONES,
  ...PRODUCT_COLLECTION_INJECTION_ZONES,
  ...PRODUCT_CATEGORY_INJECTION_ZONES,
  ...PRODUCT_TYPE_INJECTION_ZONES,
  ...PRODUCT_TAG_INJECTION_ZONES,
  ...PRICE_LIST_INJECTION_ZONES,
  ...PROMOTION_INJECTION_ZONES,
  ...USER_INJECTION_ZONES,
  ...STORE_INJECTION_ZONES,
  ...PROFILE_INJECTION_ZONES,
  ...REGION_INJECTION_ZONES,
  ...SHIPPING_PROFILE_INJECTION_ZONES,
  ...LOCATION_INJECTION_ZONES,
  ...LOGIN_INJECTION_ZONES,
  ...SALES_CHANNEL_INJECTION_ZONES,
  ...RESERVATION_INJECTION_ZONES,
  ...API_KEY_INJECTION_ZONES,
  ...WORKFLOW_INJECTION_ZONES,
  ...CAMPAIGN_INJECTION_ZONES,
  ...TAX_INJECTION_ZONES,
  ...RETURN_REASON_INJECTION_ZONES,
  ...INVENTORY_ITEM_INJECTION_ZONES
];

// node_modules/react-helmet-async/lib/index.esm.js
var import_react = __toESM(require_react());
var import_react_fast_compare = __toESM(require_react_fast_compare());
var import_invariant = __toESM(require_browser());
var import_react2 = __toESM(require_react());
var import_react3 = __toESM(require_react());
var import_react4 = __toESM(require_react());
var import_shallowequal = __toESM(require_shallowequal());
var TAG_NAMES = ((TAG_NAMES2) => {
  TAG_NAMES2["BASE"] = "base";
  TAG_NAMES2["BODY"] = "body";
  TAG_NAMES2["HEAD"] = "head";
  TAG_NAMES2["HTML"] = "html";
  TAG_NAMES2["LINK"] = "link";
  TAG_NAMES2["META"] = "meta";
  TAG_NAMES2["NOSCRIPT"] = "noscript";
  TAG_NAMES2["SCRIPT"] = "script";
  TAG_NAMES2["STYLE"] = "style";
  TAG_NAMES2["TITLE"] = "title";
  TAG_NAMES2["FRAGMENT"] = "Symbol(react.fragment)";
  return TAG_NAMES2;
})(TAG_NAMES || {});
var SEO_PRIORITY_TAGS = {
  link: { rel: ["amphtml", "canonical", "alternate"] },
  script: { type: ["application/ld+json"] },
  meta: {
    charset: "",
    name: ["generator", "robots", "description"],
    property: [
      "og:type",
      "og:title",
      "og:url",
      "og:image",
      "og:image:alt",
      "og:description",
      "twitter:url",
      "twitter:title",
      "twitter:description",
      "twitter:image",
      "twitter:image:alt",
      "twitter:card",
      "twitter:site"
    ]
  }
};
var VALID_TAG_NAMES = Object.values(TAG_NAMES);
var REACT_TAG_MAP = {
  accesskey: "accessKey",
  charset: "charSet",
  class: "className",
  contenteditable: "contentEditable",
  contextmenu: "contextMenu",
  "http-equiv": "httpEquiv",
  itemprop: "itemProp",
  tabindex: "tabIndex"
};
var HTML_TAG_MAP = Object.entries(REACT_TAG_MAP).reduce(
  (carry, [key, value]) => {
    carry[value] = key;
    return carry;
  },
  {}
);
var HELMET_ATTRIBUTE = "data-rh";
var flattenArray = (possibleArray) => Array.isArray(possibleArray) ? possibleArray.join("") : possibleArray;
var checkIfPropsMatch = (props, toMatch) => {
  const keys = Object.keys(props);
  for (let i = 0; i < keys.length; i += 1) {
    if (toMatch[keys[i]] && toMatch[keys[i]].includes(props[keys[i]])) {
      return true;
    }
  }
  return false;
};
var prioritizer = (elementsList, propsToMatch) => {
  if (Array.isArray(elementsList)) {
    return elementsList.reduce(
      (acc, elementAttrs) => {
        if (checkIfPropsMatch(elementAttrs, propsToMatch)) {
          acc.priority.push(elementAttrs);
        } else {
          acc.default.push(elementAttrs);
        }
        return acc;
      },
      { priority: [], default: [] }
    );
  }
  return { default: elementsList, priority: [] };
};
var SELF_CLOSING_TAGS = [
  "noscript",
  "script",
  "style"
  /* STYLE */
];
var encodeSpecialCharacters = (str, encode = true) => {
  if (encode === false) {
    return String(str);
  }
  return String(str).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;");
};
var generateElementAttributesAsString = (attributes) => Object.keys(attributes).reduce((str, key) => {
  const attr = typeof attributes[key] !== "undefined" ? `${key}="${attributes[key]}"` : `${key}`;
  return str ? `${str} ${attr}` : attr;
}, "");
var generateTitleAsString = (type, title, attributes, encode) => {
  const attributeString = generateElementAttributesAsString(attributes);
  const flattenedTitle = flattenArray(title);
  return attributeString ? `<${type} ${HELMET_ATTRIBUTE}="true" ${attributeString}>${encodeSpecialCharacters(
    flattenedTitle,
    encode
  )}</${type}>` : `<${type} ${HELMET_ATTRIBUTE}="true">${encodeSpecialCharacters(
    flattenedTitle,
    encode
  )}</${type}>`;
};
var generateTagsAsString = (type, tags, encode = true) => tags.reduce((str, t3) => {
  const tag = t3;
  const attributeHtml = Object.keys(tag).filter(
    (attribute) => !(attribute === "innerHTML" || attribute === "cssText")
  ).reduce((string, attribute) => {
    const attr = typeof tag[attribute] === "undefined" ? attribute : `${attribute}="${encodeSpecialCharacters(tag[attribute], encode)}"`;
    return string ? `${string} ${attr}` : attr;
  }, "");
  const tagContent = tag.innerHTML || tag.cssText || "";
  const isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;
  return `${str}<${type} ${HELMET_ATTRIBUTE}="true" ${attributeHtml}${isSelfClosing ? `/>` : `>${tagContent}</${type}>`}`;
}, "");
var convertElementAttributesToReactProps = (attributes, initProps = {}) => Object.keys(attributes).reduce((obj, key) => {
  const mapped = REACT_TAG_MAP[key];
  obj[mapped || key] = attributes[key];
  return obj;
}, initProps);
var generateTitleAsReactComponent = (_type, title, attributes) => {
  const initProps = {
    key: title,
    [HELMET_ATTRIBUTE]: true
  };
  const props = convertElementAttributesToReactProps(attributes, initProps);
  return [import_react3.default.createElement("title", props, title)];
};
var generateTagsAsReactComponent = (type, tags) => tags.map((tag, i) => {
  const mappedTag = {
    key: i,
    [HELMET_ATTRIBUTE]: true
  };
  Object.keys(tag).forEach((attribute) => {
    const mapped = REACT_TAG_MAP[attribute];
    const mappedAttribute = mapped || attribute;
    if (mappedAttribute === "innerHTML" || mappedAttribute === "cssText") {
      const content = tag.innerHTML || tag.cssText;
      mappedTag.dangerouslySetInnerHTML = { __html: content };
    } else {
      mappedTag[mappedAttribute] = tag[attribute];
    }
  });
  return import_react3.default.createElement(type, mappedTag);
});
var getMethodsForTag = (type, tags, encode = true) => {
  switch (type) {
    case "title":
      return {
        toComponent: () => generateTitleAsReactComponent(type, tags.title, tags.titleAttributes),
        toString: () => generateTitleAsString(type, tags.title, tags.titleAttributes, encode)
      };
    case "bodyAttributes":
    case "htmlAttributes":
      return {
        toComponent: () => convertElementAttributesToReactProps(tags),
        toString: () => generateElementAttributesAsString(tags)
      };
    default:
      return {
        toComponent: () => generateTagsAsReactComponent(type, tags),
        toString: () => generateTagsAsString(type, tags, encode)
      };
  }
};
var getPriorityMethods = ({ metaTags, linkTags, scriptTags, encode }) => {
  const meta = prioritizer(metaTags, SEO_PRIORITY_TAGS.meta);
  const link = prioritizer(linkTags, SEO_PRIORITY_TAGS.link);
  const script = prioritizer(scriptTags, SEO_PRIORITY_TAGS.script);
  const priorityMethods = {
    toComponent: () => [
      ...generateTagsAsReactComponent("meta", meta.priority),
      ...generateTagsAsReactComponent("link", link.priority),
      ...generateTagsAsReactComponent("script", script.priority)
    ],
    toString: () => (
      // generate all the tags as strings and concatenate them
      `${getMethodsForTag("meta", meta.priority, encode)} ${getMethodsForTag(
        "link",
        link.priority,
        encode
      )} ${getMethodsForTag("script", script.priority, encode)}`
    )
  };
  return {
    priorityMethods,
    metaTags: meta.default,
    linkTags: link.default,
    scriptTags: script.default
  };
};
var mapStateOnServer = (props) => {
  const {
    baseTag,
    bodyAttributes,
    encode = true,
    htmlAttributes,
    noscriptTags,
    styleTags,
    title = "",
    titleAttributes,
    prioritizeSeoTags
  } = props;
  let { linkTags, metaTags, scriptTags } = props;
  let priorityMethods = {
    toComponent: () => {
    },
    toString: () => ""
  };
  if (prioritizeSeoTags) {
    ({ priorityMethods, linkTags, metaTags, scriptTags } = getPriorityMethods(props));
  }
  return {
    priority: priorityMethods,
    base: getMethodsForTag("base", baseTag, encode),
    bodyAttributes: getMethodsForTag("bodyAttributes", bodyAttributes, encode),
    htmlAttributes: getMethodsForTag("htmlAttributes", htmlAttributes, encode),
    link: getMethodsForTag("link", linkTags, encode),
    meta: getMethodsForTag("meta", metaTags, encode),
    noscript: getMethodsForTag("noscript", noscriptTags, encode),
    script: getMethodsForTag("script", scriptTags, encode),
    style: getMethodsForTag("style", styleTags, encode),
    title: getMethodsForTag("title", { title, titleAttributes }, encode)
  };
};
var server_default = mapStateOnServer;
var instances = [];
var isDocument = !!(typeof window !== "undefined" && window.document && window.document.createElement);
var HelmetData = class {
  constructor(context, canUseDOM) {
    __publicField(this, "instances", []);
    __publicField(this, "canUseDOM", isDocument);
    __publicField(this, "context");
    __publicField(this, "value", {
      setHelmet: (serverState) => {
        this.context.helmet = serverState;
      },
      helmetInstances: {
        get: () => this.canUseDOM ? instances : this.instances,
        add: (instance) => {
          (this.canUseDOM ? instances : this.instances).push(instance);
        },
        remove: (instance) => {
          const index = (this.canUseDOM ? instances : this.instances).indexOf(instance);
          (this.canUseDOM ? instances : this.instances).splice(index, 1);
        }
      }
    });
    this.context = context;
    this.canUseDOM = canUseDOM || false;
    if (!canUseDOM) {
      context.helmet = server_default({
        baseTag: [],
        bodyAttributes: {},
        encodeSpecialCharacters: true,
        htmlAttributes: {},
        linkTags: [],
        metaTags: [],
        noscriptTags: [],
        scriptTags: [],
        styleTags: [],
        title: "",
        titleAttributes: {}
      });
    }
  }
};
var defaultValue = {};
var Context = import_react2.default.createContext(defaultValue);
var _a;
var HelmetProvider = (_a = class extends import_react2.Component {
  constructor(props) {
    super(props);
    __publicField(this, "helmetData");
    this.helmetData = new HelmetData(this.props.context || {}, _a.canUseDOM);
  }
  render() {
    return import_react2.default.createElement(Context.Provider, { value: this.helmetData.value }, this.props.children);
  }
}, __publicField(_a, "canUseDOM", isDocument), _a);

// node_modules/@medusajs/dashboard/dist/chunk-L4KSRFES.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react8 = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var import_react10 = __toESM(require_react(), 1);
var import_debounce = __toESM(require_debounce(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_react12 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react13 = __toESM(require_react(), 1);
var import_react14 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react15 = __toESM(require_react(), 1);
var import_react16 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react17 = __toESM(require_react(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react18 = __toESM(require_react(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_react20 = __toESM(require_react(), 1);
var import_react21 = __toESM(require_react(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_react22 = __toESM(require_react(), 1);
var import_react23 = __toESM(require_react(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_react24 = __toESM(require_react(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var import_react25 = __toESM(require_react(), 1);
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
import linkModule from "virtual:medusa/links";
var formatLocaleCode = (code) => {
  return code.replace(/([a-z])([A-Z])/g, "$1-$2");
};
var I18nProvider2 = ({ children }) => {
  var _a2;
  const { i18n } = useTranslation();
  const locale = ((_a2 = languages.find((lan) => lan.code === i18n.language)) == null ? void 0 : _a2.code) || languages[0].code;
  return (0, import_jsx_runtime.jsx)(I18nProvider, { locale: formatLocaleCode(locale), children });
};
var ThemeContext = (0, import_react6.createContext)(null);
var THEME_KEY = "medusa_admin_theme";
function getDefaultValue() {
  const persisted = localStorage == null ? void 0 : localStorage.getItem(THEME_KEY);
  if (persisted) {
    return persisted;
  }
  return "system";
}
function getThemeValue(selected) {
  if (selected === "system") {
    if (window !== void 0) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    return "light";
  }
  return selected;
}
var ThemeProvider = ({ children }) => {
  const [state, setState] = (0, import_react5.useState)(getDefaultValue());
  const [value, setValue] = (0, import_react5.useState)(getThemeValue(state));
  const setTheme = (theme) => {
    localStorage.setItem(THEME_KEY, theme);
    const themeValue = getThemeValue(theme);
    setState(theme);
    setValue(themeValue);
  };
  (0, import_react5.useEffect)(() => {
    const html = document.querySelector("html");
    if (html) {
      const css = document.createElement("style");
      css.appendChild(
        document.createTextNode(
          `* {
            -webkit-transition: none !important;
            -moz-transition: none !important;
            -o-transition: none !important;
            -ms-transition: none !important;
            transition: none !important;
          }`
        )
      );
      document.head.appendChild(css);
      html.classList.remove(value === "light" ? "dark" : "light");
      html.classList.add(value);
      html.style.colorScheme = value;
      window.getComputedStyle(css).opacity;
      document.head.removeChild(css);
    }
  }, [value]);
  return (0, import_jsx_runtime2.jsx)(ThemeContext.Provider, { value: { theme: state, setTheme }, children });
};
var useTheme = () => {
  const context = (0, import_react7.useContext)(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
var Providers = ({ api, children }) => {
  return (0, import_jsx_runtime3.jsx)(TooltipProvider, { children: (0, import_jsx_runtime3.jsx)(ExtensionProvider, { api, children: (0, import_jsx_runtime3.jsx)(HelmetProvider, { children: (0, import_jsx_runtime3.jsx)(QueryClientProvider, { client: queryClient, children: (0, import_jsx_runtime3.jsxs)(ThemeProvider, { children: [
    (0, import_jsx_runtime3.jsx)(I18n, {}),
    (0, import_jsx_runtime3.jsx)(I18nProvider2, { children }),
    (0, import_jsx_runtime3.jsx)(Toaster, {})
  ] }) }) }) }) });
};
var SEARCH_AREAS = [
  "all",
  "order",
  "product",
  "productVariant",
  "collection",
  "category",
  "inventory",
  "customer",
  "customerGroup",
  "promotion",
  "campaign",
  "priceList",
  "user",
  "region",
  "taxRegion",
  "returnReason",
  "salesChannel",
  "productType",
  "productTag",
  "location",
  "shippingProfile",
  "publishableApiKey",
  "secretApiKey",
  "command",
  "navigation"
];
var DEFAULT_SEARCH_LIMIT = 3;
var SEARCH_LIMIT_INCREMENT = 20;
var KeybindContext = (0, import_react12.createContext)(null);
var findFirstPlatformMatch = (keys) => {
  const match = Object.entries(keys).filter(
    ([, value]) => value.length > 0
  )[0] ?? [];
  return match.length ? {
    platform: match[0],
    keys: match[1]
  } : null;
};
var getShortcutKeys = (shortcut) => {
  const platform = "Mac";
  const keys = shortcut.keys[platform];
  if (!keys) {
    const defaultPlatform = findFirstPlatformMatch(shortcut.keys);
    console.warn(
      `No keys found for platform "${platform}" in "${shortcut.label}" ${defaultPlatform ? `using keys for platform "${defaultPlatform.platform}"` : ""}`
    );
    return defaultPlatform ? defaultPlatform.keys : [];
  }
  return keys;
};
var keysMatch = (keys1, keys2) => {
  return keys1.length === keys2.length && keys1.every(
    (key, index) => key.toLowerCase() === keys2[index].toLowerCase()
  );
};
var findShortcutIndex = (shortcuts, keys) => {
  if (!keys.length) {
    return -1;
  }
  let index = 0;
  for (const shortcut of shortcuts) {
    const shortcutKeys = getShortcutKeys(shortcut);
    if (keysMatch(shortcutKeys, keys)) {
      return index;
    }
    index++;
  }
  return -1;
};
var findShortcut = (shortcuts, keys) => {
  const shortcutIndex = findShortcutIndex(shortcuts, keys);
  return shortcutIndex > -1 ? shortcuts[shortcutIndex] : null;
};
var getShortcutWithDefaultValues = (shortcut, platform = "Mac") => {
  const platforms = ["Mac", "Windows", "Linux"];
  const defaultKeys = Object.values(shortcut.keys)[0] ?? shortcut.keys[platform];
  const keys = platforms.reduce((acc, curr) => {
    return {
      ...acc,
      [curr]: shortcut.keys[curr] ?? defaultKeys
    };
  }, {});
  return {
    ...shortcut,
    keys,
    _defaultKeys: shortcut.keys
  };
};
var useShortcuts = ({
  shortcuts = [],
  debounce
}) => {
  const [keys, setKeys] = (0, import_react11.useState)([]);
  const navigate = useNavigate();
  const removeKeys = (0, import_react11.useCallback)(
    (0, import_debounce.default)(() => setKeys([]), debounce),
    []
  );
  const invokeShortcut = (0, import_react11.useCallback)(
    (0, import_debounce.default)((shortcut) => {
      if (shortcut && shortcut.callback) {
        shortcut.callback();
        setKeys([]);
        return;
      }
      if (shortcut && shortcut.to) {
        navigate(shortcut.to);
        setKeys([]);
        return;
      }
    }, debounce / 2),
    []
  );
  (0, import_react11.useEffect)(() => {
    if (keys.length > 0 && shortcuts.length > 0) {
      const shortcut = findShortcut(shortcuts, keys);
      invokeShortcut(shortcut);
    }
    return () => invokeShortcut.cancel();
  }, [keys, shortcuts, invokeShortcut]);
  (0, import_react11.useEffect)(() => {
    const listener = (event) => {
      const target = event.target;
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.contentEditable === "true") {
        removeKeys();
        return;
      }
      setKeys((oldKeys) => [...oldKeys, event.key]);
      removeKeys();
    };
    window.addEventListener("keydown", listener);
    return () => {
      window.removeEventListener("keydown", listener);
    };
  }, [removeKeys]);
};
var useGlobalShortcuts = () => {
  const { t: t22 } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync } = useLogout();
  const handleLogout = async () => {
    await mutateAsync(void 0, {
      onSuccess: () => {
        queryClient.clear();
        navigate("/login");
      }
    });
  };
  const globalShortcuts = [
    // Pages
    {
      keys: {
        Mac: ["G", "O"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToOrders"),
      type: "pageShortcut",
      to: "/orders"
    },
    {
      keys: {
        Mac: ["G", "P"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToProducts"),
      type: "pageShortcut",
      to: "/products"
    },
    {
      keys: {
        Mac: ["G", "C"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToCollections"),
      type: "pageShortcut",
      to: "/collections"
    },
    {
      keys: {
        Mac: ["G", "A"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToCategories"),
      type: "pageShortcut",
      to: "/categories"
    },
    {
      keys: {
        Mac: ["G", "U"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToCustomers"),
      type: "pageShortcut",
      to: "/customers"
    },
    {
      keys: {
        Mac: ["G", "G"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToCustomerGroups"),
      type: "pageShortcut",
      to: "/customer-groups"
    },
    {
      keys: {
        Mac: ["G", "I"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToInventory"),
      type: "pageShortcut",
      to: "/inventory"
    },
    {
      keys: {
        Mac: ["G", "R"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToReservations"),
      type: "pageShortcut",
      to: "/reservations"
    },
    {
      keys: {
        Mac: ["G", "L"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToPriceLists"),
      type: "pageShortcut",
      to: "/price-lists"
    },
    {
      keys: {
        Mac: ["G", "M"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToPromotions"),
      type: "pageShortcut",
      to: "/promotions"
    },
    {
      keys: {
        Mac: ["G", "K"]
      },
      label: t22("app.keyboardShortcuts.navigation.goToCampaigns"),
      type: "pageShortcut",
      to: "/campaigns"
    },
    // Settings
    {
      keys: {
        Mac: ["G", ","]
      },
      label: t22("app.keyboardShortcuts.settings.goToSettings"),
      type: "settingShortcut",
      to: "/settings"
    },
    {
      keys: {
        Mac: ["G", ",", "S"]
      },
      label: t22("app.keyboardShortcuts.settings.goToStore"),
      type: "settingShortcut",
      to: "/settings/store"
    },
    {
      keys: {
        Mac: ["G", ",", "U"]
      },
      label: t22("app.keyboardShortcuts.settings.goToUsers"),
      type: "settingShortcut",
      to: "/settings/users"
    },
    {
      keys: {
        Mac: ["G", ",", "R"]
      },
      label: t22("app.keyboardShortcuts.settings.goToRegions"),
      type: "settingShortcut",
      to: "/settings/regions"
    },
    {
      keys: {
        Mac: ["G", ",", "T"]
      },
      label: t22("app.keyboardShortcuts.settings.goToTaxRegions"),
      type: "settingShortcut",
      to: "/settings/tax-regions"
    },
    {
      keys: {
        Mac: ["G", ",", "A"]
      },
      label: t22("app.keyboardShortcuts.settings.goToSalesChannels"),
      type: "settingShortcut",
      to: "/settings/sales-channels"
    },
    {
      keys: {
        Mac: ["G", ",", "P"]
      },
      label: t22("app.keyboardShortcuts.settings.goToProductTypes"),
      type: "settingShortcut",
      to: "/settings/product-types"
    },
    {
      keys: {
        Mac: ["G", ",", "L"]
      },
      label: t22("app.keyboardShortcuts.settings.goToLocations"),
      type: "settingShortcut",
      to: "/settings/locations"
    },
    {
      keys: {
        Mac: ["G", ",", "M"]
      },
      label: t22("app.keyboardShortcuts.settings.goToReturnReasons"),
      type: "settingShortcut",
      to: "/settings/return-reasons"
    },
    {
      keys: {
        Mac: ["G", ",", "J"]
      },
      label: t22("app.keyboardShortcuts.settings.goToPublishableApiKeys"),
      type: "settingShortcut",
      to: "/settings/publishable-api-keys"
    },
    {
      keys: {
        Mac: ["G", ",", "K"]
      },
      label: t22("app.keyboardShortcuts.settings.goToSecretApiKeys"),
      type: "settingShortcut",
      to: "/settings/secret-api-keys"
    },
    {
      keys: {
        Mac: ["G", ",", "W"]
      },
      label: t22("app.keyboardShortcuts.settings.goToWorkflows"),
      type: "settingShortcut",
      to: "/settings/workflows"
    },
    {
      keys: {
        Mac: ["G", ",", "M"]
      },
      label: t22("app.keyboardShortcuts.settings.goToProfile"),
      type: "settingShortcut",
      to: "/settings/profile"
    },
    // Commands
    {
      keys: {
        Mac: ["B", "Y", "E"]
      },
      label: t22("actions.logout"),
      type: "commandShortcut",
      callback: () => handleLogout()
    }
  ];
  return globalShortcuts;
};
var useSearchResults = ({
  q,
  limit,
  area = "all"
}) => {
  const staticResults = useStaticSearchResults(area);
  const { dynamicResults, isFetching } = useDynamicSearchResults(area, limit, q);
  return {
    staticResults,
    dynamicResults,
    isFetching
  };
};
var useStaticSearchResults = (currentArea) => {
  const globalCommands = useGlobalShortcuts();
  const results = (0, import_react10.useMemo)(() => {
    const groups = /* @__PURE__ */ new Map();
    globalCommands.forEach((command) => {
      const group = groups.get(command.type) || [];
      group.push(command);
      groups.set(command.type, group);
    });
    let filteredGroups;
    switch (currentArea) {
      case "all":
        filteredGroups = Array.from(groups);
        break;
      case "navigation":
        filteredGroups = Array.from(groups).filter(
          ([type]) => type === "pageShortcut" || type === "settingShortcut"
        );
        break;
      case "command":
        filteredGroups = Array.from(groups).filter(
          ([type]) => type === "commandShortcut"
        );
        break;
      default:
        filteredGroups = [];
    }
    return filteredGroups.map(([title, items]) => ({
      title,
      items
    }));
  }, [globalCommands, currentArea]);
  return results;
};
var useDynamicSearchResults = (currentArea, limit, q) => {
  const { t: t22 } = useTranslation();
  const debouncedSearch = useDebouncedSearch(q, 300);
  const orderResponse = useOrders(
    {
      q: debouncedSearch == null ? void 0 : debouncedSearch.replace(/^#/, ""),
      // Since we display the ID with a # prefix, it's natural for the user to include it in the search. This will however cause no results to be returned, so we remove the # prefix from the search query.
      limit,
      fields: "id,display_id,email"
    },
    {
      enabled: isAreaEnabled(currentArea, "order"),
      placeholderData: keepPreviousData
    }
  );
  const productResponse = useProducts(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title,thumbnail"
    },
    {
      enabled: isAreaEnabled(currentArea, "product"),
      placeholderData: keepPreviousData
    }
  );
  const productVariantResponse = useVariants(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title,sku"
    },
    {
      enabled: isAreaEnabled(currentArea, "productVariant"),
      placeholderData: keepPreviousData
    }
  );
  const categoryResponse = useProductCategories(
    {
      // TODO: Remove the OR condition once the list endpoint does not throw when q equals an empty string
      q: debouncedSearch || void 0,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "category"),
      placeholderData: keepPreviousData
    }
  );
  const collectionResponse = useCollections(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title"
    },
    {
      enabled: isAreaEnabled(currentArea, "collection"),
      placeholderData: keepPreviousData
    }
  );
  const customerResponse = useCustomers(
    {
      q: debouncedSearch,
      limit,
      fields: "id,email,first_name,last_name"
    },
    {
      enabled: isAreaEnabled(currentArea, "customer"),
      placeholderData: keepPreviousData
    }
  );
  const customerGroupResponse = useCustomerGroups(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "customerGroup"),
      placeholderData: keepPreviousData
    }
  );
  const inventoryResponse = useInventoryItems(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title,sku"
    },
    {
      enabled: isAreaEnabled(currentArea, "inventory"),
      placeholderData: keepPreviousData
    }
  );
  const promotionResponse = usePromotions(
    {
      q: debouncedSearch,
      limit,
      fields: "id,code,status"
    },
    {
      enabled: isAreaEnabled(currentArea, "promotion"),
      placeholderData: keepPreviousData
    }
  );
  const campaignResponse = useCampaigns(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "campaign"),
      placeholderData: keepPreviousData
    }
  );
  const priceListResponse = usePriceLists(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title"
    },
    {
      enabled: isAreaEnabled(currentArea, "priceList"),
      placeholderData: keepPreviousData
    }
  );
  const userResponse = useUsers(
    {
      q: debouncedSearch,
      limit,
      fields: "id,email,first_name,last_name"
    },
    {
      enabled: isAreaEnabled(currentArea, "user"),
      placeholderData: keepPreviousData
    }
  );
  const regionResponse = useRegions(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "region"),
      placeholderData: keepPreviousData
    }
  );
  const taxRegionResponse = useTaxRegions(
    {
      q: debouncedSearch,
      limit,
      fields: "id,country_code,province_code"
    },
    {
      enabled: isAreaEnabled(currentArea, "taxRegion"),
      placeholderData: keepPreviousData
    }
  );
  const returnReasonResponse = useReturnReasons(
    {
      q: debouncedSearch,
      limit,
      fields: "id,label,value"
    },
    {
      enabled: isAreaEnabled(currentArea, "returnReason"),
      placeholderData: keepPreviousData
    }
  );
  const salesChannelResponse = useSalesChannels(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "salesChannel"),
      placeholderData: keepPreviousData
    }
  );
  const productTypeResponse = useProductTypes(
    {
      q: debouncedSearch,
      limit,
      fields: "id,value"
    },
    {
      enabled: isAreaEnabled(currentArea, "productType"),
      placeholderData: keepPreviousData
    }
  );
  const productTagResponse = useProductTags(
    {
      q: debouncedSearch,
      limit,
      fields: "id,value"
    },
    {
      enabled: isAreaEnabled(currentArea, "productTag"),
      placeholderData: keepPreviousData
    }
  );
  const locationResponse = useStockLocations(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "location"),
      placeholderData: keepPreviousData
    }
  );
  const shippingProfileResponse = useShippingProfiles(
    {
      q: debouncedSearch,
      limit,
      fields: "id,name"
    },
    {
      enabled: isAreaEnabled(currentArea, "shippingProfile"),
      placeholderData: keepPreviousData
    }
  );
  const publishableApiKeyResponse = useApiKeys(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title,redacted",
      type: "publishable"
    },
    {
      enabled: isAreaEnabled(currentArea, "publishableApiKey"),
      placeholderData: keepPreviousData
    }
  );
  const secretApiKeyResponse = useApiKeys(
    {
      q: debouncedSearch,
      limit,
      fields: "id,title,redacted",
      type: "secret"
    },
    {
      enabled: isAreaEnabled(currentArea, "secretApiKey"),
      placeholderData: keepPreviousData
    }
  );
  const responseMap = (0, import_react10.useMemo)(
    () => ({
      order: orderResponse,
      product: productResponse,
      productVariant: productVariantResponse,
      collection: collectionResponse,
      category: categoryResponse,
      inventory: inventoryResponse,
      customer: customerResponse,
      customerGroup: customerGroupResponse,
      promotion: promotionResponse,
      campaign: campaignResponse,
      priceList: priceListResponse,
      user: userResponse,
      region: regionResponse,
      taxRegion: taxRegionResponse,
      returnReason: returnReasonResponse,
      salesChannel: salesChannelResponse,
      productType: productTypeResponse,
      productTag: productTagResponse,
      location: locationResponse,
      shippingProfile: shippingProfileResponse,
      publishableApiKey: publishableApiKeyResponse,
      secretApiKey: secretApiKeyResponse
    }),
    [
      orderResponse,
      productResponse,
      productVariantResponse,
      inventoryResponse,
      categoryResponse,
      collectionResponse,
      customerResponse,
      customerGroupResponse,
      promotionResponse,
      campaignResponse,
      priceListResponse,
      userResponse,
      regionResponse,
      taxRegionResponse,
      returnReasonResponse,
      salesChannelResponse,
      productTypeResponse,
      productTagResponse,
      locationResponse,
      shippingProfileResponse,
      publishableApiKeyResponse,
      secretApiKeyResponse
    ]
  );
  const results = (0, import_react10.useMemo)(() => {
    const groups = Object.entries(responseMap).map(([key, response]) => {
      const area = key;
      if (isAreaEnabled(currentArea, area) || currentArea === "all") {
        return transformDynamicSearchResults(area, limit, t22, response);
      }
      return null;
    }).filter(Boolean);
    return groups;
  }, [responseMap, currentArea, limit, t22]);
  const isAreaFetching = (0, import_react10.useCallback)(
    (area) => {
      var _a2;
      if (area === "all") {
        return Object.values(responseMap).some(
          (response) => response.isFetching
        );
      }
      return isAreaEnabled(currentArea, area) && ((_a2 = responseMap[area]) == null ? void 0 : _a2.isFetching);
    },
    [currentArea, responseMap]
  );
  const isFetching = (0, import_react10.useMemo)(() => {
    return isAreaFetching(currentArea);
  }, [currentArea, isAreaFetching]);
  const dynamicResults = q ? results.filter(
    (group) => !!group && group.items.length > 0
  ) : [];
  return {
    dynamicResults,
    isFetching
  };
};
var useDebouncedSearch = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = (0, import_react10.useState)(value);
  (0, import_react10.useEffect)(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};
function isAreaEnabled(area, currentArea) {
  if (area === "all") {
    return true;
  }
  if (area === currentArea) {
    return true;
  }
  return false;
}
var transformMap = {
  order: {
    dataKey: "orders",
    transform: (order) => ({
      id: order.id,
      title: `#${order.display_id}`,
      subtitle: order.email ?? void 0,
      to: `/orders/${order.id}`,
      value: `order:${order.id}`
    })
  },
  product: {
    dataKey: "products",
    transform: (product) => ({
      id: product.id,
      title: product.title,
      to: `/products/${product.id}`,
      thumbnail: product.thumbnail ?? void 0,
      value: `product:${product.id}`
    })
  },
  productVariant: {
    dataKey: "variants",
    transform: (variant) => ({
      id: variant.id,
      title: variant.title,
      subtitle: variant.sku ?? void 0,
      to: `/products/${variant.product_id}/variants/${variant.id}`,
      value: `variant:${variant.id}`
    })
  },
  category: {
    dataKey: "product_categories",
    transform: (category) => ({
      id: category.id,
      title: category.name,
      to: `/categories/${category.id}`,
      value: `category:${category.id}`
    })
  },
  inventory: {
    dataKey: "inventory_items",
    transform: (inventory) => ({
      id: inventory.id,
      title: inventory.title ?? "",
      subtitle: inventory.sku ?? void 0,
      to: `/inventory/${inventory.id}`,
      value: `inventory:${inventory.id}`
    })
  },
  customer: {
    dataKey: "customers",
    transform: (customer) => {
      const name = [customer.first_name, customer.last_name].filter(Boolean).join(" ");
      return {
        id: customer.id,
        title: name || customer.email,
        subtitle: name ? customer.email : void 0,
        to: `/customers/${customer.id}`,
        value: `customer:${customer.id}`
      };
    }
  },
  customerGroup: {
    dataKey: "customer_groups",
    transform: (customerGroup) => ({
      id: customerGroup.id,
      title: customerGroup.name,
      to: `/customer-groups/${customerGroup.id}`,
      value: `customerGroup:${customerGroup.id}`
    })
  },
  collection: {
    dataKey: "collections",
    transform: (collection) => ({
      id: collection.id,
      title: collection.title,
      to: `/collections/${collection.id}`,
      value: `collection:${collection.id}`
    })
  },
  promotion: {
    dataKey: "promotions",
    transform: (promotion) => ({
      id: promotion.id,
      title: promotion.code,
      to: `/promotions/${promotion.id}`,
      value: `promotion:${promotion.id}`
    })
  },
  campaign: {
    dataKey: "campaigns",
    transform: (campaign) => ({
      id: campaign.id,
      title: campaign.name,
      to: `/campaigns/${campaign.id}`,
      value: `campaign:${campaign.id}`
    })
  },
  priceList: {
    dataKey: "price_lists",
    transform: (priceList) => ({
      id: priceList.id,
      title: priceList.title,
      to: `/price-lists/${priceList.id}`,
      value: `priceList:${priceList.id}`
    })
  },
  user: {
    dataKey: "users",
    transform: (user) => ({
      id: user.id,
      title: `${user.first_name} ${user.last_name}`,
      subtitle: user.email,
      to: `/users/${user.id}`,
      value: `user:${user.id}`
    })
  },
  region: {
    dataKey: "regions",
    transform: (region) => ({
      id: region.id,
      title: region.name,
      to: `/regions/${region.id}`,
      value: `region:${region.id}`
    })
  },
  taxRegion: {
    dataKey: "tax_regions",
    transform: (taxRegion) => {
      var _a2;
      return {
        id: taxRegion.id,
        title: ((_a2 = taxRegion.province_code) == null ? void 0 : _a2.toUpperCase()) ?? taxRegion.country_code.toUpperCase(),
        subtitle: taxRegion.province_code ? taxRegion.country_code : void 0,
        to: `/tax-regions/${taxRegion.id}`,
        value: `taxRegion:${taxRegion.id}`
      };
    }
  },
  returnReason: {
    dataKey: "return_reasons",
    transform: (returnReason) => ({
      id: returnReason.id,
      title: returnReason.label,
      subtitle: returnReason.value,
      to: `/return-reasons/${returnReason.id}/edit`,
      value: `returnReason:${returnReason.id}`
    })
  },
  salesChannel: {
    dataKey: "sales_channels",
    transform: (salesChannel) => ({
      id: salesChannel.id,
      title: salesChannel.name,
      to: `/sales-channels/${salesChannel.id}`,
      value: `salesChannel:${salesChannel.id}`
    })
  },
  productType: {
    dataKey: "product_types",
    transform: (productType) => ({
      id: productType.id,
      title: productType.value,
      to: `/product-types/${productType.id}`,
      value: `productType:${productType.id}`
    })
  },
  productTag: {
    dataKey: "product_tags",
    transform: (productTag) => ({
      id: productTag.id,
      title: productTag.value,
      to: `/product-tags/${productTag.id}`,
      value: `productTag:${productTag.id}`
    })
  },
  location: {
    dataKey: "stock_locations",
    transform: (location) => ({
      id: location.id,
      title: location.name,
      to: `/locations/${location.id}`,
      value: `location:${location.id}`
    })
  },
  shippingProfile: {
    dataKey: "shipping_profiles",
    transform: (shippingProfile) => ({
      id: shippingProfile.id,
      title: shippingProfile.name,
      to: `/shipping-profiles/${shippingProfile.id}`,
      value: `shippingProfile:${shippingProfile.id}`
    })
  },
  publishableApiKey: {
    dataKey: "api_keys",
    transform: (apiKey) => ({
      id: apiKey.id,
      title: apiKey.title,
      subtitle: apiKey.redacted,
      to: `/publishable-api-keys/${apiKey.id}`,
      value: `publishableApiKey:${apiKey.id}`
    })
  },
  secretApiKey: {
    dataKey: "api_keys",
    transform: (apiKey) => ({
      id: apiKey.id,
      title: apiKey.title,
      subtitle: apiKey.redacted,
      to: `/secret-api-keys/${apiKey.id}`,
      value: `secretApiKey:${apiKey.id}`
    })
  }
};
function transformDynamicSearchResults(type, limit, t22, response) {
  if (!response || !transformMap[type]) {
    return void 0;
  }
  const { dataKey, transform } = transformMap[type];
  const data = response[dataKey];
  if (!data || !Array.isArray(data)) {
    return void 0;
  }
  return {
    title: t22(`app.search.groups.${type}`),
    area: type,
    hasMore: response.count > limit,
    count: response.count,
    items: data.map(transform)
  };
}
var Search = () => {
  const [area, setArea] = (0, import_react9.useState)("all");
  const [search, setSearch] = (0, import_react9.useState)("");
  const [limit, setLimit] = (0, import_react9.useState)(DEFAULT_SEARCH_LIMIT);
  const { open, onOpenChange } = useSearch();
  const location = useLocation();
  const { t: t22 } = useTranslation();
  const navigate = useNavigate();
  const inputRef = (0, import_react9.useRef)(null);
  const listRef = (0, import_react9.useRef)(null);
  const { staticResults, dynamicResults, isFetching } = useSearchResults({
    area,
    limit,
    q: search
  });
  const handleReset = (0, import_react9.useCallback)(() => {
    setArea("all");
    setSearch("");
    setLimit(DEFAULT_SEARCH_LIMIT);
  }, [setLimit]);
  const handleBack = () => {
    var _a2;
    handleReset();
    (_a2 = inputRef.current) == null ? void 0 : _a2.focus();
  };
  const handleOpenChange = (0, import_react9.useCallback)(
    (open2) => {
      if (!open2) {
        handleReset();
      }
      onOpenChange(open2);
    },
    [onOpenChange, handleReset]
  );
  (0, import_react9.useEffect)(() => {
    handleOpenChange(false);
  }, [location.pathname, handleOpenChange]);
  const handleSelect = (item) => {
    handleOpenChange(false);
    if (item.to) {
      navigate(item.to);
      return;
    }
    if (item.callback) {
      item.callback();
      return;
    }
  };
  const handleShowMore = (area2) => {
    var _a2;
    if (area2 === "all") {
      setLimit(DEFAULT_SEARCH_LIMIT);
    } else {
      setLimit(SEARCH_LIMIT_INCREMENT);
    }
    setArea(area2);
    (_a2 = inputRef.current) == null ? void 0 : _a2.focus();
  };
  const handleLoadMore = () => {
    setLimit((l) => l + SEARCH_LIMIT_INCREMENT);
  };
  const filteredStaticResults = (0, import_react9.useMemo)(() => {
    const filteredResults = [];
    staticResults.forEach((group) => {
      const filteredItems = matchSorter(group.items, search, {
        keys: ["label"]
      });
      if (filteredItems.length === 0) {
        return;
      }
      filteredResults.push({
        ...group,
        items: filteredItems
      });
    });
    return filteredResults;
  }, [staticResults, search]);
  const handleSearch = (q) => {
    var _a2;
    setSearch(q);
    (_a2 = listRef.current) == null ? void 0 : _a2.scrollTo({ top: 0 });
  };
  const showLoading = (0, import_react9.useMemo)(() => {
    return isFetching && !dynamicResults.length && !filteredStaticResults.length;
  }, [isFetching, dynamicResults, filteredStaticResults]);
  return (0, import_jsx_runtime4.jsxs)(CommandDialog, { open, onOpenChange: handleOpenChange, children: [
    (0, import_jsx_runtime4.jsx)(
      CommandInput,
      {
        isFetching,
        ref: inputRef,
        area,
        setArea,
        value: search,
        onValueChange: handleSearch,
        onBack: area !== "all" ? handleBack : void 0,
        placeholder: t22("app.search.placeholder")
      }
    ),
    (0, import_jsx_runtime4.jsxs)(CommandList, { ref: listRef, children: [
      showLoading && (0, import_jsx_runtime4.jsx)(CommandLoading, {}),
      dynamicResults.map((group) => {
        return (0, import_jsx_runtime4.jsxs)(CommandGroup, { heading: group.title, children: [
          group.items.map((item) => {
            return (0, import_jsx_runtime4.jsx)(
              CommandItem,
              {
                onSelect: () => handleSelect(item),
                value: item.value,
                className: "flex items-center justify-between",
                children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-3", children: [
                  item.thumbnail && (0, import_jsx_runtime4.jsx)(
                    Thumbnail,
                    {
                      alt: item.title,
                      src: item.thumbnail,
                      size: "small"
                    }
                  ),
                  (0, import_jsx_runtime4.jsx)("span", { children: item.title }),
                  item.subtitle && (0, import_jsx_runtime4.jsx)("span", { className: "text-ui-fg-muted", children: item.subtitle })
                ] })
              },
              item.id
            );
          }),
          group.hasMore && area === "all" && (0, import_jsx_runtime4.jsx)(
            CommandItem,
            {
              onSelect: () => handleShowMore(group.area),
              hidden: true,
              value: `${group.title}:show:more`,
              children: (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-muted flex items-center gap-x-3", children: [
                (0, import_jsx_runtime4.jsx)(Plus, {}),
                (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t22("app.search.showMore") })
              ] })
            }
          ),
          group.hasMore && area === group.area && (0, import_jsx_runtime4.jsx)(
            CommandItem,
            {
              onSelect: handleLoadMore,
              hidden: true,
              value: `${group.title}:load:more`,
              children: (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-muted flex items-center gap-x-3", children: [
                (0, import_jsx_runtime4.jsx)(Plus, {}),
                (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t22("app.search.loadMore", {
                  count: Math.min(
                    SEARCH_LIMIT_INCREMENT,
                    group.count - limit
                  )
                }) })
              ] })
            }
          )
        ] }, group.title);
      }),
      filteredStaticResults.map((group) => {
        return (0, import_jsx_runtime4.jsx)(
          CommandGroup,
          {
            heading: t22(`app.keyboardShortcuts.${group.title}`),
            children: group.items.map((item) => {
              var _a2;
              return (0, import_jsx_runtime4.jsxs)(
                CommandItem,
                {
                  onSelect: () => handleSelect(item),
                  className: "flex items-center justify-between",
                  children: [
                    (0, import_jsx_runtime4.jsx)("span", { children: item.label }),
                    (0, import_jsx_runtime4.jsx)("div", { className: "flex items-center gap-x-1.5", children: (_a2 = item.keys.Mac) == null ? void 0 : _a2.map((key, index) => {
                      var _a3;
                      return (0, import_jsx_runtime4.jsxs)(
                        "div",
                        {
                          className: "flex items-center gap-x-1",
                          children: [
                            (0, import_jsx_runtime4.jsx)(Kbd, { children: key }),
                            index < (((_a3 = item.keys.Mac) == null ? void 0 : _a3.length) || 0) - 1 && (0, import_jsx_runtime4.jsx)("span", { className: "txt-compact-xsmall text-ui-fg-subtle", children: t22("app.keyboardShortcuts.then") })
                          ]
                        },
                        index
                      );
                    }) })
                  ]
                },
                item.label
              );
            })
          },
          group.title
        );
      }),
      !showLoading && (0, import_jsx_runtime4.jsx)(CommandEmpty, { q: search })
    ] })
  ] });
};
var CommandPalette = (0, import_react9.forwardRef)(({ className, ...props }, ref) => (0, import_jsx_runtime4.jsx)(
  we,
  {
    shouldFilter: false,
    ref,
    className: clx(
      "bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",
      className
    ),
    ...props
  }
));
CommandPalette.displayName = we.displayName;
var CommandDialog = ({ children, ...props }) => {
  const { t: t22 } = useTranslation();
  const preserveHeight = (0, import_react9.useMemo)(() => {
    return props.isLoading && import_react9.Children.count(children) === 0;
  }, [props.isLoading, children]);
  return (0, import_jsx_runtime4.jsx)(dist_exports2.Root, { ...props, children: (0, import_jsx_runtime4.jsxs)(dist_exports2.Portal, { children: [
    (0, import_jsx_runtime4.jsx)(dist_exports2.Overlay, { className: "bg-ui-bg-overlay fixed inset-0" }),
    (0, import_jsx_runtime4.jsxs)(
      dist_exports2.Content,
      {
        className: clx(
          "bg-ui-bg-base shadow-elevation-modal fixed left-[50%] top-[50%] flex max-h-[calc(100%-16px)] w-[calc(100%-16px)] min-w-0 max-w-2xl translate-x-[-50%] translate-y-[-50%] flex-col overflow-hidden rounded-xl p-0",
          {
            "h-[300px]": preserveHeight
            // Prevents the dialog from collapsing when loading async results and before the no results message is displayed
          }
        ),
        children: [
          (0, import_jsx_runtime4.jsx)(dist_exports2.Title, { className: "sr-only", children: t22("app.search.title") }),
          (0, import_jsx_runtime4.jsx)(dist_exports2.Description, { className: "sr-only", children: t22("app.search.description") }),
          (0, import_jsx_runtime4.jsx)(CommandPalette, { className: "[&_[cmdk-group-heading]]:text-muted-foreground flex h-full flex-col overflow-hidden [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0", children }),
          (0, import_jsx_runtime4.jsx)("div", { className: "bg-ui-bg-field text-ui-fg-subtle flex items-center justify-end border-t px-4 py-3", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-3", children: [
            (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime4.jsx)(Text, { size: "xsmall", leading: "compact", children: t22("app.search.navigation") }),
              (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-1", children: [
                (0, import_jsx_runtime4.jsx)(Kbd, { className: "bg-ui-bg-field-component", children: "↓" }),
                (0, import_jsx_runtime4.jsx)(Kbd, { className: "bg-ui-bg-field-component", children: "↑" })
              ] })
            ] }),
            (0, import_jsx_runtime4.jsx)("div", { className: "bg-ui-border-strong h-3 w-px" }),
            (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime4.jsx)(Text, { size: "xsmall", leading: "compact", children: t22("app.search.openResult") }),
              (0, import_jsx_runtime4.jsx)(Kbd, { className: "bg-ui-bg-field-component", children: "↵" })
            ] })
          ] }) })
        ]
      }
    )
  ] }) });
};
var CommandInput = (0, import_react9.forwardRef)(
  ({
    className,
    value,
    onValueChange,
    area,
    setArea,
    isFetching,
    onBack,
    ...props
  }, ref) => {
    const { t: t22 } = useTranslation();
    const innerRef = (0, import_react9.useRef)(null);
    (0, import_react9.useImperativeHandle)(
      ref,
      () => innerRef.current
    );
    return (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col border-b", children: [
      (0, import_jsx_runtime4.jsx)("div", { className: "px-4 pt-4", children: (0, import_jsx_runtime4.jsxs)(DropdownMenu, { children: [
        (0, import_jsx_runtime4.jsx)(DropdownMenu.Trigger, { asChild: true, children: (0, import_jsx_runtime4.jsxs)(
          Badge,
          {
            size: "2xsmall",
            className: "hover:bg-ui-bg-base-pressed transition-fg cursor-pointer",
            children: [
              t22(`app.search.groups.${area}`),
              (0, import_jsx_runtime4.jsx)(TriangleDownMini, { className: "text-ui-fg-muted" })
            ]
          }
        ) }),
        (0, import_jsx_runtime4.jsx)(
          DropdownMenu.Content,
          {
            align: "start",
            className: "h-full max-h-[360px] overflow-auto",
            onCloseAutoFocus: (e) => {
              var _a2;
              e.preventDefault();
              (_a2 = innerRef.current) == null ? void 0 : _a2.focus();
            },
            children: (0, import_jsx_runtime4.jsx)(
              DropdownMenu.RadioGroup,
              {
                value: area,
                onValueChange: (v) => setArea(v),
                children: SEARCH_AREAS.map((area2) => (0, import_jsx_runtime4.jsxs)(import_react9.Fragment, { children: [
                  area2 === "command" && (0, import_jsx_runtime4.jsx)(DropdownMenu.Separator, {}),
                  (0, import_jsx_runtime4.jsx)(DropdownMenu.RadioItem, { value: area2, children: t22(`app.search.groups.${area2}`) }),
                  area2 === "all" && (0, import_jsx_runtime4.jsx)(DropdownMenu.Separator, {})
                ] }, area2))
              }
            )
          }
        )
      ] }) }),
      (0, import_jsx_runtime4.jsxs)("div", { className: "relative flex items-center gap-x-2 px-4 py-3", children: [
        onBack && (0, import_jsx_runtime4.jsx)(
          IconButton,
          {
            type: "button",
            size: "small",
            variant: "transparent",
            onClick: onBack,
            children: (0, import_jsx_runtime4.jsx)(ArrowUturnLeft, { className: "text-ui-fg-muted" })
          }
        ),
        (0, import_jsx_runtime4.jsx)(
          we.Input,
          {
            ref: innerRef,
            value,
            onValueChange,
            className: clx(
              "placeholder:text-ui-fg-muted flex !h-6 w-full rounded-md bg-transparent text-sm outline-none disabled:cursor-not-allowed disabled:opacity-50",
              className
            ),
            ...props
          }
        ),
        (0, import_jsx_runtime4.jsxs)("div", { className: "absolute right-4 top-1/2 flex -translate-y-1/2 items-center justify-end gap-x-2", children: [
          isFetching && (0, import_jsx_runtime4.jsx)(Spinner, { className: "text-ui-fg-muted animate-spin" }),
          value && (0, import_jsx_runtime4.jsx)(
            Button,
            {
              variant: "transparent",
              size: "small",
              className: "text-ui-fg-muted hover:text-ui-fg-subtle",
              type: "button",
              onClick: () => {
                var _a2;
                onValueChange == null ? void 0 : onValueChange("");
                (_a2 = innerRef.current) == null ? void 0 : _a2.focus();
              },
              children: t22("actions.clear")
            }
          )
        ] })
      ] })
    ] });
  }
);
CommandInput.displayName = we.Input.displayName;
var CommandList = (0, import_react9.forwardRef)(({ className, ...props }, ref) => (0, import_jsx_runtime4.jsx)(
  we.List,
  {
    ref,
    className: clx(
      "max-h-[300px] flex-1 overflow-y-auto overflow-x-hidden px-2 pb-4",
      className
    ),
    ...props
  }
));
CommandList.displayName = we.List.displayName;
var CommandEmpty = (0, import_react9.forwardRef)((props, ref) => {
  const { t: t22 } = useTranslation();
  return (0, import_jsx_runtime4.jsx)(we.Empty, { ref, className: "py-6 text-center text-sm", ...props, children: (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle flex min-h-[236px] flex-col items-center justify-center gap-y-3", children: [
    (0, import_jsx_runtime4.jsx)(MagnifyingGlass, { className: "text-ui-fg-subtle" }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col items-center justify-center gap-y-1", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: props.q ? t22("app.search.noResultsTitle") : t22("app.search.emptySearchTitle") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", className: "text-ui-fg-muted", children: props.q ? t22("app.search.noResultsMessage") : t22("app.search.emptySearchMessage") })
    ] })
  ] }) });
});
CommandEmpty.displayName = we.Empty.displayName;
var CommandLoading = (0, import_react9.forwardRef)((props, ref) => {
  return (0, import_jsx_runtime4.jsxs)(
    we.Loading,
    {
      ref,
      ...props,
      className: "bg-ui-bg-base flex flex-col",
      children: [
        (0, import_jsx_runtime4.jsx)("div", { className: "w-full px-2 pb-1 pt-3", children: (0, import_jsx_runtime4.jsx)(Skeleton, { className: "h-5 w-10" }) }),
        Array.from({ length: 7 }).map((_, index) => (0, import_jsx_runtime4.jsx)("div", { className: "w-full p-2", children: (0, import_jsx_runtime4.jsx)(Skeleton, { className: "h-5 w-full" }) }, index))
      ]
    }
  );
});
CommandLoading.displayName = we.Loading.displayName;
var CommandGroup = (0, import_react9.forwardRef)(({ className, ...props }, ref) => (0, import_jsx_runtime4.jsx)(
  we.Group,
  {
    ref,
    className: clx(
      "text-ui-fg-base [&_[cmdk-group-heading]]:text-ui-fg-muted [&_[cmdk-group-heading]]:txt-compact-xsmall-plus overflow-hidden [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:pb-1 [&_[cmdk-group-heading]]:pt-3 [&_[cmdk-item]]:py-2",
      className
    ),
    ...props
  }
));
CommandGroup.displayName = we.Group.displayName;
var CommandSeparator = (0, import_react9.forwardRef)(({ className, ...props }, ref) => (0, import_jsx_runtime4.jsx)(
  we.Separator,
  {
    ref,
    className: clx("bg-border -mx-1 h-px", className),
    ...props
  }
));
CommandSeparator.displayName = we.Separator.displayName;
var CommandItem = (0, import_react9.forwardRef)(({ className, ...props }, ref) => (0, import_jsx_runtime4.jsx)(
  we.Item,
  {
    ref,
    className: clx(
      "aria-selected:bg-ui-bg-base-hover focus-visible:bg-ui-bg-base-hover txt-compact-small [&>svg]:text-ui-fg-subtle relative flex cursor-pointer select-none items-center gap-x-3 rounded-md p-2 outline-none data-[disabled]:pointer-events-none data-[disabled]:cursor-not-allowed data-[disabled]:opacity-50",
      className
    ),
    ...props
  }
));
CommandItem.displayName = we.Item.displayName;
var SidebarContext = (0, import_react14.createContext)(null);
var SidebarProvider = ({ children }) => {
  const [desktop, setDesktop] = (0, import_react13.useState)(true);
  const [mobile, setMobile] = (0, import_react13.useState)(false);
  const { pathname } = useLocation();
  const toggle = (view) => {
    if (view === "desktop") {
      setDesktop(!desktop);
    } else {
      setMobile(!mobile);
    }
  };
  (0, import_react13.useEffect)(() => {
    setMobile(false);
  }, [pathname]);
  return (0, import_jsx_runtime5.jsx)(SidebarContext.Provider, { value: { desktop, mobile, toggle }, children });
};
var useSidebar = () => {
  const context = (0, import_react15.useContext)(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};
var SearchContext = (0, import_react16.createContext)(null);
var SearchProvider = ({ children }) => {
  const [open, setOpen] = (0, import_react8.useState)(false);
  const { mobile, toggle } = useSidebar();
  const toggleSearch = () => {
    const update = !open;
    if (update && mobile) {
      toggle("mobile");
    }
    setOpen(update);
  };
  (0, import_react8.useEffect)(() => {
    const onKeyDown = (e) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        setOpen((prev) => !prev);
      }
    };
    document.addEventListener("keydown", onKeyDown);
    return () => {
      document.removeEventListener("keydown", onKeyDown);
    };
  }, []);
  return (0, import_jsx_runtime6.jsxs)(
    SearchContext.Provider,
    {
      value: {
        open,
        onOpenChange: setOpen,
        toggleSearch
      },
      children: [
        children,
        (0, import_jsx_runtime6.jsx)(Search, {})
      ]
    }
  );
};
var useSearch = () => {
  const context = (0, import_react17.useContext)(SearchContext);
  if (!context) {
    throw new Error("useSearch must be used within a SearchProvider");
  }
  return context;
};
var ProtectedRoute = () => {
  const { user, isLoading } = useMe();
  const location = useLocation();
  if (isLoading) {
    return (0, import_jsx_runtime7.jsx)("div", { className: "flex min-h-screen items-center justify-center", children: (0, import_jsx_runtime7.jsx)(Spinner, { className: "text-ui-fg-interactive animate-spin" }) });
  }
  if (!user) {
    return (0, import_jsx_runtime7.jsx)(Navigate, { to: "/login", state: { from: location }, replace: true });
  }
  return (0, import_jsx_runtime7.jsx)(SidebarProvider, { children: (0, import_jsx_runtime7.jsx)(SearchProvider, { children: (0, import_jsx_runtime7.jsx)(Outlet, {}) }) });
};
var BASE_NAV_LINK_CLASSES = "text-ui-fg-subtle transition-fg hover:bg-ui-bg-subtle-hover flex items-center gap-x-2 rounded-md py-0.5 pl-0.5 pr-2 outline-none [&>svg]:text-ui-fg-subtle focus-visible:shadow-borders-focus";
var ACTIVE_NAV_LINK_CLASSES = "bg-ui-bg-base shadow-elevation-card-rest text-ui-fg-base hover:bg-ui-bg-base";
var NESTED_NAV_LINK_CLASSES = "pl-[34px] pr-2 py-1 w-full text-ui-fg-muted";
var SETTING_NAV_LINK_CLASSES = "pl-2 py-1";
var getIsOpen = (to, items, pathname) => {
  return [to, ...(items == null ? void 0 : items.map((i) => i.to)) ?? []].some(
    (p) => pathname.startsWith(p)
  );
};
var NavItemTooltip = ({
  to,
  children
}) => {
  var _a2;
  const { t: t22 } = useTranslation();
  const globalShortcuts = useGlobalShortcuts();
  const shortcut = globalShortcuts.find((s) => s.to === to);
  return (0, import_jsx_runtime8.jsx)(
    ConditionalTooltip,
    {
      showTooltip: !!shortcut,
      maxWidth: 9999,
      content: (0, import_jsx_runtime8.jsxs)("div", { className: "txt-compact-xsmall flex h-5 items-center justify-between gap-x-2 whitespace-nowrap", children: [
        (0, import_jsx_runtime8.jsx)("span", { children: shortcut == null ? void 0 : shortcut.label }),
        (0, import_jsx_runtime8.jsx)("div", { className: "flex items-center gap-x-1", children: (_a2 = shortcut == null ? void 0 : shortcut.keys.Mac) == null ? void 0 : _a2.map((key, index) => {
          var _a3;
          return (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center gap-x-1", children: [
            (0, import_jsx_runtime8.jsx)(Kbd, { children: key }, key),
            index < (((_a3 = shortcut.keys.Mac) == null ? void 0 : _a3.length) || 0) - 1 && (0, import_jsx_runtime8.jsx)("span", { className: "text-ui-fg-muted txt-compact-xsmall", children: t22("app.keyboardShortcuts.then") })
          ] }, index);
        }) })
      ] }),
      side: "right",
      delayDuration: 1500,
      children: (0, import_jsx_runtime8.jsx)("div", { className: "w-full", children })
    }
  );
};
var NavItem = ({
  icon,
  label,
  to,
  items,
  type = "core",
  from
}) => {
  const { pathname } = useLocation();
  const [open, setOpen] = (0, import_react18.useState)(getIsOpen(to, items, pathname));
  (0, import_react18.useEffect)(() => {
    setOpen(getIsOpen(to, items, pathname));
  }, [pathname, to, items]);
  const navLinkClassNames = (0, import_react18.useCallback)(
    ({
      to: to2,
      isActive,
      isNested = false,
      isSetting: isSetting2 = false
    }) => {
      if (["core", "setting"].includes(type)) {
        isActive = pathname.startsWith(to2);
      }
      return clx(BASE_NAV_LINK_CLASSES, {
        [NESTED_NAV_LINK_CLASSES]: isNested,
        [ACTIVE_NAV_LINK_CLASSES]: isActive,
        [SETTING_NAV_LINK_CLASSES]: isSetting2
      });
    },
    [type, pathname]
  );
  const isSetting = type === "setting";
  return (0, import_jsx_runtime8.jsxs)("div", { className: "px-3", children: [
    (0, import_jsx_runtime8.jsx)(NavItemTooltip, { to, children: (0, import_jsx_runtime8.jsxs)(
      NavLink,
      {
        to,
        end: items == null ? void 0 : items.some((i) => i.to === pathname),
        state: from ? {
          from
        } : void 0,
        className: ({ isActive }) => {
          return clx(navLinkClassNames({ isActive, isSetting, to }), {
            "max-lg:hidden": !!(items == null ? void 0 : items.length)
          });
        },
        children: [
          type !== "setting" && (0, import_jsx_runtime8.jsx)("div", { className: "flex size-6 items-center justify-center", children: (0, import_jsx_runtime8.jsx)(Icon, { icon, type }) }),
          (0, import_jsx_runtime8.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: label })
        ]
      }
    ) }),
    items && items.length > 0 && (0, import_jsx_runtime8.jsxs)(dist_exports.Root, { open, onOpenChange: setOpen, children: [
      (0, import_jsx_runtime8.jsxs)(
        dist_exports.Trigger,
        {
          className: clx(
            "text-ui-fg-subtle hover:text-ui-fg-base transition-fg hover:bg-ui-bg-subtle-hover flex w-full items-center gap-x-2 rounded-md py-0.5 pl-0.5 pr-2 outline-none lg:hidden",
            { "pl-2": isSetting }
          ),
          children: [
            (0, import_jsx_runtime8.jsx)("div", { className: "flex size-6 items-center justify-center", children: (0, import_jsx_runtime8.jsx)(Icon, { icon, type }) }),
            (0, import_jsx_runtime8.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: label })
          ]
        }
      ),
      (0, import_jsx_runtime8.jsx)(dist_exports.Content, { children: (0, import_jsx_runtime8.jsx)("div", { className: "flex flex-col gap-y-0.5 pb-2 pt-0.5", children: (0, import_jsx_runtime8.jsxs)("ul", { className: "flex flex-col gap-y-0.5", children: [
        (0, import_jsx_runtime8.jsx)("li", { className: "flex w-full items-center gap-x-1 lg:hidden", children: (0, import_jsx_runtime8.jsx)(NavItemTooltip, { to, children: (0, import_jsx_runtime8.jsx)(
          NavLink,
          {
            to,
            end: true,
            className: ({ isActive }) => {
              return clx(
                navLinkClassNames({
                  to,
                  isActive,
                  isSetting,
                  isNested: true
                })
              );
            },
            children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: label })
          }
        ) }) }),
        items.map((item) => {
          return (0, import_jsx_runtime8.jsx)("li", { className: "flex h-7 items-center", children: (0, import_jsx_runtime8.jsx)(NavItemTooltip, { to: item.to, children: (0, import_jsx_runtime8.jsx)(
            NavLink,
            {
              to: item.to,
              end: true,
              className: ({ isActive }) => {
                return clx(
                  navLinkClassNames({
                    to: item.to,
                    isActive,
                    isSetting,
                    isNested: true
                  })
                );
              },
              children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: item.label })
            }
          ) }) }, item.to);
        })
      ] }) }) })
    ] })
  ] });
};
var Icon = ({ icon, type }) => {
  if (!icon) {
    return null;
  }
  return type === "extension" ? (0, import_jsx_runtime8.jsx)("div", { className: "shadow-borders-base bg-ui-bg-base flex h-5 w-5 items-center justify-center rounded-[4px]", children: (0, import_jsx_runtime8.jsx)("div", { className: "h-[15px] w-[15px] overflow-hidden rounded-sm", children: icon }) }) : icon;
};
var KeybindProvider = ({
  shortcuts,
  debounce = 500,
  children
}) => {
  const [storeShortcuts, setStoreCommands] = (0, import_react21.useState)(
    shortcuts.map((shr) => getShortcutWithDefaultValues(shr))
  );
  const registerShortcut = (0, import_react21.useCallback)(
    (shortcut) => {
      setStoreCommands((prevShortcuts) => {
        const idx = findShortcutIndex(shortcuts, getShortcutKeys(shortcut));
        const newShortcuts = [...prevShortcuts];
        if (idx > -1) {
          newShortcuts[idx] = getShortcutWithDefaultValues(shortcut);
          return prevShortcuts;
        }
        return [...prevShortcuts, getShortcutWithDefaultValues(shortcut)];
      });
    },
    [shortcuts]
  );
  const getKeysByPlatform = (0, import_react21.useCallback)((command) => {
    return findFirstPlatformMatch(command.keys);
  }, []);
  useShortcuts({ shortcuts: storeShortcuts, debounce });
  const commandsContext = (0, import_react21.useMemo)(
    () => ({
      shortcuts: storeShortcuts,
      registerShortcut,
      getKeysByPlatform
    }),
    [storeShortcuts, registerShortcut, getKeysByPlatform]
  );
  return (0, import_jsx_runtime9.jsx)(KeybindContext.Provider, { value: commandsContext, children });
};
var InfiniteList = ({
  queryKey,
  queryFn,
  queryOptions,
  renderItem,
  renderEmpty,
  responseKey,
  pageSize = 20
}) => {
  const {
    data,
    error,
    fetchNextPage,
    fetchPreviousPage,
    hasPreviousPage,
    hasNextPage,
    isFetching,
    isPending
  } = useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam = 0 }) => {
      return await queryFn({
        limit: pageSize,
        offset: pageParam
      });
    },
    initialPageParam: 0,
    maxPages: 5,
    getNextPageParam: (lastPage) => {
      const moreItemsExist = lastPage.count > lastPage.offset + lastPage.limit;
      return moreItemsExist ? lastPage.offset + lastPage.limit : void 0;
    },
    getPreviousPageParam: (firstPage) => {
      const moreItemsExist = firstPage.offset !== 0;
      return moreItemsExist ? Math.max(firstPage.offset - firstPage.limit, 0) : void 0;
    },
    ...queryOptions
  });
  const items = (0, import_react23.useMemo)(() => {
    return (data == null ? void 0 : data.pages.flatMap((p) => p[responseKey])) ?? [];
  }, [data, responseKey]);
  const parentRef = (0, import_react23.useRef)(null);
  const startObserver = (0, import_react23.useRef)();
  const endObserver = (0, import_react23.useRef)();
  (0, import_react23.useEffect)(() => {
    var _a2, _b;
    if (isPending) {
      return;
    }
    if (!isFetching) {
      startObserver.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasPreviousPage) {
            fetchPreviousPage();
          }
        },
        {
          threshold: 0.5
        }
      );
      endObserver.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            fetchNextPage();
          }
        },
        {
          threshold: 0.5
        }
      );
      (_a2 = startObserver.current) == null ? void 0 : _a2.observe(parentRef.current.firstChild);
      (_b = endObserver.current) == null ? void 0 : _b.observe(parentRef.current.lastChild);
    }
    return () => {
      var _a3, _b2;
      (_a3 = startObserver.current) == null ? void 0 : _a3.disconnect();
      (_b2 = endObserver.current) == null ? void 0 : _b2.disconnect();
    };
  }, [
    fetchNextPage,
    fetchPreviousPage,
    hasNextPage,
    hasPreviousPage,
    isFetching,
    isPending
  ]);
  (0, import_react23.useEffect)(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);
  if (isPending) {
    return (0, import_jsx_runtime10.jsx)("div", { className: "flex h-full flex-col items-center justify-center", children: (0, import_jsx_runtime10.jsx)(Spinner, { className: "animate-spin" }) });
  }
  return (0, import_jsx_runtime10.jsxs)("div", { ref: parentRef, className: "h-full", children: [
    (items == null ? void 0 : items.length) ? items.map((item) => (0, import_jsx_runtime10.jsx)("div", { children: renderItem(item) }, item.id)) : renderEmpty(),
    isFetching && (0, import_jsx_runtime10.jsx)("div", { className: "flex flex-col items-center justify-center py-4", children: (0, import_jsx_runtime10.jsx)(Spinner, { className: "animate-spin" }) })
  ] });
};
var LAST_READ_NOTIFICATION_KEY = "notificationsLastReadAt";
var Notifications = () => {
  const { t: t22 } = useTranslation();
  const [open, setOpen] = (0, import_react22.useState)(false);
  const [hasUnread, setHasUnread] = useUnreadNotifications();
  const [lastReadAt, setLastReadAt] = (0, import_react22.useState)(
    localStorage.getItem(LAST_READ_NOTIFICATION_KEY)
  );
  (0, import_react22.useEffect)(() => {
    const onKeyDown = (e) => {
      if (e.key === "n" && (e.metaKey || e.ctrlKey)) {
        setOpen((prev) => !prev);
      }
    };
    document.addEventListener("keydown", onKeyDown);
    return () => {
      document.removeEventListener("keydown", onKeyDown);
    };
  }, []);
  const handleOnOpen = (shouldOpen) => {
    if (shouldOpen) {
      setHasUnread(false);
      setOpen(true);
      localStorage.setItem(LAST_READ_NOTIFICATION_KEY, (/* @__PURE__ */ new Date()).toISOString());
    } else {
      setOpen(false);
      setLastReadAt(localStorage.getItem(LAST_READ_NOTIFICATION_KEY));
    }
  };
  return (0, import_jsx_runtime11.jsxs)(Drawer, { open, onOpenChange: handleOnOpen, children: [
    (0, import_jsx_runtime11.jsx)(Drawer.Trigger, { asChild: true, children: (0, import_jsx_runtime11.jsx)(
      IconButton,
      {
        variant: "transparent",
        size: "small",
        className: "text-ui-fg-muted hover:text-ui-fg-subtle",
        children: hasUnread ? (0, import_jsx_runtime11.jsx)(BellAlertDone, {}) : (0, import_jsx_runtime11.jsx)(BellAlert, {})
      }
    ) }),
    (0, import_jsx_runtime11.jsxs)(Drawer.Content, { children: [
      (0, import_jsx_runtime11.jsxs)(Drawer.Header, { children: [
        (0, import_jsx_runtime11.jsx)(Drawer.Title, { asChild: true, children: (0, import_jsx_runtime11.jsx)(Heading, { children: t22("notifications.domain") }) }),
        (0, import_jsx_runtime11.jsx)(Drawer.Description, { className: "sr-only", children: t22("notifications.accessibility.description") })
      ] }),
      (0, import_jsx_runtime11.jsx)(Drawer.Body, { className: "overflow-y-auto px-0", children: (0, import_jsx_runtime11.jsx)(
        InfiniteList,
        {
          responseKey: "notifications",
          queryKey: notificationQueryKeys.all,
          queryFn: (params) => sdk.admin.notification.list(params),
          queryOptions: { enabled: open },
          renderEmpty: () => (0, import_jsx_runtime11.jsx)(NotificationsEmptyState, { t: t22 }),
          renderItem: (notification) => {
            return (0, import_jsx_runtime11.jsx)(
              Notification,
              {
                notification,
                unread: Date.parse(notification.created_at) > (lastReadAt ? Date.parse(lastReadAt) : 0)
              },
              notification.id
            );
          }
        }
      ) })
    ] })
  ] });
};
var Notification = ({
  notification,
  unread
}) => {
  var _a2;
  const data = notification.data;
  if (!(data == null ? void 0 : data.title)) {
    return null;
  }
  return (0, import_jsx_runtime11.jsx)(import_jsx_runtime11.Fragment, { children: (0, import_jsx_runtime11.jsxs)("div", { className: "relative flex items-start justify-center gap-3 border-b p-6", children: [
    (0, import_jsx_runtime11.jsx)("div", { className: "text-ui-fg-muted flex size-5 items-center justify-center", children: (0, import_jsx_runtime11.jsx)(InformationCircleSolid, {}) }),
    (0, import_jsx_runtime11.jsxs)("div", { className: "flex w-full flex-col gap-y-3", children: [
      (0, import_jsx_runtime11.jsxs)("div", { className: "flex flex-col", children: [
        (0, import_jsx_runtime11.jsxs)("div", { className: "flex items-center justify-between", children: [
          (0, import_jsx_runtime11.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: data.title }),
          (0, import_jsx_runtime11.jsxs)("div", { className: "align-center flex items-center justify-center gap-2", children: [
            (0, import_jsx_runtime11.jsx)(
              Text,
              {
                as: "span",
                className: clx("text-ui-fg-subtle", {
                  "text-ui-fg-base": unread
                }),
                size: "small",
                leading: "compact",
                weight: "plus",
                children: formatDistance(notification.created_at, /* @__PURE__ */ new Date(), {
                  addSuffix: true
                })
              }
            ),
            unread && (0, import_jsx_runtime11.jsx)(
              "div",
              {
                className: "bg-ui-bg-interactive h-2 w-2 rounded",
                role: "status"
              }
            )
          ] })
        ] }),
        !!data.description && (0, import_jsx_runtime11.jsx)(
          Text,
          {
            className: "text-ui-fg-subtle whitespace-pre-line",
            size: "small",
            children: data.description
          }
        )
      ] }),
      !!((_a2 = data == null ? void 0 : data.file) == null ? void 0 : _a2.url) && (0, import_jsx_runtime11.jsx)(
        FilePreview,
        {
          filename: data.file.filename ?? "",
          url: data.file.url,
          hideThumbnail: true
        }
      )
    ] })
  ] }) });
};
var NotificationsEmptyState = ({ t: t22 }) => {
  return (0, import_jsx_runtime11.jsxs)("div", { className: "flex h-full flex-col items-center justify-center", children: [
    (0, import_jsx_runtime11.jsx)(BellAlertDone, {}),
    (0, import_jsx_runtime11.jsx)(Text, { size: "small", leading: "compact", weight: "plus", className: "mt-3", children: t22("notifications.emptyState.title") }),
    (0, import_jsx_runtime11.jsx)(
      Text,
      {
        size: "small",
        className: "text-ui-fg-muted mt-1 max-w-[294px] text-center",
        children: t22("notifications.emptyState.description")
      }
    )
  ] });
};
var useUnreadNotifications = () => {
  const [hasUnread, setHasUnread] = (0, import_react22.useState)(false);
  const { notifications } = useNotifications(
    { limit: 1, offset: 0, fields: "created_at" },
    { refetchInterval: 6e4 }
  );
  const lastNotification = notifications == null ? void 0 : notifications[0];
  (0, import_react22.useEffect)(() => {
    if (!lastNotification) {
      return;
    }
    const lastNotificationAsTimestamp = Date.parse(lastNotification.created_at);
    const lastReadDatetime = localStorage.getItem(LAST_READ_NOTIFICATION_KEY);
    const lastReadAsTimestamp = lastReadDatetime ? Date.parse(lastReadDatetime) : 0;
    if (lastNotificationAsTimestamp > lastReadAsTimestamp) {
      setHasUnread(true);
    }
  }, [lastNotification]);
  return [hasUnread, setHasUnread];
};
var Shell = ({ children }) => {
  const globalShortcuts = useGlobalShortcuts();
  const navigation = useNavigation();
  const loading = navigation.state === "loading";
  return (0, import_jsx_runtime12.jsx)(KeybindProvider, { shortcuts: globalShortcuts, children: (0, import_jsx_runtime12.jsxs)("div", { className: "relative flex h-screen flex-col items-start overflow-hidden lg:flex-row", children: [
    (0, import_jsx_runtime12.jsx)(NavigationBar, { loading }),
    (0, import_jsx_runtime12.jsxs)("div", { children: [
      (0, import_jsx_runtime12.jsx)(MobileSidebarContainer, { children }),
      (0, import_jsx_runtime12.jsx)(DesktopSidebarContainer, { children })
    ] }),
    (0, import_jsx_runtime12.jsxs)("div", { className: "flex h-screen w-full flex-col overflow-auto", children: [
      (0, import_jsx_runtime12.jsx)(Topbar, {}),
      (0, import_jsx_runtime12.jsx)(
        "main",
        {
          className: clx(
            "flex h-full w-full flex-col items-center overflow-y-auto transition-opacity delay-200 duration-200",
            {
              "opacity-25": loading
            }
          ),
          children: (0, import_jsx_runtime12.jsx)(Gutter, { children: (0, import_jsx_runtime12.jsx)(Outlet, {}) })
        }
      )
    ] })
  ] }) });
};
var NavigationBar = ({ loading }) => {
  const [showBar, setShowBar] = (0, import_react20.useState)(false);
  (0, import_react20.useEffect)(() => {
    let timeout;
    if (loading) {
      timeout = setTimeout(() => {
        setShowBar(true);
      }, 200);
    } else {
      setShowBar(false);
    }
    return () => {
      clearTimeout(timeout);
    };
  }, [loading]);
  return (0, import_jsx_runtime12.jsx)("div", { className: "fixed inset-x-0 top-0 z-50 h-1", children: (0, import_jsx_runtime12.jsx)(AnimatePresence, { children: showBar ? (0, import_jsx_runtime12.jsx)(ProgressBar, {}) : null }) });
};
var Gutter = ({ children }) => {
  return (0, import_jsx_runtime12.jsx)("div", { className: "flex w-full max-w-[1600px] flex-col gap-y-2 p-3", children });
};
var Breadcrumbs = () => {
  const matches = useMatches();
  const crumbs = matches.filter((match) => {
    var _a2;
    return (_a2 = match.handle) == null ? void 0 : _a2.breadcrumb;
  }).map((match) => {
    var _a2;
    const handle = match.handle;
    let label = void 0;
    try {
      label = (_a2 = handle.breadcrumb) == null ? void 0 : _a2.call(handle, match);
    } catch (error) {
    }
    if (!label) {
      return null;
    }
    return {
      label,
      path: match.pathname
    };
  }).filter(Boolean);
  return (0, import_jsx_runtime12.jsx)(
    "ol",
    {
      className: clx(
        "text-ui-fg-muted txt-compact-small-plus flex select-none items-center"
      ),
      children: crumbs.map((crumb, index) => {
        const isLast = index === crumbs.length - 1;
        const isSingle = crumbs.length === 1;
        return (0, import_jsx_runtime12.jsxs)("li", { className: clx("flex items-center"), children: [
          !isLast ? (0, import_jsx_runtime12.jsx)(
            Link,
            {
              className: "transition-fg hover:text-ui-fg-subtle",
              to: crumb.path,
              children: crumb.label
            }
          ) : (0, import_jsx_runtime12.jsxs)("div", { children: [
            !isSingle && (0, import_jsx_runtime12.jsx)("span", { className: "block lg:hidden", children: "..." }),
            (0, import_jsx_runtime12.jsx)(
              "span",
              {
                className: clx({
                  "hidden lg:block": !isSingle
                }),
                children: crumb.label
              },
              index
            )
          ] }),
          !isLast && (0, import_jsx_runtime12.jsx)("span", { className: "mx-2", children: (0, import_jsx_runtime12.jsx)(TriangleRightMini, {}) })
        ] }, index);
      })
    }
  );
};
var ToggleSidebar = () => {
  const { toggle } = useSidebar();
  return (0, import_jsx_runtime12.jsxs)("div", { children: [
    (0, import_jsx_runtime12.jsx)(
      IconButton,
      {
        className: "hidden lg:flex",
        variant: "transparent",
        onClick: () => toggle("desktop"),
        size: "small",
        children: (0, import_jsx_runtime12.jsx)(SidebarLeft, { className: "text-ui-fg-muted" })
      }
    ),
    (0, import_jsx_runtime12.jsx)(
      IconButton,
      {
        className: "hidden max-lg:flex",
        variant: "transparent",
        onClick: () => toggle("mobile"),
        size: "small",
        children: (0, import_jsx_runtime12.jsx)(SidebarLeft, { className: "text-ui-fg-muted" })
      }
    )
  ] });
};
var Topbar = () => {
  return (0, import_jsx_runtime12.jsxs)("div", { className: "grid w-full grid-cols-2 border-b p-3", children: [
    (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
      (0, import_jsx_runtime12.jsx)(ToggleSidebar, {}),
      (0, import_jsx_runtime12.jsx)(Breadcrumbs, {})
    ] }),
    (0, import_jsx_runtime12.jsx)("div", { className: "flex items-center justify-end gap-x-3", children: (0, import_jsx_runtime12.jsx)(Notifications, {}) })
  ] });
};
var DesktopSidebarContainer = ({ children }) => {
  const { desktop } = useSidebar();
  return (0, import_jsx_runtime12.jsx)(
    "div",
    {
      className: clx("hidden h-screen w-[220px] border-r", {
        "lg:flex": desktop
      }),
      children
    }
  );
};
var MobileSidebarContainer = ({ children }) => {
  const { t: t22 } = useTranslation();
  const { mobile, toggle } = useSidebar();
  return (0, import_jsx_runtime12.jsx)(dist_exports2.Root, { open: mobile, onOpenChange: () => toggle("mobile"), children: (0, import_jsx_runtime12.jsxs)(dist_exports2.Portal, { children: [
    (0, import_jsx_runtime12.jsx)(
      dist_exports2.Overlay,
      {
        className: clx(
          "bg-ui-bg-overlay fixed inset-0",
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
        )
      }
    ),
    (0, import_jsx_runtime12.jsxs)(
      dist_exports2.Content,
      {
        className: clx(
          "bg-ui-bg-subtle shadow-elevation-modal fixed inset-y-2 left-2 flex w-full max-w-[304px] flex-col overflow-hidden rounded-lg border-r",
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-out-to-left-1/2 data-[state=open]:slide-in-from-left-1/2 duration-200"
        ),
        children: [
          (0, import_jsx_runtime12.jsxs)("div", { className: "p-3", children: [
            (0, import_jsx_runtime12.jsx)(dist_exports2.Close, { asChild: true, children: (0, import_jsx_runtime12.jsx)(
              IconButton,
              {
                size: "small",
                variant: "transparent",
                className: "text-ui-fg-subtle",
                children: (0, import_jsx_runtime12.jsx)(XMark, {})
              }
            ) }),
            (0, import_jsx_runtime12.jsx)(dist_exports2.Title, { className: "sr-only", children: t22("app.nav.accessibility.title") }),
            (0, import_jsx_runtime12.jsx)(dist_exports2.Description, { className: "sr-only", children: t22("app.nav.accessibility.description") })
          ] }),
          children
        ]
      }
    )
  ] }) });
};
var UserMenu = () => {
  const { t: t22 } = useTranslation();
  const location = useLocation();
  const [openMenu, setOpenMenu] = (0, import_react24.useState)(false);
  const [openModal, setOpenModal] = (0, import_react24.useState)(false);
  const toggleModal = () => {
    setOpenMenu(false);
    setOpenModal(!openModal);
  };
  return (0, import_jsx_runtime13.jsxs)("div", { children: [
    (0, import_jsx_runtime13.jsxs)(DropdownMenu, { open: openMenu, onOpenChange: setOpenMenu, children: [
      (0, import_jsx_runtime13.jsx)(UserBadge, {}),
      (0, import_jsx_runtime13.jsxs)(DropdownMenu.Content, { className: "min-w-[var(--radix-dropdown-menu-trigger-width)] max-w-[var(--radix-dropdown-menu-trigger-width)]", children: [
        (0, import_jsx_runtime13.jsx)(UserItem, {}),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Separator, {}),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Item, { asChild: true, children: (0, import_jsx_runtime13.jsxs)(Link, { to: "/settings/profile", state: { from: location.pathname }, children: [
          (0, import_jsx_runtime13.jsx)(User, { className: "text-ui-fg-subtle mr-2" }),
          t22("app.menus.user.profileSettings")
        ] }) }),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Separator, {}),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Item, { asChild: true, children: (0, import_jsx_runtime13.jsxs)(Link, { to: "https://docs.medusajs.com", target: "_blank", children: [
          (0, import_jsx_runtime13.jsx)(BookOpen, { className: "text-ui-fg-subtle mr-2" }),
          t22("app.menus.user.documentation")
        ] }) }),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Item, { asChild: true, children: (0, import_jsx_runtime13.jsxs)(Link, { to: "https://medusajs.com/changelog/", target: "_blank", children: [
          (0, import_jsx_runtime13.jsx)(TimelineVertical, { className: "text-ui-fg-subtle mr-2" }),
          t22("app.menus.user.changelog")
        ] }) }),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Separator, {}),
        (0, import_jsx_runtime13.jsxs)(DropdownMenu.Item, { onClick: toggleModal, children: [
          (0, import_jsx_runtime13.jsx)(Keyboard, { className: "text-ui-fg-subtle mr-2" }),
          t22("app.menus.user.shortcuts")
        ] }),
        (0, import_jsx_runtime13.jsx)(ThemeToggle, {}),
        (0, import_jsx_runtime13.jsx)(DropdownMenu.Separator, {}),
        (0, import_jsx_runtime13.jsx)(Logout, {})
      ] })
    ] }),
    (0, import_jsx_runtime13.jsx)(GlobalKeybindsModal, { open: openModal, onOpenChange: setOpenModal })
  ] });
};
var UserBadge = () => {
  const { user, isPending, isError, error } = useMe();
  const name = [user == null ? void 0 : user.first_name, user == null ? void 0 : user.last_name].filter(Boolean).join(" ");
  const displayName = name || (user == null ? void 0 : user.email);
  const fallback = displayName ? displayName[0].toUpperCase() : null;
  if (isPending) {
    return (0, import_jsx_runtime13.jsxs)("button", { className: "shadow-borders-base flex max-w-[192px] select-none items-center gap-x-2 overflow-hidden text-ellipsis whitespace-nowrap rounded-full py-1 pl-1 pr-2.5", children: [
      (0, import_jsx_runtime13.jsx)(Skeleton, { className: "h-5 w-5 rounded-full" }),
      (0, import_jsx_runtime13.jsx)(Skeleton, { className: "h-[9px] w-[70px]" })
    ] });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime13.jsx)("div", { className: "p-3", children: (0, import_jsx_runtime13.jsxs)(
    DropdownMenu.Trigger,
    {
      disabled: !user,
      className: clx(
        "bg-ui-bg-subtle grid w-full cursor-pointer grid-cols-[24px_1fr_15px] items-center gap-2 rounded-md py-1 pl-0.5 pr-2 outline-none",
        "hover:bg-ui-bg-subtle-hover",
        "data-[state=open]:bg-ui-bg-subtle-hover",
        "focus-visible:shadow-borders-focus"
      ),
      children: [
        (0, import_jsx_runtime13.jsx)("div", { className: "flex size-6 items-center justify-center", children: fallback ? (0, import_jsx_runtime13.jsx)(Avatar, { size: "xsmall", fallback }) : (0, import_jsx_runtime13.jsx)(Skeleton, { className: "h-6 w-6 rounded-full" }) }),
        (0, import_jsx_runtime13.jsx)("div", { className: "flex items-center overflow-hidden", children: displayName ? (0, import_jsx_runtime13.jsx)(
          Text,
          {
            size: "xsmall",
            weight: "plus",
            leading: "compact",
            className: "truncate",
            children: displayName
          }
        ) : (0, import_jsx_runtime13.jsx)(Skeleton, { className: "h-[9px] w-[70px]" }) }),
        (0, import_jsx_runtime13.jsx)(EllipsisHorizontal, { className: "text-ui-fg-muted" })
      ]
    }
  ) });
};
var ThemeToggle = () => {
  const { t: t22 } = useTranslation();
  const { theme, setTheme } = useTheme();
  return (0, import_jsx_runtime13.jsxs)(DropdownMenu.SubMenu, { children: [
    (0, import_jsx_runtime13.jsxs)(DropdownMenu.SubMenuTrigger, { className: "rounded-md", children: [
      (0, import_jsx_runtime13.jsx)(CircleHalfSolid, { className: "text-ui-fg-subtle mr-2" }),
      t22("app.menus.user.theme.label")
    ] }),
    (0, import_jsx_runtime13.jsx)(DropdownMenu.SubMenuContent, { children: (0, import_jsx_runtime13.jsxs)(DropdownMenu.RadioGroup, { value: theme, children: [
      (0, import_jsx_runtime13.jsx)(
        DropdownMenu.RadioItem,
        {
          value: "system",
          onClick: (e) => {
            e.preventDefault();
            setTheme("system");
          },
          children: t22("app.menus.user.theme.system")
        }
      ),
      (0, import_jsx_runtime13.jsx)(
        DropdownMenu.RadioItem,
        {
          value: "light",
          onClick: (e) => {
            e.preventDefault();
            setTheme("light");
          },
          children: t22("app.menus.user.theme.light")
        }
      ),
      (0, import_jsx_runtime13.jsx)(
        DropdownMenu.RadioItem,
        {
          value: "dark",
          onClick: (e) => {
            e.preventDefault();
            setTheme("dark");
          },
          children: t22("app.menus.user.theme.dark")
        }
      )
    ] }) })
  ] });
};
var Logout = () => {
  const { t: t22 } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync: logoutMutation } = useLogout();
  const handleLogout = async () => {
    await logoutMutation(void 0, {
      onSuccess: () => {
        queryClient.clear();
        navigate("/login");
      }
    });
  };
  return (0, import_jsx_runtime13.jsx)(DropdownMenu.Item, { onClick: handleLogout, children: (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-2", children: [
    (0, import_jsx_runtime13.jsx)(OpenRectArrowOut, { className: "text-ui-fg-subtle" }),
    (0, import_jsx_runtime13.jsx)("span", { children: t22("app.menus.actions.logout") })
  ] }) });
};
var GlobalKeybindsModal = (props) => {
  const { t: t22 } = useTranslation();
  const globalShortcuts = useGlobalShortcuts();
  const [searchValue, onSearchValueChange] = (0, import_react24.useState)("");
  const searchResults = searchValue ? globalShortcuts.filter((shortcut) => {
    return shortcut.label.toLowerCase().includes(searchValue == null ? void 0 : searchValue.toLowerCase());
  }) : globalShortcuts;
  return (0, import_jsx_runtime13.jsx)(dist_exports2.Root, { ...props, children: (0, import_jsx_runtime13.jsxs)(dist_exports2.Portal, { children: [
    (0, import_jsx_runtime13.jsx)(dist_exports2.Overlay, { className: "bg-ui-bg-overlay fixed inset-0" }),
    (0, import_jsx_runtime13.jsxs)(dist_exports2.Content, { className: "bg-ui-bg-subtle shadow-elevation-modal fixed left-[50%] top-[50%] flex h-full max-h-[612px] w-full max-w-[560px] translate-x-[-50%] translate-y-[-50%] flex-col divide-y overflow-hidden rounded-lg", children: [
      (0, import_jsx_runtime13.jsxs)("div", { className: "flex flex-col gap-y-3 px-6 py-4", children: [
        (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center justify-between", children: [
          (0, import_jsx_runtime13.jsxs)("div", { children: [
            (0, import_jsx_runtime13.jsx)(dist_exports2.Title, { asChild: true, children: (0, import_jsx_runtime13.jsx)(Heading, { children: t22("app.menus.user.shortcuts") }) }),
            (0, import_jsx_runtime13.jsx)(dist_exports2.Description, { className: "sr-only" })
          ] }),
          (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-2", children: [
            (0, import_jsx_runtime13.jsx)(Kbd, { children: "esc" }),
            (0, import_jsx_runtime13.jsx)(dist_exports2.Close, { asChild: true, children: (0, import_jsx_runtime13.jsx)(IconButton, { variant: "transparent", size: "small", children: (0, import_jsx_runtime13.jsx)(XMark, {}) }) })
          ] })
        ] }),
        (0, import_jsx_runtime13.jsx)("div", { children: (0, import_jsx_runtime13.jsx)(
          Input,
          {
            type: "search",
            value: searchValue,
            onChange: (e) => onSearchValueChange(e.target.value)
          }
        ) })
      ] }),
      (0, import_jsx_runtime13.jsx)("div", { className: "flex flex-col divide-y overflow-y-auto", children: searchResults.map((shortcut, index) => {
        var _a2;
        return (0, import_jsx_runtime13.jsxs)(
          "div",
          {
            className: "text-ui-fg-subtle flex items-center justify-between px-6 py-3",
            children: [
              (0, import_jsx_runtime13.jsx)(Text, { size: "small", children: shortcut.label }),
              (0, import_jsx_runtime13.jsx)("div", { className: "flex items-center gap-x-1", children: (_a2 = shortcut.keys.Mac) == null ? void 0 : _a2.map((key, index2) => {
                var _a3;
                return (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-1", children: [
                  (0, import_jsx_runtime13.jsx)(Kbd, { children: key }),
                  index2 < (((_a3 = shortcut.keys.Mac) == null ? void 0 : _a3.length) || 0) - 1 && (0, import_jsx_runtime13.jsx)("span", { className: "txt-compact-xsmall text-ui-fg-subtle", children: t22("app.keyboardShortcuts.then") })
                ] }, index2);
              }) })
            ]
          },
          index
        );
      }) })
    ] })
  ] }) });
};
var UserItem = () => {
  const { user, isPending, isError, error } = useMe();
  const loaded = !isPending && !!user;
  if (!loaded) {
    return (0, import_jsx_runtime13.jsx)("div", {});
  }
  const name = [user.first_name, user.last_name].filter(Boolean).join(" ");
  const email = user.email;
  const fallback = name ? name[0].toUpperCase() : email[0].toUpperCase();
  const avatar = user.avatar_url;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-3 overflow-hidden px-2 py-1", children: [
    (0, import_jsx_runtime13.jsx)(
      Avatar,
      {
        size: "small",
        variant: "rounded",
        src: avatar || void 0,
        fallback
      }
    ),
    (0, import_jsx_runtime13.jsxs)("div", { className: "block w-full min-w-0 max-w-[187px] overflow-hidden whitespace-nowrap", children: [
      (0, import_jsx_runtime13.jsx)(
        Text,
        {
          size: "small",
          weight: "plus",
          leading: "compact",
          className: "overflow-hidden text-ellipsis whitespace-nowrap",
          children: name || email
        }
      ),
      !!name && (0, import_jsx_runtime13.jsx)(
        Text,
        {
          size: "xsmall",
          leading: "compact",
          className: "text-ui-fg-subtle overflow-hidden text-ellipsis whitespace-nowrap",
          children: email
        }
      )
    ] })
  ] });
};
var MainLayout = () => {
  return (0, import_jsx_runtime14.jsx)(Shell, { children: (0, import_jsx_runtime14.jsx)(MainSidebar, {}) });
};
var MainSidebar = () => {
  return (0, import_jsx_runtime14.jsx)("aside", { className: "flex flex-1 flex-col justify-between overflow-y-auto", children: (0, import_jsx_runtime14.jsxs)("div", { className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime14.jsxs)("div", { className: "bg-ui-bg-subtle sticky top-0", children: [
      (0, import_jsx_runtime14.jsx)(Header, {}),
      (0, import_jsx_runtime14.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime14.jsx)(Divider, { variant: "dashed" }) })
    ] }),
    (0, import_jsx_runtime14.jsxs)("div", { className: "flex flex-1 flex-col justify-between", children: [
      (0, import_jsx_runtime14.jsxs)("div", { className: "flex flex-1 flex-col", children: [
        (0, import_jsx_runtime14.jsx)(CoreRouteSection, {}),
        (0, import_jsx_runtime14.jsx)(ExtensionRouteSection, {})
      ] }),
      (0, import_jsx_runtime14.jsx)(UtilitySection, {})
    ] }),
    (0, import_jsx_runtime14.jsx)("div", { className: "bg-ui-bg-subtle sticky bottom-0", children: (0, import_jsx_runtime14.jsx)(UserSection, {}) })
  ] }) });
};
var Logout2 = () => {
  const { t: t22 } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync: logoutMutation } = useLogout();
  const handleLogout = async () => {
    await logoutMutation(void 0, {
      onSuccess: () => {
        queryClient.clear();
        navigate("/login");
      }
    });
  };
  return (0, import_jsx_runtime14.jsx)(DropdownMenu.Item, { onClick: handleLogout, children: (0, import_jsx_runtime14.jsxs)("div", { className: "flex items-center gap-x-2", children: [
    (0, import_jsx_runtime14.jsx)(OpenRectArrowOut, { className: "text-ui-fg-subtle" }),
    (0, import_jsx_runtime14.jsx)("span", { children: t22("app.menus.actions.logout") })
  ] }) });
};
var Header = () => {
  var _a2;
  const { t: t22 } = useTranslation();
  const { store, isPending, isError, error } = useStore();
  const name = store == null ? void 0 : store.name;
  const fallback = (_a2 = store == null ? void 0 : store.name) == null ? void 0 : _a2.slice(0, 1).toUpperCase();
  const isLoaded = !isPending && !!store && !!name && !!fallback;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime14.jsx)("div", { className: "w-full p-3", children: (0, import_jsx_runtime14.jsxs)(DropdownMenu, { children: [
    (0, import_jsx_runtime14.jsxs)(
      DropdownMenu.Trigger,
      {
        disabled: !isLoaded,
        className: clx(
          "bg-ui-bg-subtle transition-fg grid w-full grid-cols-[24px_1fr_15px] items-center gap-x-3 rounded-md p-0.5 pr-2 outline-none",
          "hover:bg-ui-bg-subtle-hover",
          "data-[state=open]:bg-ui-bg-subtle-hover",
          "focus-visible:shadow-borders-focus"
        ),
        children: [
          fallback ? (0, import_jsx_runtime14.jsx)(Avatar, { variant: "squared", size: "xsmall", fallback }) : (0, import_jsx_runtime14.jsx)(Skeleton, { className: "h-6 w-6 rounded-md" }),
          (0, import_jsx_runtime14.jsx)("div", { className: "block overflow-hidden text-left", children: name ? (0, import_jsx_runtime14.jsx)(
            Text,
            {
              size: "small",
              weight: "plus",
              leading: "compact",
              className: "truncate",
              children: store.name
            }
          ) : (0, import_jsx_runtime14.jsx)(Skeleton, { className: "h-[9px] w-[120px]" }) }),
          (0, import_jsx_runtime14.jsx)(EllipsisHorizontal, { className: "text-ui-fg-muted" })
        ]
      }
    ),
    isLoaded && (0, import_jsx_runtime14.jsxs)(DropdownMenu.Content, { className: "w-[var(--radix-dropdown-menu-trigger-width)] min-w-0", children: [
      (0, import_jsx_runtime14.jsxs)("div", { className: "flex items-center gap-x-3 px-2 py-1", children: [
        (0, import_jsx_runtime14.jsx)(Avatar, { variant: "squared", size: "small", fallback }),
        (0, import_jsx_runtime14.jsxs)("div", { className: "flex flex-col overflow-hidden", children: [
          (0, import_jsx_runtime14.jsx)(
            Text,
            {
              size: "small",
              weight: "plus",
              leading: "compact",
              className: "truncate",
              children: name
            }
          ),
          (0, import_jsx_runtime14.jsx)(
            Text,
            {
              size: "xsmall",
              leading: "compact",
              className: "text-ui-fg-subtle",
              children: t22("app.nav.main.store")
            }
          )
        ] })
      ] }),
      (0, import_jsx_runtime14.jsx)(DropdownMenu.Separator, {}),
      (0, import_jsx_runtime14.jsx)(DropdownMenu.Item, { className: "gap-x-2", asChild: true, children: (0, import_jsx_runtime14.jsxs)(Link, { to: "/settings/store", children: [
        (0, import_jsx_runtime14.jsx)(BuildingStorefront, { className: "text-ui-fg-subtle" }),
        t22("app.nav.main.storeSettings")
      ] }) }),
      (0, import_jsx_runtime14.jsx)(DropdownMenu.Separator, {}),
      (0, import_jsx_runtime14.jsx)(Logout2, {})
    ] })
  ] }) });
};
var useCoreRoutes = () => {
  const { t: t22 } = useTranslation();
  return [
    {
      icon: (0, import_jsx_runtime14.jsx)(ShoppingCart, {}),
      label: t22("orders.domain"),
      to: "/orders",
      items: [
        // TODO: Enable when domin is introduced
        // {
        //   label: t("draftOrders.domain"),
        //   to: "/draft-orders",
        // },
      ]
    },
    {
      icon: (0, import_jsx_runtime14.jsx)(Tag, {}),
      label: t22("products.domain"),
      to: "/products",
      items: [
        {
          label: t22("collections.domain"),
          to: "/collections"
        },
        {
          label: t22("categories.domain"),
          to: "/categories"
        }
        // TODO: Enable when domin is introduced
        // {
        //   label: t("giftCards.domain"),
        //   to: "/gift-cards",
        // },
      ]
    },
    {
      icon: (0, import_jsx_runtime14.jsx)(Buildings, {}),
      label: t22("inventory.domain"),
      to: "/inventory",
      items: [
        {
          label: t22("reservations.domain"),
          to: "/reservations"
        }
      ]
    },
    {
      icon: (0, import_jsx_runtime14.jsx)(Users, {}),
      label: t22("customers.domain"),
      to: "/customers",
      items: [
        {
          label: t22("customerGroups.domain"),
          to: "/customer-groups"
        }
      ]
    },
    {
      icon: (0, import_jsx_runtime14.jsx)(ReceiptPercent, {}),
      label: t22("promotions.domain"),
      to: "/promotions",
      items: [
        {
          label: t22("campaigns.domain"),
          to: "/campaigns"
        }
      ]
    },
    {
      icon: (0, import_jsx_runtime14.jsx)(CurrencyDollar, {}),
      label: t22("priceLists.domain"),
      to: "/price-lists"
    }
  ];
};
var Searchbar = () => {
  const { t: t22 } = useTranslation();
  const { toggleSearch } = useSearch();
  return (0, import_jsx_runtime14.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime14.jsxs)(
    "button",
    {
      onClick: toggleSearch,
      className: clx(
        "bg-ui-bg-subtle text-ui-fg-subtle flex w-full items-center gap-x-2.5 rounded-md px-2 py-1 outline-none",
        "hover:bg-ui-bg-subtle-hover",
        "focus-visible:shadow-borders-focus"
      ),
      children: [
        (0, import_jsx_runtime14.jsx)(MagnifyingGlass, {}),
        (0, import_jsx_runtime14.jsx)("div", { className: "flex-1 text-left", children: (0, import_jsx_runtime14.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t22("app.search.label") }) }),
        (0, import_jsx_runtime14.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: "⌘K" })
      ]
    }
  ) });
};
var CoreRouteSection = () => {
  const coreRoutes = useCoreRoutes();
  const { getMenu } = useExtension();
  const menuItems = getMenu("coreExtensions");
  menuItems.forEach((item) => {
    var _a2;
    if (item.nested) {
      const route = coreRoutes.find((route2) => route2.to === item.nested);
      if (route) {
        (_a2 = route.items) == null ? void 0 : _a2.push(item);
      }
    }
  });
  return (0, import_jsx_runtime14.jsxs)("nav", { className: "flex flex-col gap-y-1 py-3", children: [
    (0, import_jsx_runtime14.jsx)(Searchbar, {}),
    coreRoutes.map((route) => {
      return (0, import_jsx_runtime14.jsx)(NavItem, { ...route }, route.to);
    })
  ] });
};
var ExtensionRouteSection = () => {
  const { t: t22 } = useTranslation();
  const { getMenu } = useExtension();
  const menuItems = getMenu("coreExtensions").filter((item) => !item.nested);
  if (!menuItems.length) {
    return null;
  }
  return (0, import_jsx_runtime14.jsxs)("div", { children: [
    (0, import_jsx_runtime14.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime14.jsx)(Divider, { variant: "dashed" }) }),
    (0, import_jsx_runtime14.jsx)("div", { className: "flex flex-col gap-y-1 py-3", children: (0, import_jsx_runtime14.jsxs)(dist_exports.Root, { defaultOpen: true, children: [
      (0, import_jsx_runtime14.jsx)("div", { className: "px-4", children: (0, import_jsx_runtime14.jsx)(dist_exports.Trigger, { asChild: true, className: "group/trigger", children: (0, import_jsx_runtime14.jsxs)("button", { className: "text-ui-fg-subtle flex w-full items-center justify-between px-2", children: [
        (0, import_jsx_runtime14.jsx)(Text, { size: "xsmall", weight: "plus", leading: "compact", children: t22("app.nav.common.extensions") }),
        (0, import_jsx_runtime14.jsxs)("div", { className: "text-ui-fg-muted", children: [
          (0, import_jsx_runtime14.jsx)(ChevronDownMini, { className: "group-data-[state=open]/trigger:hidden" }),
          (0, import_jsx_runtime14.jsx)(MinusMini, { className: "group-data-[state=closed]/trigger:hidden" })
        ] })
      ] }) }) }),
      (0, import_jsx_runtime14.jsx)(dist_exports.Content, { children: (0, import_jsx_runtime14.jsx)("nav", { className: "flex flex-col gap-y-0.5 py-1 pb-4", children: menuItems.map((item, i) => {
        return (0, import_jsx_runtime14.jsx)(
          NavItem,
          {
            to: item.to,
            label: item.label,
            icon: item.icon ? item.icon : (0, import_jsx_runtime14.jsx)(SquaresPlus, {}),
            items: item.items,
            type: "extension"
          },
          i
        );
      }) }) })
    ] }) })
  ] });
};
var UtilitySection = () => {
  const location = useLocation();
  const { t: t22 } = useTranslation();
  return (0, import_jsx_runtime14.jsx)("div", { className: "flex flex-col gap-y-0.5 py-3", children: (0, import_jsx_runtime14.jsx)(
    NavItem,
    {
      label: t22("app.nav.settings.header"),
      to: "/settings",
      from: location.pathname,
      icon: (0, import_jsx_runtime14.jsx)(CogSixTooth, {})
    }
  ) });
};
var UserSection = () => {
  return (0, import_jsx_runtime14.jsxs)("div", { children: [
    (0, import_jsx_runtime14.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime14.jsx)(Divider, { variant: "dashed" }) }),
    (0, import_jsx_runtime14.jsx)(UserMenu, {})
  ] });
};
var PublicLayout = () => {
  return (0, import_jsx_runtime15.jsx)(Outlet, {});
};
var SettingsLayout = () => {
  return (0, import_jsx_runtime16.jsx)(Shell, { children: (0, import_jsx_runtime16.jsx)(SettingsSidebar, {}) });
};
var useSettingRoutes = () => {
  const { t: t22 } = useTranslation();
  return (0, import_react25.useMemo)(
    () => [
      {
        label: t22("store.domain"),
        to: "/settings/store"
      },
      {
        label: t22("users.domain"),
        to: "/settings/users"
      },
      {
        label: t22("regions.domain"),
        to: "/settings/regions"
      },
      {
        label: t22("taxRegions.domain"),
        to: "/settings/tax-regions"
      },
      {
        label: t22("returnReasons.domain"),
        to: "/settings/return-reasons"
      },
      {
        label: t22("salesChannels.domain"),
        to: "/settings/sales-channels"
      },
      {
        label: t22("productTypes.domain"),
        to: "/settings/product-types"
      },
      {
        label: t22("productTags.domain"),
        to: "/settings/product-tags"
      },
      {
        label: t22("stockLocations.domain"),
        to: "/settings/locations"
      }
    ],
    [t22]
  );
};
var useDeveloperRoutes = () => {
  const { t: t22 } = useTranslation();
  return (0, import_react25.useMemo)(
    () => [
      {
        label: t22("apiKeyManagement.domain.publishable"),
        to: "/settings/publishable-api-keys"
      },
      {
        label: t22("apiKeyManagement.domain.secret"),
        to: "/settings/secret-api-keys"
      },
      {
        label: t22("workflowExecutions.domain"),
        to: "/settings/workflows"
      }
    ],
    [t22]
  );
};
var useMyAccountRoutes = () => {
  const { t: t22 } = useTranslation();
  return (0, import_react25.useMemo)(
    () => [
      {
        label: t22("profile.domain"),
        to: "/settings/profile"
      }
    ],
    [t22]
  );
};
var getSafeFromValue = (from) => {
  if (from.startsWith("/settings")) {
    return "/orders";
  }
  return from;
};
var SettingsSidebar = () => {
  const { getMenu } = useExtension();
  const routes = useSettingRoutes();
  const developerRoutes = useDeveloperRoutes();
  const myAccountRoutes = useMyAccountRoutes();
  const extensionRoutes = getMenu("settingsExtensions");
  const { t: t22 } = useTranslation();
  return (0, import_jsx_runtime16.jsxs)("aside", { className: "relative flex flex-1 flex-col justify-between overflow-y-auto", children: [
    (0, import_jsx_runtime16.jsxs)("div", { className: "bg-ui-bg-subtle sticky top-0", children: [
      (0, import_jsx_runtime16.jsx)(Header2, {}),
      (0, import_jsx_runtime16.jsx)("div", { className: "flex items-center justify-center px-3", children: (0, import_jsx_runtime16.jsx)(Divider, { variant: "dashed" }) })
    ] }),
    (0, import_jsx_runtime16.jsxs)("div", { className: "flex flex-1 flex-col", children: [
      (0, import_jsx_runtime16.jsxs)("div", { className: "flex flex-1 flex-col overflow-y-auto", children: [
        (0, import_jsx_runtime16.jsx)(
          RadixCollapsibleSection,
          {
            label: t22("app.nav.settings.general"),
            items: routes
          }
        ),
        (0, import_jsx_runtime16.jsx)("div", { className: "flex items-center justify-center px-3", children: (0, import_jsx_runtime16.jsx)(Divider, { variant: "dashed" }) }),
        (0, import_jsx_runtime16.jsx)(
          RadixCollapsibleSection,
          {
            label: t22("app.nav.settings.developer"),
            items: developerRoutes
          }
        ),
        (0, import_jsx_runtime16.jsx)("div", { className: "flex items-center justify-center px-3", children: (0, import_jsx_runtime16.jsx)(Divider, { variant: "dashed" }) }),
        (0, import_jsx_runtime16.jsx)(
          RadixCollapsibleSection,
          {
            label: t22("app.nav.settings.myAccount"),
            items: myAccountRoutes
          }
        ),
        extensionRoutes.length > 0 && (0, import_jsx_runtime16.jsxs)(import_react25.Fragment, { children: [
          (0, import_jsx_runtime16.jsx)("div", { className: "flex items-center justify-center px-3", children: (0, import_jsx_runtime16.jsx)(Divider, { variant: "dashed" }) }),
          (0, import_jsx_runtime16.jsx)(
            RadixCollapsibleSection,
            {
              label: t22("app.nav.common.extensions"),
              items: extensionRoutes
            }
          )
        ] })
      ] }),
      (0, import_jsx_runtime16.jsx)("div", { className: "bg-ui-bg-subtle sticky bottom-0", children: (0, import_jsx_runtime16.jsx)(UserSection2, {}) })
    ] })
  ] });
};
var Header2 = () => {
  const [from, setFrom] = (0, import_react25.useState)("/orders");
  const { t: t22 } = useTranslation();
  const location = useLocation();
  (0, import_react25.useEffect)(() => {
    var _a2;
    if ((_a2 = location.state) == null ? void 0 : _a2.from) {
      setFrom(getSafeFromValue(location.state.from));
    }
  }, [location]);
  return (0, import_jsx_runtime16.jsx)("div", { className: "bg-ui-bg-subtle p-3", children: (0, import_jsx_runtime16.jsx)(
    Link,
    {
      to: from,
      replace: true,
      className: clx(
        "bg-ui-bg-subtle transition-fg flex items-center rounded-md outline-none",
        "hover:bg-ui-bg-subtle-hover",
        "focus-visible:shadow-borders-focus"
      ),
      children: (0, import_jsx_runtime16.jsxs)("div", { className: "flex items-center gap-x-2.5 px-2 py-1", children: [
        (0, import_jsx_runtime16.jsx)("div", { className: "flex items-center justify-center", children: (0, import_jsx_runtime16.jsx)(ArrowUturnLeft, { className: "text-ui-fg-subtle" }) }),
        (0, import_jsx_runtime16.jsx)(Text, { leading: "compact", weight: "plus", size: "small", children: t22("app.nav.settings.header") })
      ] })
    }
  ) });
};
var RadixCollapsibleSection = ({
  label,
  items
}) => {
  return (0, import_jsx_runtime16.jsxs)(dist_exports.Root, { defaultOpen: true, className: "py-3", children: [
    (0, import_jsx_runtime16.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime16.jsxs)("div", { className: "text-ui-fg-muted flex h-7 items-center justify-between px-2", children: [
      (0, import_jsx_runtime16.jsx)(Text, { size: "small", leading: "compact", children: label }),
      (0, import_jsx_runtime16.jsx)(dist_exports.Trigger, { asChild: true, children: (0, import_jsx_runtime16.jsx)(IconButton, { size: "2xsmall", variant: "transparent", className: "static", children: (0, import_jsx_runtime16.jsx)(MinusMini, { className: "text-ui-fg-muted" }) }) })
    ] }) }),
    (0, import_jsx_runtime16.jsx)(dist_exports.Content, { children: (0, import_jsx_runtime16.jsx)("div", { className: "pt-0.5", children: (0, import_jsx_runtime16.jsx)("nav", { className: "flex flex-col gap-y-0.5", children: items.map((setting) => (0, import_jsx_runtime16.jsx)(NavItem, { type: "setting", ...setting }, setting.to)) }) }) })
  ] });
};
var UserSection2 = () => {
  return (0, import_jsx_runtime16.jsxs)("div", { children: [
    (0, import_jsx_runtime16.jsx)("div", { className: "px-3", children: (0, import_jsx_runtime16.jsx)(Divider, { variant: "dashed" }) }),
    (0, import_jsx_runtime16.jsx)(UserMenu, {})
  ] });
};
var ErrorBoundary = () => {
  const error = useRouteError();
  const location = useLocation();
  const { t: t22 } = useTranslation();
  let code = null;
  if (isFetchError(error)) {
    if (error.status === 401) {
      return (0, import_jsx_runtime17.jsx)(Navigate, { to: "/login", state: { from: location }, replace: true });
    }
    code = error.status ?? null;
  }
  if (true) {
    console.error(error);
  }
  let title;
  let message;
  switch (code) {
    case 400:
      title = t22("errorBoundary.badRequestTitle");
      message = t22("errorBoundary.badRequestMessage");
      break;
    case 404:
      title = t22("errorBoundary.notFoundTitle");
      message = t22("errorBoundary.notFoundMessage");
      break;
    case 500:
      title = t22("errorBoundary.internalServerErrorTitle");
      message = t22("errorBoundary.internalServerErrorMessage");
      break;
    default:
      title = t22("errorBoundary.defaultTitle");
      message = t22("errorBoundary.defaultMessage");
      break;
  }
  return (0, import_jsx_runtime17.jsx)("div", { className: "flex size-full min-h-[calc(100vh-57px-24px)] items-center justify-center", children: (0, import_jsx_runtime17.jsx)("div", { className: "flex flex-col gap-y-6", children: (0, import_jsx_runtime17.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col items-center gap-y-3", children: [
    (0, import_jsx_runtime17.jsx)(ExclamationCircle, {}),
    (0, import_jsx_runtime17.jsxs)("div", { className: "flex flex-col items-center justify-center gap-y-1", children: [
      (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title }),
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          size: "small",
          className: "text-ui-fg-muted text-balance text-center",
          children: message
        }
      )
    ] })
  ] }) }) });
};
function getRouteMap({
  settingsRoutes,
  coreRoutes
}) {
  var _a2;
  return [
    {
      element: (0, import_jsx_runtime18.jsx)(ProtectedRoute, {}),
      errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
      children: [
        {
          element: (0, import_jsx_runtime18.jsx)(MainLayout, {}),
          children: [
            {
              path: "/",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              lazy: () => import("./home-KSB2J7CS-O2DLHN4P.js")
            },
            {
              path: "/products",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("products.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./product-list-TFAHMX6Y-ERB76JCO.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./product-create-BUYUMGUF-FIVJA7RB.js")
                    },
                    {
                      path: "import",
                      lazy: () => import("./product-import-JCEJKM3F-BOYGDT3G.js")
                    },
                    {
                      path: "export",
                      lazy: () => import("./product-export-LEBUXAVA-SZSDJRWO.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
                  lazy: async () => {
                    const { Breadcrumb, loader } = await import("./product-detail-TE5NUPNS-K5Q4ZNQR.js");
                    return {
                      Component: Outlet,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "",
                      lazy: () => import("./product-detail-TE5NUPNS-K5Q4ZNQR.js"),
                      children: [
                        {
                          path: "edit",
                          lazy: () => import("./product-edit-PRTPBOY3-FQFENLLF.js")
                        },
                        {
                          path: "edit-variant",
                          lazy: () => import("./product-variant-edit-G6AM27AT-BYW4XZ52.js")
                        },
                        {
                          path: "sales-channels",
                          lazy: () => import("./product-sales-channels-D3EBY5FT-EXZGCR6V.js")
                        },
                        {
                          path: "attributes",
                          lazy: () => import("./product-attributes-ECCWXVSQ-UEV6ADGS.js")
                        },
                        {
                          path: "organization",
                          lazy: () => import("./product-organization-MZWNCGEA-7YFP57NM.js")
                        },
                        {
                          path: "shipping-profile",
                          lazy: () => import("./product-shipping-profile-QA3Q4N5Q-JDWJHA2L.js")
                        },
                        {
                          path: "media",
                          lazy: () => import("./product-media-2WJUTP6J-LWYD2O4O.js")
                        },
                        {
                          path: "prices",
                          lazy: () => import("./product-prices-BBTCM3XR-TLCYX2PP.js")
                        },
                        {
                          path: "options/create",
                          lazy: () => import("./product-create-option-E2GRTBWP-7JORX2OT.js")
                        },
                        {
                          path: "options/:option_id/edit",
                          lazy: () => import("./product-edit-option-RHMF3YKT-F24MMIY3.js")
                        },
                        {
                          path: "variants/create",
                          lazy: () => import("./product-create-variant-3B32EC76-WDEQP356.js")
                        },
                        {
                          path: "stock",
                          lazy: () => import("./product-stock-TZS75EG7-BM25QJCF.js")
                        },
                        {
                          path: "metadata/edit",
                          lazy: () => import("./product-metadata-PQQ7XV63-SOWXRYTC.js")
                        }
                      ]
                    },
                    {
                      path: "variants/:variant_id",
                      lazy: async () => {
                        const { Component: Component4, Breadcrumb, loader } = await import("./product-variant-detail-DLMDM6GW-Y6FJN6XX.js");
                        return {
                          Component: Component4,
                          loader,
                          handle: {
                            breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                          }
                        };
                      },
                      children: [
                        {
                          path: "edit",
                          lazy: () => import("./product-variant-edit-G6AM27AT-BYW4XZ52.js")
                        },
                        {
                          path: "prices",
                          lazy: () => import("./product-prices-BBTCM3XR-TLCYX2PP.js")
                        },
                        {
                          path: "manage-items",
                          lazy: () => import("./product-variant-manage-inventory-items-SNLRM4XR-5OD3XOV6.js")
                        },
                        {
                          path: "metadata/edit",
                          lazy: () => import("./product-variant-metadata-2V27ZMDS-SJ4PQXOQ.js")
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              path: "/categories",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("categories.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./category-list-VKQRZO2Z-Y6K3QNGS.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./category-create-NVRG7B6E-KKXZYVGH.js")
                    },
                    {
                      path: "organize",
                      lazy: () => import("./category-organize-HUXXVJAB-UMMNX3CQ.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./category-detail-BDPQPVQS-TWYNN3T2.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./category-edit-JBZQKNL7-PJCWM6R5.js")
                    },
                    {
                      path: "products",
                      lazy: () => import("./category-products-CC2XWKZI-DBUYB4ZO.js")
                    },
                    {
                      path: "organize",
                      lazy: () => import("./category-organize-HUXXVJAB-UMMNX3CQ.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./categories-metadata-TE7B2OJY-CKKGTI7Q.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/orders",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("orders.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./order-list-WVNLOXW6-HZY4AZQ4.js")
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./order-detail-X5WN4KCV-CLDVPHE4.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "fulfillment",
                      lazy: () => import("./order-create-fulfillment-62OZHL3L-7HHKPYRU.js")
                    },
                    {
                      path: "returns/:return_id/receive",
                      lazy: () => import("./order-receive-return-AXAZNUIB-KSZLAG2O.js")
                    },
                    {
                      path: "allocate-items",
                      lazy: () => import("./order-allocate-items-TMQ5M7TG-4RHK62Q2.js")
                    },
                    {
                      path: ":f_id/create-shipment",
                      lazy: () => import("./order-create-shipment-FQNPWRSW-7YCQE4PI.js")
                    },
                    {
                      path: "returns",
                      lazy: () => import("./order-create-return-AR6OS5JB-IIOXSZVM.js")
                    },
                    {
                      path: "claims",
                      lazy: () => import("./order-create-claim-JF6DURDF-W3AMDZ5H.js")
                    },
                    {
                      path: "exchanges",
                      lazy: () => import("./order-create-exchange-CYHV4UHV-3HWIV4HS.js")
                    },
                    {
                      path: "edits",
                      lazy: () => import("./order-create-edit-DKT5Y5IZ-NKWZYUOS.js")
                    },
                    {
                      path: "refund",
                      lazy: () => import("./order-create-refund-FXUURAS7-3KIQ6O2D.js")
                    },
                    {
                      path: "transfer",
                      lazy: () => import("./order-request-transfer-OZNQQL3N-QSFZN6XG.js")
                    },
                    {
                      path: "email",
                      lazy: () => import("./order-edit-email-V4DC4GJV-FHIQ3QYB.js")
                    },
                    {
                      path: "shipping-address",
                      lazy: () => import("./order-edit-shipping-address-NWT5LDKC-ICNS3NPC.js")
                    },
                    {
                      path: "billing-address",
                      lazy: () => import("./order-edit-billing-address-XDNORAHG-FVUTHOGK.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./order-metadata-4YNV2NWH-NLWDWNDZ.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/promotions",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("promotions.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./promotion-list-3DE2ZW3V-SJHINRTG.js")
                },
                {
                  path: "create",
                  lazy: () => import("./promotion-create-PLDWDZMZ-LEZB7XVN.js")
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./promotion-detail-Z246ADDL-MM2VIQWI.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./promotion-edit-details-D6M646E6-5SGNVZ7V.js")
                    },
                    {
                      path: "add-to-campaign",
                      lazy: () => import("./promotion-add-campaign-4VNRQF52-C6H5A2PH.js")
                    },
                    {
                      path: ":ruleType/edit",
                      lazy: () => import("./edit-rules-7MMNZKEQ-CXZZN7TV.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/campaigns",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("campaigns.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./campaign-list-JN5XQRQD-MMERL2QN.js"),
                  children: []
                },
                {
                  path: "create",
                  lazy: () => import("./campaign-create-NYJ4HILG-3SHC6UAS.js")
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./campaign-detail-Z33NIWPP-GIBLK2OZ.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./campaign-edit-3D5WFWCT-NHYRFWAT.js")
                    },
                    {
                      path: "configuration",
                      lazy: () => import("./campaign-configuration-QV3OHWBZ-3ZH2XQSG.js")
                    },
                    {
                      path: "edit-budget",
                      lazy: () => import("./campaign-budget-edit-57NSOYJ3-CT6NQ45X.js")
                    },
                    {
                      path: "add-promotions",
                      lazy: () => import("./add-campaign-promotions-FK5IDNEX-VTKEFCYZ.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/collections",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("collections.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./collection-list-AAZMTNDX-2ZBEBZWP.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./collection-create-PXO6DJS3-GLDZYYOE.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./collection-detail-K3K4YDZF-T6TVP5M6.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./collection-edit-D7OGD4MC-D3EFLEAD.js")
                    },
                    {
                      path: "products",
                      lazy: () => import("./collection-add-products-WH6YVTZ3-VZLBZFNW.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./collection-metadata-244B7424-YG74OLOO.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/price-lists",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("priceLists.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./price-list-list-BIZRM5LJ-UY4VANZP.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./price-list-create-AFGEP5GO-UEU6YVZA.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./price-list-detail-LH5ZLGFY-PJGMITDI.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./price-list-edit-TMZCXEFR-IME73A5B.js")
                    },
                    {
                      path: "configuration",
                      lazy: () => import("./price-list-configuration-HVIVUV7C-IFGTBD66.js")
                    },
                    {
                      path: "products/add",
                      lazy: () => import("./price-list-prices-add-DXKYOSSV-JRM2FCX4.js")
                    },
                    {
                      path: "products/edit",
                      lazy: () => import("./price-list-prices-edit-JUS5FBBN-NB2JYLN4.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/customers",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("customers.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./customer-list-VNZUNUOQ-S6JVESTT.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./customer-create-TKKVS4LF-ZLGFYZET.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./customer-detail-IUH5X7RO-WDIELO4G.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./customer-edit-I4K3LK66-XAPJPELE.js")
                    },
                    {
                      path: "create-address",
                      lazy: () => import("./customer-create-address-P4YQFUI5-MLFXDHTX.js")
                    },
                    {
                      path: "add-customer-groups",
                      lazy: () => import("./customers-add-customer-group-53TCMHNE-ZYKPXBKZ.js")
                    },
                    {
                      path: ":order_id/transfer",
                      lazy: () => import("./order-request-transfer-OZNQQL3N-QSFZN6XG.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./customer-metadata-TPIFKFIQ-YUFXC6TJ.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/customer-groups",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("customerGroups.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./customer-group-list-WKCYXNS2-SKNG5JQA.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./customer-group-create-5EILGGLH-NUV7SVYE.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./customer-group-detail-VRK37A7I-GYP2QGTS.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./customer-group-edit-NBVNSBMX-HSLO4SBG.js")
                    },
                    {
                      path: "add-customers",
                      lazy: () => import("./customer-group-add-customers-G7G2JYJI-HVEN4GIY.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./customer-group-metadata-IRTI56TA-PVYG6EOE.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/reservations",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("reservations.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./reservation-list-Y4HV7WW3-SHXVU65U.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./reservation-create-INUIVD55-DRIQB4SJ.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./reservation-detail-QKUCAH7D-E5KYQLSY.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./edit-reservation-GEJHV6OI-HDVU2ZKJ.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./reservation-metadata-5ZABNMJP-OPWMAV72.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "/inventory",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              handle: {
                breadcrumb: () => t("inventory.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./inventory-list-T7IXNYBT-EYOJH4C3.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./inventory-create-NZ67QMNB-UKEACQJE.js")
                    },
                    {
                      path: "stock",
                      lazy: () => import("./inventory-stock-XZWLARFE-DBSGZKFY.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./inventory-detail-FV2MBKBO-YTBZEE72.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./edit-inventory-item-KQUKSOGO-J2ES5IEN.js")
                    },
                    {
                      path: "attributes",
                      lazy: () => import("./edit-inventory-item-attributes-I55BRBWC-ZLNBRVLZ.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./inventory-metadata-AG5IVFBR-7KWBPRJM.js")
                    },
                    {
                      path: "locations",
                      lazy: () => import("./manage-locations-E7YAZVH2-MLN53L56.js")
                    },
                    {
                      path: "locations/:location_id",
                      lazy: () => import("./adjust-inventory-WYNH75F7-DCRXPOQC.js")
                    }
                  ]
                }
              ]
            },
            ...coreRoutes
          ]
        }
      ]
    },
    {
      element: (0, import_jsx_runtime18.jsx)(ProtectedRoute, {}),
      errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
      children: [
        {
          path: "/settings",
          handle: {
            breadcrumb: () => t("app.nav.settings.header")
          },
          element: (0, import_jsx_runtime18.jsx)(SettingsLayout, {}),
          children: [
            {
              index: true,
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              lazy: () => import("./settings-FQUPXMWF-4RHFQ65S.js")
            },
            {
              path: "profile",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              lazy: () => import("./profile-detail-VIBGPLIH-GHUYMKQZ.js"),
              handle: {
                breadcrumb: () => t("profile.domain")
              },
              children: [
                {
                  path: "edit",
                  lazy: () => import("./profile-edit-XWULVWP5-NN35JTUA.js")
                }
              ]
            },
            {
              path: "regions",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("regions.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./region-list-Q3OBP3NA-LXL4ZI7P.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./region-create-QZHI2RZP-RFTWYDVG.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./region-detail-2VHXUCKC-MZBOBLIH.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./region-edit-PXIMLBFB-U37L3OLS.js")
                    },
                    {
                      path: "countries/add",
                      lazy: () => import("./region-add-countries-IE5I4ECQ-UIIZAN6R.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./region-metadata-4S6FEUXA-QUG4GBHB.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "store",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              lazy: () => import("./store-detail-2BU2OXG3-MA5KPISG.js"),
              handle: {
                breadcrumb: () => t("store.domain")
              },
              children: [
                {
                  path: "edit",
                  lazy: () => import("./store-edit-JNSWFWIG-PWD5M4NA.js")
                },
                {
                  path: "currencies",
                  lazy: () => import("./store-add-currencies-H6UYWWGQ-XZ247GAX.js")
                },
                {
                  path: "metadata/edit",
                  lazy: () => import("./store-metadata-GLAZZPP6-TBCDH2LJ.js")
                }
              ]
            },
            {
              path: "users",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("users.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./user-list-2YEFZXDY-5XHWJ6D2.js"),
                  children: [
                    {
                      path: "invite",
                      lazy: () => import("./user-invite-AELM6U5F-4J22IWTE.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./user-detail-X2Z2IYNI-XAFT4LTR.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./user-edit-HFSJM4QT-CJIUYDBY.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./user-metadata-GJ6PIXLU-222ZL7P6.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "sales-channels",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("salesChannels.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./sales-channel-list-WMLELPSX-JRTG5DZP.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./sales-channel-create-HVZPII6E-G7VZ44LX.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./sales-channel-detail-WWBHQZF5-O6HJCLBG.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./sales-channel-edit-BK7O3ZFI-7KNRJPPU.js")
                    },
                    {
                      path: "add-products",
                      lazy: () => import("./sales-channel-add-products-INZY2JPI-XHL2XL7Y.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./sales-channel-metadata-UH4QYASI-T6EWY23J.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "locations",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("locations.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./location-list-5UJ6CSA7-REHO7KDP.js")
                },
                {
                  path: "create",
                  lazy: () => import("./location-create-G3CYBYOF-KGZIVRCB.js")
                },
                {
                  path: "shipping-profiles",
                  element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
                  handle: {
                    breadcrumb: () => t("shippingProfile.domain")
                  },
                  children: [
                    {
                      path: "",
                      lazy: () => import("./shipping-profiles-list-LPWA6QGJ-ESBO2GV4.js"),
                      children: [
                        {
                          path: "create",
                          lazy: () => import("./shipping-profile-create-PBDONCGJ-QOCFRO6E.js")
                        }
                      ]
                    },
                    {
                      path: ":shipping_profile_id",
                      lazy: async () => {
                        const { Component: Component4, Breadcrumb, loader } = await import("./shipping-profile-detail-EORJZ7NX-QR3OM5MC.js");
                        return {
                          Component: Component4,
                          loader,
                          handle: {
                            breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                          }
                        };
                      },
                      children: [
                        {
                          path: "metadata/edit",
                          lazy: () => import("./shipping-profile-metadata-UB73OBPJ-TFQ7LLLQ.js")
                        }
                      ]
                    }
                  ]
                },
                {
                  path: ":location_id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./location-detail-GHY3DXRP-ABBG46TU.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./location-edit-IMBG32Z4-S4B4FSP2.js")
                    },
                    {
                      path: "sales-channels",
                      lazy: () => import("./location-sales-channels-CTFDHOCO-EZLKLTU2.js")
                    },
                    {
                      path: "fulfillment-providers",
                      lazy: () => import("./location-fulfillment-providers-UOJUZDYF-ZZIGZ2DV.js")
                    },
                    {
                      path: "fulfillment-set/:fset_id",
                      children: [
                        {
                          path: "service-zones/create",
                          lazy: () => import("./location-service-zone-create-JE3JZUYG-J5CARB2I.js")
                        },
                        {
                          path: "service-zone/:zone_id",
                          children: [
                            {
                              path: "edit",
                              lazy: () => import("./location-service-zone-edit-W3G7XOC7-OXU3IS6I.js")
                            },
                            {
                              path: "areas",
                              lazy: () => import("./location-service-zone-manage-areas-FBMF6VLN-TJ5O72AL.js")
                            },
                            {
                              path: "shipping-option",
                              children: [
                                {
                                  path: "create",
                                  lazy: () => import("./location-service-zone-shipping-option-create-7KOH5VVW-4OX76HPV.js")
                                },
                                {
                                  path: ":so_id",
                                  children: [
                                    {
                                      path: "edit",
                                      lazy: () => import("./location-service-zone-shipping-option-edit-ODUEKZON-ALL42FO4.js")
                                    },
                                    {
                                      path: "pricing",
                                      lazy: () => import("./location-service-zone-shipping-option-pricing-SKUSE5YE-5CCSC6UT.js")
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              path: "product-tags",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("productTags.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./product-tag-list-4CCXX7II-5UNJ2HPW.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./product-tag-create-LE4MZ5BW-VOY7T7M5.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./product-tag-detail-DEEKOXLB-M6G2UOUR.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./product-tag-edit-E6WEFNFQ-KL53DIC7.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "workflows",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("workflowExecutions.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./workflow-execution-list-SFG64TZT-RMSIAPEJ.js")
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./workflow-execution-detail-D26MDYFC-AGSCIMLE.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  }
                }
              ]
            },
            {
              path: "product-types",
              errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("productTypes.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./product-type-list-EKTGZP6P-SOYDA2LO.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./product-type-create-GU5IYXQB-NTUF7VA7.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./product-type-detail-2VJPHLCB-2EDH4ARG.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./product-type-edit-S53VUBU3-JSWFFIDH.js")
                    },
                    {
                      path: "metadata/edit",
                      lazy: () => import("./product-type-metadata-P4ZKCJW5-COREXCZ7.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "publishable-api-keys",
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("apiKeyManagement.domain.publishable")
              },
              children: [
                {
                  path: "",
                  element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
                  children: [
                    {
                      path: "",
                      lazy: () => import("./api-key-management-list-BRA23MJT-LPSLOC5A.js"),
                      children: [
                        {
                          path: "create",
                          lazy: () => import("./api-key-management-create-HWBQGG6O-BE7KPMSR.js")
                        }
                      ]
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./api-key-management-detail-3ZWHEQY6-6OPOMACC.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./api-key-management-edit-UX47FHWV-YEO4JZGT.js")
                    },
                    {
                      path: "sales-channels",
                      lazy: () => import("./api-key-management-sales-channels-OMIWIBUF-2Q42R2OZ.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "secret-api-keys",
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("apiKeyManagement.domain.secret")
              },
              children: [
                {
                  path: "",
                  element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
                  children: [
                    {
                      path: "",
                      lazy: () => import("./api-key-management-list-BRA23MJT-LPSLOC5A.js"),
                      children: [
                        {
                          path: "create",
                          lazy: () => import("./api-key-management-create-HWBQGG6O-BE7KPMSR.js")
                        }
                      ]
                    }
                  ]
                },
                {
                  path: ":id",
                  lazy: async () => {
                    const { Component: Component4, Breadcrumb, loader } = await import("./api-key-management-detail-3ZWHEQY6-6OPOMACC.js");
                    return {
                      Component: Component4,
                      loader,
                      handle: {
                        breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                      }
                    };
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: () => import("./api-key-management-edit-UX47FHWV-YEO4JZGT.js")
                    }
                  ]
                }
              ]
            },
            {
              path: "tax-regions",
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("taxRegions.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./tax-region-list-FNVPRMTQ-SZURTFYW.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./tax-region-create-EW5USMNY-NBY47L74.js")
                    }
                  ]
                },
                {
                  path: ":id",
                  Component: Outlet,
                  loader: taxRegionLoader,
                  handle: {
                    breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(TaxRegionDetailBreadcrumb, { ...match })
                  },
                  children: [
                    {
                      path: "",
                      lazy: async () => {
                        const { Component: Component4 } = await import("./tax-region-detail-MVSV7BWW-GIO2UJXV.js");
                        return {
                          Component: Component4
                        };
                      },
                      children: [
                        {
                          path: "provinces/create",
                          lazy: () => import("./tax-region-province-create-LQ5SPSXW-YFZW2LEV.js")
                        },
                        {
                          path: "overrides/create",
                          lazy: () => import("./tax-region-tax-override-create-QEPYO6QH-N52KM7CV.js")
                        },
                        {
                          path: "overrides/:tax_rate_id/edit",
                          lazy: () => import("./tax-region-tax-override-edit-6JSD4HED-DYPC5VEO.js")
                        },
                        {
                          path: "tax-rates/create",
                          lazy: () => import("./tax-region-tax-rate-create-7GNLSDJ2-BHB3KXNK.js")
                        },
                        {
                          path: "tax-rates/:tax_rate_id/edit",
                          lazy: () => import("./tax-region-tax-rate-edit-6U2BQMIE-LGYHMHJ6.js")
                        }
                      ]
                    },
                    {
                      path: "provinces/:province_id",
                      lazy: async () => {
                        const { Component: Component4, Breadcrumb, loader } = await import("./tax-region-province-detail-4G2WUUBB-YXO3PLUX.js");
                        return {
                          Component: Component4,
                          loader,
                          handle: {
                            breadcrumb: (match) => (0, import_jsx_runtime18.jsx)(Breadcrumb, { ...match })
                          }
                        };
                      },
                      children: [
                        {
                          path: "tax-rates/create",
                          lazy: () => import("./tax-region-tax-rate-create-7GNLSDJ2-BHB3KXNK.js")
                        },
                        {
                          path: "tax-rates/:tax_rate_id/edit",
                          lazy: () => import("./tax-region-tax-rate-edit-6U2BQMIE-LGYHMHJ6.js")
                        },
                        {
                          path: "overrides/create",
                          lazy: () => import("./tax-region-tax-override-create-QEPYO6QH-N52KM7CV.js")
                        },
                        {
                          path: "overrides/:tax_rate_id/edit",
                          lazy: () => import("./tax-region-tax-override-edit-6JSD4HED-DYPC5VEO.js")
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              path: "return-reasons",
              element: (0, import_jsx_runtime18.jsx)(Outlet, {}),
              handle: {
                breadcrumb: () => t("returnReasons.domain")
              },
              children: [
                {
                  path: "",
                  lazy: () => import("./return-reason-list-GOIRVPSR-YMA3JWP4.js"),
                  children: [
                    {
                      path: "create",
                      lazy: () => import("./return-reason-create-YANSYS43-G3QPKY34.js")
                    },
                    {
                      path: ":id",
                      children: [
                        {
                          path: "edit",
                          lazy: () => import("./return-reason-edit-4FSM4H3M-ERGBSS2L.js")
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            ...((_a2 = settingsRoutes == null ? void 0 : settingsRoutes[0]) == null ? void 0 : _a2.children) || []
          ]
        }
      ]
    },
    {
      element: (0, import_jsx_runtime18.jsx)(PublicLayout, {}),
      children: [
        {
          errorElement: (0, import_jsx_runtime18.jsx)(ErrorBoundary, {}),
          children: [
            {
              path: "/login",
              lazy: () => import("./login-OYY6LIPR-T2OFZDX7.js")
            },
            {
              path: "/reset-password",
              lazy: () => import("./reset-password-IMUYZE5G-KSP4KDR3.js")
            },
            {
              path: "/invite",
              lazy: () => import("./invite-3KXZ7ZZI-56YRSQOB.js")
            },
            {
              path: "*",
              lazy: () => import("./no-match-YRNHGOT3-7CVEXUQA.js")
            }
          ]
        }
      ]
    }
  ];
}
var settingsRouteRegex = /^\/settings\//;
var getRouteExtensions = (module, type) => {
  return module.routes.filter((route) => {
    if (type === "settings") {
      return settingsRouteRegex.test(route.path);
    }
    return !settingsRouteRegex.test(route.path);
  });
};
var createBranchRoute = (segment) => ({
  path: segment,
  children: []
});
var createLeafRoute = (Component4, loader, handle) => ({
  path: "",
  ErrorBoundary,
  async lazy() {
    const result = { Component: Component4 };
    if (loader) {
      result.loader = loader;
    }
    if (handle) {
      result.handle = handle;
    }
    return result;
  }
});
var createParallelRoute = (path, Component4, loader, handle) => ({
  path,
  async lazy() {
    const result = { Component: Component4 };
    if (loader) {
      result.loader = loader;
    }
    if (handle) {
      result.handle = handle;
    }
    return result;
  }
});
var processParallelRoutes = (parallelRoutes, currentFullPath) => {
  return parallelRoutes == null ? void 0 : parallelRoutes.map(({ path, Component: Component4, loader, handle }) => {
    const childPath = path == null ? void 0 : path.replace(currentFullPath, "").replace(/^\/+/, "");
    if (!childPath) {
      return null;
    }
    return createParallelRoute(childPath, Component4, loader, handle);
  }).filter(Boolean);
};
var addRoute = (pathSegments, Component4, currentLevel, loader, handle, parallelRoutes, fullPath, componentPath) => {
  if (!pathSegments.length) {
    return;
  }
  const [currentSegment, ...remainingSegments] = pathSegments;
  let route = currentLevel.find((r) => r.path === currentSegment);
  if (!route) {
    route = createBranchRoute(currentSegment);
    currentLevel.push(route);
  }
  const currentFullPath = fullPath ? `${fullPath}/${currentSegment}` : currentSegment;
  const isComponentSegment = currentFullPath === componentPath;
  if (isComponentSegment || remainingSegments.length === 0) {
    route.children || (route.children = []);
    const leaf = createLeafRoute(Component4, loader);
    if (handle) {
      route.handle = handle;
    }
    if (loader) {
      route.loader = loader;
    }
    leaf.children = processParallelRoutes(parallelRoutes, currentFullPath);
    route.children.push(leaf);
    if (remainingSegments.length > 0) {
      addRoute(
        remainingSegments,
        Component4,
        route.children,
        void 0,
        void 0,
        void 0,
        currentFullPath
      );
    }
  } else {
    route.children || (route.children = []);
    addRoute(
      remainingSegments,
      Component4,
      route.children,
      loader,
      handle,
      parallelRoutes,
      currentFullPath,
      componentPath
    );
  }
};
var createRouteMap = (routes, ignore) => {
  const root = [];
  routes.forEach(({ path, Component: Component4, loader, handle, children }) => {
    const cleanedPath = ignore ? path.replace(ignore, "").replace(/^\/+/, "") : path.replace(/^\/+/, "");
    const pathSegments = cleanedPath.split("/").filter(Boolean);
    addRoute(
      pathSegments,
      Component4,
      root,
      loader,
      handle,
      children,
      void 0,
      path
    );
  });
  return root;
};
var DashboardApp = class {
  constructor({ plugins }) {
    __publicField2(this, "widgets");
    __publicField2(this, "menus");
    __publicField2(this, "fields");
    __publicField2(this, "configs");
    __publicField2(this, "displays");
    __publicField2(this, "coreRoutes");
    __publicField2(this, "settingsRoutes");
    this.widgets = this.populateWidgets(plugins);
    this.menus = this.populateMenus(plugins);
    const { coreRoutes, settingsRoutes } = this.populateRoutes(plugins);
    this.coreRoutes = coreRoutes;
    this.settingsRoutes = settingsRoutes;
    const { fields, configs } = this.populateForm(plugins);
    this.fields = fields;
    this.configs = configs;
    this.displays = this.populateDisplays(plugins);
  }
  populateRoutes(plugins) {
    const coreRoutes = [];
    const settingsRoutes = [];
    for (const plugin of plugins) {
      const filteredCoreRoutes = getRouteExtensions(plugin.routeModule, "core");
      const filteredSettingsRoutes = getRouteExtensions(
        plugin.routeModule,
        "settings"
      );
      const coreRoutesMap = createRouteMap(filteredCoreRoutes);
      const settingsRoutesMap = createRouteMap(filteredSettingsRoutes);
      coreRoutes.push(...coreRoutesMap);
      settingsRoutes.push(...settingsRoutesMap);
    }
    return { coreRoutes, settingsRoutes };
  }
  populateWidgets(plugins) {
    const registry = /* @__PURE__ */ new Map();
    plugins.forEach((plugin) => {
      const widgets = plugin.widgetModule.widgets;
      if (!widgets) {
        return;
      }
      widgets.forEach((widget) => {
        widget.zone.forEach((zone) => {
          if (!registry.has(zone)) {
            registry.set(zone, []);
          }
          registry.get(zone).push(widget.Component);
        });
      });
    });
    return registry;
  }
  populateMenus(plugins) {
    const registry = /* @__PURE__ */ new Map();
    const tempRegistry = {};
    const allMenuItems = [];
    plugins.forEach((plugin) => {
      if (plugin.menuItemModule.menuItems) {
        allMenuItems.push(...plugin.menuItemModule.menuItems);
      }
    });
    if (allMenuItems.length === 0) {
      return registry;
    }
    allMenuItems.sort((a, b) => a.path.length - b.path.length);
    allMenuItems.forEach((item) => {
      if (item.path.includes("/:")) {
        if (true) {
          console.warn(
            `[@medusajs/dashboard] Menu item for path "${item.path}" can't be added to the sidebar as it contains a parameter.`
          );
        }
        return;
      }
      const isSettingsPath = item.path.startsWith("/settings");
      const key = isSettingsPath ? "settingsExtensions" : "coreExtensions";
      const pathParts = item.path.split("/").filter(Boolean);
      const parentPath = "/" + pathParts.slice(0, -1).join("/");
      if (isSettingsPath && pathParts.length > 2) {
        if (true) {
          console.warn(
            `[@medusajs/dashboard] Nested settings menu item "${item.path}" can't be added to the sidebar. Only top-level settings items are allowed.`
          );
        }
        return;
      }
      const parentItem = allMenuItems.find(
        (menuItem) => menuItem.path === parentPath
      );
      if ((parentItem == null ? void 0 : parentItem.nested) && NESTED_ROUTE_POSITIONS.includes(parentItem == null ? void 0 : parentItem.nested) && pathParts.length > 1) {
        if (true) {
          console.warn(
            `[@medusajs/dashboard] Nested menu item "${item.path}" can't be added to the sidebar as it is nested under "${parentItem.nested}".`
          );
        }
        return;
      }
      const navItem = {
        label: item.label,
        to: item.path,
        icon: item.icon ? (0, import_jsx_runtime19.jsx)(item.icon, {}) : void 0,
        items: [],
        nested: item.nested
      };
      if (parentPath !== "/" && tempRegistry[parentPath]) {
        if (!tempRegistry[parentPath].items) {
          tempRegistry[parentPath].items = [];
        }
        tempRegistry[parentPath].items.push(navItem);
      } else {
        if (!registry.has(key)) {
          registry.set(key, []);
        }
        registry.get(key).push(navItem);
      }
      tempRegistry[item.path] = navItem;
    });
    return registry;
  }
  populateForm(plugins) {
    const fields = /* @__PURE__ */ new Map();
    const configs = /* @__PURE__ */ new Map();
    plugins.forEach((plugin) => {
      Object.entries(plugin.formModule.customFields).forEach(
        ([model, customization]) => {
          if (!fields.has(model)) {
            fields.set(model, /* @__PURE__ */ new Map());
          }
          if (!configs.has(model)) {
            configs.set(model, /* @__PURE__ */ new Map());
          }
          const modelFields = this.processFields(customization.forms);
          const existingModelFields = fields.get(model);
          modelFields.forEach((zoneStructure, zone) => {
            if (!existingModelFields.has(zone)) {
              existingModelFields.set(zone, { components: [], tabs: /* @__PURE__ */ new Map() });
            }
            const existingZoneStructure = existingModelFields.get(zone);
            existingZoneStructure.components.push(...zoneStructure.components);
            zoneStructure.tabs.forEach((fields2, tab) => {
              if (!existingZoneStructure.tabs.has(tab)) {
                existingZoneStructure.tabs.set(tab, []);
              }
              existingZoneStructure.tabs.get(tab).push(...fields2);
            });
          });
          const modelConfigs = this.processConfigs(customization.configs);
          const existingModelConfigs = configs.get(model);
          modelConfigs.forEach((configFields, zone) => {
            if (!existingModelConfigs.has(zone)) {
              existingModelConfigs.set(zone, []);
            }
            existingModelConfigs.get(zone).push(...configFields);
          });
        }
      );
    });
    return { fields, configs };
  }
  processFields(forms) {
    const formZoneMap = /* @__PURE__ */ new Map();
    forms.forEach(
      (fieldDef) => this.processFieldDefinition(formZoneMap, fieldDef)
    );
    return formZoneMap;
  }
  processConfigs(configs) {
    const modelConfigMap = /* @__PURE__ */ new Map();
    configs.forEach((configDef) => {
      const { zone, fields } = configDef;
      const zoneConfigs = [];
      Object.entries(fields).forEach(([name, config]) => {
        zoneConfigs.push({
          name,
          defaultValue: config.defaultValue,
          validation: config.validation
        });
      });
      modelConfigMap.set(zone, zoneConfigs);
    });
    return modelConfigMap;
  }
  processFieldDefinition(formZoneMap, fieldDef) {
    const { zone, tab, fields: fieldsDefinition } = fieldDef;
    const zoneStructure = this.getOrCreateZoneStructure(formZoneMap, zone);
    Object.entries(fieldsDefinition).forEach(([fieldKey, fieldDefinition]) => {
      const formField = this.createFormField(fieldKey, fieldDefinition);
      this.addFormFieldToZoneStructure(zoneStructure, formField, tab);
    });
  }
  getOrCreateZoneStructure(formZoneMap, zone) {
    let zoneStructure = formZoneMap.get(zone);
    if (!zoneStructure) {
      zoneStructure = { components: [], tabs: /* @__PURE__ */ new Map() };
      formZoneMap.set(zone, zoneStructure);
    }
    return zoneStructure;
  }
  createFormField(fieldKey, fieldDefinition) {
    return {
      name: fieldKey,
      validation: fieldDefinition.validation,
      label: fieldDefinition.label,
      description: fieldDefinition.description,
      Component: fieldDefinition.Component
    };
  }
  addFormFieldToZoneStructure(zoneStructure, formField, tab) {
    if (tab) {
      let tabFields = zoneStructure.tabs.get(tab);
      if (!tabFields) {
        tabFields = [];
        zoneStructure.tabs.set(tab, tabFields);
      }
      tabFields.push(formField);
    } else {
      zoneStructure.components.push(formField);
    }
  }
  populateDisplays(plugins) {
    const displays = /* @__PURE__ */ new Map();
    plugins.forEach((plugin) => {
      Object.entries(plugin.displayModule.displays).forEach(
        ([model, customization]) => {
          if (!displays.has(model)) {
            displays.set(
              model,
              /* @__PURE__ */ new Map()
            );
          }
          const modelDisplays = displays.get(model);
          const processedDisplays = this.processDisplays(customization);
          processedDisplays.forEach((components, zone) => {
            if (!modelDisplays.has(zone)) {
              modelDisplays.set(zone, []);
            }
            modelDisplays.get(zone).push(...components);
          });
        }
      );
    });
    return displays;
  }
  processDisplays(displays) {
    const modelDisplayMap = /* @__PURE__ */ new Map();
    displays.forEach((display) => {
      const { zone, Component: Component4 } = display;
      if (!modelDisplayMap.has(zone)) {
        modelDisplayMap.set(zone, []);
      }
      modelDisplayMap.get(zone).push(Component4);
    });
    return modelDisplayMap;
  }
  getMenu(path) {
    return this.menus.get(path) || [];
  }
  getWidgets(zone) {
    return this.widgets.get(zone) || [];
  }
  getFormFields(model, zone, tab) {
    var _a2;
    const zoneMap = (_a2 = this.fields.get(model)) == null ? void 0 : _a2.get(zone);
    if (!zoneMap) {
      return [];
    }
    if (tab) {
      return zoneMap.tabs.get(tab) || [];
    }
    return zoneMap.components;
  }
  getFormConfigs(model, zone) {
    var _a2;
    return ((_a2 = this.configs.get(model)) == null ? void 0 : _a2.get(zone)) || [];
  }
  getDisplays(model, zone) {
    var _a2;
    return ((_a2 = this.displays.get(model)) == null ? void 0 : _a2.get(zone)) || [];
  }
  get api() {
    return {
      getMenu: this.getMenu.bind(this),
      getWidgets: this.getWidgets.bind(this),
      getFormFields: this.getFormFields.bind(this),
      getFormConfigs: this.getFormConfigs.bind(this),
      getDisplays: this.getDisplays.bind(this)
    };
  }
  render() {
    const routes = getRouteMap({
      settingsRoutes: this.settingsRoutes,
      coreRoutes: this.coreRoutes
    });
    const router = createBrowserRouter(routes, {
      basename: __BASE__ || "/"
    });
    return (0, import_jsx_runtime19.jsx)(Providers, { api: this.api, children: (0, import_jsx_runtime19.jsx)(RouterProvider, { router }) });
  }
};
function getFieldType(type) {
  if (type instanceof ZodString) {
    return "text";
  }
  if (type instanceof ZodNumber) {
    return "number";
  }
  if (type instanceof ZodBoolean) {
    return "boolean";
  }
  if (type instanceof ZodNullable) {
    const innerType = type.unwrap();
    return getFieldType(innerType);
  }
  if (type instanceof ZodOptional) {
    const innerType = type.unwrap();
    return getFieldType(innerType);
  }
  if (type instanceof ZodEffects) {
    const innerType = type.innerType();
    return getFieldType(innerType);
  }
  return "unsupported";
}
var FormExtensionZone = ({ fields, form }) => {
  return (0, import_jsx_runtime20.jsx)("div", { children: fields.map((field, index) => (0, import_jsx_runtime20.jsx)(FormExtensionField, { field, form }, index)) });
};
function getFieldLabel(field) {
  if (field.label) {
    return field.label;
  }
  return field.name.split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
}
var FormExtensionField = ({ field, form }) => {
  const label = getFieldLabel(field);
  const description = field.description;
  const placeholder = field.placeholder;
  const Component4 = field.Component;
  const type = getFieldType(field.validation);
  const { control } = form;
  return (0, import_jsx_runtime20.jsx)(
    Form.Field,
    {
      control,
      name: `additional_data.${field.name}`,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime20.jsxs)(Form.Item, { children: [
          (0, import_jsx_runtime20.jsx)(Form.Label, { children: label }),
          description && (0, import_jsx_runtime20.jsx)(Form.Hint, { children: description }),
          (0, import_jsx_runtime20.jsx)(Form.Control, { children: (0, import_jsx_runtime20.jsx)(
            FormExtensionFieldComponent,
            {
              field: field2,
              type,
              component: Component4,
              placeholder
            }
          ) }),
          (0, import_jsx_runtime20.jsx)(Form.ErrorMessage, {})
        ] });
      }
    }
  );
};
var FormExtensionFieldComponent = ({
  field,
  type,
  component,
  placeholder
}) => {
  const { t: t22 } = useTranslation();
  if (component) {
    const Component4 = component;
    return (0, import_jsx_runtime20.jsx)(Component4, { ...field, placeholder });
  }
  switch (type) {
    case "text": {
      return (0, import_jsx_runtime20.jsx)(Input, { ...field, placeholder });
    }
    case "number": {
      return (0, import_jsx_runtime20.jsx)(Input, { ...field, placeholder, type: "number" });
    }
    case "boolean": {
      return (0, import_jsx_runtime20.jsx)(Switch, { ...field });
    }
    default: {
      return (0, import_jsx_runtime20.jsx)(InlineTip, { variant: "warning", label: t22("general.warning"), children: "The field type does not support rendering a fallback component. Please provide a component prop." });
    }
  }
};
function createAdditionalDataSchema(configs) {
  return configs.reduce((acc, config) => {
    acc[config.name] = config.validation;
    return acc;
  }, {});
}
function createExtendedSchema(baseSchema, additionalDataSchema) {
  const extendedObjectSchema = z.object({
    ...baseSchema instanceof ZodEffects ? baseSchema.innerType().shape : baseSchema.shape,
    additional_data: z.object(additionalDataSchema).optional()
  });
  return baseSchema instanceof ZodEffects ? baseSchema.superRefine((data, ctx) => {
    const result = extendedObjectSchema.safeParse(data);
    if (!result.success) {
      result.error.issues.forEach((issue) => ctx.addIssue(issue));
    }
  }).and(extendedObjectSchema) : extendedObjectSchema;
}
function createExtendedDefaultValues(baseDefaultValues, configs, data) {
  const additional_data = configs.reduce((acc, config) => {
    const { name, defaultValue: defaultValue2 } = config;
    acc[name] = typeof defaultValue2 === "function" ? defaultValue2(data) : defaultValue2;
    return acc;
  }, {});
  return Object.assign(baseDefaultValues, { additional_data });
}
var useExtendableForm = ({
  defaultValues: baseDefaultValues,
  schema: baseSchema,
  configs,
  data,
  ...props
}) => {
  const additionalDataSchema = createAdditionalDataSchema(configs);
  const schema = createExtendedSchema(baseSchema, additionalDataSchema);
  const defaultValues = createExtendedDefaultValues(
    baseDefaultValues,
    configs,
    data
  );
  return useForm({
    ...props,
    defaultValues,
    resolver: t2(schema)
  });
};
function appendLinkableFields(fields = "", linkable = []) {
  const linkableFields = linkable.flatMap((link) => {
    return typeof link === "string" ? [`+${link}.*`] : link.map((l) => `+${l}.*`);
  });
  return [fields, ...linkableFields].join(",");
}
function getLinkedFields(model, fields = "") {
  const links = linkModule.links[model];
  return appendLinkableFields(fields, links);
}

export {
  DashboardApp,
  FormExtensionZone,
  useExtendableForm,
  getLinkedFields
};
//# sourceMappingURL=chunk-OJUGDQLS.js.map
