{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-FVK4ZYYM.mjs"], "sourcesContent": ["import {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\n\n// src/hooks/table/filters/use-order-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useOrderTableFilters = () => {\n  const { t } = useTranslation();\n  const { regions } = useRegions({\n    limit: 1e3,\n    fields: \"id,name\"\n  });\n  const { sales_channels } = useSalesChannels({\n    limit: 1e3,\n    fields: \"id,name\"\n  });\n  let filters = [];\n  if (regions) {\n    const regionFilter = {\n      key: \"region_id\",\n      label: t(\"fields.region\"),\n      type: \"select\",\n      options: regions.map((r) => ({\n        label: r.name,\n        value: r.id\n      })),\n      multiple: true,\n      searchable: true\n    };\n    filters = [...filters, regionFilter];\n  }\n  if (sales_channels) {\n    const salesChannelFilter = {\n      key: \"sales_channel_id\",\n      label: t(\"fields.salesChannel\"),\n      type: \"select\",\n      multiple: true,\n      searchable: true,\n      options: sales_channels.map((s) => ({\n        label: s.name,\n        value: s.id\n      }))\n    };\n    filters = [...filters, salesChannelFilter];\n  }\n  const paymentStatusFilter = {\n    key: \"payment_status\",\n    label: t(\"orders.payment.statusLabel\"),\n    type: \"select\",\n    multiple: true,\n    options: [\n      {\n        label: t(\"orders.payment.status.notPaid\"),\n        value: \"not_paid\"\n      },\n      {\n        label: t(\"orders.payment.status.awaiting\"),\n        value: \"awaiting\"\n      },\n      {\n        label: t(\"orders.payment.status.captured\"),\n        value: \"captured\"\n      },\n      {\n        label: t(\"orders.payment.status.refunded\"),\n        value: \"refunded\"\n      },\n      {\n        label: t(\"orders.payment.status.partiallyRefunded\"),\n        value: \"partially_refunded\"\n      },\n      {\n        label: t(\"orders.payment.status.canceled\"),\n        value: \"canceled\"\n      },\n      {\n        label: t(\"orders.payment.status.requiresAction\"),\n        value: \"requires_action\"\n      }\n    ]\n  };\n  const fulfillmentStatusFilter = {\n    key: \"fulfillment_status\",\n    label: t(\"orders.fulfillment.statusLabel\"),\n    type: \"select\",\n    multiple: true,\n    options: [\n      {\n        label: t(\"orders.fulfillment.status.notFulfilled\"),\n        value: \"not_fulfilled\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.fulfilled\"),\n        value: \"fulfilled\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.partiallyFulfilled\"),\n        value: \"partially_fulfilled\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.returned\"),\n        value: \"returned\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.partiallyReturned\"),\n        value: \"partially_returned\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.shipped\"),\n        value: \"shipped\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.partiallyShipped\"),\n        value: \"partially_shipped\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.canceled\"),\n        value: \"canceled\"\n      },\n      {\n        label: t(\"orders.fulfillment.status.requiresAction\"),\n        value: \"requires_action\"\n      }\n    ]\n  };\n  const dateFilters = [\n    { label: \"Created At\", key: \"created_at\" },\n    { label: \"Updated At\", key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  filters = [\n    ...filters,\n    // TODO: enable when Payment, Fulfillments <> Orders are linked\n    // paymentStatusFilter,\n    // fulfillmentStatusFilter,\n    ...dateFilters\n  ];\n  return filters;\n};\n\nexport {\n  useOrderTableFilters\n};\n"], "mappings": ";;;;;;;;;;;AASA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,QAAQ,IAAI,WAAW;AAAA,IAC7B,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,eAAe,IAAI,iBAAiB;AAAA,IAC1C,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,UAAU,CAAC;AACf,MAAI,SAAS;AACX,UAAM,eAAe;AAAA,MACnB,KAAK;AAAA,MACL,OAAO,EAAE,eAAe;AAAA,MACxB,MAAM;AAAA,MACN,SAAS,QAAQ,IAAI,CAAC,OAAO;AAAA,QAC3B,OAAO,EAAE;AAAA,QACT,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,MACF,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AACA,cAAU,CAAC,GAAG,SAAS,YAAY;AAAA,EACrC;AACA,MAAI,gBAAgB;AAClB,UAAM,qBAAqB;AAAA,MACzB,KAAK;AAAA,MACL,OAAO,EAAE,qBAAqB;AAAA,MAC9B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,eAAe,IAAI,CAAC,OAAO;AAAA,QAClC,OAAO,EAAE;AAAA,QACT,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,IACJ;AACA,cAAU,CAAC,GAAG,SAAS,kBAAkB;AAAA,EAC3C;AACA,QAAM,sBAAsB;AAAA,IAC1B,KAAK;AAAA,IACL,OAAO,EAAE,4BAA4B;AAAA,IACrC,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACP;AAAA,QACE,OAAO,EAAE,+BAA+B;AAAA,QACxC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gCAAgC;AAAA,QACzC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gCAAgC;AAAA,QACzC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gCAAgC;AAAA,QACzC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,yCAAyC;AAAA,QAClD,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gCAAgC;AAAA,QACzC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,sCAAsC;AAAA,QAC/C,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,0BAA0B;AAAA,IAC9B,KAAK;AAAA,IACL,OAAO,EAAE,gCAAgC;AAAA,IACzC,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACP;AAAA,QACE,OAAO,EAAE,wCAAwC;AAAA,QACjD,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,qCAAqC;AAAA,QAC9C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,8CAA8C;AAAA,QACvD,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,oCAAoC;AAAA,QAC7C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,6CAA6C;AAAA,QACtD,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,mCAAmC;AAAA,QAC5C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,4CAA4C;AAAA,QACrD,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,oCAAoC;AAAA,QAC7C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,0CAA0C;AAAA,QACnD,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,cAAc,KAAK,aAAa;AAAA,IACzC,EAAE,OAAO,cAAc,KAAK,aAAa;AAAA,EAC3C,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,YAAU;AAAA,IACR,GAAG;AAAA;AAAA;AAAA;AAAA,IAIH,GAAG;AAAA,EACL;AACA,SAAO;AACT;", "names": []}