{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PIR2H25N.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/shipping-profiles.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar SHIPPING_PROFILE_QUERY_KEY = \"shipping_profile\";\nvar shippingProfileQueryKeys = queryKeysFactory(\n  SHIPPING_PROFILE_QUERY_KEY\n);\nvar useCreateShippingProfile = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.shippingProfile.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: shippingProfileQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useShippingProfile = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.shippingProfile.retrieve(id, query),\n    queryKey: shippingProfileQueryKeys.detail(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useShippingProfiles = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.shippingProfile.list(query),\n    queryKey: shippingProfileQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateShippingProfile = (id, options) => {\n  const { data, ...rest } = useMutation({\n    mutationFn: (payload) => sdk.admin.shippingProfile.update(id, payload),\n    onSuccess: (data2, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: shippingProfileQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: shippingProfileQueryKeys.lists()\n      });\n      options?.onSuccess?.(data2, variables, context);\n    },\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useDeleteShippingProfile = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.shippingProfile.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: shippingProfileQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: shippingProfileQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  shippingProfileQueryKeys,\n  useCreateShippingProfile,\n  useShippingProfile,\n  useShippingProfiles,\n  useUpdateShippingProfile,\n  useDeleteShippingProfile\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,6BAA6B;AACjC,IAAI,2BAA2B;AAAA,EAC7B;AACF;AACA,IAAI,2BAA2B,CAAC,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,gBAAgB,OAAO,OAAO;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AAtB7C;AAuBM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,IAAI,OAAO,YAAY;AAC/C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,gBAAgB,SAAS,IAAI,KAAK;AAAA,IAC3D,UAAU,yBAAyB,OAAO,IAAI,KAAK;AAAA,IACnD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,sBAAsB,CAAC,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,gBAAgB,KAAK,KAAK;AAAA,IACnD,UAAU,yBAAyB,KAAK,KAAK;AAAA,IAC7C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,YAAY;AAAA,IACpC,YAAY,CAAC,YAAY,IAAI,MAAM,gBAAgB,OAAO,IAAI,OAAO;AAAA,IACrE,WAAW,CAAC,OAAO,WAAW,YAAY;AAlD9C;AAmDM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB,OAAO,EAAE;AAAA,MAC9C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,OAAO,WAAW;AAAA,IACzC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,gBAAgB,OAAO,EAAE;AAAA,IACrD,WAAW,CAAC,MAAM,WAAW,YAAY;AAlE7C;AAmEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB,OAAO,EAAE;AAAA,MAC9C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB,MAAM;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}