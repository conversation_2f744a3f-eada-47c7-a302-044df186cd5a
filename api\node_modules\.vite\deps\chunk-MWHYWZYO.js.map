{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-G3QXMPRB.mjs"], "sourcesContent": ["import {\n  ProductCell,\n  ProductHeader\n} from \"./chunk-IQBAUTU5.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\n\n// src/hooks/table/columns/use-product-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\n\n// src/components/table/table-cells/product/collection-cell/collection-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CollectionCell = ({ collection }) => {\n  if (!collection) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: collection.title }) });\n};\nvar CollectionHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { children: t(\"fields.collection\") }) });\n};\n\n// src/components/table/table-cells/product/product-status-cell/product-status-cell.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProductStatusCell = ({ status }) => {\n  const { t } = useTranslation2();\n  const [color, text] = {\n    draft: [\"grey\", t(\"products.productStatus.draft\")],\n    proposed: [\"orange\", t(\"products.productStatus.proposed\")],\n    published: [\"green\", t(\"products.productStatus.published\")],\n    rejected: [\"red\", t(\"products.productStatus.rejected\")]\n  }[status];\n  return /* @__PURE__ */ jsx2(StatusCell, { color, children: text });\n};\nvar ProductStatusHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx2(\"span\", { children: t(\"fields.status\") }) });\n};\n\n// src/components/table/table-cells/product/sales-channels-cell/sales-channels-cell.tsx\nimport { Tooltip } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs } from \"react/jsx-runtime\";\nvar SalesChannelsCell = ({\n  salesChannels\n}) => {\n  const { t } = useTranslation3();\n  if (!salesChannels || !salesChannels.length) {\n    return /* @__PURE__ */ jsx3(PlaceholderCell, {});\n  }\n  if (salesChannels.length > 2) {\n    return /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full w-full items-center gap-x-1 overflow-hidden\", children: [\n      /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: salesChannels.slice(0, 2).map((sc) => sc.name).join(\", \") }),\n      /* @__PURE__ */ jsx3(\n        Tooltip,\n        {\n          content: /* @__PURE__ */ jsx3(\"ul\", { children: salesChannels.slice(2).map((sc) => /* @__PURE__ */ jsx3(\"li\", { children: sc.name }, sc.id)) }),\n          children: /* @__PURE__ */ jsx3(\"span\", { className: \"text-xs\", children: t(\"general.plusCountMore\", {\n            count: salesChannels.length - 2\n          }) })\n        }\n      )\n    ] });\n  }\n  const channels = salesChannels.map((sc) => sc.name).join(\", \");\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center overflow-hidden max-w-[250px]\", children: /* @__PURE__ */ jsx3(\"span\", { title: channels, className: \"truncate\", children: channels }) });\n};\nvar SalesChannelHeader = () => {\n  const { t } = useTranslation3();\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx3(\"span\", { children: t(\"fields.salesChannels\") }) });\n};\n\n// src/components/table/table-cells/product/variant-cell/variant-cell.tsx\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar VariantCell = ({ variants }) => {\n  const { t } = useTranslation4();\n  if (!variants || !variants.length) {\n    return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: t(\"products.variantCount\", { count: variants.length }) }) });\n};\nvar VariantHeader = () => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx4(\"span\", { children: t(\"fields.variants\") }) });\n};\n\n// src/hooks/table/columns/use-product-table-columns.tsx\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useProductTableColumns = () => {\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"product\",\n        header: () => /* @__PURE__ */ jsx5(ProductHeader, {}),\n        cell: ({ row }) => /* @__PURE__ */ jsx5(ProductCell, { product: row.original })\n      }),\n      columnHelper.accessor(\"collection\", {\n        header: () => /* @__PURE__ */ jsx5(CollectionHeader, {}),\n        cell: ({ row }) => /* @__PURE__ */ jsx5(CollectionCell, { collection: row.original.collection })\n      }),\n      columnHelper.accessor(\"sales_channels\", {\n        header: () => /* @__PURE__ */ jsx5(SalesChannelHeader, {}),\n        cell: ({ row }) => /* @__PURE__ */ jsx5(SalesChannelsCell, { salesChannels: row.original.sales_channels })\n      }),\n      columnHelper.accessor(\"variants\", {\n        header: () => /* @__PURE__ */ jsx5(VariantHeader, {}),\n        cell: ({ row }) => /* @__PURE__ */ jsx5(VariantCell, { variants: row.original.variants })\n      }),\n      columnHelper.accessor(\"status\", {\n        header: () => /* @__PURE__ */ jsx5(ProductStatusHeader, {}),\n        cell: ({ row }) => /* @__PURE__ */ jsx5(ProductStatusCell, { status: row.original.status })\n      })\n    ],\n    []\n  );\n};\n\nexport {\n  useProductTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,mBAAwB;AAIxB,yBAAoB;AAcpB,IAAAA,sBAA4B;AAmB5B,IAAAC,sBAAkC;AAgClC,IAAAC,sBAA4B;AAc5B,IAAAA,sBAA4B;AA9E5B,IAAI,iBAAiB,CAAC,EAAE,WAAW,MAAM;AACvC,MAAI,CAAC,YAAY;AACf,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,mDAAmD,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,WAAW,MAAM,CAAC,EAAE,CAAC;AAClM;AACA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,UAAU,EAAE,mBAAmB,EAAE,CAAC,EAAE,CAAC;AACjK;AAKA,IAAI,oBAAoB,CAAC,EAAE,OAAO,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,CAAC,OAAO,IAAI,IAAI;AAAA,IACpB,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC;AAAA,IACjD,UAAU,CAAC,UAAU,EAAE,iCAAiC,CAAC;AAAA,IACzD,WAAW,CAAC,SAAS,EAAE,kCAAkC,CAAC;AAAA,IAC1D,UAAU,CAAC,OAAO,EAAE,iCAAiC,CAAC;AAAA,EACxD,EAAE,MAAM;AACR,aAAuB,oBAAAC,KAAK,YAAY,EAAE,OAAO,UAAU,KAAK,CAAC;AACnE;AACA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC;AAC/J;AAMA,IAAI,oBAAoB,CAAC;AAAA,EACvB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,CAAC,iBAAiB,CAAC,cAAc,QAAQ;AAC3C,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,eAAuB,0BAAK,OAAO,EAAE,WAAW,2DAA2D,UAAU;AAAA,UACnG,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,cAAc,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,UAC3G,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,aAAyB,oBAAAA,KAAK,MAAM,EAAE,UAAU,cAAc,MAAM,CAAC,EAAE,IAAI,CAAC,WAAuB,oBAAAA,KAAK,MAAM,EAAE,UAAU,GAAG,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AAAA,UAC9I,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,WAAW,UAAU,EAAE,yBAAyB;AAAA,YAClG,OAAO,cAAc,SAAS;AAAA,UAChC,CAAC,EAAE,CAAC;AAAA,QACN;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL;AACA,QAAM,WAAW,cAAc,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,KAAK,IAAI;AAC7D,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,iEAAiE,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,OAAO,UAAU,WAAW,YAAY,UAAU,SAAS,CAAC,EAAE,CAAC;AAC3N;AACA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,UAAU,EAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC;AACtK;AAKA,IAAI,cAAc,CAAC,EAAE,SAAS,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AACjC,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,yBAAyB,EAAE,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;AAC1O;AACA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;AACjK;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,yBAAyB,MAAM;AACjC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAC,KAAK,eAAe,CAAC,CAAC;AAAA,QACpD,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,aAAa,EAAE,SAAS,IAAI,SAAS,CAAC;AAAA,MAChF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,UAAsB,oBAAAA,KAAK,kBAAkB,CAAC,CAAC;AAAA,QACvD,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,gBAAgB,EAAE,YAAY,IAAI,SAAS,WAAW,CAAC;AAAA,MACjG,CAAC;AAAA,MACD,aAAa,SAAS,kBAAkB;AAAA,QACtC,QAAQ,UAAsB,oBAAAA,KAAK,oBAAoB,CAAC,CAAC;AAAA,QACzD,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,mBAAmB,EAAE,eAAe,IAAI,SAAS,eAAe,CAAC;AAAA,MAC3G,CAAC;AAAA,MACD,aAAa,SAAS,YAAY;AAAA,QAChC,QAAQ,UAAsB,oBAAAA,KAAK,eAAe,CAAC,CAAC;AAAA,QACpD,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,aAAa,EAAE,UAAU,IAAI,SAAS,SAAS,CAAC;AAAA,MAC1F,CAAC;AAAA,MACD,aAAa,SAAS,UAAU;AAAA,QAC9B,QAAQ,UAAsB,oBAAAA,KAAK,qBAAqB,CAAC,CAAC;AAAA,QAC1D,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,mBAAmB,EAAE,QAAQ,IAAI,SAAS,OAAO,CAAC;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4", "jsx5"]}