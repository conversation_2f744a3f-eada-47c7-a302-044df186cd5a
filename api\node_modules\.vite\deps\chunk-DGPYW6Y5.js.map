{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-SYQ6IA6C.mjs"], "sourcesContent": ["import {\n  DateCell\n} from \"./chunk-3OHUAQUF.mjs\";\nimport {\n  TextCell\n} from \"./chunk-MSDRGCRR.mjs\";\n\n// src/hooks/table/columns/use-product-type-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useProductTypeTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"value\", {\n        header: () => t(\"fields.value\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue() })\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: () => t(\"fields.createdAt\"),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx(DateCell, { date: getValue() });\n        }\n      }),\n      columnHelper.accessor(\"updated_at\", {\n        header: () => t(\"fields.updatedAt\"),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx(DateCell, { date: getValue() });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\nexport {\n  useProductTypeTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,mBAAwB;AAExB,yBAAoB;AACpB,IAAI,eAAe,mBAAmB;AACtC,IAAI,6BAA6B,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,MAAM,EAAE,cAAc;AAAA,QAC9B,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC5E,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,MAAM,EAAE,kBAAkB;AAAA,QAClC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,MAAM,EAAE,kBAAkB;AAAA,QAClC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;", "names": []}