{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-fulfillment-providers-UOJUZDYF.mjs"], "sourcesContent": ["import {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport {\n  useDateTableFilters\n} from \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useFulfillmentProviders,\n  useStockLocation,\n  useUpdateStockLocationFulfillmentProviders\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/locations/location-fulfillment-providers/location-fulfillment-providers.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-fulfillment-providers/components/edit-fulfillment-providers-form/edit-fulfillment-providers-form.tsx\nimport { Button, Checkbox, toast } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport {\n  createColumnHelper as createColumnHelper2\n} from \"@tanstack/react-table\";\nimport { useMemo as useMemo2, useState } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useForm } from \"react-hook-form\";\n\n// src/hooks/table/columns/use-fulfillment-provider-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useFulfillmentProviderTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"id\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: \"Provider\" }),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: formatProvider(getValue()) })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/hooks/table/query/use-fulfillment-providers-table-query.tsx\nvar useFulfillmentProvidersTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"stock_location_id\"],\n    prefix\n  );\n  const { offset, q, stock_location_id } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    stock_location_id,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\n// src/routes/locations/location-fulfillment-providers/components/edit-fulfillment-providers-form/edit-fulfillment-providers-form.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar EditFulfillmentProvidersFormSchema = zod.object({\n  fulfillment_providers: zod.array(zod.string()).optional()\n});\nvar PAGE_SIZE = 50;\nvar LocationEditFulfillmentProvidersForm = ({\n  location\n}) => {\n  const { t } = useTranslation2();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      fulfillment_providers: location.fulfillment_providers?.map((fp) => fp.id) ?? []\n    },\n    resolver: zodResolver(EditFulfillmentProvidersFormSchema)\n  });\n  const { setValue } = form;\n  const initialState = location.fulfillment_providers?.reduce((acc, curr) => {\n    acc[curr.id] = true;\n    return acc;\n  }, {}) ?? {};\n  const [rowSelection, setRowSelection] = useState(initialState);\n  const handleRowSelectionChange = (updater) => {\n    const ids = typeof updater === \"function\" ? updater(rowSelection) : updater;\n    setValue(\"fulfillment_providers\", Object.keys(ids), {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(ids);\n  };\n  const { searchParams, raw } = useFulfillmentProvidersTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { fulfillment_providers, count, isLoading, isError, error } = useFulfillmentProviders(\n    { ...searchParams, is_enabled: true },\n    { placeholderData: keepPreviousData }\n  );\n  const filters = useDateTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: fulfillment_providers ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    rowSelection: {\n      state: rowSelection,\n      updater: handleRowSelectionChange\n    },\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  const { mutateAsync, isPending: isMutating } = useUpdateStockLocationFulfillmentProviders(location.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const originalIds = location.fulfillment_providers?.map((sc) => sc.id);\n    const arr = data.fulfillment_providers ?? [];\n    await mutateAsync(\n      {\n        add: arr.filter((i) => !originalIds?.includes(i)),\n        remove: originalIds?.filter((i) => !arr.includes(i))\n      },\n      {\n        onSuccess: ({ stock_location }) => {\n          toast.success(t(\"stockLocations.fulfillmentProviders.successToast\"));\n          handleSuccess(`/settings/locations/${stock_location.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex size-full flex-col\", children: [\n    /* @__PURE__ */ jsx2(RouteFocusModal.Header, {}),\n    /* @__PURE__ */ jsx2(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-auto\", children: /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        isLoading,\n        count,\n        filters,\n        search: \"autofocus\",\n        pagination: true,\n        orderBy: [{ key: \"id\", label: t(\"fields.id\") }],\n        queryObject: raw,\n        layout: \"fill\"\n      }\n    ) }),\n    /* @__PURE__ */ jsx2(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx2(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", type: \"button\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx2(Button, { size: \"small\", isLoading: isMutating, type: \"submit\", children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\nvar columnHelper2 = createColumnHelper2();\nvar useColumns = () => {\n  const columns = useFulfillmentProviderTableColumns();\n  return useMemo2(\n    () => [\n      columnHelper2.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx2(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx2(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...columns\n    ],\n    [columns]\n  );\n};\n\n// src/routes/locations/location-fulfillment-providers/location-fulfillment-providers.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar LocationFulfillmentProviders = () => {\n  const { location_id } = useParams();\n  const { stock_location, isPending, isError, error } = useStockLocation(\n    location_id,\n    { fields: \"id,*fulfillment_providers\" }\n  );\n  const ready = !isPending && !!stock_location;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx3(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx3(LocationEditFulfillmentProvidersForm, { location: stock_location }) });\n};\nexport {\n  LocationFulfillmentProviders as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA,mBAA8C;AAQ9C,IAAAA,gBAAwB;AAExB,yBAAoB;AAsCpB,IAAAC,sBAAkC;AAuIlC,IAAAA,sBAA4B;AA5K5B,IAAI,eAAe,mBAAmB;AACtC,IAAI,qCAAqC,MAAM;AAC7C,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,MAAM;AAAA,QAC1B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,WAAW,CAAC;AAAA,QAClE,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,IACA,CAACA,EAAC;AAAA,EACJ;AACF;AAGA,IAAI,oCAAoC,CAAC;AAAA,EACvC;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,mBAAmB;AAAA,IACnC;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,kBAAkB,IAAI;AACzC,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;AAIA,IAAI,qCAAyC,WAAO;AAAA,EAClD,uBAA2B,UAAU,WAAO,CAAC,EAAE,SAAS;AAC1D,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,uCAAuC,CAAC;AAAA,EAC1C;AACF,MAAM;AAjJN;AAkJE,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,yBAAuB,cAAS,0BAAT,mBAAgC,IAAI,CAAC,OAAO,GAAG,QAAO,CAAC;AAAA,IAChF;AAAA,IACA,UAAU,EAAY,kCAAkC;AAAA,EAC1D,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,iBAAe,cAAS,0BAAT,mBAAgC,OAAO,CAAC,KAAK,SAAS;AACzE,QAAI,KAAK,EAAE,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,OAAM,CAAC;AACX,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,YAAY;AAC7D,QAAM,2BAA2B,CAAC,YAAY;AAC5C,UAAM,MAAM,OAAO,YAAY,aAAa,QAAQ,YAAY,IAAI;AACpE,aAAS,yBAAyB,OAAO,KAAK,GAAG,GAAG;AAAA,MAClD,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,GAAG;AAAA,EACrB;AACA,QAAM,EAAE,cAAc,IAAI,IAAI,kCAAkC;AAAA,IAC9D,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,uBAAuB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAClE,EAAE,GAAG,cAAc,YAAY,KAAK;AAAA,IACpC,EAAE,iBAAiB,iBAAiB;AAAA,EACtC;AACA,QAAM,UAAU,oBAAoB;AACpC,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,yBAAyB,CAAC;AAAA,IAChC;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,2CAA2C,SAAS,EAAE;AACrG,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AA/LzD,QAAAC;AAgMI,UAAM,eAAcA,MAAA,SAAS,0BAAT,gBAAAA,IAAgC,IAAI,CAAC,OAAO,GAAG;AACnE,UAAM,MAAM,KAAK,yBAAyB,CAAC;AAC3C,UAAM;AAAA,MACJ;AAAA,QACE,KAAK,IAAI,OAAO,CAAC,MAAM,EAAC,2CAAa,SAAS,GAAE;AAAA,QAChD,QAAQ,2CAAa,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;AAAA,MACpD;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,eAAe,MAAM;AACjC,gBAAM,QAAQD,GAAE,kDAAkD,CAAC;AACnE,wBAAc,uBAAuB,eAAe,EAAE,EAAE;AAAA,QAC1D;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,0BAAK,cAAc,EAAE,UAAU,cAAc,WAAW,2BAA2B,UAAU;AAAA,QAC/J,oBAAAA,KAAK,gBAAgB,QAAQ,CAAC,CAAC;AAAA,QAC/B,oBAAAA,KAAK,gBAAgB,MAAM,EAAE,WAAW,sCAAsC,cAA0B,oBAAAA;AAAA,MACtH;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS,CAAC,EAAE,KAAK,MAAM,OAAOF,GAAE,WAAW,EAAE,CAAC;AAAA,QAC9C,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAE,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,0BAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UACnI,oBAAAA,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,MAAM,UAAU,UAAUF,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7K,oBAAAE,KAAK,QAAQ,EAAE,MAAM,SAAS,WAAW,YAAY,MAAM,UAAU,UAAUF,GAAE,cAAc,EAAE,CAAC;AAAA,IACpH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,gBAAgB,mBAAoB;AACxC,IAAI,aAAa,MAAM;AACrB,QAAM,UAAU,mCAAmC;AACnD,aAAO,aAAAG;AAAA,IACL,MAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,QACpB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAD;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AACF;AAIA,IAAI,+BAA+B,MAAM;AACvC,QAAM,EAAE,YAAY,IAAI,UAAU;AAClC,QAAM,EAAE,gBAAgB,WAAW,SAAS,MAAM,IAAI;AAAA,IACpD;AAAA,IACA,EAAE,QAAQ,4BAA4B;AAAA,EACxC;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,sCAAsC,EAAE,UAAU,eAAe,CAAC,EAAE,CAAC;AAC9J;", "names": ["import_react", "import_jsx_runtime", "t", "_a", "jsx2", "useMemo2", "jsx3"]}