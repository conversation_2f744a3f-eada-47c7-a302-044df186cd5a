import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useStockLocations
} from "./chunk-CXC4I63N.js";
import {
  useCreateReservationItem
} from "./chunk-OX66UHHF.js";
import {
  useInventoryItems
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/reservation-create-INUIVD55.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateReservationSchema = objectType({
  inventory_item_id: stringType().min(1),
  location_id: stringType().min(1),
  quantity: numberType().min(1),
  description: stringType().optional()
});
var AttributeGridRow = ({
  title,
  value
}) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 divide-x", children: [
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: title }),
    (0, import_jsx_runtime.jsx)(Text, { className: "px-2 py-1.5", size: "small", leading: "compact", children: value })
  ] });
};
var ReservationCreateForm = (props) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [inventorySearch, setInventorySearch] = import_react.default.useState(
    null
  );
  const form = useForm({
    defaultValues: {
      inventory_item_id: props.inventoryItemId || "",
      location_id: "",
      quantity: 0,
      description: ""
    },
    resolver: t(CreateReservationSchema)
  });
  const { inventory_items } = useInventoryItems({
    q: inventorySearch
  });
  const inventoryItemId = form.watch("inventory_item_id");
  const selectedInventoryItem = inventory_items == null ? void 0 : inventory_items.find(
    (it) => it.id === inventoryItemId
  );
  const locationId = form.watch("location_id");
  const selectedLocationLevel = (_a = selectedInventoryItem == null ? void 0 : selectedInventoryItem.location_levels) == null ? void 0 : _a.find(
    (it) => it.location_id === locationId
  );
  const quantity = form.watch("quantity");
  const { stock_locations } = useStockLocations(
    {
      id: ((_b = selectedInventoryItem == null ? void 0 : selectedInventoryItem.location_levels) == null ? void 0 : _b.map(
        (level) => level.location_id
      )) ?? []
    },
    {
      enabled: !!selectedInventoryItem
    }
  );
  const { mutateAsync, isPending } = useCreateReservationItem();
  const handleSubmit = form.handleSubmit(async (data) => {
    const min = 1;
    const max = (selectedLocationLevel == null ? void 0 : selectedLocationLevel.available_quantity) ? selectedLocationLevel.available_quantity : 0;
    if (!(selectedLocationLevel == null ? void 0 : selectedLocationLevel.available_quantity)) {
      form.setError("quantity", {
        type: "manual",
        message: t2("inventory.reservation.errors.noAvaliableQuantity")
      });
      return;
    }
    if (data.quantity < min || data.quantity > max) {
      form.setError("quantity", {
        type: "manual",
        message: t2("inventory.reservation.errors.quantityOutOfRange", {
          max
        })
      });
      return;
    }
    await mutateAsync(data, {
      onSuccess: ({ reservation }) => {
        toast.success(t2("inventory.reservation.successToast"));
        handleSuccess(
          props.inventoryItemId ? `/inventory/${props.inventoryItemId}` : `/reservations/${reservation.id}`
        );
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col items-center overflow-auto py-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsx)(Heading, { children: t2("inventory.reservation.create") }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "inventory_item_id",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("inventory.reservation.itemToReserve") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Combobox,
                      {
                        onSearchValueChange: (value2) => setInventorySearch(value2),
                        value,
                        onChange: (v) => {
                          onChange(v);
                        },
                        ...field,
                        disabled: !!props.inventoryItemId,
                        options: (inventory_items ?? []).map(
                          (inventoryItem) => ({
                            label: inventoryItem.title ?? inventoryItem.sku,
                            value: inventoryItem.id
                          })
                        )
                      }
                    ) })
                  ] });
                }
              },
              "inventory_item_id"
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "location_id",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.location") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Combobox,
                      {
                        value,
                        onChange: (v) => {
                          onChange(v);
                        },
                        ...field,
                        disabled: !inventoryItemId,
                        options: (stock_locations ?? []).map(
                          (stockLocation) => ({
                            label: stockLocation.name,
                            value: stockLocation.id
                          })
                        )
                      }
                    ) })
                  ] });
                }
              },
              "location_id"
            )
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-4 divide-y rounded-lg", children: [
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.title"),
                value: (selectedInventoryItem == null ? void 0 : selectedInventoryItem.title) ?? (selectedInventoryItem == null ? void 0 : selectedInventoryItem.sku) ?? "-"
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.sku"),
                value: (selectedInventoryItem == null ? void 0 : selectedInventoryItem.sku) ?? "-"
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("fields.inStock"),
                value: (selectedLocationLevel == null ? void 0 : selectedLocationLevel.stocked_quantity) ?? "-"
              }
            ),
            (0, import_jsx_runtime.jsx)(
              AttributeGridRow,
              {
                title: t2("inventory.available"),
                value: (selectedLocationLevel == null ? void 0 : selectedLocationLevel.available_quantity) ? selectedLocationLevel.available_quantity - (quantity || 0) : "-"
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "w-full lg:w-1/2", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "quantity",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.quantity") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      placeholder: t2(
                        "inventory.reservation.quantityPlaceholder"
                      ),
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field,
                      disabled: !inventoryItemId || !locationId
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "description",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.description") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Textarea,
                    {
                      ...field,
                      disabled: !inventoryItemId || !locationId,
                      placeholder: t2(
                        "inventory.reservation.descriptionPlaceholder"
                      )
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isPending,
              children: t2("actions.create")
            }
          )
        ] }) })
      ]
    }
  ) });
};
var ReservationCreate = () => {
  const [params] = useSearchParams();
  const inventoryItemId = params.get("item_id");
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(ReservationCreateForm, { inventoryItemId }) });
};
export {
  ReservationCreate as Component
};
//# sourceMappingURL=reservation-create-INUIVD55-DRIQB4SJ.js.map
