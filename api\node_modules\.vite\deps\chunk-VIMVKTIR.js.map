{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-2WQFRVK5.mjs"], "sourcesContent": ["import {\n  useProductTags\n} from \"./chunk-CLRTJ6DI.mjs\";\nimport {\n  useProductTypes\n} from \"./chunk-B4GODIOW.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\n\n// src/hooks/table/filters/use-product-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useProductTableFilters = (exclude) => {\n  const { t } = useTranslation();\n  const isProductTypeExcluded = exclude?.includes(\"product_types\");\n  const { product_types } = useProductTypes(\n    {\n      limit: 1e3,\n      offset: 0\n    },\n    {\n      enabled: !isProductTypeExcluded\n    }\n  );\n  const isProductTagExcluded = exclude?.includes(\"product_tags\");\n  const { product_tags } = useProductTags({\n    limit: 1e3,\n    offset: 0\n  });\n  const isSalesChannelExcluded = exclude?.includes(\"sales_channel_id\");\n  const { sales_channels } = useSalesChannels(\n    {\n      limit: 1e3,\n      fields: \"id,name\"\n    },\n    {\n      enabled: !isSalesChannelExcluded\n    }\n  );\n  const isCategoryExcluded = exclude?.includes(\"categories\");\n  const isCollectionExcluded = exclude?.includes(\"collections\");\n  let filters = [];\n  if (product_types && !isProductTypeExcluded) {\n    const typeFilter = {\n      key: \"type_id\",\n      label: t(\"fields.type\"),\n      type: \"select\",\n      multiple: true,\n      options: product_types.map((t2) => ({\n        label: t2.value,\n        value: t2.id\n      }))\n    };\n    filters = [...filters, typeFilter];\n  }\n  if (product_tags && !isProductTagExcluded) {\n    const tagFilter = {\n      key: \"tag_id\",\n      label: t(\"fields.tag\"),\n      type: \"select\",\n      multiple: true,\n      options: product_tags.map((t2) => ({\n        label: t2.value,\n        value: t2.id\n      }))\n    };\n    filters = [...filters, tagFilter];\n  }\n  if (sales_channels) {\n    const salesChannelFilter = {\n      key: \"sales_channel_id\",\n      label: t(\"fields.salesChannel\"),\n      type: \"select\",\n      multiple: true,\n      options: sales_channels.map((s) => ({\n        label: s.name,\n        value: s.id\n      }))\n    };\n    filters = [...filters, salesChannelFilter];\n  }\n  const statusFilter = {\n    key: \"status\",\n    label: t(\"fields.status\"),\n    type: \"select\",\n    multiple: true,\n    options: [\n      {\n        label: t(\"products.productStatus.draft\"),\n        value: \"draft\"\n      },\n      {\n        label: t(\"products.productStatus.proposed\"),\n        value: \"proposed\"\n      },\n      {\n        label: t(\"products.productStatus.published\"),\n        value: \"published\"\n      },\n      {\n        label: t(\"products.productStatus.rejected\"),\n        value: \"rejected\"\n      }\n    ]\n  };\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  filters = [...filters, statusFilter, ...dateFilters];\n  return filters;\n};\n\nexport {\n  useProductTableFilters\n};\n"], "mappings": ";;;;;;;;;;;;;;AAYA,IAAI,yBAAyB,CAAC,YAAY;AACxC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,wBAAwB,mCAAS,SAAS;AAChD,QAAM,EAAE,cAAc,IAAI;AAAA,IACxB;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,SAAS,CAAC;AAAA,IACZ;AAAA,EACF;AACA,QAAM,uBAAuB,mCAAS,SAAS;AAC/C,QAAM,EAAE,aAAa,IAAI,eAAe;AAAA,IACtC,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,yBAAyB,mCAAS,SAAS;AACjD,QAAM,EAAE,eAAe,IAAI;AAAA,IACzB;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,SAAS,CAAC;AAAA,IACZ;AAAA,EACF;AACA,QAAM,qBAAqB,mCAAS,SAAS;AAC7C,QAAM,uBAAuB,mCAAS,SAAS;AAC/C,MAAI,UAAU,CAAC;AACf,MAAI,iBAAiB,CAAC,uBAAuB;AAC3C,UAAM,aAAa;AAAA,MACjB,KAAK;AAAA,MACL,OAAO,EAAE,aAAa;AAAA,MACtB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,cAAc,IAAI,CAAC,QAAQ;AAAA,QAClC,OAAO,GAAG;AAAA,QACV,OAAO,GAAG;AAAA,MACZ,EAAE;AAAA,IACJ;AACA,cAAU,CAAC,GAAG,SAAS,UAAU;AAAA,EACnC;AACA,MAAI,gBAAgB,CAAC,sBAAsB;AACzC,UAAM,YAAY;AAAA,MAChB,KAAK;AAAA,MACL,OAAO,EAAE,YAAY;AAAA,MACrB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,aAAa,IAAI,CAAC,QAAQ;AAAA,QACjC,OAAO,GAAG;AAAA,QACV,OAAO,GAAG;AAAA,MACZ,EAAE;AAAA,IACJ;AACA,cAAU,CAAC,GAAG,SAAS,SAAS;AAAA,EAClC;AACA,MAAI,gBAAgB;AAClB,UAAM,qBAAqB;AAAA,MACzB,KAAK;AAAA,MACL,OAAO,EAAE,qBAAqB;AAAA,MAC9B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,eAAe,IAAI,CAAC,OAAO;AAAA,QAClC,OAAO,EAAE;AAAA,QACT,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,IACJ;AACA,cAAU,CAAC,GAAG,SAAS,kBAAkB;AAAA,EAC3C;AACA,QAAM,eAAe;AAAA,IACnB,KAAK;AAAA,IACL,OAAO,EAAE,eAAe;AAAA,IACxB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,MACP;AAAA,QACE,OAAO,EAAE,8BAA8B;AAAA,QACvC,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,iCAAiC;AAAA,QAC1C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,kCAAkC;AAAA,QAC3C,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,OAAO,EAAE,iCAAiC;AAAA,QAC1C,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,YAAU,CAAC,GAAG,SAAS,cAAc,GAAG,WAAW;AACnD,SAAO;AACT;", "names": []}