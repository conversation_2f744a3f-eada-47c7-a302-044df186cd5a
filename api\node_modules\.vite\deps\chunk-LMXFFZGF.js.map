{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-SKR2YX52.mjs"], "sourcesContent": ["import {\n  LocalizedTablePagination,\n  TaxRegionCard\n} from \"./chunk-X2Y4KNQI.mjs\";\nimport {\n  DataTableOrderBy\n} from \"./chunk-AOFGTNG6.mjs\";\nimport {\n  NoRecords,\n  NoResults\n} from \"./chunk-EMIHDNB7.mjs\";\nimport {\n  TableFooterSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\n\n// src/routes/tax-regions/common/components/tax-region-table/tax-region-table.tsx\nimport { Button } from \"@medusajs/ui\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionTable = ({\n  variant = \"country\",\n  isPending,\n  action,\n  count = 0,\n  table,\n  queryObject,\n  prefix,\n  children\n}) => {\n  if (isPending) {\n    return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col divide-y\", children: [\n      Array.from({ length: 3 }).map((_, index) => {\n        return /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: \"bg-ui-bg-field-component h-[52px] w-full animate-pulse\"\n          },\n          index\n        );\n      }),\n      /* @__PURE__ */ jsx(TableFooterSkeleton, { layout: \"fit\" })\n    ] });\n  }\n  const noQuery = Object.values(queryObject).filter((v) => Boolean(v)).length === 0;\n  const noResults = !isPending && count === 0 && !noQuery;\n  const noRecords = !isPending && count === 0 && noQuery;\n  const { pageIndex, pageSize } = table.getState().pagination;\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col divide-y\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col justify-between gap-x-4 gap-y-3 px-6 py-4 md:flex-row md:items-center\", children: [\n      /* @__PURE__ */ jsx(\"div\", { children }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        !noRecords && /* @__PURE__ */ jsx(\"div\", { className: \"flex w-full items-center gap-x-2 md:w-fit\", children: /* @__PURE__ */ jsx(\n          DataTableOrderBy,\n          {\n            keys: [\"updated_at\", \"created_at\"],\n            prefix\n          }\n        ) }),\n        /* @__PURE__ */ jsx(Link, { to: action.to, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: action.label }) })\n      ] })\n    ] }),\n    noResults && /* @__PURE__ */ jsx(NoResults, {}),\n    noRecords && /* @__PURE__ */ jsx(NoRecords, {}),\n    !noRecords && !noResults ? !isPending ? table.getRowModel().rows.map((row) => {\n      return /* @__PURE__ */ jsx(\n        TaxRegionCard,\n        {\n          variant,\n          taxRegion: row.original,\n          role: \"row\",\n          \"aria-rowindex\": row.index\n        },\n        row.id\n      );\n    }) : Array.from({ length: 3 }).map((_, index) => {\n      return /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          className: \"bg-ui-bg-field-component h-[60px] w-full animate-pulse\"\n        },\n        index\n      );\n    }) : null,\n    !noRecords && /* @__PURE__ */ jsx(\n      LocalizedTablePagination,\n      {\n        prefix,\n        canNextPage: table.getCanNextPage(),\n        canPreviousPage: table.getCanPreviousPage(),\n        count,\n        nextPage: table.nextPage,\n        previousPage: table.previousPage,\n        pageCount: table.getPageCount(),\n        pageIndex,\n        pageSize\n      }\n    )\n  ] });\n};\n\n// src/routes/tax-regions/common/hooks/use-tax-region-table.tsx\nimport {\n  getCoreRowModel,\n  getPaginationRowModel,\n  useReactTable\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useSearchParams } from \"react-router-dom\";\nvar useTaxRegionTable = ({\n  data = [],\n  count = 0,\n  pageSize: _pageSize = 10,\n  prefix\n}) => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const offsetKey = `${prefix ? `${prefix}_` : \"\"}offset`;\n  const offset = searchParams.get(offsetKey);\n  const [{ pageIndex, pageSize }, setPagination] = useState({\n    pageIndex: offset ? Math.ceil(Number(offset) / _pageSize) : 0,\n    pageSize: _pageSize\n  });\n  const pagination = useMemo(\n    () => ({\n      pageIndex,\n      pageSize\n    }),\n    [pageIndex, pageSize]\n  );\n  useEffect(() => {\n    const index = offset ? Math.ceil(Number(offset) / _pageSize) : 0;\n    if (index === pageIndex) {\n      return;\n    }\n    setPagination((prev) => ({\n      ...prev,\n      pageIndex: index\n    }));\n  }, [offset, _pageSize, pageIndex]);\n  const onPaginationChange = (updater) => {\n    const state = updater(pagination);\n    const { pageIndex: pageIndex2, pageSize: pageSize2 } = state;\n    setSearchParams((prev) => {\n      if (!pageIndex2) {\n        prev.delete(offsetKey);\n        return prev;\n      }\n      const newSearch = new URLSearchParams(prev);\n      newSearch.set(offsetKey, String(pageIndex2 * pageSize2));\n      return newSearch;\n    });\n    setPagination(state);\n    return state;\n  };\n  const table = useReactTable({\n    data,\n    columns: [],\n    // We don't actually want to render any columns\n    pageCount: Math.ceil(count / pageSize),\n    state: {\n      pagination\n    },\n    getCoreRowModel: getCoreRowModel(),\n    onPaginationChange,\n    getPaginationRowModel: getPaginationRowModel(),\n    manualPagination: true\n  });\n  return {\n    table\n  };\n};\n\nexport {\n  TaxRegionTable,\n  useTaxRegionTable\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,yBAA0B;AAwF1B,mBAA6C;AAvF7C,IAAI,iBAAiB,CAAC;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,WAAW;AACb,eAAuB,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,MAClF,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC1C,mBAAuB;AAAA,UACrB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,UACe,wBAAI,qBAAqB,EAAE,QAAQ,MAAM,CAAC;AAAA,IAC5D,EAAE,CAAC;AAAA,EACL;AACA,QAAM,UAAU,OAAO,OAAO,WAAW,EAAE,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC,EAAE,WAAW;AAChF,QAAM,YAAY,CAAC,aAAa,UAAU,KAAK,CAAC;AAChD,QAAM,YAAY,CAAC,aAAa,UAAU,KAAK;AAC/C,QAAM,EAAE,WAAW,SAAS,IAAI,MAAM,SAAS,EAAE;AACjD,aAAuB,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,QAClE,yBAAK,OAAO,EAAE,WAAW,uFAAuF,UAAU;AAAA,UACxH,wBAAI,OAAO,EAAE,SAAS,CAAC;AAAA,UACvB,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QAC9E,CAAC,iBAA6B,wBAAI,OAAO,EAAE,WAAW,6CAA6C,cAA0B;AAAA,UAC3H;AAAA,UACA;AAAA,YACE,MAAM,CAAC,cAAc,YAAY;AAAA,YACjC;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,MAAM,EAAE,IAAI,OAAO,IAAI,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC;AAAA,MACrJ,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,iBAA6B,wBAAI,WAAW,CAAC,CAAC;AAAA,IAC9C,iBAA6B,wBAAI,WAAW,CAAC,CAAC;AAAA,IAC9C,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,MAAM,YAAY,EAAE,KAAK,IAAI,CAAC,QAAQ;AAC5E,iBAAuB;AAAA,QACrB;AAAA,QACA;AAAA,UACE;AAAA,UACA,WAAW,IAAI;AAAA,UACf,MAAM;AAAA,UACN,iBAAiB,IAAI;AAAA,QACvB;AAAA,QACA,IAAI;AAAA,MACN;AAAA,IACF,CAAC,IAAI,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC/C,iBAAuB;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,IAAI;AAAA,IACL,CAAC,iBAA6B;AAAA,MAC5B;AAAA,MACA;AAAA,QACE;AAAA,QACA,aAAa,MAAM,eAAe;AAAA,QAClC,iBAAiB,MAAM,mBAAmB;AAAA,QAC1C;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,cAAc,MAAM;AAAA,QACpB,WAAW,MAAM,aAAa;AAAA,QAC9B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAUA,IAAI,oBAAoB,CAAC;AAAA,EACvB,OAAO,CAAC;AAAA,EACR,QAAQ;AAAA,EACR,UAAU,YAAY;AAAA,EACtB;AACF,MAAM;AACJ,QAAM,CAAC,cAAc,eAAe,IAAI,gBAAgB;AACxD,QAAM,YAAY,GAAG,SAAS,GAAG,MAAM,MAAM,EAAE;AAC/C,QAAM,SAAS,aAAa,IAAI,SAAS;AACzC,QAAM,CAAC,EAAE,WAAW,SAAS,GAAG,aAAa,QAAI,uBAAS;AAAA,IACxD,WAAW,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS,IAAI;AAAA,IAC5D,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,iBAAa;AAAA,IACjB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,IACA,CAAC,WAAW,QAAQ;AAAA,EACtB;AACA,8BAAU,MAAM;AACd,UAAM,QAAQ,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS,IAAI;AAC/D,QAAI,UAAU,WAAW;AACvB;AAAA,IACF;AACA,kBAAc,CAAC,UAAU;AAAA,MACvB,GAAG;AAAA,MACH,WAAW;AAAA,IACb,EAAE;AAAA,EACJ,GAAG,CAAC,QAAQ,WAAW,SAAS,CAAC;AACjC,QAAM,qBAAqB,CAAC,YAAY;AACtC,UAAM,QAAQ,QAAQ,UAAU;AAChC,UAAM,EAAE,WAAW,YAAY,UAAU,UAAU,IAAI;AACvD,oBAAgB,CAAC,SAAS;AACxB,UAAI,CAAC,YAAY;AACf,aAAK,OAAO,SAAS;AACrB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,IAAI,gBAAgB,IAAI;AAC1C,gBAAU,IAAI,WAAW,OAAO,aAAa,SAAS,CAAC;AACvD,aAAO;AAAA,IACT,CAAC;AACD,kBAAc,KAAK;AACnB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,cAAc;AAAA,IAC1B;AAAA,IACA,SAAS,CAAC;AAAA;AAAA,IAEV,WAAW,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACrC,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,iBAAiB,gBAAgB;AAAA,IACjC;AAAA,IACA,uBAAuB,sBAAsB;AAAA,IAC7C,kBAAkB;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": []}