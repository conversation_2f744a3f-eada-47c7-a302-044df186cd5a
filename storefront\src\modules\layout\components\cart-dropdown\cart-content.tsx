"use client"

import { useRouter } from "next/navigation"
import React from "react"
import { useTranslation } from "react-i18next"

import { TCartCustomField } from "types/cart"
import { PAGE_PATH } from "utils/path"

import { Button } from "components/ui/button"
import { ScrollArea } from "components/ui/scroll-area"
import Typography from "components/ui/typography"

import { convertCurrencyToLocale } from "@lib/util/money"
import Divider from "@modules/common/components/divider"
import { CartLineItem } from "../cart-line-item"

interface CartProps {
  cart?: TCartCustomField | null
  currency_code?: string
  onClose: () => void
}

const CartContent: React.FC<CartProps> = ({ cart, currency_code, onClose }) => {
  const { t } = useTranslation("layout")
  const { push } = useRouter()

  const handleGoToCart = () => {
    push(PAGE_PATH.CART)
    onClose()
  }

  const renderEmptyCart = () => (
    <div className="flex flex-col items-center justify-center gap-y-4 py-16">
      <Typography
        variant="p"
        size="md"
        className="font-semibold italic text-gray-600"
      >
        {t("cart.empty")}
      </Typography>
    </div>
  )

  return (
    <div className="flex flex-col items-center gap-y-4">
      <Typography
        variant="h6"
        size="md"
        color="primary"
        className="text-center font-bold uppercase"
      >
        {t("cart.title")}
      </Typography>
      {cart?.items?.length ? (
        <ScrollArea className="max-h-[500px] min-h-[120px] w-full">
          {cart.items.map((item) => {
            return (
              <React.Fragment key={item.id}>
                <CartLineItem
                  item={item}
                  currency_code={currency_code || "VND"}
                />
                <Divider className="my-6 w-full" />
              </React.Fragment>
            )
          })}
        </ScrollArea>
      ) : (
        renderEmptyCart()
      )}
      <div className="flex w-full justify-between">
        <Typography variant="p" size="base" className="text-gray-600">
          {t("cart.sub_total")}
        </Typography>
        <Typography
          variant="p"
          size="base"
          color="primary"
          className="font-bold"
        >
          {convertCurrencyToLocale({
            amount: cart?.subtotal || 0,
            currency_code: currency_code || "VND",
          })}
        </Typography>
      </div>
      <Button
        className="w-full rounded-xs"
        disabled={!cart?.items?.length}
        onClick={handleGoToCart}
      >
        {t("cart.go_to_cart")}
      </Button>
    </div>
  )
}

export default CartContent
