import {
  DataGridCellContainer,
  useC<PERSON>inedRefs,
  useDataGridCell,
  useDataGridCellError
} from "./chunk-JGBVAJ3K.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  Controller
} from "./chunk-DPO7J5IQ.js";
import {
  CurrencyInput,
  Switch
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NOZD6HRU.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DataGridTogglableNumberCell = ({
  context,
  disabledToggleTooltip,
  ...rest
}) => {
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  return (0, import_jsx_runtime.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime.jsx)(
          DataGridCellContainer,
          {
            ...container,
            ...errorProps,
            outerComponent: (0, import_jsx_runtime.jsx)(
              OuterComponent,
              {
                field: field2,
                inputProps: input,
                isAnchor: container.isAnchor,
                tooltip: disabledToggleTooltip
              }
            ),
            children: (0, import_jsx_runtime.jsx)(Inner, { field: field2, inputProps: input, ...rest })
          }
        );
      }
    }
  );
};
var OuterComponent = ({
  field,
  inputProps,
  isAnchor,
  tooltip
}) => {
  const buttonRef = (0, import_react.useRef)(null);
  const { value } = field;
  const { onChange } = inputProps;
  const [localValue, setLocalValue] = (0, import_react.useState)(value);
  (0, import_react.useEffect)(() => {
    setLocalValue(value);
  }, [value]);
  const handleCheckedChange = (update) => {
    const newValue = { ...localValue, checked: update };
    if (!update && !newValue.disabledToggle) {
      newValue.quantity = "";
    }
    if (update && newValue.quantity === "") {
      newValue.quantity = 0;
    }
    setLocalValue(newValue);
    onChange(newValue, value);
  };
  (0, import_react.useEffect)(() => {
    const handleKeyDown = (e) => {
      var _a;
      if (isAnchor && e.key.toLowerCase() === "x") {
        e.preventDefault();
        (_a = buttonRef.current) == null ? void 0 : _a.click();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isAnchor]);
  return (0, import_jsx_runtime.jsx)(
    ConditionalTooltip,
    {
      showTooltip: localValue.disabledToggle && tooltip,
      content: tooltip,
      children: (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 left-4 z-[3] flex w-fit items-center justify-center", children: (0, import_jsx_runtime.jsx)(
        Switch,
        {
          ref: buttonRef,
          size: "small",
          className: "shrink-0",
          checked: localValue.checked,
          disabled: localValue.disabledToggle,
          onCheckedChange: handleCheckedChange
        }
      ) })
    }
  );
};
var Inner = ({
  field,
  inputProps,
  placeholder,
  ...props
}) => {
  const { ref, value, onChange: _, onBlur, ...fieldProps } = field;
  const {
    ref: inputRef,
    onChange,
    onBlur: onInputBlur,
    onFocus,
    ...attributes
  } = inputProps;
  const [localValue, setLocalValue] = (0, import_react.useState)(value);
  (0, import_react.useEffect)(() => {
    setLocalValue(value);
  }, [value]);
  const combinedRefs = useCombinedRefs(inputRef, ref);
  const handleInputChange = (updatedValue, _name, _values) => {
    const ensuredValue = updatedValue !== void 0 ? updatedValue : "";
    const newValue = { ...localValue, quantity: ensuredValue };
    if (ensuredValue !== "") {
      newValue.checked = true;
    } else if (newValue.checked && newValue.disabledToggle === false) {
      newValue.checked = false;
    }
    setLocalValue(newValue);
  };
  const handleOnChange = () => {
    if (localValue.disabledToggle && localValue.quantity === "") {
      localValue.quantity = 0;
    }
    onChange(localValue, value);
  };
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center gap-x-2", children: (0, import_jsx_runtime.jsx)(
    CurrencyInput,
    {
      ...fieldProps,
      ...attributes,
      ...props,
      ref: combinedRefs,
      className: "txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-8 text-right outline-none",
      value: localValue == null ? void 0 : localValue.quantity,
      onValueChange: handleInputChange,
      formatValueOnBlur: true,
      onBlur: () => {
        onBlur();
        onInputBlur();
        handleOnChange();
      },
      onFocus,
      decimalsLimit: 0,
      autoComplete: "off",
      tabIndex: -1,
      placeholder: !localValue.checked ? placeholder : void 0
    }
  ) });
};

export {
  DataGridTogglableNumberCell
};
//# sourceMappingURL=chunk-BX5SNYTN.js.map
