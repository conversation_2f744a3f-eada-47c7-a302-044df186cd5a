import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  require_MapCache,
  require_eq,
  require_getNative,
  require_isArray,
  require_isIndex
} from "./chunk-7ANVLPZR.js";
import {
  require_isSymbol
} from "./chunk-5QX4V4M4.js";
import {
  Skeleton
} from "./chunk-TP2BI5T3.js";
import {
  ConditionalTooltip
} from "./chunk-KJC3C3MI.js";
import {
  require_Symbol,
  require_isObject
} from "./chunk-NV2N3EWM.js";
import {
  Controller,
  get,
  useFormContext,
  useWatch
} from "./chunk-DPO7J5IQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  __publicField
} from "./chunk-XMKQFEQ4.js";
import {
  Adjustments,
  AdjustmentsDone,
  Badge,
  Button,
  Checkbox,
  CurrencyInput,
  DropdownMenu,
  ExclamationCircle,
  Heading,
  IconButton,
  Input,
  Kbd,
  TaxExclusive,
  TaxInclusive,
  Text,
  Tooltip,
  XMark,
  clx,
  createColumnHelper,
  dist_exports4 as dist_exports,
  flexRender,
  formatValue,
  getCoreRowModel,
  useReactTable
} from "./chunk-RJPG7Y6U.js";
import {
  require_react_dom
} from "./chunk-RPCDYKBN.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __commonJS,
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/lodash/_isKey.js
var require_isKey = __commonJS({
  "node_modules/lodash/_isKey.js"(exports, module) {
    var isArray = require_isArray();
    var isSymbol = require_isSymbol();
    var reIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
    var reIsPlainProp = /^\w*$/;
    function isKey(value, object) {
      if (isArray(value)) {
        return false;
      }
      var type = typeof value;
      if (type == "number" || type == "symbol" || type == "boolean" || value == null || isSymbol(value)) {
        return true;
      }
      return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);
    }
    module.exports = isKey;
  }
});

// node_modules/lodash/memoize.js
var require_memoize = __commonJS({
  "node_modules/lodash/memoize.js"(exports, module) {
    var MapCache = require_MapCache();
    var FUNC_ERROR_TEXT = "Expected a function";
    function memoize(func, resolver) {
      if (typeof func != "function" || resolver != null && typeof resolver != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      var memoized = function() {
        var args = arguments, key = resolver ? resolver.apply(this, args) : args[0], cache = memoized.cache;
        if (cache.has(key)) {
          return cache.get(key);
        }
        var result = func.apply(this, args);
        memoized.cache = cache.set(key, result) || cache;
        return result;
      };
      memoized.cache = new (memoize.Cache || MapCache)();
      return memoized;
    }
    memoize.Cache = MapCache;
    module.exports = memoize;
  }
});

// node_modules/lodash/_memoizeCapped.js
var require_memoizeCapped = __commonJS({
  "node_modules/lodash/_memoizeCapped.js"(exports, module) {
    var memoize = require_memoize();
    var MAX_MEMOIZE_SIZE = 500;
    function memoizeCapped(func) {
      var result = memoize(func, function(key) {
        if (cache.size === MAX_MEMOIZE_SIZE) {
          cache.clear();
        }
        return key;
      });
      var cache = result.cache;
      return result;
    }
    module.exports = memoizeCapped;
  }
});

// node_modules/lodash/_stringToPath.js
var require_stringToPath = __commonJS({
  "node_modules/lodash/_stringToPath.js"(exports, module) {
    var memoizeCapped = require_memoizeCapped();
    var rePropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;
    var reEscapeChar = /\\(\\)?/g;
    var stringToPath = memoizeCapped(function(string) {
      var result = [];
      if (string.charCodeAt(0) === 46) {
        result.push("");
      }
      string.replace(rePropName, function(match, number, quote, subString) {
        result.push(quote ? subString.replace(reEscapeChar, "$1") : number || match);
      });
      return result;
    });
    module.exports = stringToPath;
  }
});

// node_modules/lodash/_arrayMap.js
var require_arrayMap = __commonJS({
  "node_modules/lodash/_arrayMap.js"(exports, module) {
    function arrayMap(array, iteratee) {
      var index = -1, length = array == null ? 0 : array.length, result = Array(length);
      while (++index < length) {
        result[index] = iteratee(array[index], index, array);
      }
      return result;
    }
    module.exports = arrayMap;
  }
});

// node_modules/lodash/_baseToString.js
var require_baseToString = __commonJS({
  "node_modules/lodash/_baseToString.js"(exports, module) {
    var Symbol = require_Symbol();
    var arrayMap = require_arrayMap();
    var isArray = require_isArray();
    var isSymbol = require_isSymbol();
    var INFINITY = 1 / 0;
    var symbolProto = Symbol ? Symbol.prototype : void 0;
    var symbolToString = symbolProto ? symbolProto.toString : void 0;
    function baseToString(value) {
      if (typeof value == "string") {
        return value;
      }
      if (isArray(value)) {
        return arrayMap(value, baseToString) + "";
      }
      if (isSymbol(value)) {
        return symbolToString ? symbolToString.call(value) : "";
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    module.exports = baseToString;
  }
});

// node_modules/lodash/toString.js
var require_toString = __commonJS({
  "node_modules/lodash/toString.js"(exports, module) {
    var baseToString = require_baseToString();
    function toString(value) {
      return value == null ? "" : baseToString(value);
    }
    module.exports = toString;
  }
});

// node_modules/lodash/_castPath.js
var require_castPath = __commonJS({
  "node_modules/lodash/_castPath.js"(exports, module) {
    var isArray = require_isArray();
    var isKey = require_isKey();
    var stringToPath = require_stringToPath();
    var toString = require_toString();
    function castPath(value, object) {
      if (isArray(value)) {
        return value;
      }
      return isKey(value, object) ? [value] : stringToPath(toString(value));
    }
    module.exports = castPath;
  }
});

// node_modules/lodash/_toKey.js
var require_toKey = __commonJS({
  "node_modules/lodash/_toKey.js"(exports, module) {
    var isSymbol = require_isSymbol();
    var INFINITY = 1 / 0;
    function toKey(value) {
      if (typeof value == "string" || isSymbol(value)) {
        return value;
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    module.exports = toKey;
  }
});

// node_modules/lodash/_baseGet.js
var require_baseGet = __commonJS({
  "node_modules/lodash/_baseGet.js"(exports, module) {
    var castPath = require_castPath();
    var toKey = require_toKey();
    function baseGet(object, path) {
      path = castPath(path, object);
      var index = 0, length = path.length;
      while (object != null && index < length) {
        object = object[toKey(path[index++])];
      }
      return index && index == length ? object : void 0;
    }
    module.exports = baseGet;
  }
});

// node_modules/lodash/get.js
var require_get = __commonJS({
  "node_modules/lodash/get.js"(exports, module) {
    var baseGet = require_baseGet();
    function get3(object, path, defaultValue) {
      var result = object == null ? void 0 : baseGet(object, path);
      return result === void 0 ? defaultValue : result;
    }
    module.exports = get3;
  }
});

// node_modules/lodash/_defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/lodash/_defineProperty.js"(exports, module) {
    var getNative = require_getNative();
    var defineProperty = function() {
      try {
        var func = getNative(Object, "defineProperty");
        func({}, "", {});
        return func;
      } catch (e2) {
      }
    }();
    module.exports = defineProperty;
  }
});

// node_modules/lodash/_baseAssignValue.js
var require_baseAssignValue = __commonJS({
  "node_modules/lodash/_baseAssignValue.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function baseAssignValue(object, key, value) {
      if (key == "__proto__" && defineProperty) {
        defineProperty(object, key, {
          "configurable": true,
          "enumerable": true,
          "value": value,
          "writable": true
        });
      } else {
        object[key] = value;
      }
    }
    module.exports = baseAssignValue;
  }
});

// node_modules/lodash/_assignValue.js
var require_assignValue = __commonJS({
  "node_modules/lodash/_assignValue.js"(exports, module) {
    var baseAssignValue = require_baseAssignValue();
    var eq = require_eq();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function assignValue(object, key, value) {
      var objValue = object[key];
      if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
        baseAssignValue(object, key, value);
      }
    }
    module.exports = assignValue;
  }
});

// node_modules/lodash/_baseSet.js
var require_baseSet = __commonJS({
  "node_modules/lodash/_baseSet.js"(exports, module) {
    var assignValue = require_assignValue();
    var castPath = require_castPath();
    var isIndex = require_isIndex();
    var isObject = require_isObject();
    var toKey = require_toKey();
    function baseSet(object, path, value, customizer) {
      if (!isObject(object)) {
        return object;
      }
      path = castPath(path, object);
      var index = -1, length = path.length, lastIndex = length - 1, nested = object;
      while (nested != null && ++index < length) {
        var key = toKey(path[index]), newValue = value;
        if (key === "__proto__" || key === "constructor" || key === "prototype") {
          return object;
        }
        if (index != lastIndex) {
          var objValue = nested[key];
          newValue = customizer ? customizer(objValue, key, nested) : void 0;
          if (newValue === void 0) {
            newValue = isObject(objValue) ? objValue : isIndex(path[index + 1]) ? [] : {};
          }
        }
        assignValue(nested, key, newValue);
        nested = nested[key];
      }
      return object;
    }
    module.exports = baseSet;
  }
});

// node_modules/lodash/set.js
var require_set = __commonJS({
  "node_modules/lodash/set.js"(exports, module) {
    var baseSet = require_baseSet();
    function set2(object, path, value) {
      return object == null ? object : baseSet(object, path, value);
    }
    module.exports = set2;
  }
});

// node_modules/@medusajs/dashboard/dist/chunk-GE4APTT2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_react8 = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var import_react10 = __toESM(require_react(), 1);
var import_get = __toESM(require_get(), 1);
var import_set = __toESM(require_set(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_react12 = __toESM(require_react(), 1);
var import_react13 = __toESM(require_react(), 1);
var import_react14 = __toESM(require_react(), 1);
var import_react15 = __toESM(require_react(), 1);

// node_modules/@hookform/error-message/dist/index.esm.js
var e = __toESM(require_react());
var s = function(s2) {
  var t = s2.as, a = s2.errors, m = s2.name, o = s2.message, i = s2.render, l = function(e2, r) {
    if (null == e2) return {};
    var n, s3, t2 = {}, a2 = Object.keys(e2);
    for (s3 = 0; s3 < a2.length; s3++) r.indexOf(n = a2[s3]) >= 0 || (t2[n] = e2[n]);
    return t2;
  }(s2, ["as", "errors", "name", "message", "render"]), f = useFormContext(), c = get(a || f.formState.errors, m);
  if (!c) return null;
  var g = c.message, u = c.types, d = Object.assign({}, l, { children: g || o });
  return e.isValidElement(t) ? e.cloneElement(t, d) : i ? i({ message: g || o, messages: u }) : e.createElement(t || e.Fragment, d);
};

// node_modules/@medusajs/dashboard/dist/chunk-GE4APTT2.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react16 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react17 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);

// node_modules/@tanstack/react-virtual/dist/esm/index.js
var React = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());

// node_modules/@tanstack/virtual-core/dist/esm/utils.js
function memo(getDeps, fn, opts) {
  let deps = opts.initialDeps ?? [];
  let result;
  return () => {
    var _a, _b, _c, _d;
    let depTime;
    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();
    const newDeps = getDeps();
    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);
    if (!depsChanged) {
      return result;
    }
    deps = newDeps;
    let resultTime;
    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();
    result = fn(...newDeps);
    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {
      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;
      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;
      const resultFpsPercentage = resultEndTime / 16;
      const pad = (str, num) => {
        str = String(str);
        while (str.length < num) {
          str = " " + str;
        }
        return str;
      };
      console.info(
        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,
        `
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(
          0,
          Math.min(120 - 120 * resultFpsPercentage, 120)
        )}deg 100% 31%);`,
        opts == null ? void 0 : opts.key
      );
    }
    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);
    return result;
  };
}
function notUndefined(value, msg) {
  if (value === void 0) {
    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ""}`);
  } else {
    return value;
  }
}
var approxEqual = (a, b) => Math.abs(a - b) < 1;
var debounce = (targetWindow, fn, ms) => {
  let timeoutId;
  return function(...args) {
    targetWindow.clearTimeout(timeoutId);
    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);
  };
};

// node_modules/@tanstack/virtual-core/dist/esm/index.js
var defaultKeyExtractor = (index) => index;
var defaultRangeExtractor = (range) => {
  const start = Math.max(range.startIndex - range.overscan, 0);
  const end = Math.min(range.endIndex + range.overscan, range.count - 1);
  const arr = [];
  for (let i = start; i <= end; i++) {
    arr.push(i);
  }
  return arr;
};
var observeElementRect = (instance, cb) => {
  const element = instance.scrollElement;
  if (!element) {
    return;
  }
  const targetWindow = instance.targetWindow;
  if (!targetWindow) {
    return;
  }
  const handler = (rect) => {
    const { width, height } = rect;
    cb({ width: Math.round(width), height: Math.round(height) });
  };
  handler(element.getBoundingClientRect());
  if (!targetWindow.ResizeObserver) {
    return () => {
    };
  }
  const observer = new targetWindow.ResizeObserver((entries) => {
    const run = () => {
      const entry = entries[0];
      if (entry == null ? void 0 : entry.borderBoxSize) {
        const box = entry.borderBoxSize[0];
        if (box) {
          handler({ width: box.inlineSize, height: box.blockSize });
          return;
        }
      }
      handler(element.getBoundingClientRect());
    };
    instance.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();
  });
  observer.observe(element, { box: "border-box" });
  return () => {
    observer.unobserve(element);
  };
};
var addEventListenerOptions = {
  passive: true
};
var supportsScrollend = typeof window == "undefined" ? true : "onscrollend" in window;
var observeElementOffset = (instance, cb) => {
  const element = instance.scrollElement;
  if (!element) {
    return;
  }
  const targetWindow = instance.targetWindow;
  if (!targetWindow) {
    return;
  }
  let offset = 0;
  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(
    targetWindow,
    () => {
      cb(offset, false);
    },
    instance.options.isScrollingResetDelay
  );
  const createHandler = (isScrolling) => () => {
    const { horizontal, isRtl } = instance.options;
    offset = horizontal ? element["scrollLeft"] * (isRtl && -1 || 1) : element["scrollTop"];
    fallback();
    cb(offset, isScrolling);
  };
  const handler = createHandler(true);
  const endHandler = createHandler(false);
  endHandler();
  element.addEventListener("scroll", handler, addEventListenerOptions);
  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;
  if (registerScrollendEvent) {
    element.addEventListener("scrollend", endHandler, addEventListenerOptions);
  }
  return () => {
    element.removeEventListener("scroll", handler);
    if (registerScrollendEvent) {
      element.removeEventListener("scrollend", endHandler);
    }
  };
};
var measureElement = (element, entry, instance) => {
  if (entry == null ? void 0 : entry.borderBoxSize) {
    const box = entry.borderBoxSize[0];
    if (box) {
      const size = Math.round(
        box[instance.options.horizontal ? "inlineSize" : "blockSize"]
      );
      return size;
    }
  }
  return Math.round(
    element.getBoundingClientRect()[instance.options.horizontal ? "width" : "height"]
  );
};
var elementScroll = (offset, {
  adjustments = 0,
  behavior
}, instance) => {
  var _a, _b;
  const toOffset = offset + adjustments;
  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {
    [instance.options.horizontal ? "left" : "top"]: toOffset,
    behavior
  });
};
var Virtualizer = class {
  constructor(opts) {
    this.unsubs = [];
    this.scrollElement = null;
    this.targetWindow = null;
    this.isScrolling = false;
    this.scrollToIndexTimeoutId = null;
    this.measurementsCache = [];
    this.itemSizeCache = /* @__PURE__ */ new Map();
    this.pendingMeasuredCacheIndexes = [];
    this.scrollRect = null;
    this.scrollOffset = null;
    this.scrollDirection = null;
    this.scrollAdjustments = 0;
    this.elementsCache = /* @__PURE__ */ new Map();
    this.observer = /* @__PURE__ */ (() => {
      let _ro = null;
      const get3 = () => {
        if (_ro) {
          return _ro;
        }
        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {
          return null;
        }
        return _ro = new this.targetWindow.ResizeObserver((entries) => {
          entries.forEach((entry) => {
            const run = () => {
              this._measureElement(entry.target, entry);
            };
            this.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();
          });
        });
      };
      return {
        disconnect: () => {
          var _a;
          (_a = get3()) == null ? void 0 : _a.disconnect();
          _ro = null;
        },
        observe: (target) => {
          var _a;
          return (_a = get3()) == null ? void 0 : _a.observe(target, { box: "border-box" });
        },
        unobserve: (target) => {
          var _a;
          return (_a = get3()) == null ? void 0 : _a.unobserve(target);
        }
      };
    })();
    this.range = null;
    this.setOptions = (opts2) => {
      Object.entries(opts2).forEach(([key, value]) => {
        if (typeof value === "undefined") delete opts2[key];
      });
      this.options = {
        debug: false,
        initialOffset: 0,
        overscan: 1,
        paddingStart: 0,
        paddingEnd: 0,
        scrollPaddingStart: 0,
        scrollPaddingEnd: 0,
        horizontal: false,
        getItemKey: defaultKeyExtractor,
        rangeExtractor: defaultRangeExtractor,
        onChange: () => {
        },
        measureElement,
        initialRect: { width: 0, height: 0 },
        scrollMargin: 0,
        gap: 0,
        indexAttribute: "data-index",
        initialMeasurementsCache: [],
        lanes: 1,
        isScrollingResetDelay: 150,
        enabled: true,
        isRtl: false,
        useScrollendEvent: true,
        useAnimationFrameWithResizeObserver: false,
        ...opts2
      };
    };
    this.notify = (sync) => {
      var _a, _b;
      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);
    };
    this.maybeNotify = memo(
      () => {
        this.calculateRange();
        return [
          this.isScrolling,
          this.range ? this.range.startIndex : null,
          this.range ? this.range.endIndex : null
        ];
      },
      (isScrolling) => {
        this.notify(isScrolling);
      },
      {
        key: "maybeNotify",
        debug: () => this.options.debug,
        initialDeps: [
          this.isScrolling,
          this.range ? this.range.startIndex : null,
          this.range ? this.range.endIndex : null
        ]
      }
    );
    this.cleanup = () => {
      this.unsubs.filter(Boolean).forEach((d) => d());
      this.unsubs = [];
      this.observer.disconnect();
      this.scrollElement = null;
      this.targetWindow = null;
    };
    this._didMount = () => {
      return () => {
        this.cleanup();
      };
    };
    this._willUpdate = () => {
      var _a;
      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;
      if (this.scrollElement !== scrollElement) {
        this.cleanup();
        if (!scrollElement) {
          this.maybeNotify();
          return;
        }
        this.scrollElement = scrollElement;
        if (this.scrollElement && "ownerDocument" in this.scrollElement) {
          this.targetWindow = this.scrollElement.ownerDocument.defaultView;
        } else {
          this.targetWindow = ((_a = this.scrollElement) == null ? void 0 : _a.window) ?? null;
        }
        this.elementsCache.forEach((cached) => {
          this.observer.observe(cached);
        });
        this._scrollToOffset(this.getScrollOffset(), {
          adjustments: void 0,
          behavior: void 0
        });
        this.unsubs.push(
          this.options.observeElementRect(this, (rect) => {
            this.scrollRect = rect;
            this.maybeNotify();
          })
        );
        this.unsubs.push(
          this.options.observeElementOffset(this, (offset, isScrolling) => {
            this.scrollAdjustments = 0;
            this.scrollDirection = isScrolling ? this.getScrollOffset() < offset ? "forward" : "backward" : null;
            this.scrollOffset = offset;
            this.isScrolling = isScrolling;
            this.maybeNotify();
          })
        );
      }
    };
    this.getSize = () => {
      if (!this.options.enabled) {
        this.scrollRect = null;
        return 0;
      }
      this.scrollRect = this.scrollRect ?? this.options.initialRect;
      return this.scrollRect[this.options.horizontal ? "width" : "height"];
    };
    this.getScrollOffset = () => {
      if (!this.options.enabled) {
        this.scrollOffset = null;
        return 0;
      }
      this.scrollOffset = this.scrollOffset ?? (typeof this.options.initialOffset === "function" ? this.options.initialOffset() : this.options.initialOffset);
      return this.scrollOffset;
    };
    this.getFurthestMeasurement = (measurements, index) => {
      const furthestMeasurementsFound = /* @__PURE__ */ new Map();
      const furthestMeasurements = /* @__PURE__ */ new Map();
      for (let m = index - 1; m >= 0; m--) {
        const measurement = measurements[m];
        if (furthestMeasurementsFound.has(measurement.lane)) {
          continue;
        }
        const previousFurthestMeasurement = furthestMeasurements.get(
          measurement.lane
        );
        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {
          furthestMeasurements.set(measurement.lane, measurement);
        } else if (measurement.end < previousFurthestMeasurement.end) {
          furthestMeasurementsFound.set(measurement.lane, true);
        }
        if (furthestMeasurementsFound.size === this.options.lanes) {
          break;
        }
      }
      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a, b) => {
        if (a.end === b.end) {
          return a.index - b.index;
        }
        return a.end - b.end;
      })[0] : void 0;
    };
    this.getMeasurementOptions = memo(
      () => [
        this.options.count,
        this.options.paddingStart,
        this.options.scrollMargin,
        this.options.getItemKey,
        this.options.enabled
      ],
      (count, paddingStart, scrollMargin, getItemKey, enabled) => {
        this.pendingMeasuredCacheIndexes = [];
        return {
          count,
          paddingStart,
          scrollMargin,
          getItemKey,
          enabled
        };
      },
      {
        key: false
      }
    );
    this.getMeasurements = memo(
      () => [this.getMeasurementOptions(), this.itemSizeCache],
      ({ count, paddingStart, scrollMargin, getItemKey, enabled }, itemSizeCache) => {
        if (!enabled) {
          this.measurementsCache = [];
          this.itemSizeCache.clear();
          return [];
        }
        if (this.measurementsCache.length === 0) {
          this.measurementsCache = this.options.initialMeasurementsCache;
          this.measurementsCache.forEach((item) => {
            this.itemSizeCache.set(item.key, item.size);
          });
        }
        const min = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;
        this.pendingMeasuredCacheIndexes = [];
        const measurements = this.measurementsCache.slice(0, min);
        for (let i = min; i < count; i++) {
          const key = getItemKey(i);
          const furthestMeasurement = this.options.lanes === 1 ? measurements[i - 1] : this.getFurthestMeasurement(measurements, i);
          const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;
          const measuredSize = itemSizeCache.get(key);
          const size = typeof measuredSize === "number" ? measuredSize : this.options.estimateSize(i);
          const end = start + size;
          const lane = furthestMeasurement ? furthestMeasurement.lane : i % this.options.lanes;
          measurements[i] = {
            index: i,
            start,
            size,
            end,
            key,
            lane
          };
        }
        this.measurementsCache = measurements;
        return measurements;
      },
      {
        key: "getMeasurements",
        debug: () => this.options.debug
      }
    );
    this.calculateRange = memo(
      () => [
        this.getMeasurements(),
        this.getSize(),
        this.getScrollOffset(),
        this.options.lanes
      ],
      (measurements, outerSize, scrollOffset, lanes) => {
        return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({
          measurements,
          outerSize,
          scrollOffset,
          lanes
        }) : null;
      },
      {
        key: "calculateRange",
        debug: () => this.options.debug
      }
    );
    this.getVirtualIndexes = memo(
      () => {
        let startIndex = null;
        let endIndex = null;
        const range = this.calculateRange();
        if (range) {
          startIndex = range.startIndex;
          endIndex = range.endIndex;
        }
        return [
          this.options.rangeExtractor,
          this.options.overscan,
          this.options.count,
          startIndex,
          endIndex
        ];
      },
      (rangeExtractor, overscan, count, startIndex, endIndex) => {
        return startIndex === null || endIndex === null ? [] : rangeExtractor({
          startIndex,
          endIndex,
          overscan,
          count
        });
      },
      {
        key: "getVirtualIndexes",
        debug: () => this.options.debug
      }
    );
    this.indexFromElement = (node) => {
      const attributeName = this.options.indexAttribute;
      const indexStr = node.getAttribute(attributeName);
      if (!indexStr) {
        console.warn(
          `Missing attribute name '${attributeName}={index}' on measured element.`
        );
        return -1;
      }
      return parseInt(indexStr, 10);
    };
    this._measureElement = (node, entry) => {
      const index = this.indexFromElement(node);
      const item = this.measurementsCache[index];
      if (!item) {
        return;
      }
      const key = item.key;
      const prevNode = this.elementsCache.get(key);
      if (prevNode !== node) {
        if (prevNode) {
          this.observer.unobserve(prevNode);
        }
        this.observer.observe(node);
        this.elementsCache.set(key, node);
      }
      if (node.isConnected) {
        this.resizeItem(index, this.options.measureElement(node, entry, this));
      }
    };
    this.resizeItem = (index, size) => {
      const item = this.measurementsCache[index];
      if (!item) {
        return;
      }
      const itemSize = this.itemSizeCache.get(item.key) ?? item.size;
      const delta = size - itemSize;
      if (delta !== 0) {
        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : item.start < this.getScrollOffset() + this.scrollAdjustments) {
          if (this.options.debug) {
            console.info("correction", delta);
          }
          this._scrollToOffset(this.getScrollOffset(), {
            adjustments: this.scrollAdjustments += delta,
            behavior: void 0
          });
        }
        this.pendingMeasuredCacheIndexes.push(item.index);
        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size));
        this.notify(false);
      }
    };
    this.measureElement = (node) => {
      if (!node) {
        this.elementsCache.forEach((cached, key) => {
          if (!cached.isConnected) {
            this.observer.unobserve(cached);
            this.elementsCache.delete(key);
          }
        });
        return;
      }
      this._measureElement(node, void 0);
    };
    this.getVirtualItems = memo(
      () => [this.getVirtualIndexes(), this.getMeasurements()],
      (indexes, measurements) => {
        const virtualItems = [];
        for (let k = 0, len = indexes.length; k < len; k++) {
          const i = indexes[k];
          const measurement = measurements[i];
          virtualItems.push(measurement);
        }
        return virtualItems;
      },
      {
        key: "getVirtualItems",
        debug: () => this.options.debug
      }
    );
    this.getVirtualItemForOffset = (offset) => {
      const measurements = this.getMeasurements();
      if (measurements.length === 0) {
        return void 0;
      }
      return notUndefined(
        measurements[findNearestBinarySearch(
          0,
          measurements.length - 1,
          (index) => notUndefined(measurements[index]).start,
          offset
        )]
      );
    };
    this.getOffsetForAlignment = (toOffset, align, itemSize = 0) => {
      const size = this.getSize();
      const scrollOffset = this.getScrollOffset();
      if (align === "auto") {
        align = toOffset >= scrollOffset + size ? "end" : "start";
      }
      if (align === "center") {
        toOffset += (itemSize - size) / 2;
      } else if (align === "end") {
        toOffset -= size;
      }
      const scrollSizeProp = this.options.horizontal ? "scrollWidth" : "scrollHeight";
      const scrollSize = this.scrollElement ? "document" in this.scrollElement ? this.scrollElement.document.documentElement[scrollSizeProp] : this.scrollElement[scrollSizeProp] : 0;
      const maxOffset = scrollSize - size;
      return Math.max(Math.min(maxOffset, toOffset), 0);
    };
    this.getOffsetForIndex = (index, align = "auto") => {
      index = Math.max(0, Math.min(index, this.options.count - 1));
      const item = this.measurementsCache[index];
      if (!item) {
        return void 0;
      }
      const size = this.getSize();
      const scrollOffset = this.getScrollOffset();
      if (align === "auto") {
        if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {
          align = "end";
        } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {
          align = "start";
        } else {
          return [scrollOffset, align];
        }
      }
      const toOffset = align === "end" ? item.end + this.options.scrollPaddingEnd : item.start - this.options.scrollPaddingStart;
      return [
        this.getOffsetForAlignment(toOffset, align, item.size),
        align
      ];
    };
    this.isDynamicMode = () => this.elementsCache.size > 0;
    this.cancelScrollToIndex = () => {
      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {
        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);
        this.scrollToIndexTimeoutId = null;
      }
    };
    this.scrollToOffset = (toOffset, { align = "start", behavior } = {}) => {
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {
        adjustments: void 0,
        behavior
      });
    };
    this.scrollToIndex = (index, { align: initialAlign = "auto", behavior } = {}) => {
      index = Math.max(0, Math.min(index, this.options.count - 1));
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      const offsetAndAlign = this.getOffsetForIndex(index, initialAlign);
      if (!offsetAndAlign) return;
      const [offset, align] = offsetAndAlign;
      this._scrollToOffset(offset, { adjustments: void 0, behavior });
      if (behavior !== "smooth" && this.isDynamicMode() && this.targetWindow) {
        this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {
          this.scrollToIndexTimeoutId = null;
          const elementInDOM = this.elementsCache.has(
            this.options.getItemKey(index)
          );
          if (elementInDOM) {
            const [latestOffset] = notUndefined(
              this.getOffsetForIndex(index, align)
            );
            if (!approxEqual(latestOffset, this.getScrollOffset())) {
              this.scrollToIndex(index, { align, behavior });
            }
          } else {
            this.scrollToIndex(index, { align, behavior });
          }
        });
      }
    };
    this.scrollBy = (delta, { behavior } = {}) => {
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      this._scrollToOffset(this.getScrollOffset() + delta, {
        adjustments: void 0,
        behavior
      });
    };
    this.getTotalSize = () => {
      var _a;
      const measurements = this.getMeasurements();
      let end;
      if (measurements.length === 0) {
        end = this.options.paddingStart;
      } else {
        end = this.options.lanes === 1 ? ((_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) ?? 0 : Math.max(
          ...measurements.slice(-this.options.lanes).map((m) => m.end)
        );
      }
      return Math.max(
        end - this.options.scrollMargin + this.options.paddingEnd,
        0
      );
    };
    this._scrollToOffset = (offset, {
      adjustments,
      behavior
    }) => {
      this.options.scrollToFn(offset, { behavior, adjustments }, this);
    };
    this.measure = () => {
      this.itemSizeCache = /* @__PURE__ */ new Map();
      this.notify(false);
    };
    this.setOptions(opts);
  }
};
var findNearestBinarySearch = (low, high, getCurrentValue, value) => {
  while (low <= high) {
    const middle = (low + high) / 2 | 0;
    const currentValue = getCurrentValue(middle);
    if (currentValue < value) {
      low = middle + 1;
    } else if (currentValue > value) {
      high = middle - 1;
    } else {
      return middle;
    }
  }
  if (low > 0) {
    return low - 1;
  } else {
    return 0;
  }
};
function calculateRange({
  measurements,
  outerSize,
  scrollOffset,
  lanes
}) {
  const lastIndex = measurements.length - 1;
  const getOffset = (index) => measurements[index].start;
  let startIndex = findNearestBinarySearch(
    0,
    lastIndex,
    getOffset,
    scrollOffset
  );
  let endIndex = startIndex;
  while (endIndex < lastIndex && measurements[endIndex].end < scrollOffset + outerSize) {
    endIndex++;
  }
  if (lanes > 1) {
    startIndex = Math.max(0, startIndex - startIndex % lanes);
    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - endIndex % lanes));
  }
  return { startIndex, endIndex };
}

// node_modules/@tanstack/react-virtual/dist/esm/index.js
var useIsomorphicLayoutEffect = typeof document !== "undefined" ? React.useLayoutEffect : React.useEffect;
function useVirtualizerBase(options) {
  const rerender = React.useReducer(() => ({}), {})[1];
  const resolvedOptions = {
    ...options,
    onChange: (instance2, sync) => {
      var _a;
      if (sync) {
        (0, import_react_dom.flushSync)(rerender);
      } else {
        rerender();
      }
      (_a = options.onChange) == null ? void 0 : _a.call(options, instance2, sync);
    }
  };
  const [instance] = React.useState(
    () => new Virtualizer(resolvedOptions)
  );
  instance.setOptions(resolvedOptions);
  useIsomorphicLayoutEffect(() => {
    return instance._didMount();
  }, []);
  useIsomorphicLayoutEffect(() => {
    return instance._willUpdate();
  });
  return instance;
}
function useVirtualizer(options) {
  return useVirtualizerBase({
    observeElementRect,
    observeElementOffset,
    scrollToFn: elementScroll,
    ...options
  });
}

// node_modules/@medusajs/dashboard/dist/chunk-GE4APTT2.mjs
var import_react18 = __toESM(require_react(), 1);
var import_react19 = __toESM(require_react(), 1);
var import_react20 = __toESM(require_react(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_react21 = __toESM(require_react(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var DataGridSkeleton = ({
  columns,
  rows: rowCount = 10
}) => {
  const rows = Array.from({ length: rowCount }, (_, i) => i);
  const colCount = columns.length;
  return (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-subtle size-full", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-base border-b p-4", children: (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-button-neutral h-7 w-[116px] animate-pulse rounded-md" }) }),
    (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-subtle size-full overflow-auto", children: [
      (0, import_jsx_runtime.jsx)(
        "div",
        {
          className: "grid",
          style: {
            gridTemplateColumns: `repeat(${colCount}, 1fr)`
          },
          children: columns.map((_col, i) => {
            return (0, import_jsx_runtime.jsx)(
              "div",
              {
                className: "bg-ui-bg-base flex h-10 w-[200px] items-center border-b border-r px-4 py-2.5 last:border-r-0",
                children: (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-[14px] w-[164px]" })
              },
              i
            );
          })
        }
      ),
      (0, import_jsx_runtime.jsx)("div", { children: rows.map((_, j) => (0, import_jsx_runtime.jsx)(
        "div",
        {
          className: "grid",
          style: { gridTemplateColumns: `repeat(${colCount}, 1fr)` },
          children: columns.map((_col, k) => {
            return (0, import_jsx_runtime.jsx)(
              "div",
              {
                className: "bg-ui-bg-base flex h-10 w-[200px] items-center border-b border-r px-4 py-2.5 last:border-r-0",
                children: (0, import_jsx_runtime.jsx)(Skeleton, { className: "h-[14px] w-[164px]" })
              },
              k
            );
          })
        },
        j
      )) })
    ] })
  ] });
};
function setRef(ref, value) {
  if (typeof ref === "function") {
    ref(value);
  } else if (ref && "current" in ref) {
    ;
    ref.current = value;
  }
}
var useCombinedRefs = (...refs) => {
  return (value) => {
    refs.forEach((ref) => setRef(ref, value));
  };
};
var DataGridContext = (0, import_react2.createContext)(
  null
);
var useDataGridContext = () => {
  const context = (0, import_react3.useContext)(DataGridContext);
  if (!context) {
    throw new Error(
      "useDataGridContext must be used within a DataGridContextProvider"
    );
  }
  return context;
};
function generateCellId(coords) {
  return `${coords.row}:${coords.col}`;
}
function isCellMatch(cell, coords) {
  if (!coords) {
    return false;
  }
  return cell.row === coords.row && cell.col === coords.col;
}
var SPECIAL_FOCUS_KEYS = [".", ","];
function isSpecialFocusKey(event) {
  return SPECIAL_FOCUS_KEYS.includes(event.key) && event.ctrlKey && event.altKey;
}
var textCharacterRegex = /^.$/u;
var numberCharacterRegex = /^[0-9]$/u;
var useDataGridCell = ({
  context
}) => {
  const {
    register,
    control,
    anchor,
    setIsEditing,
    setSingleRange,
    setIsSelecting,
    setRangeEnd,
    getWrapperFocusHandler,
    getWrapperMouseOverHandler,
    getInputChangeHandler,
    getIsCellSelected,
    getIsCellDragSelected,
    getCellMetadata
  } = useDataGridContext();
  const { rowIndex, columnIndex } = context;
  const coords = (0, import_react.useMemo)(
    () => ({ row: rowIndex, col: columnIndex }),
    [rowIndex, columnIndex]
  );
  const { id, field, type, innerAttributes, inputAttributes } = (0, import_react.useMemo)(() => {
    return getCellMetadata(coords);
  }, [coords, getCellMetadata]);
  const [showOverlay, setShowOverlay] = (0, import_react.useState)(true);
  const containerRef = (0, import_react.useRef)(null);
  const inputRef = (0, import_react.useRef)(null);
  const handleOverlayMouseDown = (0, import_react.useCallback)(
    (e2) => {
      e2.preventDefault();
      e2.stopPropagation();
      if (e2.detail === 2) {
        if (inputRef.current) {
          setShowOverlay(false);
          inputRef.current.focus();
          return;
        }
      }
      if (e2.shiftKey) {
        if (coords.col === (anchor == null ? void 0 : anchor.col)) {
          setRangeEnd(coords);
          return;
        }
      }
      if (containerRef.current) {
        setSingleRange(coords);
        setIsSelecting(true);
        containerRef.current.focus();
      }
    },
    [coords, anchor, setRangeEnd, setSingleRange, setIsSelecting]
  );
  const handleBooleanInnerMouseDown = (0, import_react.useCallback)(
    (e2) => {
      var _a;
      e2.preventDefault();
      e2.stopPropagation();
      if (e2.detail === 2) {
        (_a = inputRef.current) == null ? void 0 : _a.focus();
        return;
      }
      if (e2.shiftKey) {
        setRangeEnd(coords);
        return;
      }
      if (containerRef.current) {
        setSingleRange(coords);
        setIsSelecting(true);
        containerRef.current.focus();
      }
    },
    [setIsSelecting, setSingleRange, setRangeEnd, coords]
  );
  const handleInputBlur = (0, import_react.useCallback)(() => {
    setShowOverlay(true);
    setIsEditing(false);
  }, [setIsEditing]);
  const handleInputFocus = (0, import_react.useCallback)(() => {
    setShowOverlay(false);
    setIsEditing(true);
  }, [setIsEditing]);
  const validateKeyStroke = (0, import_react.useCallback)(
    (key) => {
      switch (type) {
        case "togglable-number":
        case "number":
          return numberCharacterRegex.test(key);
        case "text":
          return textCharacterRegex.test(key);
        default:
          return false;
      }
    },
    [type]
  );
  const handleContainerKeyDown = (0, import_react.useCallback)(
    (e2) => {
      var _a;
      if (!inputRef.current || !validateKeyStroke(e2.key) || !showOverlay) {
        return;
      }
      if (e2.key.toLowerCase() === "z" && (e2.ctrlKey || e2.metaKey)) {
        return;
      }
      if (e2.key.toLowerCase() === "c" && (e2.ctrlKey || e2.metaKey)) {
        return;
      }
      if (e2.key.toLowerCase() === "v" && (e2.ctrlKey || e2.metaKey)) {
        return;
      }
      if (e2.key === "Enter") {
        return;
      }
      if (isSpecialFocusKey(e2.nativeEvent)) {
        return;
      }
      inputRef.current.focus();
      setShowOverlay(false);
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.value = "";
        const nativeInputValueSetter = (_a = Object.getOwnPropertyDescriptor(
          window.HTMLInputElement.prototype,
          "value"
        )) == null ? void 0 : _a.set;
        nativeInputValueSetter == null ? void 0 : nativeInputValueSetter.call(inputRef.current, e2.key);
        const event = new Event("input", { bubbles: true });
        inputRef.current.dispatchEvent(event);
      }
      e2.stopPropagation();
      e2.preventDefault();
    },
    [showOverlay, validateKeyStroke]
  );
  const isAnchor = (0, import_react.useMemo)(() => {
    return anchor ? isCellMatch(coords, anchor) : false;
  }, [anchor, coords]);
  const fieldWithoutOverlay = (0, import_react.useMemo)(() => {
    return type === "boolean";
  }, [type]);
  (0, import_react.useEffect)(() => {
    var _a, _b;
    if (isAnchor && !((_a = containerRef.current) == null ? void 0 : _a.contains(document.activeElement))) {
      (_b = containerRef.current) == null ? void 0 : _b.focus();
    }
  }, [isAnchor]);
  const renderProps = {
    container: {
      field,
      isAnchor,
      isSelected: getIsCellSelected(coords),
      isDragSelected: getIsCellDragSelected(coords),
      showOverlay: fieldWithoutOverlay ? false : showOverlay,
      innerProps: {
        ref: containerRef,
        onMouseOver: getWrapperMouseOverHandler(coords),
        onMouseDown: type === "boolean" ? handleBooleanInnerMouseDown : void 0,
        onKeyDown: handleContainerKeyDown,
        onFocus: getWrapperFocusHandler(coords),
        ...innerAttributes
      },
      overlayProps: {
        onMouseDown: handleOverlayMouseDown
      }
    },
    input: {
      ref: inputRef,
      onBlur: handleInputBlur,
      onFocus: handleInputFocus,
      onChange: getInputChangeHandler(field),
      ...inputAttributes
    }
  };
  return {
    id,
    field,
    register,
    control,
    renderProps
  };
};
var useDataGridCellError = ({
  context
}) => {
  const { errors, getCellErrorMetadata, navigateToField } = useDataGridContext();
  const { rowIndex, columnIndex } = context;
  const { accessor, field } = (0, import_react4.useMemo)(() => {
    return getCellErrorMetadata({ row: rowIndex, col: columnIndex });
  }, [rowIndex, columnIndex, getCellErrorMetadata]);
  const rowErrorsObject = accessor && columnIndex === 0 ? get(errors, accessor) : void 0;
  const rowErrors = [];
  function collectErrors(errorObject, baseAccessor) {
    if (!errorObject) {
      return;
    }
    if (isFieldError(errorObject)) {
      const message = errorObject.message;
      const to = () => navigateToField(baseAccessor);
      if (message) {
        rowErrors.push({ message, to });
      }
    } else {
      Object.keys(errorObject).forEach((key) => {
        const nestedError = errorObject[key];
        const fieldAccessor = `${baseAccessor}.${key}`;
        if (nestedError && typeof nestedError === "object") {
          collectErrors(nestedError, fieldAccessor);
        }
      });
    }
  }
  if (rowErrorsObject && accessor) {
    collectErrors(rowErrorsObject, accessor);
  }
  const cellError = field ? get(errors, field) : void 0;
  return {
    errors,
    rowErrors,
    cellError
  };
};
function isFieldError(errors) {
  return typeof errors === "object" && "message" in errors && "type" in errors;
}
var DataGridBulkUpdateCommand = class {
  constructor({ fields, prev, next, setter }) {
    __publicField(this, "_fields");
    __publicField(this, "_prev");
    __publicField(this, "_next");
    __publicField(this, "_setter");
    this._fields = fields;
    this._prev = prev;
    this._next = next;
    this._setter = setter;
  }
  execute(redo = false) {
    this._setter(this._fields, this._next, redo);
  }
  undo() {
    this._setter(this._fields, this._prev, true);
  }
  redo() {
    this.execute(true);
  }
};
var DataGridMatrix = class {
  constructor(data, columns, multiColumnSelection = false) {
    __publicField(this, "multiColumnSelection");
    __publicField(this, "cells");
    __publicField(this, "rowAccessors", []);
    __publicField(this, "columnAccessors", []);
    this.multiColumnSelection = multiColumnSelection;
    this.cells = this._populateCells(data, columns);
    this.rowAccessors = this._computeRowAccessors();
    this.columnAccessors = this._computeColumnAccessors();
  }
  _computeRowAccessors() {
    return this.cells.map((_, rowIndex) => this.getRowAccessor(rowIndex));
  }
  _computeColumnAccessors() {
    if (this.cells.length === 0) {
      return [];
    }
    return this.cells[0].map((_, colIndex) => this.getColumnAccessor(colIndex));
  }
  getFirstNavigableCell() {
    for (let row = 0; row < this.cells.length; row++) {
      for (let col = 0; col < this.cells[0].length; col++) {
        if (this.cells[row][col] !== null) {
          return { row, col };
        }
      }
    }
    return null;
  }
  getFieldsInRow(row) {
    const keys = [];
    if (row < 0 || row >= this.cells.length) {
      return keys;
    }
    this.cells[row].forEach((cell) => {
      if (cell !== null) {
        keys.push(cell.field);
      }
    });
    return keys;
  }
  getFieldsInSelection(start, end) {
    var _a;
    const keys = [];
    if (!start || !end) {
      return keys;
    }
    if (!this.multiColumnSelection && start.col !== end.col) {
      throw new Error(
        "Selection must be in the same column when multiColumnSelection is disabled"
      );
    }
    const startRow = Math.min(start.row, end.row);
    const endRow = Math.max(start.row, end.row);
    const startCol = this.multiColumnSelection ? Math.min(start.col, end.col) : start.col;
    const endCol = this.multiColumnSelection ? Math.max(start.col, end.col) : start.col;
    for (let row = startRow; row <= endRow; row++) {
      for (let col = startCol; col <= endCol; col++) {
        if (this._isValidPosition(row, col) && this.cells[row][col] !== null) {
          keys.push((_a = this.cells[row][col]) == null ? void 0 : _a.field);
        }
      }
    }
    return keys;
  }
  getCellField(cell) {
    var _a;
    if (this._isValidPosition(cell.row, cell.col)) {
      return ((_a = this.cells[cell.row][cell.col]) == null ? void 0 : _a.field) || null;
    }
    return null;
  }
  getCellType(cell) {
    var _a;
    if (this._isValidPosition(cell.row, cell.col)) {
      return ((_a = this.cells[cell.row][cell.col]) == null ? void 0 : _a.type) || null;
    }
    return null;
  }
  getIsCellSelected(cell, start, end) {
    if (!cell || !start || !end) {
      return false;
    }
    if (!this.multiColumnSelection && start.col !== end.col) {
      throw new Error(
        "Selection must be in the same column when multiColumnSelection is disabled"
      );
    }
    const startRow = Math.min(start.row, end.row);
    const endRow = Math.max(start.row, end.row);
    const startCol = this.multiColumnSelection ? Math.min(start.col, end.col) : start.col;
    const endCol = this.multiColumnSelection ? Math.max(start.col, end.col) : start.col;
    return cell.row >= startRow && cell.row <= endRow && cell.col >= startCol && cell.col <= endCol;
  }
  toggleColumn(col, enabled) {
    if (col < 0 || col >= this.cells[0].length) {
      return;
    }
    this.cells.forEach((row, index) => {
      const cell = row[col];
      if (cell) {
        this.cells[index][col] = {
          ...cell,
          enabled
        };
      }
    });
  }
  toggleRow(row, enabled) {
    if (row < 0 || row >= this.cells.length) {
      return;
    }
    this.cells[row].forEach((cell, index) => {
      if (cell) {
        this.cells[row][index] = {
          ...cell,
          enabled
        };
      }
    });
  }
  getCoordinatesByField(field) {
    if (this.rowAccessors.length === 1) {
      const col = this.columnAccessors.indexOf(field);
      if (col === -1) {
        return null;
      }
      return { row: 0, col };
    }
    for (let row = 0; row < this.rowAccessors.length; row++) {
      const rowAccessor = this.rowAccessors[row];
      if (rowAccessor === null) {
        continue;
      }
      if (!field.startsWith(rowAccessor)) {
        continue;
      }
      for (let column = 0; column < this.columnAccessors.length; column++) {
        const columnAccessor = this.columnAccessors[column];
        if (columnAccessor === null) {
          continue;
        }
        const fullFieldPath = `${rowAccessor}.${columnAccessor}`;
        if (fullFieldPath === field) {
          return { row, col: column };
        }
      }
    }
    return null;
  }
  getRowAccessor(row) {
    if (row < 0 || row >= this.cells.length) {
      return null;
    }
    const cells = this.cells[row];
    const nonNullFields = cells.filter((cell) => cell !== null).map((cell) => cell.field.split("."));
    if (nonNullFields.length === 0) {
      return null;
    }
    let commonParts = nonNullFields[0];
    for (const segments of nonNullFields) {
      commonParts = commonParts.filter(
        (part, index) => segments[index] === part
      );
      if (commonParts.length === 0) {
        break;
      }
    }
    const accessor = commonParts.join(".");
    if (!accessor) {
      return null;
    }
    return accessor;
  }
  getColumnAccessor(column) {
    if (column < 0 || column >= this.cells[0].length) {
      return null;
    }
    const uniqueParts = this.cells.map((row, rowIndex) => {
      const cell = row[column];
      if (!cell) {
        return null;
      }
      const rowAccessor = this.getRowAccessor(rowIndex);
      if (rowAccessor && cell.field.startsWith(rowAccessor + ".")) {
        return cell.field.slice(rowAccessor.length + 1);
      }
      return null;
    }).filter((part) => part !== null);
    if (uniqueParts.length === 0) {
      return null;
    }
    const firstPart = uniqueParts[0];
    const isConsistent = uniqueParts.every((part) => part === firstPart);
    return isConsistent ? firstPart : null;
  }
  getValidMovement(row, col, direction, metaKey = false) {
    var _a;
    const [dRow, dCol] = this._getDirectionDeltas(direction);
    if (metaKey) {
      return this._getLastValidCellInDirection(row, col, dRow, dCol);
    } else {
      let newRow = row + dRow;
      let newCol = col + dCol;
      while (this._isValidPosition(newRow, newCol)) {
        if (this.cells[newRow][newCol] !== null && ((_a = this.cells[newRow][newCol]) == null ? void 0 : _a.enabled) !== false) {
          return { row: newRow, col: newCol };
        }
        newRow += dRow;
        newCol += dCol;
      }
      return { row, col };
    }
  }
  _isValidPosition(row, col, cells) {
    if (!cells) {
      cells = this.cells;
    }
    return row >= 0 && row < cells.length && col >= 0 && col < cells[0].length;
  }
  _getDirectionDeltas(direction) {
    switch (direction) {
      case "ArrowUp":
        return [-1, 0];
      case "ArrowDown":
        return [1, 0];
      case "ArrowLeft":
        return [0, -1];
      case "ArrowRight":
        return [0, 1];
      default:
        return [0, 0];
    }
  }
  _getLastValidCellInDirection(row, col, dRow, dCol) {
    let newRow = row;
    let newCol = col;
    let lastValidRow = row;
    let lastValidCol = col;
    while (this._isValidPosition(newRow + dRow, newCol + dCol)) {
      newRow += dRow;
      newCol += dCol;
      if (this.cells[newRow][newCol] !== null) {
        lastValidRow = newRow;
        lastValidCol = newCol;
      }
    }
    return {
      row: lastValidRow,
      col: lastValidCol
    };
  }
  _populateCells(rows, columns) {
    const cells = Array.from(
      { length: rows.length },
      () => Array(columns.length).fill(null)
    );
    rows.forEach((row, rowIndex) => {
      columns.forEach((column, colIndex) => {
        if (!this._isValidPosition(rowIndex, colIndex, cells)) {
          return;
        }
        const {
          name: _,
          field,
          type,
          ...rest
        } = column.meta;
        const context = {
          row,
          column: {
            ...column,
            meta: rest
          }
        };
        const fieldValue = field ? field(context) : null;
        if (!fieldValue || !type) {
          return;
        }
        cells[rowIndex][colIndex] = {
          field: fieldValue,
          type,
          enabled: true
        };
      });
    });
    return cells;
  }
};
var DataGridQueryTool = class {
  constructor(container) {
    __publicField(this, "container");
    this.container = container;
  }
  getInput(cell) {
    var _a;
    const id = this._getCellId(cell);
    const input = (_a = this.container) == null ? void 0 : _a.querySelector(`[data-cell-id="${id}"]`);
    if (!input) {
      return null;
    }
    return input;
  }
  getInputByField(field) {
    var _a;
    const input = (_a = this.container) == null ? void 0 : _a.querySelector(`[data-field="${field}"]`);
    if (!input) {
      return null;
    }
    return input;
  }
  getCoordinatesByField(field) {
    var _a;
    const cell = (_a = this.container) == null ? void 0 : _a.querySelector(
      `[data-field="${field}"][data-cell-id]`
    );
    if (!cell) {
      return null;
    }
    const cellId = cell.getAttribute("data-cell-id");
    if (!cellId) {
      return null;
    }
    const [row, col] = cellId.split(":").map((n) => parseInt(n, 10));
    if (isNaN(row) || isNaN(col)) {
      return null;
    }
    return { row, col };
  }
  getContainer(cell) {
    var _a;
    const id = this._getCellId(cell);
    const container = (_a = this.container) == null ? void 0 : _a.querySelector(
      `[data-container-id="${id}"]`
    );
    if (!container) {
      return null;
    }
    return container;
  }
  _getCellId(cell) {
    return generateCellId(cell);
  }
};
var DataGridUpdateCommand = class {
  constructor({ prev, next, setter }) {
    __publicField(this, "_prev");
    __publicField(this, "_next");
    __publicField(this, "_setter");
    this._prev = prev;
    this._next = next;
    this._setter = setter;
  }
  execute() {
    this._setter(this._next);
  }
  undo() {
    this._setter(this._prev);
  }
  redo() {
    this.execute();
  }
};
var useDataGridCellHandlers = ({
  matrix,
  anchor,
  rangeEnd,
  setRangeEnd,
  isDragging,
  setIsDragging,
  isSelecting,
  setIsSelecting,
  setSingleRange,
  dragEnd,
  setDragEnd,
  setValue: setValue2,
  execute,
  multiColumnSelection
}) => {
  const getWrapperFocusHandler = (0, import_react5.useCallback)(
    (coords) => {
      return (_e) => {
        setSingleRange(coords);
      };
    },
    [setSingleRange]
  );
  const getOverlayMouseDownHandler = (0, import_react5.useCallback)(
    (coords) => {
      return (e2) => {
        e2.stopPropagation();
        e2.preventDefault();
        if (e2.shiftKey) {
          setRangeEnd(coords);
          return;
        }
        setIsSelecting(true);
        setSingleRange(coords);
      };
    },
    [setIsSelecting, setRangeEnd, setSingleRange]
  );
  const getWrapperMouseOverHandler = (0, import_react5.useCallback)(
    (coords) => {
      if (!isDragging && !isSelecting) {
        return;
      }
      return (_e) => {
        if ((anchor == null ? void 0 : anchor.col) !== coords.col && !multiColumnSelection) {
          return;
        }
        if (isSelecting) {
          setRangeEnd(coords);
        } else {
          setDragEnd(coords);
        }
      };
    },
    [
      anchor == null ? void 0 : anchor.col,
      isDragging,
      isSelecting,
      setDragEnd,
      setRangeEnd,
      multiColumnSelection
    ]
  );
  const getInputChangeHandler = (0, import_react5.useCallback)(
    // Using `any` here as the generic type of Path<TFieldValues> will
    // not be inferred correctly.
    (field) => {
      return (next, prev) => {
        const command = new DataGridUpdateCommand({
          next,
          prev,
          setter: (value) => {
            setValue2(field, value, {
              shouldDirty: true,
              shouldTouch: true
            });
          }
        });
        execute(command);
      };
    },
    [setValue2, execute]
  );
  const onDragToFillStart = (0, import_react5.useCallback)(
    (_e) => {
      setIsDragging(true);
    },
    [setIsDragging]
  );
  const getIsCellSelected = (0, import_react5.useCallback)(
    (cell) => {
      if (!cell || !anchor || !rangeEnd) {
        return false;
      }
      return matrix.getIsCellSelected(cell, anchor, rangeEnd);
    },
    [anchor, rangeEnd, matrix]
  );
  const getIsCellDragSelected = (0, import_react5.useCallback)(
    (cell) => {
      if (!cell || !anchor || !dragEnd) {
        return false;
      }
      return matrix.getIsCellSelected(cell, anchor, dragEnd);
    },
    [anchor, dragEnd, matrix]
  );
  return {
    getWrapperFocusHandler,
    getOverlayMouseDownHandler,
    getWrapperMouseOverHandler,
    getInputChangeHandler,
    getIsCellSelected,
    getIsCellDragSelected,
    onDragToFillStart
  };
};
var useDataGridCellMetadata = ({
  matrix
}) => {
  const getCellMetadata = (0, import_react6.useCallback)(
    (coords) => {
      const { row, col } = coords;
      const id = generateCellId(coords);
      const field = matrix.getCellField(coords);
      const type = matrix.getCellType(coords);
      if (!field || !type) {
        throw new Error(`'field' or 'type' is null for cell ${id}`);
      }
      const inputAttributes = {
        "data-row": row,
        "data-col": col,
        "data-cell-id": id,
        "data-field": field
      };
      const innerAttributes = {
        "data-container-id": id
      };
      return {
        id,
        field,
        type,
        inputAttributes,
        innerAttributes
      };
    },
    [matrix]
  );
  const getCellErrorMetadata = (0, import_react6.useCallback)(
    (coords) => {
      const accessor = matrix.getRowAccessor(coords.row);
      const field = matrix.getCellField(coords);
      return {
        accessor,
        field
      };
    },
    [matrix]
  );
  return {
    getCellMetadata,
    getCellErrorMetadata
  };
};
var useDataGridCellSnapshot = ({
  matrix,
  form
}) => {
  const [snapshot, setSnapshot] = (0, import_react7.useState)(null);
  const { getValues, setValue: setValue2 } = form;
  const createSnapshot = (0, import_react7.useCallback)(
    (cell) => {
      if (!cell) {
        return null;
      }
      const field = matrix.getCellField(cell);
      if (!field) {
        return null;
      }
      const value = getValues(field);
      setSnapshot((curr) => {
        if ((curr == null ? void 0 : curr.field) === field) {
          return curr;
        }
        return { field, value };
      });
    },
    [getValues, matrix]
  );
  const restoreSnapshot = (0, import_react7.useCallback)(() => {
    if (!snapshot) {
      return;
    }
    const { field, value } = snapshot;
    requestAnimationFrame(() => {
      setValue2(field, value);
    });
  }, [setValue2, snapshot]);
  return {
    createSnapshot,
    restoreSnapshot
  };
};
var useDataGridClipboardEvents = ({
  matrix,
  anchor,
  rangeEnd,
  isEditing,
  getSelectionValues,
  setSelectionValues,
  execute
}) => {
  const handleCopyEvent = (0, import_react8.useCallback)(
    (e2) => {
      var _a;
      if (isEditing || !anchor || !rangeEnd) {
        return;
      }
      e2.preventDefault();
      const fields = matrix.getFieldsInSelection(anchor, rangeEnd);
      const values = getSelectionValues(fields);
      const text = values.map((value) => {
        if (typeof value === "object" && value !== null) {
          return JSON.stringify(value);
        }
        return `${value}` ?? "";
      }).join("	");
      (_a = e2.clipboardData) == null ? void 0 : _a.setData("text/plain", text);
    },
    [isEditing, anchor, rangeEnd, matrix, getSelectionValues]
  );
  const handlePasteEvent = (0, import_react8.useCallback)(
    (e2) => {
      var _a;
      if (isEditing || !anchor || !rangeEnd) {
        return;
      }
      e2.preventDefault();
      const text = (_a = e2.clipboardData) == null ? void 0 : _a.getData("text/plain");
      if (!text) {
        return;
      }
      const next = text.split("	");
      const fields = matrix.getFieldsInSelection(anchor, rangeEnd);
      const prev = getSelectionValues(fields);
      const command = new DataGridBulkUpdateCommand({
        fields,
        next,
        prev,
        setter: setSelectionValues
      });
      execute(command);
    },
    [
      isEditing,
      anchor,
      rangeEnd,
      matrix,
      getSelectionValues,
      setSelectionValues,
      execute
    ]
  );
  return {
    handleCopyEvent,
    handlePasteEvent
  };
};
function useDataGridColumnVisibility(grid, matrix) {
  const columns = grid.getAllLeafColumns();
  const columnOptions = columns.map((column) => ({
    id: column.id,
    name: getColumnName(column),
    checked: column.getIsVisible(),
    disabled: !column.getCanHide()
  }));
  const handleToggleColumn = (0, import_react9.useCallback)(
    (index) => (value) => {
      const column = columns[index];
      if (!column.getCanHide()) {
        return;
      }
      matrix.toggleColumn(index, value);
      column.toggleVisibility(value);
    },
    [columns, matrix]
  );
  const handleResetColumns = (0, import_react9.useCallback)(() => {
    grid.setColumnVisibility({});
  }, [grid]);
  const optionCount = columnOptions.filter((c) => !c.disabled).length;
  const isDisabled = optionCount === 0;
  return {
    columnOptions,
    handleToggleColumn,
    handleResetColumns,
    isDisabled
  };
}
function getColumnName(column) {
  const id = column.columnDef.id;
  const enableHiding = column.columnDef.enableHiding;
  const meta = column == null ? void 0 : column.columnDef.meta;
  if (!id) {
    throw new Error(
      "Column is missing an id, which is a required field. Please provide an id for the column."
    );
  }
  if (!(meta == null ? void 0 : meta.name) && enableHiding) {
    console.warn(
      `Column "${id}" does not have a name. You should add a name to the column definition. Falling back to the column id.`
    );
  }
  return (meta == null ? void 0 : meta.name) || id;
}
var useDataGridDuplicateCell = ({
  duplicateOf
}) => {
  const { control } = useDataGridContext();
  const watchedValue = useWatch({ control, name: duplicateOf });
  return {
    watchedValue
  };
};
var useDataGridErrorHighlighting = (matrix, grid, errors) => {
  const [isHighlighted, setIsHighlighted] = (0, import_react10.useState)(false);
  const [visibilitySnapshot, setVisibilitySnapshot] = (0, import_react10.useState)(null);
  const { flatRows } = grid.getRowModel();
  const flatColumns = grid.getAllFlatColumns();
  const errorPaths = findErrorPaths(errors);
  const errorCount = errorPaths.length;
  const { rowsWithErrors, columnsWithErrors } = (0, import_react10.useMemo)(() => {
    const rowsWithErrors2 = /* @__PURE__ */ new Set();
    const columnsWithErrors2 = /* @__PURE__ */ new Set();
    errorPaths.forEach((errorPath) => {
      const rowIndex = matrix.rowAccessors.findIndex(
        (accessor) => accessor && (errorPath === accessor || errorPath.startsWith(`${accessor}.`))
      );
      if (rowIndex !== -1) {
        rowsWithErrors2.add(rowIndex);
      }
      const columnIndex = matrix.columnAccessors.findIndex(
        (accessor) => accessor && (errorPath === accessor || errorPath.endsWith(`.${accessor}`))
      );
      if (columnIndex !== -1) {
        columnsWithErrors2.add(columnIndex);
      }
    });
    return { rowsWithErrors: rowsWithErrors2, columnsWithErrors: columnsWithErrors2 };
  }, [errorPaths, matrix.rowAccessors, matrix.columnAccessors]);
  const toggleErrorHighlighting = (0, import_react10.useCallback)(
    (currentRowVisibility, currentColumnVisibility, setRowVisibility, setColumnVisibility) => {
      if (isHighlighted) {
        if (visibilitySnapshot) {
          setRowVisibility(visibilitySnapshot.rows);
          setColumnVisibility(visibilitySnapshot.columns);
        }
      } else {
        setVisibilitySnapshot({
          rows: { ...currentRowVisibility },
          columns: { ...currentColumnVisibility }
        });
        const rowsToHide = flatRows.map((_, index) => {
          return !rowsWithErrors.has(index) ? index : void 0;
        }).filter((index) => index !== void 0);
        const columnsToHide = flatColumns.map((column, index) => {
          return !columnsWithErrors.has(index) && index !== 0 ? column.id : void 0;
        }).filter((id) => id !== void 0);
        setRowVisibility(
          rowsToHide.reduce((acc, row) => ({ ...acc, [row]: false }), {})
        );
        setColumnVisibility(
          columnsToHide.reduce(
            (acc, column) => ({ ...acc, [column]: false }),
            {}
          )
        );
      }
      setIsHighlighted((prev) => !prev);
    },
    [
      isHighlighted,
      visibilitySnapshot,
      flatRows,
      flatColumns,
      rowsWithErrors,
      columnsWithErrors
    ]
  );
  return {
    errorCount,
    isHighlighted,
    toggleErrorHighlighting
  };
};
function findErrorPaths(obj, path = []) {
  if (typeof obj !== "object" || obj === null) {
    return [];
  }
  if ("message" in obj && "type" in obj) {
    return [path.join(".")];
  }
  return Object.entries(obj).flatMap(
    ([key, value]) => findErrorPaths(value, [...path, key])
  );
}
var useDataGridFormHandlers = ({
  matrix,
  form,
  anchor
}) => {
  const { getValues, reset } = form;
  const getSelectionValues = (0, import_react11.useCallback)(
    (fields) => {
      if (!fields.length) {
        return [];
      }
      const allValues = getValues();
      return fields.map((field) => {
        return field.split(".").reduce((obj, key) => obj == null ? void 0 : obj[key], allValues);
      });
    },
    [getValues]
  );
  const setSelectionValues = (0, import_react11.useCallback)(
    async (fields, values, isHistory) => {
      if (!fields.length || !anchor) {
        return;
      }
      const type = matrix.getCellType(anchor);
      if (!type) {
        return;
      }
      const convertedValues = convertArrayToPrimitive(values, type);
      const currentValues = getValues();
      fields.forEach((field, index) => {
        if (!field) {
          return;
        }
        const valueIndex = index % values.length;
        const newValue = convertedValues[valueIndex];
        setValue(currentValues, field, newValue, type, isHistory);
      });
      reset(currentValues, {
        keepDirty: true,
        keepTouched: true,
        keepDefaultValues: true
      });
    },
    [matrix, anchor, getValues, reset]
  );
  return {
    getSelectionValues,
    setSelectionValues
  };
};
function convertToNumber(value) {
  if (typeof value === "number") {
    return value;
  }
  const converted = Number(value);
  if (isNaN(converted)) {
    throw new Error(`String "${value}" cannot be converted to number.`);
  }
  return converted;
}
function convertToBoolean(value) {
  if (typeof value === "boolean") {
    return value;
  }
  if (typeof value === "undefined" || value === null) {
    return false;
  }
  const lowerValue = value.toLowerCase();
  if (lowerValue === "true" || lowerValue === "false") {
    return lowerValue === "true";
  }
  throw new Error(`String "${value}" cannot be converted to boolean.`);
}
function covertToString(value) {
  if (typeof value === "undefined" || value === null) {
    return "";
  }
  return String(value);
}
function convertToggleableNumber(value) {
  let obj = value;
  if (typeof obj === "string") {
    try {
      obj = JSON.parse(obj);
    } catch (error) {
      throw new Error(`String "${value}" cannot be converted to object.`);
    }
  }
  return obj;
}
function setValue(currentValues, field, newValue, type, isHistory) {
  if (type !== "togglable-number") {
    (0, import_set.default)(currentValues, field, newValue);
    return;
  }
  setValueToggleableNumber(currentValues, field, newValue, isHistory);
}
function setValueToggleableNumber(currentValues, field, newValue, isHistory) {
  const currentValue = (0, import_get.default)(currentValues, field);
  const { disabledToggle } = currentValue;
  const normalizeQuantity = (value) => {
    if (disabledToggle && value === "") {
      return 0;
    }
    return value;
  };
  const determineChecked = (quantity2) => {
    if (disabledToggle) {
      return true;
    }
    return quantity2 !== "" && quantity2 != null;
  };
  const quantity = normalizeQuantity(newValue.quantity);
  const checked = isHistory ? disabledToggle ? true : newValue.checked : determineChecked(quantity);
  (0, import_set.default)(currentValues, field, {
    ...currentValue,
    quantity,
    checked
  });
}
function convertArrayToPrimitive(values, type) {
  switch (type) {
    case "number":
      return values.map((v) => {
        if (v === "") {
          return v;
        }
        if (v == null) {
          return "";
        }
        return convertToNumber(v);
      });
    case "togglable-number":
      return values.map(convertToggleableNumber);
    case "boolean":
      return values.map(convertToBoolean);
    case "text":
      return values.map(covertToString);
    default:
      throw new Error(`Unsupported target type "${type}".`);
  }
}
var ARROW_KEYS = ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"];
var VERTICAL_KEYS = ["ArrowUp", "ArrowDown"];
var useDataGridKeydownEvent = ({
  containerRef,
  matrix,
  anchor,
  rangeEnd,
  isEditing,
  setTrapActive,
  scrollToCoordinates,
  setSingleRange,
  setRangeEnd,
  onEditingChangeHandler,
  getValues,
  setValue: setValue2,
  execute,
  undo,
  redo,
  queryTool,
  getSelectionValues,
  setSelectionValues,
  restoreSnapshot,
  createSnapshot
}) => {
  const handleKeyboardNavigation = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor) {
        return;
      }
      const type = matrix.getCellType(anchor);
      if (isEditing && type !== "boolean") {
        return;
      }
      const direction = VERTICAL_KEYS.includes(e2.key) ? "vertical" : "horizontal";
      const basis = direction === "horizontal" ? anchor : e2.shiftKey ? rangeEnd : anchor;
      const updater = direction === "horizontal" ? setSingleRange : e2.shiftKey ? setRangeEnd : setSingleRange;
      if (!basis) {
        return;
      }
      const { row, col } = basis;
      const handleNavigation = (coords) => {
        e2.preventDefault();
        e2.stopPropagation();
        scrollToCoordinates(coords, direction);
        updater(coords);
      };
      const next = matrix.getValidMovement(
        row,
        col,
        e2.key,
        e2.metaKey || e2.ctrlKey
      );
      handleNavigation(next);
    },
    [
      isEditing,
      anchor,
      rangeEnd,
      scrollToCoordinates,
      setSingleRange,
      setRangeEnd,
      matrix
    ]
  );
  const handleTabKey = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor) {
        return;
      }
      e2.preventDefault();
      e2.stopPropagation();
      const { row, col } = anchor;
      const key = e2.shiftKey ? "ArrowLeft" : "ArrowRight";
      const direction = "horizontal";
      const next = matrix.getValidMovement(
        row,
        col,
        key,
        e2.metaKey || e2.ctrlKey
      );
      scrollToCoordinates(next, direction);
      setSingleRange(next);
    },
    [anchor, scrollToCoordinates, setSingleRange, matrix]
  );
  const handleUndo = (0, import_react12.useCallback)(
    (e2) => {
      e2.preventDefault();
      if (e2.shiftKey) {
        redo();
        return;
      }
      undo();
    },
    [redo, undo]
  );
  const handleSpaceKeyBoolean = (0, import_react12.useCallback)(
    (anchor2) => {
      const end = rangeEnd ?? anchor2;
      const fields = matrix.getFieldsInSelection(anchor2, end);
      const prev = getSelectionValues(fields);
      const allChecked = prev.every((value) => value === true);
      const next = Array.from({ length: prev.length }, () => !allChecked);
      const command = new DataGridBulkUpdateCommand({
        fields,
        next,
        prev,
        setter: setSelectionValues
      });
      execute(command);
    },
    [rangeEnd, matrix, getSelectionValues, setSelectionValues, execute]
  );
  const handleSpaceKeyTextOrNumber = (0, import_react12.useCallback)(
    (anchor2) => {
      const field = matrix.getCellField(anchor2);
      const input = queryTool == null ? void 0 : queryTool.getInput(anchor2);
      if (!field || !input) {
        return;
      }
      createSnapshot(anchor2);
      const current = getValues(field);
      const next = "";
      const command = new DataGridUpdateCommand({
        next,
        prev: current,
        setter: (value) => {
          setValue2(field, value, {
            shouldDirty: true,
            shouldTouch: true
          });
        }
      });
      execute(command);
      input.focus();
    },
    [matrix, queryTool, getValues, execute, setValue2, createSnapshot]
  );
  const handleSpaceKeyTogglableNumber = (0, import_react12.useCallback)(
    (anchor2) => {
      const field = matrix.getCellField(anchor2);
      const input = queryTool == null ? void 0 : queryTool.getInput(anchor2);
      if (!field || !input) {
        return;
      }
      createSnapshot(anchor2);
      const current = getValues(field);
      let checked = current.checked;
      if (!current.disabledToggle) {
        checked = false;
      }
      const next = { ...current, quantity: "", checked };
      const command = new DataGridUpdateCommand({
        next,
        prev: current,
        setter: (value) => {
          setValue2(field, value, {
            shouldDirty: true,
            shouldTouch: true
          });
        }
      });
      execute(command);
      input.focus();
    },
    [matrix, queryTool, getValues, execute, setValue2, createSnapshot]
  );
  const handleSpaceKey = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor || isEditing) {
        return;
      }
      e2.preventDefault();
      const type = matrix.getCellType(anchor);
      if (!type) {
        return;
      }
      switch (type) {
        case "boolean":
          handleSpaceKeyBoolean(anchor);
          break;
        case "togglable-number":
          handleSpaceKeyTogglableNumber(anchor);
          break;
        case "number":
        case "text":
          handleSpaceKeyTextOrNumber(anchor);
          break;
      }
    },
    [
      anchor,
      isEditing,
      matrix,
      handleSpaceKeyBoolean,
      handleSpaceKeyTextOrNumber,
      handleSpaceKeyTogglableNumber
    ]
  );
  const handleMoveOnEnter = (0, import_react12.useCallback)(
    (e2, anchor2) => {
      const direction = e2.shiftKey ? "ArrowUp" : "ArrowDown";
      const pos = matrix.getValidMovement(
        anchor2.row,
        anchor2.col,
        direction,
        false
      );
      if (anchor2.row !== pos.row || anchor2.col !== pos.col) {
        setSingleRange(pos);
        scrollToCoordinates(pos, "vertical");
      } else {
        const container = queryTool == null ? void 0 : queryTool.getContainer(anchor2);
        container == null ? void 0 : container.focus();
      }
      onEditingChangeHandler(false);
    },
    [
      queryTool,
      matrix,
      scrollToCoordinates,
      setSingleRange,
      onEditingChangeHandler
    ]
  );
  const handleEditOnEnter = (0, import_react12.useCallback)(
    (anchor2) => {
      const input = queryTool == null ? void 0 : queryTool.getInput(anchor2);
      if (!input) {
        return;
      }
      input.focus();
      onEditingChangeHandler(true);
    },
    [queryTool, onEditingChangeHandler]
  );
  const handleEnterKeyTextOrNumber = (0, import_react12.useCallback)(
    (e2, anchor2) => {
      if (isEditing) {
        handleMoveOnEnter(e2, anchor2);
        return;
      }
      handleEditOnEnter(anchor2);
    },
    [handleMoveOnEnter, handleEditOnEnter, isEditing]
  );
  const handleEnterKeyBoolean = (0, import_react12.useCallback)(
    (e2, anchor2) => {
      const field = matrix.getCellField(anchor2);
      if (!field) {
        return;
      }
      const current = getValues(field);
      let next;
      if (typeof current === "boolean") {
        next = !current;
      } else {
        next = true;
      }
      const command = new DataGridUpdateCommand({
        next,
        prev: current,
        setter: (value) => {
          setValue2(field, value, {
            shouldDirty: true,
            shouldTouch: true
          });
        }
      });
      execute(command);
      handleMoveOnEnter(e2, anchor2);
    },
    [execute, getValues, handleMoveOnEnter, matrix, setValue2]
  );
  const handleEnterKey = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor) {
        return;
      }
      e2.preventDefault();
      const type = matrix.getCellType(anchor);
      switch (type) {
        case "togglable-number":
        case "text":
        case "number":
          handleEnterKeyTextOrNumber(e2, anchor);
          break;
        case "boolean": {
          handleEnterKeyBoolean(e2, anchor);
          break;
        }
      }
    },
    [anchor, matrix, handleEnterKeyTextOrNumber, handleEnterKeyBoolean]
  );
  const handleDeleteKeyTogglableNumber = (0, import_react12.useCallback)(
    (anchor2, rangeEnd2) => {
      const fields = matrix.getFieldsInSelection(anchor2, rangeEnd2);
      const prev = getSelectionValues(fields);
      const next = prev.map((value) => ({
        ...value,
        quantity: "",
        checked: value.disableToggle ? value.checked : false
      }));
      const command = new DataGridBulkUpdateCommand({
        fields,
        next,
        prev,
        setter: setSelectionValues
      });
      execute(command);
    },
    [matrix, getSelectionValues, setSelectionValues, execute]
  );
  const handleDeleteKeyTextOrNumber = (0, import_react12.useCallback)(
    (anchor2, rangeEnd2) => {
      const fields = matrix.getFieldsInSelection(anchor2, rangeEnd2);
      const prev = getSelectionValues(fields);
      const next = Array.from({ length: prev.length }, () => "");
      const command = new DataGridBulkUpdateCommand({
        fields,
        next,
        prev,
        setter: setSelectionValues
      });
      execute(command);
    },
    [matrix, getSelectionValues, setSelectionValues, execute]
  );
  const handleDeleteKeyBoolean = (0, import_react12.useCallback)(
    (anchor2, rangeEnd2) => {
      const fields = matrix.getFieldsInSelection(anchor2, rangeEnd2);
      const prev = getSelectionValues(fields);
      const next = Array.from({ length: prev.length }, () => false);
      const command = new DataGridBulkUpdateCommand({
        fields,
        next,
        prev,
        setter: setSelectionValues
      });
      execute(command);
    },
    [execute, getSelectionValues, matrix, setSelectionValues]
  );
  const handleDeleteKey = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor || !rangeEnd || isEditing) {
        return;
      }
      e2.preventDefault();
      const type = matrix.getCellType(anchor);
      if (!type) {
        return;
      }
      switch (type) {
        case "text":
        case "number":
          handleDeleteKeyTextOrNumber(anchor, rangeEnd);
          break;
        case "boolean":
          handleDeleteKeyBoolean(anchor, rangeEnd);
          break;
        case "togglable-number":
          handleDeleteKeyTogglableNumber(anchor, rangeEnd);
          break;
      }
    },
    [
      anchor,
      rangeEnd,
      isEditing,
      matrix,
      handleDeleteKeyTextOrNumber,
      handleDeleteKeyBoolean,
      handleDeleteKeyTogglableNumber
    ]
  );
  const handleEscapeKey = (0, import_react12.useCallback)(
    (e2) => {
      if (!anchor || !isEditing) {
        return;
      }
      e2.preventDefault();
      e2.stopPropagation();
      restoreSnapshot();
      const container = queryTool == null ? void 0 : queryTool.getContainer(anchor);
      container == null ? void 0 : container.focus();
    },
    [queryTool, isEditing, anchor, restoreSnapshot]
  );
  const handleSpecialFocusKeys = (0, import_react12.useCallback)(
    (e2) => {
      if (!containerRef || isEditing) {
        return;
      }
      const focusableElements = getFocusableElements(containerRef);
      const focusElement = (element) => {
        if (element) {
          setTrapActive(false);
          element.focus();
        }
      };
      switch (e2.key) {
        case ".":
          focusElement(focusableElements.cancel);
          break;
        case ",":
          focusElement(focusableElements.shortcuts);
          break;
        default:
          break;
      }
    },
    [isEditing, setTrapActive, containerRef]
  );
  const handleKeyDownEvent = (0, import_react12.useCallback)(
    (e2) => {
      if (ARROW_KEYS.includes(e2.key)) {
        handleKeyboardNavigation(e2);
        return;
      }
      if (e2.key === "z" && (e2.metaKey || e2.ctrlKey)) {
        handleUndo(e2);
        return;
      }
      if (e2.key === " ") {
        handleSpaceKey(e2);
        return;
      }
      if (e2.key === "Delete" || e2.key === "Backspace") {
        handleDeleteKey(e2);
        return;
      }
      if (e2.key === "Enter") {
        handleEnterKey(e2);
        return;
      }
      if (e2.key === "Escape") {
        handleEscapeKey(e2);
        return;
      }
      if (e2.key === "Tab") {
        handleTabKey(e2);
        return;
      }
    },
    [
      handleEscapeKey,
      handleKeyboardNavigation,
      handleUndo,
      handleSpaceKey,
      handleEnterKey,
      handleDeleteKey,
      handleTabKey
    ]
  );
  return {
    handleKeyDownEvent,
    handleSpecialFocusKeys
  };
};
function getFocusableElements(ref) {
  const focusableElements = Array.from(
    document.querySelectorAll(
      "[tabindex], a, button, input, select, textarea"
    )
  );
  const currentElementIndex = focusableElements.indexOf(ref.current);
  const shortcuts = currentElementIndex > 0 ? focusableElements[currentElementIndex - 1] : null;
  let cancel = null;
  for (let i = currentElementIndex + 1; i < focusableElements.length; i++) {
    if (!ref.current.contains(focusableElements[i])) {
      cancel = focusableElements[i];
      break;
    }
  }
  return { shortcuts, cancel };
}
var useDataGridMouseUpEvent = ({
  matrix,
  anchor,
  dragEnd,
  setDragEnd,
  isDragging,
  setIsDragging,
  setRangeEnd,
  setIsSelecting,
  getSelectionValues,
  setSelectionValues,
  execute
}) => {
  const handleDragEnd = (0, import_react13.useCallback)(() => {
    if (!isDragging) {
      return;
    }
    if (!anchor || !dragEnd) {
      return;
    }
    const dragSelection = matrix.getFieldsInSelection(anchor, dragEnd);
    const anchorField = matrix.getCellField(anchor);
    if (!anchorField || !dragSelection.length) {
      return;
    }
    const anchorValue = getSelectionValues([anchorField]);
    const fields = dragSelection.filter((field) => field !== anchorField);
    const prev = getSelectionValues(fields);
    const next = Array.from({ length: prev.length }, () => anchorValue[0]);
    const command = new DataGridBulkUpdateCommand({
      fields,
      prev,
      next,
      setter: setSelectionValues
    });
    execute(command);
    setIsDragging(false);
    setDragEnd(null);
    setRangeEnd(dragEnd);
  }, [
    isDragging,
    anchor,
    dragEnd,
    matrix,
    getSelectionValues,
    setSelectionValues,
    execute,
    setIsDragging,
    setDragEnd,
    setRangeEnd
  ]);
  const handleMouseUpEvent = (0, import_react13.useCallback)(() => {
    handleDragEnd();
    setIsSelecting(false);
  }, [handleDragEnd, setIsSelecting]);
  return {
    handleMouseUpEvent
  };
};
var useDataGridNavigation = ({
  matrix,
  anchor,
  visibleColumns,
  visibleRows,
  columnVirtualizer,
  rowVirtualizer,
  setColumnVisibility,
  flatColumns,
  queryTool,
  setSingleRange
}) => {
  const scrollToCoordinates = (0, import_react14.useCallback)(
    (coords, direction) => {
      if (!anchor) {
        return;
      }
      const { row, col } = coords;
      const { row: anchorRow, col: anchorCol } = anchor;
      const rowDirection = row >= anchorRow ? "down" : "up";
      const colDirection = col >= anchorCol ? "right" : "left";
      let toRow = rowDirection === "down" ? row + 1 : row - 1;
      if (visibleRows[toRow] === void 0) {
        toRow = row;
      }
      let toCol = colDirection === "right" ? col + 1 : col - 1;
      if (visibleColumns[toCol] === void 0) {
        toCol = col;
      }
      const scrollOptions = { align: "auto", behavior: "auto" };
      if (direction === "horizontal" || direction === "both") {
        columnVirtualizer.scrollToIndex(toCol, scrollOptions);
      }
      if (direction === "vertical" || direction === "both") {
        rowVirtualizer.scrollToIndex(toRow, scrollOptions);
      }
    },
    [anchor, columnVirtualizer, visibleRows, rowVirtualizer, visibleColumns]
  );
  const navigateToField = (0, import_react14.useCallback)(
    (field) => {
      const coords = matrix.getCoordinatesByField(field);
      if (!coords) {
        return;
      }
      const column = flatColumns[coords.col];
      setColumnVisibility((prev) => {
        return {
          ...prev,
          [column.id]: true
        };
      });
      requestAnimationFrame(() => {
        scrollToCoordinates(coords, "both");
        setSingleRange(coords);
      });
      requestAnimationFrame(() => {
        const input = queryTool == null ? void 0 : queryTool.getInput(coords);
        if (input) {
          input.focus();
        }
      });
    },
    [
      matrix,
      flatColumns,
      setColumnVisibility,
      scrollToCoordinates,
      setSingleRange,
      queryTool
    ]
  );
  return {
    scrollToCoordinates,
    navigateToField
  };
};
var useDataGridQueryTool = (containerRef) => {
  const queryToolRef = (0, import_react15.useRef)(null);
  (0, import_react15.useEffect)(() => {
    if (containerRef.current) {
      queryToolRef.current = new DataGridQueryTool(containerRef.current);
    }
  }, [containerRef]);
  return queryToolRef.current;
};
var DataGridRowErrorIndicator = ({
  rowErrors
}) => {
  const rowErrorCount = rowErrors ? rowErrors.length : 0;
  if (!rowErrors || rowErrorCount <= 0) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(
    Tooltip,
    {
      content: (0, import_jsx_runtime2.jsx)("ul", { className: "flex flex-col gap-y-3", children: rowErrors.map((error, index) => (0, import_jsx_runtime2.jsx)(DataGridRowErrorLine, { error }, index)) }),
      delayDuration: 0,
      children: (0, import_jsx_runtime2.jsx)(Badge, { color: "red", size: "2xsmall", className: "cursor-default", children: rowErrorCount })
    }
  );
};
var DataGridRowErrorLine = ({
  error
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)("li", { className: "txt-compact-small flex flex-col items-start", children: [
    error.message,
    (0, import_jsx_runtime2.jsx)(
      "button",
      {
        type: "button",
        onClick: error.to,
        className: "text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",
        children: t("dataGrid.errors.fixError")
      }
    )
  ] });
};
var DataGridCellContainer = ({
  isAnchor,
  isSelected,
  isDragSelected,
  field,
  showOverlay,
  placeholder,
  innerProps,
  overlayProps,
  children,
  errors,
  rowErrors,
  outerComponent
}) => {
  const error = get(errors, field);
  const hasError = !!error;
  return (0, import_jsx_runtime3.jsxs)("div", { className: "group/container relative size-full", children: [
    (0, import_jsx_runtime3.jsxs)(
      "div",
      {
        className: clx(
          "bg-ui-bg-base group/cell relative flex size-full items-center gap-x-2 px-4 py-2.5 outline-none",
          {
            "bg-ui-tag-red-bg text-ui-tag-red-text": hasError && !isAnchor && !isSelected && !isDragSelected,
            "ring-ui-bg-interactive ring-2 ring-inset": isAnchor,
            "bg-ui-bg-highlight [&:has([data-field]:focus)]:bg-ui-bg-base": isSelected || isAnchor,
            "bg-ui-bg-subtle": isDragSelected && !isAnchor
          }
        ),
        tabIndex: -1,
        ...innerProps,
        children: [
          (0, import_jsx_runtime3.jsx)(
            s,
            {
              name: field,
              errors,
              render: ({ message }) => {
                return (0, import_jsx_runtime3.jsx)("div", { className: "flex items-center justify-center", children: (0, import_jsx_runtime3.jsx)(Tooltip, { content: message, delayDuration: 0, children: (0, import_jsx_runtime3.jsx)(ExclamationCircle, { className: "text-ui-tag-red-icon z-[3]" }) }) });
              }
            }
          ),
          (0, import_jsx_runtime3.jsx)("div", { className: "relative z-[1] flex size-full items-center justify-center", children: (0, import_jsx_runtime3.jsx)(RenderChildren, { isAnchor, placeholder, children }) }),
          (0, import_jsx_runtime3.jsx)(DataGridRowErrorIndicator, { rowErrors }),
          showOverlay && (0, import_jsx_runtime3.jsx)(
            "div",
            {
              ...overlayProps,
              "data-cell-overlay": "true",
              className: "absolute inset-0 z-[2]"
            }
          )
        ]
      }
    ),
    outerComponent
  ] });
};
var RenderChildren = ({
  isAnchor,
  placeholder,
  children
}) => {
  if (!isAnchor && placeholder) {
    return placeholder;
  }
  return children;
};
var DataGridBooleanCell = ({
  context,
  disabled
}) => {
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  return (0, import_jsx_runtime4.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime4.jsx)(DataGridCellContainer, { ...container, ...errorProps, children: (0, import_jsx_runtime4.jsx)(Inner, { field: field2, inputProps: input, disabled }) });
      }
    }
  );
};
var Inner = ({
  field,
  inputProps,
  disabled
}) => {
  const { ref, value, onBlur, name, disabled: fieldDisabled } = field;
  const {
    ref: inputRef,
    onBlur: onInputBlur,
    onChange,
    onFocus,
    ...attributes
  } = inputProps;
  const combinedRefs = useCombinedRefs(ref, inputRef);
  return (0, import_jsx_runtime4.jsx)(
    Checkbox,
    {
      disabled: disabled || fieldDisabled,
      name,
      checked: value,
      onCheckedChange: (newValue) => onChange(newValue === true, value),
      onFocus,
      onBlur: () => {
        onBlur();
        onInputBlur();
      },
      ref: combinedRefs,
      tabIndex: -1,
      ...attributes
    }
  );
};
var DataGridCurrencyCell = ({
  context,
  code
}) => {
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  const currency = currencies[code.toUpperCase()];
  return (0, import_jsx_runtime5.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime5.jsx)(DataGridCellContainer, { ...container, ...errorProps, children: (0, import_jsx_runtime5.jsx)(Inner2, { field: field2, inputProps: input, currencyInfo: currency }) });
      }
    }
  );
};
var Inner2 = ({
  field,
  inputProps,
  currencyInfo
}) => {
  const { value, onChange: _, onBlur, ref, ...rest } = field;
  const {
    ref: inputRef,
    onBlur: onInputBlur,
    onFocus,
    onChange,
    ...attributes
  } = inputProps;
  const formatter = (0, import_react16.useCallback)(
    (value2) => {
      const ensuredValue = typeof value2 === "number" ? value2.toString() : value2 || "";
      return formatValue({
        value: ensuredValue,
        decimalScale: currencyInfo.decimal_digits,
        disableGroupSeparators: true,
        decimalSeparator: "."
      });
    },
    [currencyInfo]
  );
  const [localValue, setLocalValue] = (0, import_react16.useState)(value || "");
  const handleValueChange = (value2, _name, _values) => {
    if (!value2) {
      setLocalValue("");
      return;
    }
    setLocalValue(value2);
  };
  (0, import_react16.useEffect)(() => {
    let update = value;
    if (!isNaN(Number(value))) {
      update = formatter(update);
    }
    setLocalValue(update);
  }, [value, formatter]);
  const combinedRed = useCombinedRefs(inputRef, ref);
  return (0, import_jsx_runtime5.jsxs)("div", { className: "relative flex size-full items-center", children: [
    (0, import_jsx_runtime5.jsx)(
      "span",
      {
        className: "txt-compact-small text-ui-fg-muted pointer-events-none absolute left-0 w-fit min-w-4",
        "aria-hidden": true,
        children: currencyInfo.symbol_native
      }
    ),
    (0, import_jsx_runtime5.jsx)(
      CurrencyInput,
      {
        ...rest,
        ...attributes,
        ref: combinedRed,
        className: "txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-8 text-right outline-none",
        value: localValue || void 0,
        onValueChange: handleValueChange,
        formatValueOnBlur: true,
        onBlur: () => {
          onBlur();
          onInputBlur();
          onChange(localValue, value);
        },
        onFocus,
        decimalScale: currencyInfo.decimal_digits,
        decimalsLimit: currencyInfo.decimal_digits,
        autoComplete: "off",
        tabIndex: -1
      }
    )
  ] });
};
var DataGridNumberCell = ({
  context,
  ...rest
}) => {
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  return (0, import_jsx_runtime6.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime6.jsx)(DataGridCellContainer, { ...container, ...errorProps, children: (0, import_jsx_runtime6.jsx)(Inner3, { field: field2, inputProps: input, ...rest }) });
      }
    }
  );
};
var Inner3 = ({
  field,
  inputProps,
  ...props
}) => {
  const { ref, value, onChange: _, onBlur, ...fieldProps } = field;
  const {
    ref: inputRef,
    onChange,
    onBlur: onInputBlur,
    onFocus,
    ...attributes
  } = inputProps;
  const [localValue, setLocalValue] = (0, import_react17.useState)(value);
  (0, import_react17.useEffect)(() => {
    setLocalValue(value);
  }, [value]);
  const combinedRefs = useCombinedRefs(inputRef, ref);
  return (0, import_jsx_runtime6.jsx)("div", { className: "size-full", children: (0, import_jsx_runtime6.jsx)(
    "input",
    {
      ref: combinedRefs,
      value: localValue,
      onChange: (e2) => setLocalValue(e2.target.value),
      onBlur: () => {
        onBlur();
        onInputBlur();
        onChange(localValue, value);
      },
      onFocus,
      type: "number",
      inputMode: "decimal",
      className: clx(
        "txt-compact-small size-full bg-transparent outline-none",
        "placeholder:text-ui-fg-muted"
      ),
      tabIndex: -1,
      ...props,
      ...fieldProps,
      ...attributes
    }
  ) });
};
var DataGridReadonlyCell = ({
  context,
  color = "muted",
  children
}) => {
  const { rowErrors } = useDataGridCellError({ context });
  return (0, import_jsx_runtime7.jsxs)(
    "div",
    {
      className: clx(
        "txt-compact-small text-ui-fg-subtle flex size-full cursor-not-allowed items-center justify-between overflow-hidden px-4 py-2.5 outline-none",
        color === "muted" && "bg-ui-bg-subtle",
        color === "normal" && "bg-ui-bg-base"
      ),
      children: [
        (0, import_jsx_runtime7.jsx)("div", { className: "flex-1 truncate", children }),
        (0, import_jsx_runtime7.jsx)(DataGridRowErrorIndicator, { rowErrors })
      ]
    }
  );
};
var useCommandHistory = (maxHistory = 20) => {
  const [past, setPast] = (0, import_react19.useState)([]);
  const [future, setFuture] = (0, import_react19.useState)([]);
  const canUndo = past.length > 0;
  const canRedo = future.length > 0;
  const undo = (0, import_react19.useCallback)(() => {
    if (!canUndo) {
      return;
    }
    const previous = past[past.length - 1];
    const newPast = past.slice(0, past.length - 1);
    previous.undo();
    setPast(newPast);
    setFuture([previous, ...future.slice(0, maxHistory - 1)]);
  }, [canUndo, future, past, maxHistory]);
  const redo = (0, import_react19.useCallback)(() => {
    if (!canRedo) {
      return;
    }
    const next = future[0];
    const newFuture = future.slice(1);
    next.redo();
    setPast([...past, next].slice(0, maxHistory - 1));
    setFuture(newFuture);
  }, [canRedo, future, past, maxHistory]);
  const execute = (0, import_react19.useCallback)(
    (command) => {
      command.execute();
      setPast((past2) => [...past2, command].slice(0, maxHistory - 1));
      setFuture([]);
    },
    [maxHistory]
  );
  return {
    undo,
    redo,
    execute,
    canUndo,
    canRedo
  };
};
var useDataGridShortcuts = () => {
  const { t } = useTranslation();
  const shortcuts = (0, import_react20.useMemo)(
    () => [
      {
        label: t("dataGrid.shortcuts.commands.undo"),
        keys: {
          Mac: ["⌘", "Z"],
          Windows: ["Ctrl", "Z"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.redo"),
        keys: {
          Mac: ["⇧", "⌘", "Z"],
          Windows: ["Shift", "Ctrl", "Z"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.copy"),
        keys: {
          Mac: ["⌘", "C"],
          Windows: ["Ctrl", "C"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.paste"),
        keys: {
          Mac: ["⌘", "V"],
          Windows: ["Ctrl", "V"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.edit"),
        keys: {
          Mac: ["↵"],
          Windows: ["Enter"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.delete"),
        keys: {
          Mac: ["⌫"],
          Windows: ["Backspace"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.clear"),
        keys: {
          Mac: ["Space"],
          Windows: ["Space"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveUp"),
        keys: {
          Mac: ["↑"],
          Windows: ["↑"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveDown"),
        keys: {
          Mac: ["↓"],
          Windows: ["↓"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveLeft"),
        keys: {
          Mac: ["←"],
          Windows: ["←"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveRight"),
        keys: {
          Mac: ["→"],
          Windows: ["→"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveTop"),
        keys: {
          Mac: ["⌘", "↑"],
          Windows: ["Ctrl", "↑"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.moveBottom"),
        keys: {
          Mac: ["⌘", "↓"],
          Windows: ["Ctrl", "↓"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.selectDown"),
        keys: {
          Mac: ["⇧", "↓"],
          Windows: ["Shift", "↓"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.selectUp"),
        keys: {
          Mac: ["⇧", "↑"],
          Windows: ["Shift", "↑"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.selectColumnDown"),
        keys: {
          Mac: ["⇧", "⌘", "↓"],
          Windows: ["Shift", "Ctrl", "↓"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.selectColumnUp"),
        keys: {
          Mac: ["⇧", "⌘", "↑"],
          Windows: ["Shift", "Ctrl", "↑"]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.focusToolbar"),
        keys: {
          Mac: ["⌃", "⌥", ","],
          Windows: ["Ctrl", "Alt", ","]
        }
      },
      {
        label: t("dataGrid.shortcuts.commands.focusCancel"),
        keys: {
          Mac: ["⌃", "⌥", "."],
          Windows: ["Ctrl", "Alt", "."]
        }
      }
    ],
    [t]
  );
  return shortcuts;
};
var DataGridKeyboardShortcutModal = ({
  open,
  onOpenChange
}) => {
  const { t } = useTranslation();
  const [searchValue, onSearchValueChange] = (0, import_react20.useState)("");
  const shortcuts = useDataGridShortcuts();
  const searchResults = (0, import_react20.useMemo)(() => {
    return shortcuts.filter(
      (shortcut) => shortcut.label.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [searchValue, shortcuts]);
  return (0, import_jsx_runtime8.jsxs)(dist_exports.Root, { open, onOpenChange, children: [
    (0, import_jsx_runtime8.jsx)(dist_exports.Trigger, { asChild: true, children: (0, import_jsx_runtime8.jsx)(Button, { size: "small", variant: "secondary", children: t("dataGrid.shortcuts.label") }) }),
    (0, import_jsx_runtime8.jsxs)(dist_exports.Portal, { children: [
      (0, import_jsx_runtime8.jsx)(
        dist_exports.Overlay,
        {
          className: clx(
            "bg-ui-bg-overlay fixed inset-0",
            "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
          )
        }
      ),
      (0, import_jsx_runtime8.jsxs)(dist_exports.Content, { className: "bg-ui-bg-subtle shadow-elevation-modal fixed left-[50%] top-[50%] flex h-full max-h-[612px] w-full max-w-[560px] translate-x-[-50%] translate-y-[-50%] flex-col divide-y overflow-hidden rounded-lg outline-none", children: [
        (0, import_jsx_runtime8.jsxs)("div", { className: "flex flex-col gap-y-3 px-6 py-4", children: [
          (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center justify-between", children: [
            (0, import_jsx_runtime8.jsxs)("div", { children: [
              (0, import_jsx_runtime8.jsx)(dist_exports.Title, { asChild: true, children: (0, import_jsx_runtime8.jsx)(Heading, { children: t("app.menus.user.shortcuts") }) }),
              (0, import_jsx_runtime8.jsx)(dist_exports.Description, { className: "sr-only" })
            ] }),
            (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime8.jsx)(Kbd, { children: "esc" }),
              (0, import_jsx_runtime8.jsx)(dist_exports.Close, { asChild: true, children: (0, import_jsx_runtime8.jsx)(IconButton, { variant: "transparent", size: "small", children: (0, import_jsx_runtime8.jsx)(XMark, {}) }) })
            ] })
          ] }),
          (0, import_jsx_runtime8.jsx)("div", { children: (0, import_jsx_runtime8.jsx)(
            Input,
            {
              type: "search",
              value: searchValue,
              autoFocus: true,
              onChange: (e2) => onSearchValueChange(e2.target.value)
            }
          ) })
        ] }),
        (0, import_jsx_runtime8.jsx)("div", { className: "flex flex-col divide-y overflow-y-auto", children: searchResults.map((shortcut, index) => {
          var _a;
          return (0, import_jsx_runtime8.jsxs)(
            "div",
            {
              className: "text-ui-fg-subtle flex items-center justify-between px-6 py-3",
              children: [
                (0, import_jsx_runtime8.jsx)(Text, { size: "small", children: shortcut.label }),
                (0, import_jsx_runtime8.jsx)("div", { className: "flex items-center gap-x-1", children: (_a = shortcut.keys.Mac) == null ? void 0 : _a.map((key, index2) => {
                  return (0, import_jsx_runtime8.jsx)("div", { className: "flex items-center gap-x-1", children: (0, import_jsx_runtime8.jsx)(Kbd, { children: key }) }, index2);
                }) })
              ]
            },
            index
          );
        }) })
      ] })
    ] })
  ] });
};
var ROW_HEIGHT = 40;
var getCommonPinningStyles = (column) => {
  const isPinned = column.getIsPinned();
  const isDarkMode = document.documentElement.classList.contains("dark");
  const BORDER_COLOR = isDarkMode ? "rgb(50,50,53)" : "rgb(228,228,231)";
  return {
    position: isPinned ? "sticky" : "relative",
    width: column.getSize(),
    zIndex: isPinned ? 1 : 0,
    borderBottom: isPinned ? `1px solid ${BORDER_COLOR}` : void 0,
    borderRight: isPinned ? `1px solid ${BORDER_COLOR}` : void 0,
    left: isPinned === "left" ? `${column.getStart("left")}px` : void 0,
    right: isPinned === "right" ? `${column.getAfter("right")}px` : void 0
  };
};
var DataGridRoot = ({
  data = [],
  columns,
  state,
  getSubRows,
  onEditingChange,
  disableInteractions,
  multiColumnSelection = false
}) => {
  var _a, _b;
  const containerRef = (0, import_react18.useRef)(null);
  const { redo, undo, execute } = useCommandHistory();
  const {
    register,
    control,
    getValues,
    setValue: setValue2,
    formState: { errors }
  } = state;
  const [internalTrapActive, setTrapActive] = (0, import_react18.useState)(true);
  const trapActive = !disableInteractions && internalTrapActive;
  const [anchor, setAnchor] = (0, import_react18.useState)(null);
  const [rangeEnd, setRangeEnd] = (0, import_react18.useState)(null);
  const [dragEnd, setDragEnd] = (0, import_react18.useState)(null);
  const [isSelecting, setIsSelecting] = (0, import_react18.useState)(false);
  const [isDragging, setIsDragging] = (0, import_react18.useState)(false);
  const [isEditing, setIsEditing] = (0, import_react18.useState)(false);
  const [columnVisibility, setColumnVisibility] = (0, import_react18.useState)({});
  const [rowVisibility, setRowVisibility] = (0, import_react18.useState)({});
  const grid = useReactTable({
    data,
    columns,
    initialState: {
      columnPinning: {
        left: [columns[0].id]
      }
    },
    state: {
      columnVisibility
    },
    onColumnVisibilityChange: setColumnVisibility,
    getSubRows,
    getCoreRowModel: getCoreRowModel(),
    defaultColumn: {
      size: 200,
      maxSize: 400
    }
  });
  const { flatRows } = grid.getRowModel();
  const flatColumns = grid.getAllFlatColumns();
  const visibleRows = (0, import_react18.useMemo)(
    () => flatRows.filter((_, index) => (rowVisibility == null ? void 0 : rowVisibility[index]) !== false),
    [flatRows, rowVisibility]
  );
  const visibleColumns = grid.getVisibleLeafColumns();
  const rowVirtualizer = useVirtualizer({
    count: visibleRows.length,
    estimateSize: () => ROW_HEIGHT,
    getScrollElement: () => containerRef.current,
    overscan: 5,
    rangeExtractor: (range) => {
      const toRender = new Set(
        Array.from(
          { length: range.endIndex - range.startIndex + 1 },
          (_, i) => range.startIndex + i
        )
      );
      if (anchor && visibleRows[anchor.row]) {
        toRender.add(anchor.row);
      }
      if (rangeEnd && visibleRows[rangeEnd.row]) {
        toRender.add(rangeEnd.row);
      }
      return Array.from(toRender).sort((a, b) => a - b);
    }
  });
  const virtualRows = rowVirtualizer.getVirtualItems();
  const columnVirtualizer = useVirtualizer({
    count: visibleColumns.length,
    estimateSize: (index) => visibleColumns[index].getSize(),
    getScrollElement: () => containerRef.current,
    horizontal: true,
    overscan: 3,
    rangeExtractor: (range) => {
      const startIndex = range.startIndex;
      const endIndex = range.endIndex;
      const toRender = new Set(
        Array.from(
          { length: endIndex - startIndex + 1 },
          (_, i) => startIndex + i
        )
      );
      if (anchor && visibleColumns[anchor.col]) {
        toRender.add(anchor.col);
      }
      if (rangeEnd && visibleColumns[rangeEnd.col]) {
        toRender.add(rangeEnd.col);
      }
      toRender.add(0);
      return Array.from(toRender).sort((a, b) => a - b);
    }
  });
  const virtualColumns = columnVirtualizer.getVirtualItems();
  let virtualPaddingLeft;
  let virtualPaddingRight;
  if (columnVirtualizer && (virtualColumns == null ? void 0 : virtualColumns.length)) {
    virtualPaddingLeft = ((_a = virtualColumns[0]) == null ? void 0 : _a.start) ?? 0;
    virtualPaddingRight = columnVirtualizer.getTotalSize() - (((_b = virtualColumns[virtualColumns.length - 1]) == null ? void 0 : _b.end) ?? 0);
  }
  const matrix = (0, import_react18.useMemo)(
    () => new DataGridMatrix(
      flatRows,
      columns,
      multiColumnSelection
    ),
    [flatRows, columns, multiColumnSelection]
  );
  const queryTool = useDataGridQueryTool(containerRef);
  const setSingleRange = (0, import_react18.useCallback)(
    (coordinates) => {
      setAnchor(coordinates);
      setRangeEnd(coordinates);
    },
    []
  );
  const { errorCount, isHighlighted, toggleErrorHighlighting } = useDataGridErrorHighlighting(matrix, grid, errors);
  const handleToggleErrorHighlighting = (0, import_react18.useCallback)(() => {
    toggleErrorHighlighting(
      rowVisibility,
      columnVisibility,
      setRowVisibility,
      setColumnVisibility
    );
  }, [toggleErrorHighlighting, rowVisibility, columnVisibility]);
  const {
    columnOptions,
    handleToggleColumn,
    handleResetColumns,
    isDisabled: isColumsDisabled
  } = useDataGridColumnVisibility(grid, matrix);
  const handleToggleColumnVisibility = (0, import_react18.useCallback)(
    (index) => {
      return handleToggleColumn(index);
    },
    [handleToggleColumn]
  );
  const { navigateToField, scrollToCoordinates } = useDataGridNavigation({
    matrix,
    queryTool,
    anchor,
    columnVirtualizer,
    rowVirtualizer,
    flatColumns,
    setColumnVisibility,
    setSingleRange,
    visibleColumns,
    visibleRows
  });
  const { createSnapshot, restoreSnapshot } = useDataGridCellSnapshot({
    matrix,
    form: state
  });
  const onEditingChangeHandler = (0, import_react18.useCallback)(
    (value) => {
      if (onEditingChange) {
        onEditingChange(value);
      }
      if (value) {
        createSnapshot(anchor);
      }
      setIsEditing(value);
    },
    [anchor, createSnapshot, onEditingChange]
  );
  const { getSelectionValues, setSelectionValues } = useDataGridFormHandlers({
    matrix,
    form: state,
    anchor
  });
  const { handleKeyDownEvent, handleSpecialFocusKeys } = useDataGridKeydownEvent({
    containerRef,
    matrix,
    queryTool,
    anchor,
    rangeEnd,
    isEditing,
    setTrapActive,
    setRangeEnd,
    getSelectionValues,
    getValues,
    setSelectionValues,
    onEditingChangeHandler,
    restoreSnapshot,
    createSnapshot,
    setSingleRange,
    scrollToCoordinates,
    execute,
    undo,
    redo,
    setValue: setValue2
  });
  const { handleMouseUpEvent } = useDataGridMouseUpEvent({
    matrix,
    anchor,
    dragEnd,
    setDragEnd,
    isDragging,
    setIsDragging,
    setRangeEnd,
    setIsSelecting,
    getSelectionValues,
    setSelectionValues,
    execute
  });
  const { handleCopyEvent, handlePasteEvent } = useDataGridClipboardEvents({
    matrix,
    isEditing,
    anchor,
    rangeEnd,
    getSelectionValues,
    setSelectionValues,
    execute
  });
  const {
    getWrapperFocusHandler,
    getInputChangeHandler,
    getOverlayMouseDownHandler,
    getWrapperMouseOverHandler,
    getIsCellDragSelected,
    getIsCellSelected,
    onDragToFillStart
  } = useDataGridCellHandlers({
    matrix,
    anchor,
    rangeEnd,
    setRangeEnd,
    isDragging,
    setIsDragging,
    isSelecting,
    setIsSelecting,
    setSingleRange,
    dragEnd,
    setDragEnd,
    setValue: setValue2,
    execute,
    multiColumnSelection
  });
  const { getCellErrorMetadata, getCellMetadata } = useDataGridCellMetadata({
    matrix
  });
  (0, import_react18.useEffect)(() => {
    if (!trapActive) {
      return;
    }
    window.addEventListener("keydown", handleKeyDownEvent);
    window.addEventListener("mouseup", handleMouseUpEvent);
    window.addEventListener("copy", handleCopyEvent);
    window.addEventListener("paste", handlePasteEvent);
    return () => {
      window.removeEventListener("keydown", handleKeyDownEvent);
      window.removeEventListener("mouseup", handleMouseUpEvent);
      window.removeEventListener("copy", handleCopyEvent);
      window.removeEventListener("paste", handlePasteEvent);
    };
  }, [
    trapActive,
    handleKeyDownEvent,
    handleMouseUpEvent,
    handleCopyEvent,
    handlePasteEvent
  ]);
  (0, import_react18.useEffect)(() => {
    const specialFocusHandler = (e2) => {
      if (isSpecialFocusKey(e2)) {
        handleSpecialFocusKeys(e2);
        return;
      }
    };
    window.addEventListener("keydown", specialFocusHandler);
    return () => {
      window.removeEventListener("keydown", specialFocusHandler);
    };
  }, [handleSpecialFocusKeys]);
  const handleHeaderInteractionChange = (0, import_react18.useCallback)((isActive) => {
    if (isActive) {
      setTrapActive(false);
    }
  }, []);
  (0, import_react18.useEffect)(() => {
    if (!anchor) {
      return;
    }
    if (rangeEnd) {
      return;
    }
    setRangeEnd(anchor);
  }, [anchor, rangeEnd]);
  (0, import_react18.useEffect)(() => {
    if (!anchor && matrix) {
      const coords = matrix.getFirstNavigableCell();
      if (coords) {
        setSingleRange(coords);
      }
    }
  }, [anchor, matrix, setSingleRange]);
  const values = (0, import_react18.useMemo)(
    () => ({
      anchor,
      control,
      trapActive,
      errors,
      setTrapActive,
      setIsSelecting,
      setIsEditing: onEditingChangeHandler,
      setSingleRange,
      setRangeEnd,
      getWrapperFocusHandler,
      getInputChangeHandler,
      getOverlayMouseDownHandler,
      getWrapperMouseOverHandler,
      register,
      getIsCellSelected,
      getIsCellDragSelected,
      getCellMetadata,
      getCellErrorMetadata,
      navigateToField
    }),
    [
      anchor,
      control,
      trapActive,
      errors,
      setTrapActive,
      setIsSelecting,
      onEditingChangeHandler,
      setSingleRange,
      setRangeEnd,
      getWrapperFocusHandler,
      getInputChangeHandler,
      getOverlayMouseDownHandler,
      getWrapperMouseOverHandler,
      register,
      getIsCellSelected,
      getIsCellDragSelected,
      getCellMetadata,
      getCellErrorMetadata,
      navigateToField
    ]
  );
  const handleRestoreGridFocus = (0, import_react18.useCallback)(() => {
    if (anchor && !trapActive) {
      setTrapActive(true);
      setSingleRange(anchor);
      scrollToCoordinates(anchor, "both");
      requestAnimationFrame(() => {
        var _a2;
        (_a2 = queryTool == null ? void 0 : queryTool.getContainer(anchor)) == null ? void 0 : _a2.focus();
      });
    }
  }, [anchor, trapActive, setSingleRange, scrollToCoordinates, queryTool]);
  return (0, import_jsx_runtime9.jsx)(DataGridContext.Provider, { value: values, children: (0, import_jsx_runtime9.jsxs)("div", { className: "bg-ui-bg-subtle flex size-full flex-col", children: [
    (0, import_jsx_runtime9.jsx)(
      DataGridHeader,
      {
        columnOptions,
        isDisabled: isColumsDisabled,
        onToggleColumn: handleToggleColumnVisibility,
        errorCount,
        onToggleErrorHighlighting: handleToggleErrorHighlighting,
        onResetColumns: handleResetColumns,
        isHighlighted,
        onHeaderInteractionChange: handleHeaderInteractionChange
      }
    ),
    (0, import_jsx_runtime9.jsx)("div", { className: "size-full overflow-hidden", children: (0, import_jsx_runtime9.jsx)(
      "div",
      {
        ref: containerRef,
        autoFocus: true,
        tabIndex: 0,
        className: "relative h-full select-none overflow-auto outline-none",
        onFocus: handleRestoreGridFocus,
        onClick: handleRestoreGridFocus,
        "data-container": true,
        role: "application",
        children: (0, import_jsx_runtime9.jsxs)("div", { role: "grid", className: "text-ui-fg-subtle grid", children: [
          (0, import_jsx_runtime9.jsx)(
            "div",
            {
              role: "rowgroup",
              className: "txt-compact-small-plus bg-ui-bg-subtle sticky top-0 z-[1] grid",
              children: grid.getHeaderGroups().map((headerGroup) => (0, import_jsx_runtime9.jsxs)(
                "div",
                {
                  role: "row",
                  className: "flex h-10 w-full",
                  children: [
                    virtualPaddingLeft ? (0, import_jsx_runtime9.jsx)(
                      "div",
                      {
                        role: "presentation",
                        style: { display: "flex", width: virtualPaddingLeft }
                      }
                    ) : null,
                    virtualColumns.reduce((acc, vc, index, array) => {
                      const header = headerGroup.headers[vc.index];
                      const previousVC = array[index - 1];
                      if (previousVC && vc.index !== previousVC.index + 1) {
                        acc.push(
                          (0, import_jsx_runtime9.jsx)(
                            "div",
                            {
                              role: "presentation",
                              style: {
                                display: "flex",
                                width: `${vc.start - previousVC.end}px`
                              }
                            },
                            `padding-${previousVC.index}-${vc.index}`
                          )
                        );
                      }
                      acc.push(
                        (0, import_jsx_runtime9.jsx)(
                          "div",
                          {
                            role: "columnheader",
                            "data-column-index": vc.index,
                            style: {
                              width: header.getSize(),
                              ...getCommonPinningStyles(header.column)
                            },
                            className: "bg-ui-bg-base txt-compact-small-plus flex items-center border-b border-r px-4 py-2.5",
                            children: header.isPlaceholder ? null : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )
                          },
                          header.id
                        )
                      );
                      return acc;
                    }, []),
                    virtualPaddingRight ? (0, import_jsx_runtime9.jsx)(
                      "div",
                      {
                        role: "presentation",
                        style: {
                          display: "flex",
                          width: virtualPaddingRight
                        }
                      }
                    ) : null
                  ]
                },
                headerGroup.id
              ))
            }
          ),
          (0, import_jsx_runtime9.jsx)(
            "div",
            {
              role: "rowgroup",
              className: "relative grid",
              style: {
                height: `${rowVirtualizer.getTotalSize()}px`
              },
              children: virtualRows.map((virtualRow) => {
                const row = visibleRows[virtualRow.index];
                const rowIndex = flatRows.findIndex((r) => r.id === row.id);
                return (0, import_jsx_runtime9.jsx)(
                  DataGridRow,
                  {
                    row,
                    rowIndex,
                    virtualRow,
                    flatColumns,
                    virtualColumns,
                    anchor,
                    virtualPaddingLeft,
                    virtualPaddingRight,
                    onDragToFillStart,
                    multiColumnSelection
                  },
                  row.id
                );
              })
            }
          )
        ] })
      }
    ) })
  ] }) });
};
var DataGridHeader = ({
  columnOptions,
  isDisabled,
  onToggleColumn,
  onResetColumns,
  isHighlighted,
  errorCount,
  onToggleErrorHighlighting,
  onHeaderInteractionChange
}) => {
  const [shortcutsOpen, setShortcutsOpen] = (0, import_react18.useState)(false);
  const [columnsOpen, setColumnsOpen] = (0, import_react18.useState)(false);
  const { t } = useTranslation();
  const hasChanged = columnOptions.some((column) => !column.checked);
  const handleShortcutsOpenChange = (value) => {
    onHeaderInteractionChange(value);
    setShortcutsOpen(value);
  };
  const handleColumnsOpenChange = (value) => {
    onHeaderInteractionChange(value);
    setColumnsOpen(value);
  };
  return (0, import_jsx_runtime9.jsxs)("div", { className: "bg-ui-bg-base flex items-center justify-between border-b p-4", children: [
    (0, import_jsx_runtime9.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime9.jsxs)(DropdownMenu, { open: columnsOpen, onOpenChange: handleColumnsOpenChange, children: [
        (0, import_jsx_runtime9.jsx)(
          ConditionalTooltip,
          {
            showTooltip: isDisabled,
            content: t("dataGrid.columns.disabled"),
            children: (0, import_jsx_runtime9.jsx)(DropdownMenu.Trigger, { asChild: true, disabled: isDisabled, children: (0, import_jsx_runtime9.jsxs)(Button, { size: "small", variant: "secondary", children: [
              hasChanged ? (0, import_jsx_runtime9.jsx)(AdjustmentsDone, {}) : (0, import_jsx_runtime9.jsx)(Adjustments, {}),
              t("dataGrid.columns.view")
            ] }) })
          }
        ),
        (0, import_jsx_runtime9.jsx)(DropdownMenu.Content, { children: columnOptions.map((column, index) => {
          const { checked, disabled, id, name } = column;
          if (disabled) {
            return null;
          }
          return (0, import_jsx_runtime9.jsx)(
            DropdownMenu.CheckboxItem,
            {
              checked,
              onCheckedChange: onToggleColumn(index),
              onSelect: (e2) => e2.preventDefault(),
              children: name
            },
            id
          );
        }) })
      ] }),
      hasChanged && (0, import_jsx_runtime9.jsx)(
        Button,
        {
          size: "small",
          variant: "transparent",
          type: "button",
          onClick: onResetColumns,
          className: "text-ui-fg-muted hover:text-ui-fg-subtle",
          "data-id": "reset-columns",
          children: t("dataGrid.columns.resetToDefault")
        }
      )
    ] }),
    (0, import_jsx_runtime9.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      errorCount > 0 && (0, import_jsx_runtime9.jsxs)(
        Button,
        {
          size: "small",
          variant: "secondary",
          type: "button",
          onClick: onToggleErrorHighlighting,
          className: clx({
            "bg-ui-button-neutral-pressed": isHighlighted
          }),
          children: [
            (0, import_jsx_runtime9.jsx)(ExclamationCircle, { className: "text-ui-fg-subtle" }),
            (0, import_jsx_runtime9.jsx)("span", { children: t("dataGrid.errors.count", {
              count: errorCount
            }) })
          ]
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        DataGridKeyboardShortcutModal,
        {
          open: shortcutsOpen,
          onOpenChange: handleShortcutsOpenChange
        }
      )
    ] })
  ] });
};
var DataGridCell = ({
  cell,
  columnIndex,
  rowIndex,
  anchor,
  onDragToFillStart,
  multiColumnSelection
}) => {
  const coords = {
    row: rowIndex,
    col: columnIndex
  };
  const isAnchor = isCellMatch(coords, anchor);
  return (0, import_jsx_runtime9.jsx)(
    "div",
    {
      role: "gridcell",
      "aria-rowindex": rowIndex,
      "aria-colindex": columnIndex,
      style: {
        width: cell.column.getSize(),
        ...getCommonPinningStyles(cell.column)
      },
      "data-row-index": rowIndex,
      "data-column-index": columnIndex,
      className: clx(
        "relative flex items-center border-b border-r p-0 outline-none"
      ),
      tabIndex: -1,
      children: (0, import_jsx_runtime9.jsxs)("div", { className: "relative h-full w-full", children: [
        flexRender(cell.column.columnDef.cell, {
          ...cell.getContext(),
          columnIndex,
          rowIndex
        }),
        isAnchor && (0, import_jsx_runtime9.jsx)(
          "div",
          {
            onMouseDown: onDragToFillStart,
            className: clx(
              "bg-ui-fg-interactive absolute bottom-0 right-0 z-[3] size-1.5 cursor-ns-resize",
              {
                "cursor-nwse-resize": multiColumnSelection
              }
            )
          }
        )
      ] })
    }
  );
};
var DataGridRow = ({
  row,
  rowIndex,
  virtualRow,
  virtualPaddingLeft,
  virtualPaddingRight,
  virtualColumns,
  flatColumns,
  anchor,
  onDragToFillStart,
  multiColumnSelection
}) => {
  const visibleCells = row.getVisibleCells();
  return (0, import_jsx_runtime9.jsxs)(
    "div",
    {
      role: "row",
      "aria-rowindex": virtualRow.index,
      style: {
        transform: `translateY(${virtualRow.start}px)`
      },
      className: "bg-ui-bg-subtle txt-compact-small absolute flex h-10 w-full",
      children: [
        virtualPaddingLeft ? (0, import_jsx_runtime9.jsx)(
          "div",
          {
            role: "presentation",
            style: { display: "flex", width: virtualPaddingLeft }
          }
        ) : null,
        virtualColumns.reduce((acc, vc, index, array) => {
          const cell = visibleCells[vc.index];
          const column = cell.column;
          const columnIndex = flatColumns.findIndex((c) => c.id === column.id);
          const previousVC = array[index - 1];
          if (previousVC && vc.index !== previousVC.index + 1) {
            acc.push(
              (0, import_jsx_runtime9.jsx)(
                "div",
                {
                  role: "presentation",
                  style: {
                    display: "flex",
                    width: `${vc.start - previousVC.end}px`
                  }
                },
                `padding-${previousVC.index}-${vc.index}`
              )
            );
          }
          acc.push(
            (0, import_jsx_runtime9.jsx)(
              DataGridCell,
              {
                cell,
                columnIndex,
                rowIndex,
                anchor,
                onDragToFillStart,
                multiColumnSelection
              },
              cell.id
            )
          );
          return acc;
        }, []),
        virtualPaddingRight ? (0, import_jsx_runtime9.jsx)(
          "div",
          {
            role: "presentation",
            style: { display: "flex", width: virtualPaddingRight }
          }
        ) : null
      ]
    }
  );
};
var DataGridTextCell = ({
  context
}) => {
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  return (0, import_jsx_runtime10.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: field2 }) => {
        return (0, import_jsx_runtime10.jsx)(DataGridCellContainer, { ...container, ...errorProps, children: (0, import_jsx_runtime10.jsx)(Inner4, { field: field2, inputProps: input }) });
      }
    }
  );
};
var Inner4 = ({
  field,
  inputProps
}) => {
  const { onChange: _, onBlur, ref, value, ...rest } = field;
  const { ref: inputRef, onBlur: onInputBlur, onChange, ...input } = inputProps;
  const [localValue, setLocalValue] = (0, import_react21.useState)(value);
  (0, import_react21.useEffect)(() => {
    setLocalValue(value);
  }, [value]);
  const combinedRefs = useCombinedRefs(inputRef, ref);
  return (0, import_jsx_runtime10.jsx)(
    "input",
    {
      className: clx(
        "txt-compact-small text-ui-fg-subtle flex size-full cursor-pointer items-center justify-center bg-transparent outline-none",
        "focus:cursor-text"
      ),
      autoComplete: "off",
      tabIndex: -1,
      value: localValue,
      onChange: (e2) => setLocalValue(e2.target.value),
      ref: combinedRefs,
      onBlur: () => {
        onBlur();
        onInputBlur();
        onChange(localValue, value);
      },
      ...input,
      ...rest
    }
  );
};
var _DataGrid = ({
  isLoading,
  ...props
}) => {
  var _a;
  return isLoading ? (0, import_jsx_runtime11.jsx)(
    DataGridSkeleton,
    {
      columns: props.columns,
      rows: ((_a = props.data) == null ? void 0 : _a.length) && props.data.length > 0 ? props.data.length : 10
    }
  ) : (0, import_jsx_runtime11.jsx)(DataGridRoot, { ...props });
};
var DataGrid = Object.assign(_DataGrid, {
  BooleanCell: DataGridBooleanCell,
  TextCell: DataGridTextCell,
  NumberCell: DataGridNumberCell,
  CurrencyCell: DataGridCurrencyCell,
  ReadonlyCell: DataGridReadonlyCell
});
function createDataGridHelper() {
  const columnHelper = createColumnHelper();
  return {
    column: ({
      id,
      name,
      header,
      cell,
      disableHiding = false,
      field,
      type
    }) => columnHelper.display({
      id,
      header,
      cell,
      enableHiding: !disableHiding,
      meta: {
        name,
        field,
        type
      }
    })
  };
}
var IncludesTaxTooltip = ({
  includesTax
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime12.jsx)(
    Tooltip,
    {
      maxWidth: 999,
      content: includesTax ? t("general.includesTaxTooltip") : t("general.excludesTaxTooltip"),
      children: includesTax ? (0, import_jsx_runtime12.jsx)(TaxInclusive, { className: "text-ui-fg-muted shrink-0" }) : (0, import_jsx_runtime12.jsx)(TaxExclusive, { className: "text-ui-fg-muted shrink-0" })
    }
  );
};
var createDataGridPriceColumns = ({
  currencies: currencies2,
  regions,
  pricePreferences,
  isReadyOnly,
  getFieldName,
  t
}) => {
  const columnHelper = createDataGridHelper();
  return [
    ...(currencies2 == null ? void 0 : currencies2.map((currency) => {
      const preference = pricePreferences == null ? void 0 : pricePreferences.find(
        (p) => p.attribute === "currency_code" && p.value === currency
      );
      const translatedCurrencyName = t("fields.priceTemplate", {
        regionOrCurrency: currency.toUpperCase()
      });
      return columnHelper.column({
        id: `currency_prices.${currency}`,
        name: t("fields.priceTemplate", {
          regionOrCurrency: currency.toUpperCase()
        }),
        field: (context) => {
          const isReadyOnlyValue = isReadyOnly == null ? void 0 : isReadyOnly(context);
          if (isReadyOnlyValue) {
            return null;
          }
          return getFieldName(context, currency);
        },
        type: "number",
        header: () => (0, import_jsx_runtime13.jsxs)("div", { className: "flex w-full items-center justify-between gap-3", children: [
          (0, import_jsx_runtime13.jsx)("span", { className: "truncate", title: translatedCurrencyName, children: translatedCurrencyName }),
          (0, import_jsx_runtime13.jsx)(IncludesTaxTooltip, { includesTax: preference == null ? void 0 : preference.is_tax_inclusive })
        ] }),
        cell: (context) => {
          if (isReadyOnly == null ? void 0 : isReadyOnly(context)) {
            return (0, import_jsx_runtime13.jsx)(DataGridReadonlyCell, { context });
          }
          return (0, import_jsx_runtime13.jsx)(DataGridCurrencyCell, { code: currency, context });
        }
      });
    })) ?? [],
    ...(regions == null ? void 0 : regions.map((region) => {
      const preference = pricePreferences == null ? void 0 : pricePreferences.find(
        (p) => p.attribute === "region_id" && p.value === region.id
      );
      const translatedRegionName = t("fields.priceTemplate", {
        regionOrCurrency: region.name
      });
      return columnHelper.column({
        id: `region_prices.${region.id}`,
        name: t("fields.priceTemplate", {
          regionOrCurrency: region.name
        }),
        field: (context) => {
          const isReadyOnlyValue = isReadyOnly == null ? void 0 : isReadyOnly(context);
          if (isReadyOnlyValue) {
            return null;
          }
          return getFieldName(context, region.id);
        },
        type: "number",
        header: () => (0, import_jsx_runtime13.jsxs)("div", { className: "flex w-full items-center justify-between gap-3", children: [
          (0, import_jsx_runtime13.jsx)("span", { className: "truncate", title: translatedRegionName, children: translatedRegionName }),
          (0, import_jsx_runtime13.jsx)(IncludesTaxTooltip, { includesTax: preference == null ? void 0 : preference.is_tax_inclusive })
        ] }),
        cell: (context) => {
          if (isReadyOnly == null ? void 0 : isReadyOnly(context)) {
            return (0, import_jsx_runtime13.jsx)(DataGridReadonlyCell, { context });
          }
          const currency = currencies2 == null ? void 0 : currencies2.find((c) => c === region.currency_code);
          if (!currency) {
            return null;
          }
          return (0, import_jsx_runtime13.jsx)(
            DataGridCurrencyCell,
            {
              code: region.currency_code,
              context
            }
          );
        }
      });
    })) ?? []
  ];
};

export {
  DataGridSkeleton,
  useCombinedRefs,
  useDataGridCell,
  useDataGridCellError,
  useDataGridDuplicateCell,
  DataGridCellContainer,
  DataGridReadonlyCell,
  DataGrid,
  createDataGridHelper,
  IncludesTaxTooltip,
  createDataGridPriceColumns
};
//# sourceMappingURL=chunk-JGBVAJ3K.js.map
