import {
  optionalFloat,
  optionalInt
} from "./chunk-7LOZU53L.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import {
  FileUpload
} from "./chunk-ZE42WU6O.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form
} from "./chunk-DPO7J5IQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-QNPT2JGT.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var normalizeProductFormValues = (values) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  const thumbnail = (_b = (_a = values.media) == null ? void 0 : _a.find((media) => media.isThumbnail)) == null ? void 0 : _b.url;
  const images = (_c = values.media) == null ? void 0 : _c.filter((media) => !media.isThumbnail).map((media) => ({ url: media.url }));
  return {
    status: values.status,
    is_giftcard: false,
    tags: ((_d = values == null ? void 0 : values.tags) == null ? void 0 : _d.length) ? (_e = values.tags) == null ? void 0 : _e.map((tag) => ({ id: tag })) : void 0,
    sales_channels: ((_f = values == null ? void 0 : values.sales_channels) == null ? void 0 : _f.length) ? (_g = values.sales_channels) == null ? void 0 : _g.map((sc) => ({ id: sc.id })) : void 0,
    images,
    collection_id: values.collection_id || void 0,
    shipping_profile_id: values.shipping_profile_id || void 0,
    categories: values.categories.map((id) => ({ id })),
    type_id: values.type_id || void 0,
    handle: (_h = values.handle) == null ? void 0 : _h.trim(),
    origin_country: values.origin_country || void 0,
    material: values.material || void 0,
    mid_code: values.mid_code || void 0,
    hs_code: values.hs_code || void 0,
    thumbnail,
    title: values.title.trim(),
    subtitle: (_i = values.subtitle) == null ? void 0 : _i.trim(),
    description: (_j = values.description) == null ? void 0 : _j.trim(),
    discountable: values.discountable,
    width: values.width ? parseFloat(values.width) : void 0,
    length: values.length ? parseFloat(values.length) : void 0,
    height: values.height ? parseFloat(values.height) : void 0,
    weight: values.weight ? parseFloat(values.weight) : void 0,
    options: values.options.filter((o) => o.title),
    // clean temp. values
    variants: normalizeVariants(
      values.variants.filter((variant) => variant.should_create),
      values.regionsCurrencyMap
    )
  };
};
var normalizeVariants = (variants, regionsCurrencyMap) => {
  return variants.map((variant) => ({
    title: variant.title || Object.values(variant.options || {}).join(" / "),
    options: variant.options,
    sku: variant.sku || void 0,
    manage_inventory: !!variant.manage_inventory,
    allow_backorder: !!variant.allow_backorder,
    variant_rank: variant.variant_rank,
    inventory_items: variant.inventory.map((i) => {
      const quantity = i.required_quantity ? castNumber(i.required_quantity) : null;
      if (!i.inventory_item_id || !quantity) {
        return false;
      }
      return {
        ...i,
        required_quantity: quantity
      };
    }).filter(
      (item) => item !== false
    ),
    prices: Object.entries(variant.prices || {}).map(([key, value]) => {
      if (value === "" || value === void 0) {
        return void 0;
      }
      if (key.startsWith("reg_")) {
        return {
          currency_code: regionsCurrencyMap[key],
          amount: castNumber(value),
          rules: { region_id: key }
        };
      } else {
        return {
          currency_code: key,
          amount: castNumber(value)
        };
      }
    }).filter((v) => !!v)
  }));
};
var decorateVariantsWithDefaultValues = (variants) => {
  return variants.map((variant) => ({
    ...variant,
    title: variant.title || "",
    sku: variant.sku || "",
    manage_inventory: variant.manage_inventory || false,
    allow_backorder: variant.allow_backorder || false,
    inventory_kit: variant.inventory_kit || false
  }));
};
var MediaSchema = z.object({
  id: z.string().optional(),
  url: z.string(),
  isThumbnail: z.boolean(),
  file: z.any().nullable()
  // File
});
var ProductCreateVariantSchema = z.object({
  should_create: z.boolean(),
  is_default: z.boolean().optional(),
  title: z.string(),
  upc: z.string().optional(),
  ean: z.string().optional(),
  barcode: z.string().optional(),
  mid_code: z.string().optional(),
  hs_code: z.string().optional(),
  width: optionalInt,
  height: optionalInt,
  length: optionalInt,
  weight: optionalInt,
  material: z.string().optional(),
  origin_country: z.string().optional(),
  sku: z.string().optional(),
  manage_inventory: z.boolean().optional(),
  allow_backorder: z.boolean().optional(),
  inventory_kit: z.boolean().optional(),
  options: z.record(z.string(), z.string()),
  variant_rank: z.number(),
  prices: z.record(z.string(), optionalFloat).optional(),
  inventory: z.array(
    z.object({
      inventory_item_id: z.string(),
      required_quantity: optionalInt
    })
  ).optional()
});
var ProductCreateOptionSchema = z.object({
  title: z.string(),
  values: z.array(z.string()).min(1)
});
var ProductCreateSchema = z.object({
  title: z.string().min(1),
  subtitle: z.string().optional(),
  handle: z.string().optional(),
  description: z.string().optional(),
  discountable: z.boolean(),
  type_id: z.string().optional(),
  collection_id: z.string().optional(),
  shipping_profile_id: z.string().optional(),
  categories: z.array(z.string()),
  tags: z.array(z.string()).optional(),
  sales_channels: z.array(
    z.object({
      id: z.string(),
      name: z.string()
    })
  ).optional(),
  origin_country: z.string().optional(),
  material: z.string().optional(),
  width: z.string().optional(),
  length: z.string().optional(),
  height: z.string().optional(),
  weight: z.string().optional(),
  mid_code: z.string().optional(),
  hs_code: z.string().optional(),
  options: z.array(ProductCreateOptionSchema).min(1),
  enable_variants: z.boolean(),
  variants: z.array(ProductCreateVariantSchema).min(1),
  media: z.array(MediaSchema).optional()
}).superRefine((data, ctx) => {
  if (data.variants.every((v) => !v.should_create)) {
    return ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["variants"],
      message: "invalid_length"
    });
  }
  const skus = /* @__PURE__ */ new Set();
  data.variants.forEach((v, index) => {
    if (v.sku) {
      if (skus.has(v.sku)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: [`variants.${index}.sku`],
          message: instance.t("products.create.errors.uniqueSku")
        });
      }
      skus.add(v.sku);
    }
  });
});
var EditProductMediaSchema = z.object({
  media: z.array(MediaSchema)
});
var PRODUCT_CREATE_FORM_DEFAULTS = {
  discountable: true,
  tags: [],
  sales_channels: [],
  options: [
    {
      title: "Default option",
      values: ["Default option value"]
    }
  ],
  variants: decorateVariantsWithDefaultValues([
    {
      title: "Default variant",
      should_create: true,
      variant_rank: 0,
      options: {
        "Default option": "Default option value"
      },
      inventory: [{ inventory_item_id: "", required_quantity: "" }],
      is_default: true
    }
  ]),
  enable_variants: false,
  media: [],
  categories: [],
  collection_id: "",
  shipping_profile_id: "",
  description: "",
  handle: "",
  height: "",
  hs_code: "",
  length: "",
  material: "",
  mid_code: "",
  origin_country: "",
  subtitle: "",
  title: "",
  type_id: "",
  weight: "",
  width: ""
};
var SUPPORTED_FORMATS = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/heic",
  "image/svg+xml"
];
var SUPPORTED_FORMATS_FILE_EXTENSIONS = [
  ".jpeg",
  ".png",
  ".gif",
  ".webp",
  ".heic",
  ".svg"
];
var UploadMediaFormItem = ({
  form,
  append,
  showHint = true
}) => {
  const { t } = useTranslation();
  const hasInvalidFiles = (0, import_react.useCallback)(
    (fileList) => {
      const invalidFile = fileList.find(
        (f) => !SUPPORTED_FORMATS.includes(f.file.type)
      );
      if (invalidFile) {
        form.setError("media", {
          type: "invalid_file",
          message: t("products.media.invalidFileType", {
            name: invalidFile.file.name,
            types: SUPPORTED_FORMATS_FILE_EXTENSIONS.join(", ")
          })
        });
        return true;
      }
      return false;
    },
    [form, t]
  );
  const onUploaded = (0, import_react.useCallback)(
    (files) => {
      form.clearErrors("media");
      if (hasInvalidFiles(files)) {
        return;
      }
      files.forEach((f) => append({ ...f, isThumbnail: false }));
    },
    [form, append, hasInvalidFiles]
  );
  return (0, import_jsx_runtime.jsx)(
    Form.Field,
    {
      control: form.control,
      name: "media",
      render: () => {
        return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-2", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.media.label") }),
            showHint && (0, import_jsx_runtime.jsx)(Form.Hint, { children: t("products.media.editHint") })
          ] }),
          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
            FileUpload,
            {
              label: t("products.media.uploadImagesLabel"),
              hint: t("products.media.uploadImagesHint"),
              hasError: !!form.formState.errors.media,
              formats: SUPPORTED_FORMATS,
              onUploaded
            }
          ) }),
          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
        ] }) });
      }
    }
  );
};

export {
  normalizeProductFormValues,
  decorateVariantsWithDefaultValues,
  ProductCreateSchema,
  EditProductMediaSchema,
  PRODUCT_CREATE_FORM_DEFAULTS,
  UploadMediaFormItem
};
//# sourceMappingURL=chunk-E4NIVO46.js.map
