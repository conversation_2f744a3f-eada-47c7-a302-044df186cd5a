{"version": 3, "sources": ["../../@medusajs/dashboard/dist/api-key-management-sales-channels-OMIWIBUF.mjs"], "sourcesContent": ["import {\n  VisuallyHidden\n} from \"./chunk-F6ZOHZVB.mjs\";\nimport {\n  useSalesChannelTableColumns,\n  useSalesChannelTableEmptyState,\n  useSalesChannelTableFilters,\n  useSalesChannelTableQuery\n} from \"./chunk-44QN6VEG.mjs\";\nimport \"./chunk-4BTG27L5.mjs\";\nimport {\n  DataTable\n} from \"./chunk-3IIOXMXN.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  use<PERSON><PERSON><PERSON><PERSON>,\n  useBatchAddSalesChannelsToApiKey\n} from \"./chunk-F6IJV2I2.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/api-key-management/api-key-management-sales-channels/api-key-management-sales-channels.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/api-key-management/api-key-management-sales-channels/components/api-key-sales-channels-form/api-key-sales-channels-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  Checkbox,\n  Hint,\n  createDataTableColumnHelper,\n  toast\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddSalesChannelsToApiKeySchema = zod.object({\n  sales_channel_ids: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"sc_add\";\nvar ApiKeySalesChannelsForm = ({\n  apiKey,\n  preSelected = []\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      sales_channel_ids: []\n    },\n    resolver: zodResolver(AddSalesChannelsToApiKeySchema)\n  });\n  const { setValue } = form;\n  const [rowSelection, setRowSelection] = useState({});\n  const { mutateAsync, isPending: isMutating } = useBatchAddSalesChannelsToApiKey(apiKey);\n  const searchParams = useSalesChannelTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const columns = useColumns();\n  const filters = useSalesChannelTableFilters();\n  const emptyState = useSalesChannelTableEmptyState();\n  const { sales_channels, count, isPending } = useSalesChannels(\n    { ...searchParams },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const updater = (selection) => {\n    const ids = Object.keys(selection);\n    setValue(\"sales_channel_ids\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(selection);\n  };\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(values.sales_channel_ids, {\n      onSuccess: () => {\n        toast.success(\n          t(\"apiKeyManagement.salesChannels.successToast\", {\n            count: values.sales_channel_ids.length\n          })\n        );\n        handleSuccess();\n      },\n      onError: (err) => {\n        toast.error(err.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsxs(RouteFocusModal.Header, { children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"apiKeyManagement.salesChannels.title\") }) }),\n      /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"apiKeyManagement.salesChannels.description\") }) }),\n      /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: form.formState.errors.sales_channel_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.sales_channel_ids.message }) })\n    ] }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-auto\", children: /* @__PURE__ */ jsx(\n      DataTable,\n      {\n        data: sales_channels,\n        columns,\n        filters,\n        getRowId: (row) => row.id,\n        rowCount: count,\n        layout: \"fill\",\n        emptyState,\n        isLoading: isPending,\n        rowSelection: {\n          state: rowSelection,\n          onRowSelectionChange: updater,\n          enableRowSelection: (row) => !preSelected.includes(row.id)\n        },\n        prefix: PREFIX,\n        pageSize: PAGE_SIZE,\n        autoFocusSearch: true\n      }\n    ) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isMutating, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const base = useSalesChannelTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.select({\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isSelected = row.getIsSelected() || isPreSelected;\n          return /* @__PURE__ */ jsx(\n            ConditionalTooltip,\n            {\n              content: t(\"apiKeyManagement.salesChannels.alreadyAddedTooltip\"),\n              showTooltip: isPreSelected,\n              children: /* @__PURE__ */ jsx(\"div\", { children: /* @__PURE__ */ jsx(\n                Checkbox,\n                {\n                  checked: isSelected,\n                  disabled: isPreSelected,\n                  onCheckedChange: (value) => row.toggleSelected(!!value),\n                  onClick: (e) => {\n                    e.stopPropagation();\n                  }\n                }\n              ) })\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n};\n\n// src/routes/api-key-management/api-key-management-sales-channels/api-key-management-sales-channels.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ApiKeyManagementAddSalesChannels = () => {\n  const { id } = useParams();\n  const { api_key, isLoading, isError, error } = useApiKey(id);\n  const preSelected = api_key?.sales_channels?.map((sc) => sc.id);\n  const ready = !isLoading && api_key;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(ApiKeySalesChannelsForm, { apiKey: id, preSelected }) });\n};\nexport {\n  ApiKeyManagementAddSalesChannels as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA,mBAAkC;AAIlC,yBAA0B;AA+H1B,IAAAA,sBAA4B;AA9H5B,IAAI,iCAAqC,WAAO;AAAA,EAC9C,mBAAuB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAClD,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AAAA,EACA,cAAc,CAAC;AACjB,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,mBAAmB,CAAC;AAAA,IACtB;AAAA,IACA,UAAU,EAAY,8BAA8B;AAAA,EACtD,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,iCAAiC,MAAM;AACtF,QAAM,eAAe,0BAA0B;AAAA,IAC7C,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,4BAA4B;AAC5C,QAAM,aAAa,+BAA+B;AAClD,QAAM,EAAE,gBAAgB,OAAO,UAAU,IAAI;AAAA,IAC3C,EAAE,GAAG,aAAa;AAAA,IAClB;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,CAAC,cAAc;AAC7B,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,aAAS,qBAAqB,KAAK;AAAA,MACjC,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,SAAS;AAAA,EAC3B;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,YAAY,OAAO,mBAAmB;AAAA,MAC1C,WAAW,MAAM;AACf,cAAM;AAAA,UACJA,GAAE,+CAA+C;AAAA,YAC/C,OAAO,OAAO,kBAAkB;AAAA,UAClC,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,QAAQ;AAChB,cAAM,MAAM,IAAI,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QAC3J,yBAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,UACvC,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,sCAAsC,EAAE,CAAC,EAAE,CAAC;AAAA,UACpJ,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,4CAA4C,EAAE,CAAC,EAAE,CAAC;AAAA,UAChK,wBAAI,OAAO,EAAE,WAAW,yCAAyC,UAAU,KAAK,UAAU,OAAO,yBAAqC,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,kBAAkB,QAAQ,CAAC,EAAE,CAAC;AAAA,IACpP,EAAE,CAAC;AAAA,QACa,wBAAI,gBAAgB,MAAM,EAAE,WAAW,sCAAsC,cAA0B;AAAA,MACrH;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,UAAU,CAAC,QAAQ,IAAI;AAAA,QACvB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR;AAAA,QACA,WAAW;AAAA,QACX,cAAc;AAAA,UACZ,OAAO;AAAA,UACP,sBAAsB;AAAA,UACtB,oBAAoB,CAAC,QAAQ,CAAC,YAAY,SAAS,IAAI,EAAE;AAAA,QAC3D;AAAA,QACA,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,iBAAiB;AAAA,MACnB;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,YAAY,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IACnH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,4BAA4B;AACzC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,OAAO;AAAA,QAClB,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAASA,GAAE,oDAAoD;AAAA,cAC/D,aAAa;AAAA,cACb,cAA0B,wBAAI,OAAO,EAAE,cAA0B;AAAA,gBAC/D;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,kBACtD,SAAS,CAAC,MAAM;AACd,sBAAE,gBAAgB;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACF;AAIA,IAAI,mCAAmC,MAAM;AA3L7C;AA4LE,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,UAAU,EAAE;AAC3D,QAAM,eAAc,wCAAS,mBAAT,mBAAyB,IAAI,CAAC,OAAO,GAAG;AAC5D,QAAM,QAAQ,CAAC,aAAa;AAC5B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,yBAAyB,EAAE,QAAQ,IAAI,YAAY,CAAC,EAAE,CAAC;AAChJ;", "names": ["import_jsx_runtime", "t", "jsx2"]}