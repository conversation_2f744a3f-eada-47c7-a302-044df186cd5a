{"version": 3, "sources": ["../../@medusajs/dashboard/dist/adjust-inventory-WYNH75F7.mjs"], "sourcesContent": ["import {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  useInventoryItem,\n  useUpdateInventoryLevel\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/inventory/inventory-detail/components/adjust-inventory/adjust-inventory-drawer.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/inventory/inventory-detail/components/adjust-inventory/components/adjust-inventory-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm, useWatch } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AttributeGridRow = ({\n  title,\n  value\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 divide-x\", children: [\n    /* @__PURE__ */ jsx(Text, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: title }),\n    /* @__PURE__ */ jsx(Text, { className: \"px-2 py-1.5\", size: \"small\", leading: \"compact\", children: value })\n  ] });\n};\nvar AdjustInventoryForm = ({\n  item,\n  level,\n  location\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const AdjustInventorySchema = z.object({\n    stocked_quantity: z.union([z.number(), z.string()])\n  }).superRefine((data, ctx) => {\n    const quantity = data.stocked_quantity ? castNumber(data.stocked_quantity) : null;\n    if (quantity === null) {\n      ctx.addIssue({\n        code: z.ZodIssueCode.invalid_type,\n        expected: \"number\",\n        received: \"undefined\",\n        path: [\"stocked_quantity\"]\n      });\n      return;\n    }\n    if (quantity < level.reserved_quantity) {\n      ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: t(\"inventory.adjustInventory.errors.stockedQuantity\", {\n          quantity: level.reserved_quantity\n        }),\n        path: [\"stocked_quantity\"]\n      });\n    }\n  });\n  const form = useForm({\n    defaultValues: {\n      stocked_quantity: level.stocked_quantity\n    },\n    resolver: zodResolver(AdjustInventorySchema)\n  });\n  const stockedQuantityUpdate = useWatch({\n    control: form.control,\n    name: \"stocked_quantity\"\n  });\n  const availableQuantity = stockedQuantityUpdate ? castNumber(stockedQuantityUpdate) - level.reserved_quantity : 0 - level.reserved_quantity;\n  const { mutateAsync, isPending: isLoading } = useUpdateInventoryLevel(\n    item.id,\n    level.location_id\n  );\n  const handleSubmit = form.handleSubmit(async (value) => {\n    await mutateAsync(\n      {\n        stocked_quantity: castNumber(value.stocked_quantity)\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"inventory.toast.updateLevel\"));\n          handleSuccess();\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-8 overflow-auto\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle shadow-elevation-card-rest grid grid-rows-4 divide-y rounded-lg border\", children: [\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"fields.title\"),\n                value: item.title ?? \"-\"\n              }\n            ),\n            /* @__PURE__ */ jsx(AttributeGridRow, { title: t(\"fields.sku\"), value: item.sku }),\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"locations.domain\"),\n                value: location.name\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"inventory.reserved\"),\n                value: level.reserved_quantity\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              AttributeGridRow,\n              {\n                title: t(\"inventory.available\"),\n                value: availableQuantity\n              }\n            )\n          ] }),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"stocked_quantity\",\n              render: ({ field: { onChange, value, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.inStock\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Input,\n                    {\n                      type: \"number\",\n                      value,\n                      onChange,\n                      ...field\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/inventory/inventory-detail/components/adjust-inventory/adjust-inventory-drawer.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar AdjustInventoryDrawer = () => {\n  const { id, location_id } = useParams();\n  const { t } = useTranslation2();\n  const {\n    inventory_item: inventoryItem,\n    isPending: isLoading,\n    isError,\n    error\n  } = useInventoryItem(id);\n  const inventoryLevel = inventoryItem?.location_levels.find(\n    (level) => level.location_id === location_id\n  );\n  const { stock_location, isLoading: isLoadingLocation } = useStockLocation(\n    location_id\n  );\n  const ready = !isLoading && inventoryItem && inventoryLevel && !isLoadingLocation && stock_location;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"inventory.manageLocationQuantity\") }) }),\n    ready && /* @__PURE__ */ jsx2(\n      AdjustInventoryForm,\n      {\n        item: inventoryItem,\n        level: inventoryLevel,\n        location: stock_location\n      }\n    )\n  ] });\n};\nexport {\n  AdjustInventoryDrawer as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,yBAA0B;AA8I1B,IAAAA,sBAA2C;AA7I3C,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QACrE,wBAAI,MAAM,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,MAAM,CAAC;AAAA,QAC1F,wBAAI,MAAM,EAAE,WAAW,eAAe,MAAM,SAAS,SAAS,WAAW,UAAU,MAAM,CAAC;AAAA,EAC5G,EAAE,CAAC;AACL;AACA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,wBAAwB,EAAE,OAAO;AAAA,IACrC,kBAAkB,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,EAAE,YAAY,CAAC,MAAM,QAAQ;AAC5B,UAAM,WAAW,KAAK,mBAAmB,WAAW,KAAK,gBAAgB,IAAI;AAC7E,QAAI,aAAa,MAAM;AACrB,UAAI,SAAS;AAAA,QACX,MAAM,EAAE,aAAa;AAAA,QACrB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM,CAAC,kBAAkB;AAAA,MAC3B,CAAC;AACD;AAAA,IACF;AACA,QAAI,WAAW,MAAM,mBAAmB;AACtC,UAAI,SAAS;AAAA,QACX,MAAM,EAAE,aAAa;AAAA,QACrB,SAASA,GAAE,oDAAoD;AAAA,UAC7D,UAAU,MAAM;AAAA,QAClB,CAAC;AAAA,QACD,MAAM,CAAC,kBAAkB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,kBAAkB,MAAM;AAAA,IAC1B;AAAA,IACA,UAAU,EAAY,qBAAqB;AAAA,EAC7C,CAAC;AACD,QAAM,wBAAwB,SAAS;AAAA,IACrC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,oBAAoB,wBAAwB,WAAW,qBAAqB,IAAI,MAAM,oBAAoB,IAAI,MAAM;AAC1H,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI;AAAA,IAC5C,KAAK;AAAA,IACL,MAAM;AAAA,EACR;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,UAAU;AACtD,UAAM;AAAA,MACJ;AAAA,QACE,kBAAkB,WAAW,MAAM,gBAAgB;AAAA,MACrD;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,6BAA6B,CAAC;AAC9C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F,yBAAK,OAAO,EAAE,WAAW,4FAA4F,UAAU;AAAA,gBAC7H;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,cAAc;AAAA,gBACvB,OAAO,KAAK,SAAS;AAAA,cACvB;AAAA,YACF;AAAA,gBACgB,wBAAI,kBAAkB,EAAE,OAAOA,GAAE,YAAY,GAAG,OAAO,KAAK,IAAI,CAAC;AAAA,gBACjE;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,kBAAkB;AAAA,gBAC3B,OAAO,SAAS;AAAA,cAClB;AAAA,YACF;AAAA,gBACgB;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,oBAAoB;AAAA,gBAC7B,OAAO,MAAM;AAAA,cACf;AAAA,YACF;AAAA,gBACgB;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAOA,GAAE,qBAAqB;AAAA,gBAC9B,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,sBACjD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN;AAAA,sBACA;AAAA,sBACA,GAAG;AAAA,oBACL;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACvG,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,IAAI,YAAY,IAAI,UAAU;AACtC,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,EAAE;AACvB,QAAM,iBAAiB,+CAAe,gBAAgB;AAAA,IACpD,CAAC,UAAU,MAAM,gBAAgB;AAAA;AAEnC,QAAM,EAAE,gBAAgB,WAAW,kBAAkB,IAAI;AAAA,IACvD;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,iBAAiB,kBAAkB,CAAC,qBAAqB;AACrF,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,kCAAkC,EAAE,CAAC,EAAE,CAAC;AAAA,IACzI,aAAyB,oBAAAE;AAAA,MACvB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}