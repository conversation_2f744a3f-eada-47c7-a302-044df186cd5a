{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-THZJC662.mjs"], "sourcesContent": ["// src/lib/data/country-states.ts\nfunction getCountryProvinceObjectByIso2(iso2) {\n  if (!iso2) {\n    return null;\n  }\n  const code = iso2.toUpperCase();\n  return countryProvinceMap[code] || null;\n}\nfunction getProvinceByIso2(iso2) {\n  if (!iso2) {\n    return null;\n  }\n  const key = iso2.toUpperCase();\n  for (const country in countryProvinceMap) {\n    if (countryProvinceMap[country].options[key]) {\n      return countryProvinceMap[country].options[key];\n    }\n  }\n  return null;\n}\nfunction isProvinceInCountry(countryCode, provinceCode) {\n  if (!countryCode || !provinceCode) {\n    return false;\n  }\n  const code = provinceCode.toUpperCase();\n  const country = countryProvinceMap[countryCode.toUpperCase()];\n  return country?.options[code] !== void 0;\n}\nvar countryProvinceMap = {\n  AR: {\n    type: \"province\",\n    options: {\n      \"AR-B\": \"Buenos Aires\",\n      \"AR-C\": \"Ciudad Aut\\xF3noma de Buenos Aires\",\n      \"AR-K\": \"Catamarca\",\n      \"AR-H\": \"Chaco\",\n      \"AR-U\": \"Chubut\",\n      \"AR-X\": \"C\\xF3rdoba\",\n      \"AR-W\": \"Corrientes\",\n      \"AR-E\": \"Entre R\\xEDos\",\n      \"AR-P\": \"Formosa\",\n      \"AR-Y\": \"Jujuy\",\n      \"AR-L\": \"La Pampa\",\n      \"AR-F\": \"La Rioja\",\n      \"AR-M\": \"Mendoza\",\n      \"AR-N\": \"Misiones\",\n      \"AR-Q\": \"Neuqu\\xE9n\",\n      \"AR-R\": \"R\\xEDo Negro\",\n      \"AR-A\": \"Salta\",\n      \"AR-J\": \"San Juan\",\n      \"AR-D\": \"San Luis\",\n      \"AR-Z\": \"Santa Cruz\",\n      \"AR-S\": \"Santa Fe\",\n      \"AR-G\": \"Santiago del Estero\",\n      \"AR-V\": \"Tierra del Fuego\",\n      \"AR-T\": \"Tucum\\xE1n\"\n    }\n  },\n  AU: {\n    type: \"stateOrTerritory\",\n    options: {\n      \"AU-ACT\": \"Australian Capital Territory\",\n      \"AU-NSW\": \"New South Wales\",\n      \"AU-NT\": \"Northern Territory\",\n      \"AU-QLD\": \"Queensland\",\n      \"AU-SA\": \"South Australia\",\n      \"AU-TAS\": \"Tasmania\",\n      \"AU-VIC\": \"Victoria\",\n      \"AU-WA\": \"Western Australia\"\n    }\n  },\n  BR: {\n    type: \"state\",\n    options: {\n      \"BR-AC\": \"Acre\",\n      \"BR-AL\": \"Alagoas\",\n      \"BR-AP\": \"Amap\\xE1\",\n      \"BR-AM\": \"Amazonas\",\n      \"BR-BA\": \"Bahia\",\n      \"BR-CE\": \"Cear\\xE1\",\n      \"BR-DF\": \"Distrito Federal\",\n      \"BR-ES\": \"Esp\\xEDrito Santo\",\n      \"BR-GO\": \"Goi\\xE1s\",\n      \"BR-MA\": \"Maranh\\xE3o\",\n      \"BR-MT\": \"Mato Grosso\",\n      \"BR-MS\": \"Mato Grosso do Sul\",\n      \"BR-MG\": \"Minas Gerais\",\n      \"BR-PA\": \"Par\\xE1\",\n      \"BR-PB\": \"Para\\xEDba\",\n      \"BR-PR\": \"Paran\\xE1\",\n      \"BR-PE\": \"Pernambuco\",\n      \"BR-PI\": \"Piau\\xED\",\n      \"BR-RJ\": \"Rio de Janeiro\",\n      \"BR-RN\": \"Rio Grande do Norte\",\n      \"BR-RS\": \"Rio Grande do Sul\",\n      \"BR-RO\": \"Rond\\xF4nia\",\n      \"BR-RR\": \"Roraima\",\n      \"BR-SC\": \"Santa Catarina\",\n      \"BR-SP\": \"S\\xE3o Paulo\",\n      \"BR-SE\": \"Sergipe\",\n      \"BR-TO\": \"Tocantins\"\n    }\n  },\n  CA: {\n    type: \"province\",\n    options: {\n      \"CA-AB\": \"Alberta\",\n      \"CA-BC\": \"British Columbia\",\n      \"CA-MB\": \"Manitoba\",\n      \"CA-NB\": \"New Brunswick\",\n      \"CA-NL\": \"Newfoundland and Labrador\",\n      \"CA-NS\": \"Nova Scotia\",\n      \"CA-NT\": \"Northwest Territories\",\n      \"CA-NU\": \"Nunavut\",\n      \"CA-ON\": \"Ontario\",\n      \"CA-PE\": \"Prince Edward Island\",\n      \"CA-QC\": \"Quebec\",\n      \"CA-SK\": \"Saskatchewan\",\n      \"CA-YT\": \"Yukon\"\n    }\n  },\n  CL: {\n    type: \"region\",\n    options: {\n      \"CL-AI\": \"Ais\\xE9n del General Carlos Ib\\xE1\\xF1ez del Campo\",\n      \"CL-AN\": \"Antofagasta\",\n      \"CL-AR\": \"Araucan\\xEDa\",\n      \"CL-AP\": \"Arica y Parinacota\",\n      \"CL-AT\": \"Atacama\",\n      \"CL-BI\": \"B\\xEDo B\\xEDo\",\n      \"CL-CO\": \"Coquimbo\",\n      \"CL-LI\": \"Libertador General Bernardo O'Higgins\",\n      \"CL-LL\": \"Los Lagos\",\n      \"CL-LR\": \"Los R\\xEDos\",\n      \"CL-MA\": \"Magallanes y de la Ant\\xE1rtica Chilena\",\n      \"CL-ML\": \"Maule\",\n      \"CL-RM\": \"Regi\\xF3n Metropolitana de Santiago\",\n      \"CL-TA\": \"Tarapac\\xE1\",\n      \"CL-VS\": \"Valpara\\xEDso\"\n    }\n  },\n  CN: {\n    type: \"province\",\n    options: {\n      \"CN-AH\": \"Anhui\",\n      \"CN-BJ\": \"Beijing\",\n      \"CN-CQ\": \"Chongqing\",\n      \"CN-FJ\": \"Fujian\",\n      \"CN-GD\": \"Guangdong\",\n      \"CN-GS\": \"Gansu\",\n      \"CN-GX\": \"Guangxi Zhuang\",\n      \"CN-GZ\": \"Guizhou\",\n      \"CN-HA\": \"Henan\",\n      \"CN-HB\": \"Hubei\",\n      \"CN-HE\": \"Hebei\",\n      \"CN-HI\": \"Hainan\",\n      \"CN-HK\": \"Hong Kong\",\n      \"CN-HL\": \"Heilongjiang\",\n      \"CN-HN\": \"Hunan\",\n      \"CN-JL\": \"Jilin\",\n      \"CN-JS\": \"Jiangsu\",\n      \"CN-JX\": \"Jiangxi\",\n      \"CN-LN\": \"Liaoning\",\n      \"CN-NM\": \"Inner Mongolia\",\n      \"CN-NX\": \"Ningxia Hui\",\n      \"CN-QH\": \"Qinghai\",\n      \"CN-SA\": \"Shaanxi\",\n      \"CN-SC\": \"Sichuan\",\n      \"CN-SD\": \"Shandong\",\n      \"CN-SH\": \"Shanghai\",\n      \"CN-SN\": \"Shanxi\",\n      \"CN-TJ\": \"Tianjin\",\n      \"CN-XJ\": \"Xinjiang Uygur\",\n      \"CN-XZ\": \"Tibet\",\n      \"CN-YN\": \"Yunnan\",\n      \"CN-ZJ\": \"Zhejiang\"\n    }\n  },\n  CO: {\n    type: \"province\",\n    options: {\n      \"CO-AMA\": \"Amazonas\",\n      \"CO-ANT\": \"Antioquia\",\n      \"CO-ARA\": \"Arauca\",\n      \"CO-ATL\": \"Atl\\xE1ntico\",\n      \"CO-BOL\": \"Bol\\xEDvar\",\n      \"CO-BOY\": \"Boyac\\xE1\",\n      \"CO-CAL\": \"Caldas\",\n      \"CO-CAQ\": \"Caquet\\xE1\",\n      \"CO-CAS\": \"Casanare\",\n      \"CO-CAU\": \"Cauca\",\n      \"CO-CES\": \"Cesar\",\n      \"CO-COR\": \"C\\xF3rdoba\",\n      \"CO-CUN\": \"Cundinamarca\",\n      \"CO-DC\": \"Bogot\\xE1 D.C.\",\n      \"CO-GUA\": \"Guain\\xEDa\",\n      \"CO-GUV\": \"Guaviare\",\n      \"CO-HUI\": \"Huila\",\n      \"CO-LAG\": \"La Guajira\",\n      \"CO-MAG\": \"Magdalena\",\n      \"CO-MET\": \"Meta\",\n      \"CO-NAR\": \"Nari\\xF1o\",\n      \"CO-NSA\": \"Norte de Santander\",\n      \"CO-PUT\": \"Putumayo\",\n      \"CO-QUI\": \"Quind\\xEDo\",\n      \"CO-RIS\": \"Risaralda\",\n      \"CO-SAP\": \"San Andr\\xE9s y Providencia\",\n      \"CO-SAN\": \"Santander\",\n      \"CO-SUC\": \"Sucre\",\n      \"CO-TOL\": \"Tolima\",\n      \"CO-VAC\": \"Valle del Cauca\",\n      \"CO-VAU\": \"Vaup\\xE9s\",\n      \"CO-VID\": \"Vichada\"\n    }\n  },\n  CR: {\n    type: \"province\",\n    options: {\n      \"CR-A\": \"Alajuela\",\n      \"CR-C\": \"Cartago\",\n      \"CR-G\": \"Guanacaste\",\n      \"CR-H\": \"Heredia\",\n      \"CR-L\": \"Lim\\xF3n\",\n      \"CR-P\": \"Puntarenas\",\n      \"CR-SJ\": \"San Jos\\xE9\"\n    }\n  },\n  CH: {\n    type: \"canton\",\n    options: {\n      \"CH-AG\": \"Aargau\",\n      \"CH-AR\": \"Appenzell Ausserrhoden\",\n      \"CH-AI\": \"Appenzell Innerrhoden\",\n      \"CH-BL\": \"Basel-Landschaft\",\n      \"CH-BS\": \"Basel-Stadt\",\n      \"CH-BE\": \"Bern\",\n      \"CH-FR\": \"Fribourg\",\n      \"CH-GE\": \"Geneva\",\n      \"CH-GL\": \"Glarus\",\n      \"CH-GR\": \"Graub\\xFCnden\",\n      \"CH-JU\": \"Jura\",\n      \"CH-LU\": \"Lucerne\",\n      \"CH-NE\": \"Neuch\\xE2tel\",\n      \"CH-NW\": \"Nidwalden\",\n      \"CH-OW\": \"Obwalden\",\n      \"CH-SG\": \"St. Gallen\",\n      \"CH-SH\": \"Schaffhausen\",\n      \"CH-SZ\": \"Schwyz\",\n      \"CH-SO\": \"Solothurn\",\n      \"CH-TG\": \"Thurgau\",\n      \"CH-TI\": \"Ticino\",\n      \"CH-UR\": \"Uri\",\n      \"CH-VS\": \"Valais\",\n      \"CH-VD\": \"Vaud\",\n      \"CH-ZG\": \"Zug\",\n      \"CH-ZH\": \"Zurich\"\n    }\n  },\n  EG: {\n    type: \"governorate\",\n    options: {\n      \"EG-ALX\": \"Alexandria\",\n      \"EG-ASN\": \"Aswan\",\n      \"EG-AST\": \"Asyut\",\n      \"EG-BA\": \"Red Sea\",\n      \"EG-BH\": \"Beheira\",\n      \"EG-BNS\": \"Beni Suef\",\n      \"EG-C\": \"Cairo\",\n      \"EG-DK\": \"Dakahlia\",\n      \"EG-DT\": \"Damietta\",\n      \"EG-FYM\": \"Faiyum\",\n      \"EG-GH\": \"Gharbia\",\n      \"EG-GZ\": \"Giza\",\n      \"EG-IS\": \"Ismailia\",\n      \"EG-JS\": \"South Sinai\",\n      \"EG-KB\": \"Qalyubia\",\n      \"EG-KFS\": \"Kafr el-Sheikh\",\n      \"EG-KN\": \"Qena\",\n      \"EG-LX\": \"Luxor\",\n      \"EG-MNF\": \"Monufia\",\n      \"EG-MT\": \"Matrouh\",\n      \"EG-PTS\": \"Port Said\",\n      \"EG-SHG\": \"Sohag\",\n      \"EG-SHR\": \"Al Sharqia\",\n      \"EG-SIN\": \"North Sinai\",\n      \"EG-SUZ\": \"Suez\"\n    }\n  },\n  GT: {\n    type: \"department\",\n    options: {\n      \"GT-AV\": \"Alta Verapaz\",\n      \"GT-BV\": \"Baja Verapaz\",\n      \"GT-CM\": \"Chimaltenango\",\n      \"GT-CQ\": \"Chiquimula\",\n      \"GT-PR\": \"El Progreso\",\n      \"GT-ES\": \"Escuintla\",\n      \"GT-GU\": \"Guatemala\",\n      \"GT-HU\": \"Huehuetenango\",\n      \"GT-IZ\": \"Izabal\",\n      \"GT-JA\": \"Jalapa\",\n      \"GT-JU\": \"Jutiapa\",\n      \"GT-PE\": \"Pet\\xE9n\",\n      \"GT-QZ\": \"Quetzaltenango\",\n      \"GT-QC\": \"Quich\\xE9\",\n      \"GT-RE\": \"Retalhuleu\",\n      \"GT-SA\": \"Sacatep\\xE9quez\",\n      \"GT-SM\": \"San Marcos\",\n      \"GT-SR\": \"Santa Rosa\",\n      \"GT-SO\": \"Solol\\xE1\",\n      \"GT-SU\": \"Suchitep\\xE9quez\",\n      \"GT-TO\": \"Totonicap\\xE1n\",\n      \"GT-ZA\": \"Zacapa\"\n    }\n  },\n  HK: {\n    type: \"region\",\n    options: {\n      \"HK-HKI\": \"Hong Kong Island\",\n      \"HK-KLN\": \"Kowloon\",\n      \"HK-NT\": \"New Territories\"\n    }\n  },\n  IN: {\n    type: \"state\",\n    options: {\n      \"IN-AN\": \"Andaman and Nicobar Islands\",\n      \"IN-AP\": \"Andhra Pradesh\",\n      \"IN-AR\": \"Arunachal Pradesh\",\n      \"IN-AS\": \"Assam\",\n      \"IN-BR\": \"Bihar\",\n      \"IN-CH\": \"Chandigarh\",\n      \"IN-CT\": \"Chhattisgarh\",\n      \"IN-DN\": \"Dadra and Nagar Haveli and Daman and Diu\",\n      \"IN-DL\": \"Delhi\",\n      \"IN-GA\": \"Goa\",\n      \"IN-GJ\": \"Gujarat\",\n      \"IN-HR\": \"Haryana\",\n      \"IN-HP\": \"Himachal Pradesh\",\n      \"IN-JH\": \"Jharkhand\",\n      \"IN-KA\": \"Karnataka\",\n      \"IN-KL\": \"Kerala\",\n      \"IN-LD\": \"Lakshadweep\",\n      \"IN-MP\": \"Madhya Pradesh\",\n      \"IN-MH\": \"Maharashtra\",\n      \"IN-MN\": \"Manipur\",\n      \"IN-ML\": \"Meghalaya\",\n      \"IN-MZ\": \"Mizoram\",\n      \"IN-NL\": \"Nagaland\",\n      \"IN-OR\": \"Odisha\",\n      \"IN-PY\": \"Puducherry\",\n      \"IN-PB\": \"Punjab\",\n      \"IN-RJ\": \"Rajasthan\",\n      \"IN-SK\": \"Sikkim\",\n      \"IN-TN\": \"Tamil Nadu\",\n      \"IN-TG\": \"Telangana\",\n      \"IN-TR\": \"Tripura\",\n      \"IN-UP\": \"Uttar Pradesh\",\n      \"IN-UT\": \"Uttarakhand\",\n      \"IN-WB\": \"West Bengal\"\n    }\n  },\n  IE: {\n    type: \"county\",\n    options: {\n      \"IE-CW\": \"Carlow\",\n      \"IE-CN\": \"Cavan\",\n      \"IE-CE\": \"Clare\",\n      \"IE-C\": \"Cork\",\n      \"IE-D\": \"Dublin\",\n      \"IE-DL\": \"Donegal\",\n      \"IE-G\": \"Galway\",\n      \"IE-KY\": \"Kerry\",\n      \"IE-KE\": \"Kildare\",\n      \"IE-KK\": \"Kilkenny\",\n      \"IE-LS\": \"Laois\",\n      \"IE-LM\": \"Leitrim\",\n      \"IE-LK\": \"Limerick\",\n      \"IE-LD\": \"Longford\",\n      \"IE-LH\": \"Louth\",\n      \"IE-MO\": \"Mayo\",\n      \"IE-MH\": \"Meath\",\n      \"IE-MN\": \"Monaghan\",\n      \"IE-OY\": \"Offaly\",\n      \"IE-RN\": \"Roscommon\",\n      \"IE-SO\": \"Sligo\",\n      \"IE-TA\": \"Tipperary\",\n      \"IE-WD\": \"Waterford\",\n      \"IE-WH\": \"Westmeath\",\n      \"IE-WX\": \"Wexford\",\n      \"IE-WW\": \"Wicklow\"\n    }\n  },\n  IT: {\n    type: \"province\",\n    options: {\n      \"IT-AG\": \"Agrigento\",\n      \"IT-AL\": \"Alessandria\",\n      \"IT-AN\": \"Ancona\",\n      \"IT-AO\": \"Aosta\",\n      \"IT-AR\": \"Arezzo\",\n      \"IT-AP\": \"Ascoli Piceno\",\n      \"IT-AT\": \"Asti\",\n      \"IT-AV\": \"Avellino\",\n      \"IT-BA\": \"Bari\",\n      \"IT-BT\": \"Barletta-Andria-Trani\",\n      \"IT-BL\": \"Belluno\",\n      \"IT-BN\": \"Benevento\",\n      \"IT-BG\": \"Bergamo\",\n      \"IT-BI\": \"Biella\",\n      \"IT-BO\": \"Bologna\",\n      \"IT-BZ\": \"Bolzano\",\n      \"IT-BS\": \"Brescia\",\n      \"IT-BR\": \"Brindisi\",\n      \"IT-CA\": \"Cagliari\",\n      \"IT-CL\": \"Caltanissetta\",\n      \"IT-CB\": \"Campobasso\",\n      \"IT-CI\": \"Carbonia-Iglesias\",\n      \"IT-CE\": \"Caserta\",\n      \"IT-CT\": \"Catania\",\n      \"IT-CZ\": \"Catanzaro\",\n      \"IT-CH\": \"Chieti\",\n      \"IT-CO\": \"Como\",\n      \"IT-CS\": \"Cosenza\",\n      \"IT-CR\": \"Cremona\",\n      \"IT-KR\": \"Crotone\",\n      \"IT-CN\": \"Cuneo\",\n      \"IT-EN\": \"Enna\",\n      \"IT-FM\": \"Fermo\",\n      \"IT-FE\": \"Ferrara\",\n      \"IT-FI\": \"Firenze\",\n      \"IT-FG\": \"Foggia\",\n      \"IT-FC\": \"Forl\\xEC-Cesena\",\n      \"IT-FR\": \"Frosinone\",\n      \"IT-GE\": \"Genova\",\n      \"IT-GO\": \"Gorizia\",\n      \"IT-GR\": \"Grosseto\",\n      \"IT-IM\": \"Imperia\",\n      \"IT-IS\": \"Isernia\",\n      \"IT-SP\": \"La Spezia\",\n      \"IT-AQ\": \"L'Aquila\",\n      \"IT-LT\": \"Latina\",\n      \"IT-LE\": \"Lecce\",\n      \"IT-LC\": \"Lecco\",\n      \"IT-LI\": \"Livorno\",\n      \"IT-LO\": \"Lodi\",\n      \"IT-LU\": \"Lucca\",\n      \"IT-MC\": \"Macerata\",\n      \"IT-MN\": \"Mantova\",\n      \"IT-MS\": \"Massa-Carrara\",\n      \"IT-MT\": \"Matera\",\n      \"IT-VS\": \"Medio Campidano\",\n      \"IT-ME\": \"Messina\",\n      \"IT-MI\": \"Milano\",\n      \"IT-MO\": \"Modena\",\n      \"IT-MB\": \"Monza e Brianza\",\n      \"IT-NA\": \"Napoli\",\n      \"IT-NO\": \"Novara\",\n      \"IT-NU\": \"Nuoro\",\n      \"IT-OG\": \"Ogliastra\",\n      \"IT-OT\": \"Olbia-Tempio\",\n      \"IT-OR\": \"Oristano\",\n      \"IT-PD\": \"Padova\",\n      \"IT-PA\": \"Palermo\",\n      \"IT-PR\": \"Parma\",\n      \"IT-PV\": \"Pavia\",\n      \"IT-PG\": \"Perugia\",\n      \"IT-PU\": \"Pesaro e Urbino\",\n      \"IT-PE\": \"Pescara\",\n      \"IT-PC\": \"Piacenza\",\n      \"IT-PI\": \"Pisa\",\n      \"IT-PT\": \"Pistoia\",\n      \"IT-PN\": \"Pordenone\",\n      \"IT-PZ\": \"Potenza\",\n      \"IT-PO\": \"Prato\",\n      \"IT-RG\": \"Ragusa\",\n      \"IT-RA\": \"Ravenna\",\n      \"IT-RC\": \"Reggio Calabria\",\n      \"IT-RE\": \"Reggio Emilia\",\n      \"IT-RI\": \"Rieti\",\n      \"IT-RN\": \"Rimini\",\n      \"IT-RM\": \"Roma\",\n      \"IT-RO\": \"Rovigo\",\n      \"IT-SA\": \"Salerno\",\n      \"IT-SS\": \"Sassari\",\n      \"IT-SV\": \"Savona\",\n      \"IT-SI\": \"Siena\",\n      \"IT-SR\": \"Siracusa\",\n      \"IT-SO\": \"Sondrio\",\n      \"IT-TA\": \"Taranto\",\n      \"IT-TE\": \"Teramo\",\n      \"IT-TR\": \"Terni\",\n      \"IT-TO\": \"Torino\",\n      \"IT-TP\": \"Trapani\",\n      \"IT-TN\": \"Trento\",\n      \"IT-TV\": \"Treviso\",\n      \"IT-TS\": \"Trieste\",\n      \"IT-UD\": \"Udine\",\n      \"IT-VA\": \"Varese\",\n      \"IT-VE\": \"Venezia\",\n      \"IT-VB\": \"Verbano-Cusio-Ossola\",\n      \"IT-VC\": \"Vercelli\",\n      \"IT-VR\": \"Verona\",\n      \"IT-VV\": \"Vibo Valentia\",\n      \"IT-VI\": \"Vicenza\",\n      \"IT-VT\": \"Viterbo\"\n    }\n  },\n  JP: {\n    type: \"prefecture\",\n    options: {\n      \"JP-01\": \"Hokkaido\",\n      \"JP-02\": \"Aomori\",\n      \"JP-03\": \"Iwate\",\n      \"JP-04\": \"Miyagi\",\n      \"JP-05\": \"Akita\",\n      \"JP-06\": \"Yamagata\",\n      \"JP-07\": \"Fukushima\",\n      \"JP-08\": \"Ibaraki\",\n      \"JP-09\": \"Tochigi\",\n      \"JP-10\": \"Gunma\",\n      \"JP-11\": \"Saitama\",\n      \"JP-12\": \"Chiba\",\n      \"JP-13\": \"Tokyo\",\n      \"JP-14\": \"Kanagawa\",\n      \"JP-15\": \"Niigata\",\n      \"JP-16\": \"Toyama\",\n      \"JP-17\": \"Ishikawa\",\n      \"JP-18\": \"Fukui\",\n      \"JP-19\": \"Yamanashi\",\n      \"JP-20\": \"Nagano\",\n      \"JP-21\": \"Gifu\",\n      \"JP-22\": \"Shizuoka\",\n      \"JP-23\": \"Aichi\",\n      \"JP-24\": \"Mie\",\n      \"JP-25\": \"Shiga\",\n      \"JP-26\": \"Kyoto\",\n      \"JP-27\": \"Osaka\",\n      \"JP-28\": \"Hyogo\",\n      \"JP-29\": \"Nara\",\n      \"JP-30\": \"Wakayama\",\n      \"JP-31\": \"Tottori\",\n      \"JP-32\": \"Shimane\",\n      \"JP-33\": \"Okayama\",\n      \"JP-34\": \"Hiroshima\",\n      \"JP-35\": \"Yamaguchi\",\n      \"JP-36\": \"Tokushima\",\n      \"JP-37\": \"Kagawa\",\n      \"JP-38\": \"Ehime\",\n      \"JP-39\": \"Kochi\",\n      \"JP-40\": \"Fukuoka\",\n      \"JP-41\": \"Saga\",\n      \"JP-42\": \"Nagasaki\",\n      \"JP-43\": \"Kumamoto\",\n      \"JP-44\": \"Oita\",\n      \"JP-45\": \"Miyazaki\",\n      \"JP-46\": \"Kagoshima\",\n      \"JP-47\": \"Okinawa\"\n    }\n  },\n  KW: {\n    type: \"governorate\",\n    options: {\n      \"KW-AH\": \"Al Ahmadi\",\n      \"KW-FA\": \"Al Farwaniyah\",\n      \"KW-HA\": \"Hawalli\",\n      \"KW-JA\": \"Al Jahra\",\n      \"KW-KU\": \"Capital\",\n      \"KW-MU\": \"Mubarak Al-Kabeer\"\n    }\n  },\n  MY: {\n    type: \"stateOrTerritory\",\n    options: {\n      \"MY-01\": \"Johor\",\n      \"MY-02\": \"Kedah\",\n      \"MY-03\": \"Kelantan\",\n      \"MY-04\": \"Melaka\",\n      \"MY-05\": \"Negeri Sembilan\",\n      \"MY-06\": \"Pahang\",\n      \"MY-08\": \"Perak\",\n      \"MY-09\": \"Perlis\",\n      \"MY-07\": \"Pulau Pinang\",\n      \"MY-12\": \"Sabah\",\n      \"MY-13\": \"Sarawak\",\n      \"MY-10\": \"Selangor\",\n      \"MY-11\": \"Terengganu\",\n      \"MY-14\": \"Wilayah Persekutuan Kuala Lumpur\",\n      \"MY-15\": \"Wilayah Persekutuan Labuan\",\n      \"MY-16\": \"Wilayah Persekutuan Putrajaya\"\n    }\n  },\n  MX: {\n    type: \"state\",\n    options: {\n      \"MX-AGU\": \"Aguascalientes\",\n      \"MX-BCN\": \"Baja California\",\n      \"MX-BCS\": \"Baja California Sur\",\n      \"MX-CAM\": \"Campeche\",\n      \"MX-CMX\": \"Ciudad de M\\xE9xico\",\n      \"MX-COA\": \"Coahuila de Zaragoza\",\n      \"MX-COL\": \"Colima\",\n      \"MX-CHP\": \"Chiapas\",\n      \"MX-CHH\": \"Chihuahua\",\n      \"MX-DUR\": \"Durango\",\n      \"MX-GUA\": \"Guanajuato\",\n      \"MX-GRO\": \"Guerrero\",\n      \"MX-HID\": \"Hidalgo\",\n      \"MX-JAL\": \"Jalisco\",\n      \"MX-MEX\": \"M\\xE9xico\",\n      \"MX-MIC\": \"Michoac\\xE1n de Ocampo\",\n      \"MX-MOR\": \"Morelos\",\n      \"MX-NAY\": \"Nayarit\",\n      \"MX-NLE\": \"Nuevo Le\\xF3n\",\n      \"MX-OAX\": \"Oaxaca\",\n      \"MX-PUE\": \"Puebla\",\n      \"MX-QUE\": \"Quer\\xE9taro\",\n      \"MX-ROO\": \"Quintana Roo\",\n      \"MX-SLP\": \"San Luis Potos\\xED\",\n      \"MX-SIN\": \"Sinaloa\",\n      \"MX-SON\": \"Sonora\",\n      \"MX-TAB\": \"Tabasco\",\n      \"MX-TAM\": \"Tamaulipas\",\n      \"MX-TLA\": \"Tlaxcala\",\n      \"MX-VER\": \"Veracruz de Ignacio de la Llave\",\n      \"MX-YUC\": \"Yucat\\xE1n\",\n      \"MX-ZAC\": \"Zacatecas\"\n    }\n  },\n  NZ: {\n    type: \"region\",\n    options: {\n      \"NZ-AUK\": \"Auckland\",\n      \"NZ-BOP\": \"Bay of Plenty\",\n      \"NZ-CAN\": \"Canterbury\",\n      \"NZ-CIT\": \"Chatham Islands Territory\",\n      \"NZ-GIS\": \"Gisborne\",\n      \"NZ-WGN\": \"Greater Wellington\",\n      \"NZ-HKB\": \"Hawke's Bay\",\n      \"NZ-MWT\": \"Manawat\\u016B-Whanganui\",\n      \"NZ-MBH\": \"Marlborough\",\n      \"NZ-NSN\": \"Nelson\",\n      \"NZ-NTL\": \"Northland\",\n      \"NZ-OTA\": \"Otago\",\n      \"NZ-STL\": \"Southland\",\n      \"NZ-TKI\": \"Taranaki\",\n      \"NZ-TAS\": \"Tasman\",\n      \"NZ-WKO\": \"Waikato\",\n      \"NZ-WTC\": \"West Coast\"\n    }\n  },\n  NG: {\n    type: \"state\",\n    options: {\n      \"NG-AB\": \"Abia\",\n      \"NG-AD\": \"Adamawa\",\n      \"NG-AK\": \"Akwa Ibom\",\n      \"NG-AN\": \"Anambra\",\n      \"NG-BA\": \"Bauchi\",\n      \"NG-BY\": \"Bayelsa\",\n      \"NG-BE\": \"Benue\",\n      \"NG-BO\": \"Borno\",\n      \"NG-CR\": \"Cross River\",\n      \"NG-DE\": \"Delta\",\n      \"NG-EB\": \"Ebonyi\",\n      \"NG-ED\": \"Edo\",\n      \"NG-EK\": \"Ekiti\",\n      \"NG-EN\": \"Enugu\",\n      \"NG-FC\": \"Federal Capital Territory\",\n      \"NG-GO\": \"Gombe\",\n      \"NG-IM\": \"Imo\",\n      \"NG-JI\": \"Jigawa\",\n      \"NG-KD\": \"Kaduna\",\n      \"NG-KN\": \"Kano\",\n      \"NG-KT\": \"Katsina\",\n      \"NG-KE\": \"Kebbi\",\n      \"NG-KO\": \"Kogi\",\n      \"NG-KW\": \"Kwara\",\n      \"NG-LA\": \"Lagos\",\n      \"NG-NA\": \"Nasarawa\",\n      \"NG-NI\": \"Niger\",\n      \"NG-OG\": \"Ogun\",\n      \"NG-ON\": \"Ondo\",\n      \"NG-OS\": \"Osun\",\n      \"NG-OY\": \"Oyo\",\n      \"NG-PL\": \"Plateau\",\n      \"NG-RI\": \"Rivers\",\n      \"NG-SO\": \"Sokoto\",\n      \"NG-TA\": \"Taraba\",\n      \"NG-YO\": \"Yobe\",\n      \"NG-ZA\": \"Zamfara\"\n    }\n  },\n  PA: {\n    type: \"province\",\n    options: {\n      \"PA-1\": \"Bocas del Toro\",\n      \"PA-4\": \"Chiriqu\\xED\",\n      \"PA-2\": \"Cocl\\xE9\",\n      \"PA-3\": \"Col\\xF3n\",\n      \"PA-5\": \"Dari\\xE9n\",\n      \"PA-6\": \"Herrera\",\n      \"PA-7\": \"Los Santos\",\n      \"PA-8\": \"Panam\\xE1\",\n      \"PA-9\": \"Panam\\xE1 Oeste\",\n      \"PA-10\": \"Veraguas\",\n      \"PA-EM\": \"Ember\\xE1\",\n      \"PA-KY\": \"Guna Yala\",\n      \"PA-NB\": \"Ng\\xE4be-Bugl\\xE9\"\n    }\n  },\n  PE: {\n    type: \"province\",\n    options: {\n      \"PE-AMA\": \"Amazonas\",\n      \"PE-ANC\": \"Ancash\",\n      \"PE-APU\": \"Apur\\xEDmac\",\n      \"PE-ARE\": \"Arequipa\",\n      \"PE-AYA\": \"Ayacucho\",\n      \"PE-CAJ\": \"Cajamarca\",\n      \"PE-CAL\": \"El Callao\",\n      \"PE-CUS\": \"Cusco\",\n      \"PE-HUV\": \"Huancavelica\",\n      \"PE-HUC\": \"Hu\\xE1nuco\",\n      \"PE-ICA\": \"Ica\",\n      \"PE-JUN\": \"Jun\\xEDn\",\n      \"PE-LAL\": \"La Libertad\",\n      \"PE-LAM\": \"Lambayeque\",\n      \"PE-LIMD\": \"Lima (Department)\",\n      \"PE-LIM\": \"Lima (Metropolitan)\",\n      \"PE-LOR\": \"Loreto\",\n      \"PE-MDD\": \"Madre de Dios\",\n      \"PE-MOQ\": \"Moquegua\",\n      \"PE-PAS\": \"Pasco\",\n      \"PE-PIU\": \"Piura\",\n      \"PE-PUN\": \"Puno\",\n      \"PE-SAM\": \"San Mart\\xEDn\",\n      \"PE-TAC\": \"Tacna\",\n      \"PE-TUM\": \"Tumbes\",\n      \"PE-UCA\": \"Ucayali\"\n    }\n  },\n  PH: {\n    type: \"region\",\n    options: {\n      \"PH-ABR\": \"Abra\",\n      \"PH-AGN\": \"Agusan del Norte\",\n      \"PH-AGS\": \"Agusan del Sur\",\n      \"PH-AKL\": \"Aklan\",\n      \"PH-ALB\": \"Albay\",\n      \"PH-ANT\": \"Antique\",\n      \"PH-APA\": \"Apayao\",\n      \"PH-AUR\": \"Aurora\",\n      \"PH-BAS\": \"Basilan\",\n      \"PH-BAN\": \"Bataan\",\n      \"PH-BTG\": \"Batangas\",\n      \"PH-BEN\": \"Benguet\",\n      \"PH-BOH\": \"Bohol\",\n      \"PH-BUK\": \"Bukidnon\",\n      \"PH-BUL\": \"Bulacan\",\n      \"PH-CAG\": \"Cagayan\",\n      \"PH-CAM\": \"Camiguin\",\n      \"PH-CAN\": \"Camarines Norte\",\n      \"PH-CAS\": \"Camarines Sur\",\n      \"PH-CAP\": \"Capiz\",\n      \"PH-CAT\": \"Catanduanes\",\n      \"PH-CAV\": \"Cavite\",\n      \"PH-CEB\": \"Cebu\",\n      \"PH-COM\": \"Compostela Valley\",\n      \"PH-COT\": \"Cotabato\",\n      \"PH-DAV\": \"Davao del Norte\",\n      \"PH-DAS\": \"Davao del Sur\",\n      \"PH-DAO\": \"Davao Oriental\",\n      \"PH-EAS\": \"Eastern Samar\",\n      \"PH-GUI\": \"Guimaras\",\n      \"PH-IFU\": \"Ifugao\",\n      \"PH-ILN\": \"Ilocos Norte\",\n      \"PH-ILS\": \"Ilocos Sur\",\n      \"PH-ILI\": \"Iloilo\",\n      \"PH-ISA\": \"Isabela\",\n      \"PH-KAL\": \"Kalinga\",\n      \"PH-LUN\": \"La Union\",\n      \"PH-LAG\": \"Laguna\",\n      \"PH-LAN\": \"Lanao del Norte\",\n      \"PH-LAS\": \"Lanao del Sur\",\n      \"PH-LEY\": \"Leyte\",\n      \"PH-MAG\": \"Maguindanao\",\n      \"PH-MAD\": \"Marinduque\",\n      \"PH-MAS\": \"Masbate\",\n      \"PH-MDC\": \"Misamis Occidental\",\n      \"PH-MDR\": \"Misamis Oriental\",\n      \"PH-MOU\": \"Mountain Province\",\n      \"PH-NEC\": \"Negros Occidental\",\n      \"PH-NER\": \"Negros Oriental\",\n      \"PH-NCO\": \"North Cotabato\",\n      \"PH-NSA\": \"Northern Samar\",\n      \"PH-NUE\": \"Nueva Ecija\",\n      \"PH-NUV\": \"Nueva Vizcaya\",\n      \"PH-PLW\": \"Palawan\",\n      \"PH-PAM\": \"Pampanga\",\n      \"PH-PAN\": \"Pangasinan\",\n      \"PH-QUE\": \"Quezon\",\n      \"PH-QUI\": \"Quirino\",\n      \"PH-RIZ\": \"Rizal\",\n      \"PH-ROM\": \"Romblon\",\n      \"PH-WSA\": \"Samar\",\n      \"PH-SAR\": \"Sarangani\",\n      \"PH-SIG\": \"Siquijor\",\n      \"PH-SOR\": \"Sorsogon\",\n      \"PH-SCO\": \"South Cotabato\",\n      \"PH-SLE\": \"Southern Leyte\",\n      \"PH-SUK\": \"Sultan Kudarat\",\n      \"PH-SLU\": \"Sulu\",\n      \"PH-SUN\": \"Surigao del Norte\",\n      \"PH-SUR\": \"Surigao del Sur\",\n      \"PH-TAR\": \"Tarlac\",\n      \"PH-TAW\": \"Tawi-Tawi\",\n      \"PH-ZMB\": \"Zambales\",\n      \"PH-ZAN\": \"Zamboanga del Norte\",\n      \"PH-ZAS\": \"Zamboanga del Sur\",\n      \"PH-ZSI\": \"Zamboanga Sibugay\",\n      \"PH-00\": \"Metro Manila\"\n    }\n  },\n  PT: {\n    type: \"district\",\n    options: {\n      \"PT-01\": \"Aveiro\",\n      \"PT-02\": \"Beja\",\n      \"PT-03\": \"Braga\",\n      \"PT-04\": \"Bragan\\xE7a\",\n      \"PT-05\": \"Castelo Branco\",\n      \"PT-06\": \"Coimbra\",\n      \"PT-07\": \"\\xC9vora\",\n      \"PT-08\": \"Faro\",\n      \"PT-09\": \"Guarda\",\n      \"PT-10\": \"Leiria\",\n      \"PT-11\": \"Lisboa\",\n      \"PT-12\": \"Portalegre\",\n      \"PT-13\": \"Porto\",\n      \"PT-14\": \"Santar\\xE9m\",\n      \"PT-15\": \"Set\\xFAbal\",\n      \"PT-16\": \"Viana do Castelo\",\n      \"PT-17\": \"Vila Real\",\n      \"PT-18\": \"Viseu\",\n      \"PT-20\": \"Azores\",\n      \"PT-30\": \"Madeira\"\n    }\n  },\n  RO: {\n    type: \"county\",\n    options: {\n      \"RO-AB\": \"Alba\",\n      \"RO-AR\": \"Arad\",\n      \"RO-AG\": \"Arge\\u0219\",\n      \"RO-BC\": \"Bac\\u0103u\",\n      \"RO-BH\": \"Bihor\",\n      \"RO-BN\": \"Bistri\\u021Ba-N\\u0103s\\u0103ud\",\n      \"RO-BT\": \"Boto\\u0219ani\",\n      \"RO-BV\": \"Bra\\u0219ov\",\n      \"RO-BR\": \"Br\\u0103ila\",\n      \"RO-BZ\": \"Buz\\u0103u\",\n      \"RO-CS\": \"Cara\\u0219-Severin\",\n      \"RO-CL\": \"C\\u0103l\\u0103ra\\u0219i\",\n      \"RO-CJ\": \"Cluj\",\n      \"RO-CT\": \"Constan\\u021Ba\",\n      \"RO-CV\": \"Covasna\",\n      \"RO-DB\": \"D\\xE2mbovi\\u021Ba\",\n      \"RO-DJ\": \"Dolj\",\n      \"RO-GL\": \"Gala\\u021Bi\",\n      \"RO-GR\": \"Giurgiu\",\n      \"RO-GJ\": \"Gorj\",\n      \"RO-HR\": \"Harghita\",\n      \"RO-HD\": \"Hunedoara\",\n      \"RO-IL\": \"Ialomi\\u021Ba\",\n      \"RO-IS\": \"Ia\\u0219i\",\n      \"RO-MM\": \"Maramure\\u0219\",\n      \"RO-MH\": \"Mehedin\\u021Bi\",\n      \"RO-MS\": \"Mure\\u0219\",\n      \"RO-NT\": \"Neam\\u021B\",\n      \"RO-OT\": \"Olt\",\n      \"RO-PH\": \"Prahova\",\n      \"RO-SJ\": \"S\\u0103laj\",\n      \"RO-SM\": \"Satu Mare\",\n      \"RO-SB\": \"Sibiu\",\n      \"RO-SV\": \"Suceava\",\n      \"RO-TR\": \"Teleorman\",\n      \"RO-TM\": \"Timi\\u0219\",\n      \"RO-TL\": \"Tulcea\",\n      \"RO-VS\": \"Vaslui\",\n      \"RO-VL\": \"V\\xE2lcea\",\n      \"RO-VN\": \"Vrancea\"\n    }\n  },\n  RU: {\n    type: \"region\",\n    options: {\n      \"RU-AD\": \"Adygea\",\n      \"RU-AL\": \"Altai\",\n      \"RU-ALT\": \"Altai Krai\",\n      \"RU-AMU\": \"Amur\",\n      \"RU-ARK\": \"Arkhangelsk\",\n      \"RU-AST\": \"Astrakhan\",\n      \"RU-BA\": \"Bashkortostan\",\n      \"RU-BEL\": \"Belgorod\",\n      \"RU-BRY\": \"Bryansk\",\n      \"RU-BU\": \"Buryat\",\n      \"RU-CE\": \"Chechen\",\n      \"RU-CHE\": \"Chelyabinsk\",\n      \"RU-CHU\": \"Chukotka Okrug\",\n      \"RU-CU\": \"Chuvash\",\n      \"RU-DA\": \"Dagestan\",\n      \"RU-IRK\": \"Irkutsk\",\n      \"RU-IVA\": \"Ivanovo\",\n      \"RU-YEV\": \"Jewish\",\n      \"RU-KB\": \"Kabardino-Balkar\",\n      \"RU-KGD\": \"Kaliningrad\",\n      \"RU-KLU\": \"Kaluga\",\n      \"RU-KAM\": \"Kamchatka Krai\",\n      \"RU-KC\": \"Karachay-Cherkess\",\n      \"RU-KEM\": \"Kemerovo\",\n      \"RU-KHA\": \"Khabarovsk Krai\",\n      \"RU-KHM\": \"Khanty-Mansi\",\n      \"RU-KIR\": \"Kirov\",\n      \"RU-KO\": \"Komi\",\n      \"RU-KOS\": \"Kostroma\",\n      \"RU-KDA\": \"Krasnodar Krai\",\n      \"RU-KYA\": \"Krasnoyarsk Krai\",\n      \"RU-KGN\": \"Kurgan\",\n      \"RU-KRS\": \"Kursk\",\n      \"RU-LEN\": \"Leningrad\",\n      \"RU-LIP\": \"Lipetsk\",\n      \"RU-MAG\": \"Magadan\",\n      \"RU-ME\": \"Mari El\",\n      \"RU-MOW\": \"Moscow\",\n      \"RU-MOS\": \"Moscow Province\",\n      \"RU-MUR\": \"Murmansk\",\n      \"RU-NIZ\": \"Nizhny Novgorod\",\n      \"RU-NGR\": \"Novgorod\",\n      \"RU-NVS\": \"Novosibirsk\",\n      \"RU-OMS\": \"Omsk\",\n      \"RU-ORE\": \"Orenburg\",\n      \"RU-ORL\": \"Oryol\",\n      \"RU-PNZ\": \"Penza\",\n      \"RU-PER\": \"Perm Krai\",\n      \"RU-PRI\": \"Primorsky Krai\",\n      \"RU-PSK\": \"Pskov\",\n      \"RU-SA\": \"Sakha\",\n      \"RU-SAK\": \"Sakhalin\",\n      \"RU-SAM\": \"Samara\",\n      \"RU-SAR\": \"Saratov\",\n      \"RU-SMO\": \"Smolensk\",\n      \"RU-SE\": \"North Ossetia-Alania\",\n      \"RU-STA\": \"Stavropol Krai\",\n      \"RU-SVE\": \"Sverdlovsk\",\n      \"RU-TAM\": \"Tambov\",\n      \"RU-TOM\": \"Tomsk\",\n      \"RU-TUL\": \"Tula\",\n      \"RU-TVE\": \"Tver\",\n      \"RU-TYU\": \"Tyumen\",\n      \"RU-TY\": \"Tuva\",\n      \"RU-UD\": \"Udmurt\",\n      \"RU-ULY\": \"Ulyanovsk\",\n      \"RU-VLA\": \"Vladimir\",\n      \"RU-VGG\": \"Volgograd\",\n      \"RU-VLG\": \"Vologda\",\n      \"RU-VOR\": \"Voronezh\",\n      \"RU-YAN\": \"Yamalo-Nenets Okrug\",\n      \"RU-YAR\": \"Yaroslavl\",\n      \"RU-ZAB\": \"Zabaykalsky Krai\"\n    }\n  },\n  ZA: {\n    type: \"province\",\n    options: {\n      \"ZA-EC\": \"Eastern Cape\",\n      \"ZA-FS\": \"Free State\",\n      \"ZA-GT\": \"Gauteng\",\n      \"ZA-NL\": \"KwaZulu-Natal\",\n      \"ZA-LP\": \"Limpopo\",\n      \"ZA-MP\": \"Mpumalanga\",\n      \"ZA-NC\": \"Northern Cape\",\n      \"ZA-NW\": \"North West\",\n      \"ZA-WC\": \"Western Cape\"\n    }\n  },\n  KR: {\n    type: \"province\",\n    options: {\n      \"KR-11\": \"Seoul\",\n      \"KR-26\": \"Busan\",\n      \"KR-27\": \"Daegu\",\n      \"KR-28\": \"Incheon\",\n      \"KR-29\": \"Gwangju\",\n      \"KR-30\": \"Daejeon\",\n      \"KR-31\": \"Ulsan\",\n      \"KR-41\": \"Gyeonggi\",\n      \"KR-42\": \"Gangwon\",\n      \"KR-43\": \"North Chungcheong\",\n      \"KR-44\": \"South Chungcheong\",\n      \"KR-45\": \"North Jeolla\",\n      \"KR-46\": \"South Jeolla\",\n      \"KR-47\": \"North Gyeongsang\",\n      \"KR-48\": \"South Gyeongsang\",\n      \"KR-49\": \"Jeju\"\n    }\n  },\n  ES: {\n    type: \"province\",\n    options: {\n      \"ES-C\": \"A Coru\\xF1a\",\n      \"ES-VI\": \"\\xC1lava\",\n      \"ES-AB\": \"Albacete\",\n      \"ES-A\": \"Alicante\",\n      \"ES-AL\": \"Almer\\xEDa\",\n      \"ES-O\": \"Asturias\",\n      \"ES-AV\": \"\\xC1vila\",\n      \"ES-BA\": \"Badajoz\",\n      \"ES-B\": \"Barcelona\",\n      \"ES-BI\": \"Bizkaia\",\n      \"ES-BU\": \"Burgos\",\n      \"ES-CC\": \"C\\xE1ceres\",\n      \"ES-CA\": \"C\\xE1diz\",\n      \"ES-S\": \"Cantabria\",\n      \"ES-CS\": \"Castell\\xF3n\",\n      \"ES-CE\": \"Ceuta\",\n      \"ES-CR\": \"Ciudad Real\",\n      \"ES-CO\": \"C\\xF3rdoba\",\n      \"ES-CU\": \"Cuenca\",\n      \"ES-SS\": \"Gipuzkoa\",\n      \"ES-GI\": \"Girona\",\n      \"ES-GR\": \"Granada\",\n      \"ES-GU\": \"Guadalajara\",\n      \"ES-H\": \"Huelva\",\n      \"ES-HU\": \"Huesca\",\n      \"ES-PM\": \"Illes Balears\",\n      \"ES-J\": \"Ja\\xE9n\",\n      \"ES-LO\": \"La Rioja\",\n      \"ES-GC\": \"Las Palmas\",\n      \"ES-LE\": \"Le\\xF3n\",\n      \"ES-L\": \"Lleida\",\n      \"ES-LU\": \"Lugo\",\n      \"ES-M\": \"Madrid\",\n      \"ES-MA\": \"M\\xE1laga\",\n      \"ES-ML\": \"Melilla\",\n      \"ES-MU\": \"Murcia\",\n      \"ES-NA\": \"Navarra\",\n      \"ES-OR\": \"Ourense\",\n      \"ES-P\": \"Palencia\",\n      \"ES-PO\": \"Pontevedra\",\n      \"ES-SA\": \"Salamanca\",\n      \"ES-TF\": \"Santa Cruz de Tenerife\",\n      \"ES-SG\": \"Segovia\",\n      \"ES-SE\": \"Sevilla\",\n      \"ES-SO\": \"Soria\",\n      \"ES-T\": \"Tarragona\",\n      \"ES-TE\": \"Teruel\",\n      \"ES-TO\": \"Toledo\",\n      \"ES-V\": \"Valencia\",\n      \"ES-VA\": \"Valladolid\",\n      \"ES-ZA\": \"Zamora\",\n      \"ES-Z\": \"Zaragoza\"\n    }\n  },\n  TH: {\n    type: \"province\",\n    options: {\n      \"TH-10\": \"Bangkok\",\n      \"TH-11\": \"Samut Prakan\",\n      \"TH-12\": \"Nonthaburi\",\n      \"TH-13\": \"Pathum Thani\",\n      \"TH-14\": \"Phra Nakhon Si Ayutthaya\",\n      \"TH-15\": \"Ang Thong\",\n      \"TH-16\": \"Lop Buri\",\n      \"TH-17\": \"Sing Buri\",\n      \"TH-18\": \"Chai Nat\",\n      \"TH-19\": \"Saraburi\",\n      \"TH-20\": \"Chon Buri\",\n      \"TH-21\": \"Rayong\",\n      \"TH-22\": \"Chanthaburi\",\n      \"TH-23\": \"Trat\",\n      \"TH-24\": \"Chachoengsao\",\n      \"TH-25\": \"Prachin Buri\",\n      \"TH-26\": \"Nakhon Nayok\",\n      \"TH-27\": \"Sa Kaeo\",\n      \"TH-30\": \"Nakhon Ratchasima\",\n      \"TH-31\": \"Buri Ram\",\n      \"TH-32\": \"Surin\",\n      \"TH-33\": \"Si Sa Ket\",\n      \"TH-34\": \"Ubon Ratchathani\",\n      \"TH-35\": \"Yasothon\",\n      \"TH-36\": \"Chaiyaphum\",\n      \"TH-37\": \"Amnat Charoen\",\n      \"TH-38\": \"Bueng Kan\",\n      \"TH-39\": \"Nong Bua Lam Phu\",\n      \"TH-40\": \"Khon Kaen\",\n      \"TH-41\": \"Udon Thani\",\n      \"TH-42\": \"Loei\",\n      \"TH-43\": \"Nong Khai\",\n      \"TH-44\": \"Maha Sarakham\",\n      \"TH-45\": \"Roi Et\",\n      \"TH-46\": \"Kalasin\",\n      \"TH-47\": \"Sakon Nakhon\",\n      \"TH-48\": \"Nakhon Phanom\",\n      \"TH-49\": \"Mukdahan\",\n      \"TH-50\": \"Chiang Mai\",\n      \"TH-51\": \"Lamphun\",\n      \"TH-52\": \"Lampang\",\n      \"TH-53\": \"Uttaradit\",\n      \"TH-54\": \"Phrae\",\n      \"TH-55\": \"Nan\",\n      \"TH-56\": \"Phayao\",\n      \"TH-57\": \"Chiang Rai\",\n      \"TH-58\": \"Mae Hong Son\",\n      \"TH-60\": \"Nakhon Sawan\",\n      \"TH-61\": \"Uthai Thani\",\n      \"TH-62\": \"Kamphaeng Phet\",\n      \"TH-63\": \"Tak\",\n      \"TH-64\": \"Sukhothai\",\n      \"TH-65\": \"Phitsanulok\",\n      \"TH-66\": \"Phichit\",\n      \"TH-67\": \"Phetchabun\",\n      \"TH-70\": \"Ratchaburi\",\n      \"TH-71\": \"Kanchanaburi\",\n      \"TH-72\": \"Suphan Buri\",\n      \"TH-73\": \"Nakhon Pathom\",\n      \"TH-74\": \"Samut Sakhon\",\n      \"TH-75\": \"Samut Songkhram\",\n      \"TH-76\": \"Phetchaburi\",\n      \"TH-77\": \"Prachuap Khiri Khan\",\n      \"TH-80\": \"Nakhon Si Thammarat\",\n      \"TH-81\": \"Krabi\",\n      \"TH-82\": \"Phang Nga\",\n      \"TH-83\": \"Phuket\",\n      \"TH-84\": \"Surat Thani\",\n      \"TH-85\": \"Ranong\",\n      \"TH-86\": \"Chumphon\",\n      \"TH-90\": \"Songkhla\",\n      \"TH-91\": \"Satun\",\n      \"TH-92\": \"Trang\",\n      \"TH-93\": \"Phatthalung\",\n      \"TH-94\": \"Pattani\",\n      \"TH-95\": \"Yala\",\n      \"TH-96\": \"Narathiwat\"\n    }\n  },\n  AE: {\n    type: \"emirate\",\n    options: {\n      \"AE-AJ\": \"Ajman\",\n      \"AE-AZ\": \"Abu Dhabi\",\n      \"AE-DU\": \"Dubai\",\n      \"AE-FU\": \"Fujairah\",\n      \"AE-RK\": \"Ras al-Khaimah\",\n      \"AE-SH\": \"Sharjah\",\n      \"AE-UQ\": \"Umm al-Quwain\"\n    }\n  },\n  UY: {\n    type: \"department\",\n    options: {\n      \"UY-AR\": \"Artigas\",\n      \"UY-CA\": \"Canelones\",\n      \"UY-CL\": \"Cerro Largo\",\n      \"UY-CO\": \"Colonia\",\n      \"UY-DU\": \"Durazno\",\n      \"UY-FD\": \"Flores\",\n      \"UY-FS\": \"Florida\",\n      \"UY-LA\": \"Lavalleja\",\n      \"UY-MA\": \"Maldonado\",\n      \"UY-MO\": \"Montevideo\",\n      \"UY-PA\": \"Paysand\\xFA\",\n      \"UY-RN\": \"R\\xEDo Negro\",\n      \"UY-RV\": \"Rivera\",\n      \"UY-RO\": \"Rocha\",\n      \"UY-SA\": \"Salto\",\n      \"UY-SJ\": \"San Jos\\xE9\",\n      \"UY-SO\": \"Soriano\",\n      \"UY-TA\": \"Tacuaremb\\xF3\",\n      \"UY-TT\": \"Treinta y Tres\"\n    }\n  },\n  US: {\n    type: \"state\",\n    options: {\n      \"US-AL\": \"Alabama\",\n      \"US-AK\": \"Alaska\",\n      \"US-AZ\": \"Arizona\",\n      \"US-AR\": \"Arkansas\",\n      \"US-CA\": \"California\",\n      \"US-CO\": \"Colorado\",\n      \"US-CT\": \"Connecticut\",\n      \"US-FL\": \"Florida\",\n      \"US-GA\": \"Georgia\",\n      \"US-HI\": \"Hawaii\",\n      \"US-ID\": \"Idaho\",\n      \"US-IL\": \"Illinois\",\n      \"US-IN\": \"Indiana\",\n      \"US-IA\": \"Iowa\",\n      \"US-KS\": \"Kansas\",\n      \"US-KY\": \"Kentucky\",\n      \"US-LA\": \"Louisiana\",\n      \"US-ME\": \"Maine\",\n      \"US-MD\": \"Maryland\",\n      \"US-MA\": \"Massachusetts\",\n      \"US-MI\": \"Michigan\",\n      \"US-MN\": \"Minnesota\",\n      \"US-MS\": \"Mississippi\",\n      \"US-MO\": \"Missouri\",\n      \"US-NE\": \"Nebraska\",\n      \"US-NV\": \"Nevada\",\n      \"US-NJ\": \"New Jersey\",\n      \"US-NM\": \"New Mexico\",\n      \"US-NY\": \"New York\",\n      \"US-NC\": \"North Carolina\",\n      \"US-ND\": \"North Dakota\",\n      \"US-OH\": \"Ohio\",\n      \"US-OK\": \"Oklahoma\",\n      \"US-PA\": \"Pennsylvania\",\n      \"US-PR\": \"Puerto Rico\",\n      \"US-RI\": \"Rhode Island\",\n      \"US-SC\": \"South Carolina\",\n      \"US-SD\": \"South Dakota\",\n      \"US-TN\": \"Tennessee\",\n      \"US-TX\": \"Texas\",\n      \"US-UT\": \"Utah\",\n      \"US-VT\": \"Vermont\",\n      \"US-VA\": \"Virginia\",\n      \"US-WA\": \"Washington\",\n      \"US-DC\": \"Washington DC\",\n      \"US-WV\": \"West Virginia\",\n      \"US-WI\": \"Wisconsin\",\n      \"US-WY\": \"Wyoming\"\n    }\n  },\n  VE: {\n    type: \"state\",\n    options: {\n      \"VE-A\": \"Distrito Capital\",\n      \"VE-B\": \"Anzo\\xE1tegui\",\n      \"VE-C\": \"Apure\",\n      \"VE-D\": \"Aragua\",\n      \"VE-E\": \"Barinas\",\n      \"VE-F\": \"Bol\\xEDvar\",\n      \"VE-G\": \"Carabobo\",\n      \"VE-H\": \"Cojedes\",\n      \"VE-I\": \"Falc\\xF3n\",\n      \"VE-J\": \"Gu\\xE1rico\",\n      \"VE-K\": \"Lara\",\n      \"VE-L\": \"M\\xE9rida\",\n      \"VE-M\": \"Miranda\",\n      \"VE-N\": \"Monagas\",\n      \"VE-O\": \"Nueva Esparta\",\n      \"VE-P\": \"Portuguesa\",\n      \"VE-R\": \"Sucre\",\n      \"VE-S\": \"T\\xE1chira\",\n      \"VE-T\": \"Trujillo\",\n      \"VE-U\": \"La Guaira\",\n      \"VE-V\": \"Yaracuy\",\n      \"VE-W\": \"Zulia\",\n      \"VE-X\": \"Dependencias Federales\",\n      \"VE-Y\": \"Vargas\"\n    }\n  }\n};\n\nexport {\n  getCountryProvinceObjectByIso2,\n  getProvinceByIso2,\n  isProvinceInCountry\n};\n"], "mappings": ";AACA,SAAS,+BAA+B,MAAM;AAC5C,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,YAAY;AAC9B,SAAO,mBAAmB,IAAI,KAAK;AACrC;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAM,MAAM,KAAK,YAAY;AAC7B,aAAW,WAAW,oBAAoB;AACxC,QAAI,mBAAmB,OAAO,EAAE,QAAQ,GAAG,GAAG;AAC5C,aAAO,mBAAmB,OAAO,EAAE,QAAQ,GAAG;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,aAAa,cAAc;AACtD,MAAI,CAAC,eAAe,CAAC,cAAc;AACjC,WAAO;AAAA,EACT;AACA,QAAM,OAAO,aAAa,YAAY;AACtC,QAAM,UAAU,mBAAmB,YAAY,YAAY,CAAC;AAC5D,UAAO,mCAAS,QAAQ,WAAU;AACpC;AACA,IAAI,qBAAqB;AAAA,EACvB,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AACF;", "names": []}