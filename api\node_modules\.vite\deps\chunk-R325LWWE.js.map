{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-44QN6VEG.mjs"], "sourcesContent": ["import {\n  useDataTableDateColumns\n} from \"./chunk-4BTG27L5.mjs\";\nimport {\n  useDataTableDateFilters\n} from \"./chunk-3IIOXMXN.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/components/data-table/helpers/sales-channels/use-sales-channel-table-columns.tsx\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { createDataTableColumnHelper, Toolt<PERSON> } from \"@medusajs/ui\";\n\n// src/components/data-table/components/data-table-status-cell/data-table-status-cell.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar DataTableStatusCell = ({\n  color,\n  children\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"txt-compact-small text-ui-fg-subtle flex h-full w-full items-center gap-x-2 overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        role: \"presentation\",\n        className: \"flex h-5 w-2 items-center justify-center\",\n        children: /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: clx(\n              \"h-2 w-2 rounded-sm shadow-[0px_0px_0px_1px_rgba(0,0,0,0.12)_inset]\",\n              {\n                \"bg-ui-tag-neutral-icon\": color === \"grey\",\n                \"bg-ui-tag-green-icon\": color === \"green\",\n                \"bg-ui-tag-red-icon\": color === \"red\",\n                \"bg-ui-tag-blue-icon\": color === \"blue\",\n                \"bg-ui-tag-orange-icon\": color === \"orange\",\n                \"bg-ui-tag-purple-icon\": color === \"purple\"\n              }\n            )\n          }\n        )\n      }\n    ),\n    /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children })\n  ] });\n};\n\n// src/components/data-table/helpers/sales-channels/use-sales-channel-table-columns.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar columnHelper = createDataTableColumnHelper();\nvar useSalesChannelTableColumns = () => {\n  const { t } = useTranslation();\n  const dateColumns = useDataTableDateColumns();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: () => t(\"fields.name\"),\n        enableSorting: true,\n        sortLabel: t(\"fields.name\"),\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      columnHelper.accessor(\"description\", {\n        header: () => t(\"fields.description\"),\n        cell: ({ getValue }) => {\n          return /* @__PURE__ */ jsx2(Tooltip, { content: getValue(), children: /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: getValue() }) }) });\n        },\n        enableSorting: true,\n        sortLabel: t(\"fields.description\"),\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\"),\n        maxSize: 250,\n        minSize: 100\n      }),\n      columnHelper.accessor(\"is_disabled\", {\n        header: () => t(\"fields.status\"),\n        enableSorting: true,\n        sortLabel: t(\"fields.status\"),\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\"),\n        cell: ({ getValue }) => {\n          const value = getValue();\n          return /* @__PURE__ */ jsx2(DataTableStatusCell, { color: value ? \"grey\" : \"green\", children: value ? t(\"general.disabled\") : t(\"general.enabled\") });\n        }\n      }),\n      ...dateColumns\n    ],\n    [t, dateColumns]\n  );\n};\n\n// src/components/data-table/helpers/sales-channels/use-sales-channel-table-empty-state.tsx\nimport { useMemo as useMemo2 } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nvar useSalesChannelTableEmptyState = () => {\n  const { t } = useTranslation2();\n  return useMemo2(() => {\n    const content = {\n      empty: {\n        heading: t(\"salesChannels.list.empty.heading\"),\n        description: t(\"salesChannels.list.empty.description\")\n      },\n      filtered: {\n        heading: t(\"salesChannels.list.filtered.heading\"),\n        description: t(\"salesChannels.list.filtered.description\")\n      }\n    };\n    return content;\n  }, [t]);\n};\n\n// src/components/data-table/helpers/sales-channels/use-sales-channel-table-filters.tsx\nimport { createDataTableFilterHelper } from \"@medusajs/ui\";\nimport { useMemo as useMemo3 } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nvar filterHelper = createDataTableFilterHelper();\nvar useSalesChannelTableFilters = () => {\n  const { t } = useTranslation3();\n  const dateFilters = useDataTableDateFilters();\n  return useMemo3(\n    () => [\n      filterHelper.accessor(\"is_disabled\", {\n        label: t(\"fields.status\"),\n        type: \"radio\",\n        options: [\n          {\n            label: t(\"general.enabled\"),\n            value: \"false\"\n          },\n          {\n            label: t(\"general.disabled\"),\n            value: \"true\"\n          }\n        ]\n      }),\n      ...dateFilters\n    ],\n    [dateFilters, t]\n  );\n};\n\n// src/components/data-table/helpers/sales-channels/use-sales-channel-table-query.tsx\nvar useSalesChannelTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\", \"is_disabled\"],\n    prefix\n  );\n  const { offset, created_at, updated_at, is_disabled, ...rest } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    is_disabled: is_disabled ? JSON.parse(is_disabled) : void 0,\n    ...rest\n  };\n  return searchParams;\n};\n\nexport {\n  useSalesChannelTableColumns,\n  useSalesChannelTableEmptyState,\n  useSalesChannelTableFilters,\n  useSalesChannelTableQuery\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,mBAAwB;AAMxB,yBAA0B;AAkC1B,IAAAA,sBAA4B;AA4C5B,IAAAC,gBAAoC;AAqBpC,IAAAC,gBAAoC;AAlGpC,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,+FAA+F,UAAU;AAAA,QACvI;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,QACX,cAA0B;AAAA,UACxB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,cACT;AAAA,cACA;AAAA,gBACE,0BAA0B,UAAU;AAAA,gBACpC,wBAAwB,UAAU;AAAA,gBAClC,sBAAsB,UAAU;AAAA,gBAChC,uBAAuB,UAAU;AAAA,gBACjC,yBAAyB,UAAU;AAAA,gBACnC,yBAAyB,UAAU;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,QAAQ,EAAE,WAAW,YAAY,SAAS,CAAC;AAAA,EACjE,EAAE,CAAC;AACL;AAIA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,8BAA8B,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAc,wBAAwB;AAC5C,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,MAAM,EAAE,aAAa;AAAA,QAC7B,eAAe;AAAA,QACf,WAAW,EAAE,aAAa;AAAA,QAC1B,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,MAAM,EAAE,oBAAoB;AAAA,QACpC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,qBAAuB,oBAAAC,KAAK,SAAS,EAAE,SAAS,SAAS,GAAG,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QAChQ;AAAA,QACA,eAAe;AAAA,QACf,WAAW,EAAE,oBAAoB;AAAA,QACjC,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,QACrD,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,MACD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,MAAM,EAAE,eAAe;AAAA,QAC/B,eAAe;AAAA,QACf,WAAW,EAAE,eAAe;AAAA,QAC5B,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,QACrD,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,qBAAuB,oBAAAA,KAAK,qBAAqB,EAAE,OAAO,QAAQ,SAAS,SAAS,UAAU,QAAQ,EAAE,kBAAkB,IAAI,EAAE,iBAAiB,EAAE,CAAC;AAAA,QACtJ;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,GAAG,WAAW;AAAA,EACjB;AACF;AAKA,IAAI,iCAAiC,MAAM;AACzC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO,cAAAC,SAAS,MAAM;AACpB,UAAM,UAAU;AAAA,MACd,OAAO;AAAA,QACL,SAAS,EAAE,kCAAkC;AAAA,QAC7C,aAAa,EAAE,sCAAsC;AAAA,MACvD;AAAA,MACA,UAAU;AAAA,QACR,SAAS,EAAE,qCAAqC;AAAA,QAChD,aAAa,EAAE,yCAAyC;AAAA,MAC1D;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,CAAC;AACR;AAMA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,8BAA8B,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,cAAc,wBAAwB;AAC5C,aAAO,cAAAC;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,eAAe;AAAA,QACnC,OAAO,EAAE,eAAe;AAAA,QACxB,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,YACE,OAAO,EAAE,iBAAiB;AAAA,YAC1B,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO,EAAE,kBAAkB;AAAA,YAC3B,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,aAAa,CAAC;AAAA,EACjB;AACF;AAGA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,SAAS,cAAc,cAAc,aAAa;AAAA,IAClE;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,YAAY,YAAY,aAAa,GAAG,KAAK,IAAI;AACjE,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,aAAa,cAAc,KAAK,MAAM,WAAW,IAAI;AAAA,IACrD,GAAG;AAAA,EACL;AACA,SAAO;AACT;", "names": ["import_jsx_runtime", "import_react", "import_react", "jsx2", "useMemo2", "useMemo3"]}