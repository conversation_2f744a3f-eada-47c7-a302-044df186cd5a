import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-EUULPFXI.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-XYBN3KRC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  productsQueryKeys,
  useDeleteProduct,
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  Outlet,
  useLoaderData,
  useLocation
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-list-TFAHMX6Y.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var productsListQuery = () => ({
  queryKey: productsQueryKeys.list({
    limit: 20,
    offset: 0,
    is_giftcard: false
  }),
  queryFn: async () => sdk.admin.product.list({ limit: 20, offset: 0, is_giftcard: false })
});
var productsLoader = (client) => {
  return async () => {
    const query = productsListQuery();
    return queryClient.getQueryData(
      query.queryKey
    ) ?? await client.fetchQuery(query);
  };
};
var PAGE_SIZE = 20;
var ProductListTable = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const initialData = useLoaderData();
  const { searchParams, raw } = useProductTableQuery({ pageSize: PAGE_SIZE });
  const { products, count, isLoading, isError, error } = useProducts(
    {
      ...searchParams,
      is_giftcard: false
    },
    {
      initialData,
      placeholderData: keepPreviousData
    }
  );
  const filters = useProductTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    data: products ?? [],
    columns,
    count,
    enablePagination: true,
    pageSize: PAGE_SIZE,
    getRowId: (row) => row.id
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t("products.domain") }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-center gap-x-2", children: [
        (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: `export${location.search}`, children: t("actions.export") }) }),
        (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: "import", children: t("actions.import") }) }),
        (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: "create", children: t("actions.create") }) })
      ] })
    ] }),
    (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        columns,
        count,
        pageSize: PAGE_SIZE,
        filters,
        search: true,
        pagination: true,
        isLoading,
        queryObject: raw,
        navigateTo: (row) => `${row.original.id}`,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        noRecords: {
          message: t("products.list.noRecordsMessage")
        }
      }
    ),
    (0, import_jsx_runtime.jsx)(Outlet, {})
  ] });
};
var ProductActions = ({ product }) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteProduct(product.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("products.deleteWarning", {
        title: product.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("products.toasts.delete.success.header"), {
          description: t("products.toasts.delete.success.description", {
            title: product.title
          })
        });
      },
      onError: (e) => {
        toast.error(t("products.toasts.delete.error.header"), {
          description: e.message
        });
      }
    });
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `/products/${product.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTableColumns();
  const columns = (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(ProductActions, { product: row.original });
        }
      })
    ],
    [base]
  );
  return columns;
};
var ProductList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product.list.after"),
        before: getWidgets("product.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(ProductListTable, {})
    }
  );
};
export {
  ProductList as Component,
  productsLoader as productLoader
};
//# sourceMappingURL=product-list-TFAHMX6Y-ERB76JCO.js.map
