{"version": 3, "sources": ["../../@medusajs/dashboard/dist/campaign-list-JN5XQRQD.mjs"], "sourcesContent": ["import {\n  useCampaignTableColumns\n} from \"./chunk-DM7MO4FV.mjs\";\nimport {\n  useCampaignTableQuery\n} from \"./chunk-OMC5JCQH.mjs\";\nimport \"./chunk-3OHUAQUF.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  useCampaigns,\n  useDeleteCampaign\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/campaign-list/components/campaign-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading, toast, usePrompt } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar CampaignListTable = () => {\n  const { t } = useTranslation();\n  const { raw, searchParams } = useCampaignTableQuery({ pageSize: PAGE_SIZE });\n  const {\n    campaigns,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useCampaigns(searchParams, {\n    placeholderData: keepPreviousData\n  });\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: campaigns ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Heading, { level: \"h2\", children: t(\"campaigns.domain\") }),\n      /* @__PURE__ */ jsx(Link, { to: \"/campaigns/create\", children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        pagination: true,\n        search: true,\n        navigateTo: (row) => row.id,\n        isLoading,\n        queryObject: raw,\n        orderBy: [\n          { key: \"name\", label: t(\"fields.name\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ]\n      }\n    )\n  ] });\n};\nvar CampaignActions = ({ campaign }) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteCampaign(campaign.id);\n  const handleDelete = async () => {\n    const confirm = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"campaigns.deleteCampaignWarning\", {\n        name: campaign.name\n      }),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      verificationText: campaign.name,\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!confirm) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"campaigns.delete.successToast\", { name: campaign.name })\n        );\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `/campaigns/${campaign.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCampaignTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(CampaignActions, { campaign: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/campaigns/campaign-list/campaign-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CampaignList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"campaign.list.after\"),\n        before: getWidgets(\"campaign.list.before\")\n      },\n      hasOutlet: true,\n      children: /* @__PURE__ */ jsx2(CampaignListTable, {})\n    }\n  );\n};\nexport {\n  CampaignList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,mBAAwB;AAGxB,yBAA0B;AA6H1B,IAAAA,sBAA4B;AA5H5B,IAAI,YAAY;AAChB,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,KAAK,aAAa,IAAI,sBAAsB,EAAE,UAAU,UAAU,CAAC;AAC3E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,aAAa,cAAc;AAAA,IAC7B,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,aAAa,CAAC;AAAA,IACpB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,wBAAI,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,kBAAkB,EAAE,CAAC;AAAA,UAC7D,wBAAI,MAAM,EAAE,IAAI,qBAAqB,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IACtK,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY,CAAC,QAAQ,IAAI;AAAA,QACzB;AAAA,QACA,aAAa;AAAA,QACb,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,kBAAkB,CAAC,EAAE,SAAS,MAAM;AACtC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,kBAAkB,SAAS,EAAE;AACrD,QAAM,eAAe,YAAY;AAC/B,UAAM,UAAU,MAAM,OAAO;AAAA,MAC3B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,mCAAmC;AAAA,QAChD,MAAM,SAAS;AAAA,MACjB,CAAC;AAAA,MACD,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,kBAAkB,SAAS;AAAA,MAC3B,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,iCAAiC,EAAE,MAAM,SAAS,KAAK,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,cAAc,SAAS,EAAE;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,wBAAwB;AACrC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,iBAAiB,EAAE,UAAU,IAAI,SAAS,CAAC;AAAA,QACxE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,qBAAqB;AAAA,QACvC,QAAQ,WAAW,sBAAsB;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}