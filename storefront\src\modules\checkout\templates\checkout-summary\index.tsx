"use client"

import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { useCartStore } from "zustand-store/cart-store"

import { TCartCustomField } from "types/cart"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "components/ui/card"
import Typography from "components/ui/typography"

import { formatDescription, formatTitle } from "@lib/util/format-description"
import { convertCurrencyToLocale } from "@lib/util/money"
import DiscountCode from "@modules/checkout/components/discount-code"
import PaymentButton from "@modules/checkout/components/payment-button"
import CartTotals from "@modules/common/components/cart-totals"
import SafeHTML from "components/ui/safe-html"
import { T_Promotion } from "types/promotion"
import { cn } from "utils"

type CheckoutSummaryProps = {
  cart: TCartCustomField
  promotionList: T_Promotion[]
  currency_code: any
  className?: string
  passThrough?: {
    card?: string
    cardHeader?: string
    cardTitle?: string
    button?: string
    content?: string
  }
  isCheckout: boolean
  setIsCheckout: (value: boolean) => void
  isShippingLoading?: boolean
}

const CheckoutSummary = ({
  cart,
  currency_code,
  promotionList,
  className,
  passThrough = {},
  isCheckout,
  setIsCheckout,
  isShippingLoading = false,
}: CheckoutSummaryProps) => {
  const { t, i18n } = useTranslation("checkout")
  const { push } = useRouter()
  const { cartCustomField } = useCartStore()

  // Use cart from store if available, fallback to prop
  const currentCart = cartCustomField || cart

  // Add loading state tracking
  return (
    <Card className={`${className} ${passThrough.card || ""}`}>
      <CardHeader className={cn("!py-0")}>
        <CardTitle
          className={`flex items-start justify-between py-4 sm:!py-0 ${passThrough.cardTitle || ""}`}
        >
          <Typography
            variant="h3"
            size="md"
            className="font-bold text-gray-800"
          >
            {t("summary.title")}
          </Typography>
          <button
            className="hidden text-sm font-semibold text-primary-main underline sm:inline-block"
            onClick={() => setIsCheckout(false)}
          >
            {t("summary.back_to_cart")}
          </button>
        </CardTitle>
      </CardHeader>

      <CardContent className={passThrough.content}>
        <div className="flex w-full flex-col gap-6 bg-white">
          {cart?.items?.map((item, index) => (
            <div key={item.id} className={`flex justify-between border-b pb-4`}>
              <div className="flex w-full flex-col gap-2">
                <div className="flex w-full items-center justify-between">
                  <Typography className="text-base font-semibold">
                    {formatTitle(item.product_title, i18n.language)}
                  </Typography>
                  <Typography className="text-sm text-gray-950">
                    x{item.quantity}
                  </Typography>
                </div>

                {(item as any)?.metadata?.note ? (
                  <Typography className="text-sm text-gray-950">
                    {(item as any)?.metadata?.note}
                  </Typography>
                ) : (
                  <SafeHTML
                    html={formatDescription(
                      item.product_description,
                      i18n.language,
                      "Mô tả sản phẩm"
                    )}
                    className="line-clamp-2 text-sm italic !leading-6 text-gray-800"
                  />
                )}

                <div className="mt-2 flex w-full items-center justify-between">
                  <Typography className="font-semibold text-primary-main">
                    {convertCurrencyToLocale({
                      amount: item.unit_price,
                      currency_code,
                    })}
                  </Typography>
                  <Typography size="sm" className="text-gray-700">
                    {item.product?.options
                      ?.map((opt) => {
                        const selectedVariant = item.product?.variants?.find(
                          (v) => v.id === item.variant_id
                        )
                        const optionValue = selectedVariant?.options?.find(
                          (ov) => ov.option_id === opt.id
                        )?.value

                        return optionValue
                          ? `${opt.title}: ${optionValue}`
                          : null
                      })
                      .filter(Boolean)
                      .join(", ") || ""}
                  </Typography>
                </div>
              </div>
            </div>
          ))}
          {cart.metadata && !!cart.metadata.brewing_note && (
            <p className="text-sm text-gray-750">
              <strong>{t("product_note")}: </strong>
              <span className="italic">
                {String(cart.metadata.brewing_note)}
              </span>
            </p>
          )}

          <DiscountCode cart={cart} promotionList={promotionList} />
          <CartTotals
            cart={currentCart}
            isSubtotalLoading={
              isShippingLoading &&
              (!currentCart?.item_subtotal || currentCart.item_subtotal === 0)
            }
          />
          {/* Validate to Payment */}
          <PaymentButton cart={cart} data-testid="submit-order-button" />
        </div>
      </CardContent>
    </Card>
  )
}

export default CheckoutSummary
