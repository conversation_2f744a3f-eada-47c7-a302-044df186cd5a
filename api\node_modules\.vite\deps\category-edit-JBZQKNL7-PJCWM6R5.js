import {
  HandleInput
} from "./chunk-DHII2FGS.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useProductCategory,
  useUpdateProductCategory
} from "./chunk-ZJNBJBHK.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  Textarea,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/category-edit-JBZQKNL7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditCategorySchema = z.object({
  name: z.string().min(1),
  handle: z.string().min(1),
  description: z.string().optional(),
  status: z.enum(["active", "inactive"]),
  visibility: z.enum(["public", "internal"])
});
var EditCategoryForm = ({ category }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: category.name,
      handle: category.handle,
      description: category.description || "",
      status: category.is_active ? "active" : "inactive",
      visibility: category.is_internal ? "internal" : "public"
    },
    resolver: t(EditCategorySchema)
  });
  const { mutateAsync, isPending } = useUpdateProductCategory(category.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        name: data.name,
        description: data.description,
        handle: data.handle,
        is_active: data.status === "active",
        is_internal: data.visibility === "internal"
      },
      {
        onSuccess: () => {
          toast.success(t2("categories.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { autoComplete: "off", ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "handle",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(
                Form.Label,
                {
                  optional: true,
                  tooltip: t2("collections.handleTooltip"),
                  children: t2("fields.handle")
                }
              ),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(HandleInput, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "description",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.description") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "status",
            render: ({ field: { ref, onChange, ...field } }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("categories.fields.status.label") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                  (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                  (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "active", children: t2("categories.fields.status.active") }),
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "inactive", children: t2("categories.fields.status.inactive") })
                  ] })
                ] }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "visibility",
            render: ({ field: { ref, onChange, ...field } }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("categories.fields.visibility.label") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                  (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                  (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "public", children: t2("categories.fields.visibility.public") }),
                    (0, import_jsx_runtime.jsx)(Select.Item, { value: "internal", children: t2("categories.fields.visibility.internal") })
                  ] })
                ] }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] })
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var CategoryEdit = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { product_category, isPending, isError, error } = useProductCategory(
    id
  );
  const ready = !isPending && !!product_category;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("categories.edit.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { className: "sr-only", children: t2("categories.edit.description") })
    ] }),
    ready && (0, import_jsx_runtime2.jsx)(EditCategoryForm, { category: product_category })
  ] });
};
export {
  CategoryEdit as Component
};
//# sourceMappingURL=category-edit-JBZQKNL7-PJCWM6R5.js.map
