{"version": 3, "sources": ["../../@medusajs/dashboard/dist/settings-FQUPXMWF.mjs"], "sourcesContent": ["import \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/settings/settings.tsx\nimport { useEffect } from \"react\";\nimport { Outlet, useLocation, useNavigate } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Settings = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    if (location.pathname === \"/settings\") {\n      navigate(\"/settings/store\", { replace: true });\n    }\n  }, [location.pathname, navigate]);\n  return /* @__PURE__ */ jsx(Outlet, {});\n};\nexport {\n  Settings as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,mBAA0B;AAE1B,yBAAoB;AACpB,IAAI,WAAW,MAAM;AACnB,QAAM,WAAW,YAAY;AAC7B,QAAM,WAAW,YAAY;AAC7B,8BAAU,MAAM;AACd,QAAI,SAAS,aAAa,aAAa;AACrC,eAAS,mBAAmB,EAAE,SAAS,KAAK,CAAC;AAAA,IAC/C;AAAA,EACF,GAAG,CAAC,SAAS,UAAU,QAAQ,CAAC;AAChC,aAAuB,wBAAI,QAAQ,CAAC,CAAC;AACvC;", "names": []}