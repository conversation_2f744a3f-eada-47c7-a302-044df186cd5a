{"version": 3, "sources": ["../../@medusajs/dashboard/dist/category-edit-JBZQKNL7.mjs"], "sourcesContent": ["import {\n  HandleInput\n} from \"./chunk-7OYLCEKK.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useProductCategory,\n  useUpdateProductCategory\n} from \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/categories/category-edit/category-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/categories/category-edit/components/edit-category-form/edit-category-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Select, Textarea, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditCategorySchema = z.object({\n  name: z.string().min(1),\n  handle: z.string().min(1),\n  description: z.string().optional(),\n  status: z.enum([\"active\", \"inactive\"]),\n  visibility: z.enum([\"public\", \"internal\"])\n});\nvar EditCategoryForm = ({ category }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: category.name,\n      handle: category.handle,\n      description: category.description || \"\",\n      status: category.is_active ? \"active\" : \"inactive\",\n      visibility: category.is_internal ? \"internal\" : \"public\"\n    },\n    resolver: zodResolver(EditCategorySchema)\n  });\n  const { mutateAsync, isPending } = useUpdateProductCategory(category.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        name: data.name,\n        description: data.description,\n        handle: data.handle,\n        is_active: data.status === \"active\",\n        is_internal: data.visibility === \"internal\"\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"categories.edit.successToast\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"name\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"handle\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(\n                Form.Label,\n                {\n                  optional: true,\n                  tooltip: t(\"collections.handleTooltip\"),\n                  children: t(\"fields.handle\")\n                }\n              ),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(HandleInput, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"description\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.description\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"status\",\n            render: ({ field: { ref, onChange, ...field } }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"categories.fields.status.label\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: onChange, children: [\n                  /* @__PURE__ */ jsx(Select.Trigger, { ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                  /* @__PURE__ */ jsxs(Select.Content, { children: [\n                    /* @__PURE__ */ jsx(Select.Item, { value: \"active\", children: t(\"categories.fields.status.active\") }),\n                    /* @__PURE__ */ jsx(Select.Item, { value: \"inactive\", children: t(\"categories.fields.status.inactive\") })\n                  ] })\n                ] }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"visibility\",\n            render: ({ field: { ref, onChange, ...field } }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"categories.fields.visibility.label\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: onChange, children: [\n                  /* @__PURE__ */ jsx(Select.Trigger, { ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                  /* @__PURE__ */ jsxs(Select.Content, { children: [\n                    /* @__PURE__ */ jsx(Select.Item, { value: \"public\", children: t(\"categories.fields.visibility.public\") }),\n                    /* @__PURE__ */ jsx(Select.Item, { value: \"internal\", children: t(\"categories.fields.visibility.internal\") })\n                  ] })\n                ] }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        )\n      ] })\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/categories/category-edit/category-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CategoryEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product_category, isPending, isError, error } = useProductCategory(\n    id\n  );\n  const ready = !isPending && !!product_category;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"categories.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"categories.edit.description\") })\n    ] }),\n    ready && /* @__PURE__ */ jsx2(EditCategoryForm, { category: product_category })\n  ] });\n};\nexport {\n  CategoryEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,yBAA0B;AAgJ1B,IAAAA,sBAA2C;AA/I3C,IAAI,qBAAqB,EAAE,OAAO;AAAA,EAChC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACxB,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,QAAQ,EAAE,KAAK,CAAC,UAAU,UAAU,CAAC;AAAA,EACrC,YAAY,EAAE,KAAK,CAAC,UAAU,UAAU,CAAC;AAC3C,CAAC;AACD,IAAI,mBAAmB,CAAC,EAAE,SAAS,MAAM;AACvC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,SAAS;AAAA,MACf,QAAQ,SAAS;AAAA,MACjB,aAAa,SAAS,eAAe;AAAA,MACrC,QAAQ,SAAS,YAAY,WAAW;AAAA,MACxC,YAAY,SAAS,cAAc,aAAa;AAAA,IAClD;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,yBAAyB,SAAS,EAAE;AACvE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,KAAK;AAAA,QACX,aAAa,KAAK;AAAA,QAClB,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK,WAAW;AAAA,QAC3B,aAAa,KAAK,eAAe;AAAA,MACnC;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,8BAA8B,CAAC;AAC/C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,UAAU;AAAA,kBACV,SAASA,GAAE,2BAA2B;AAAA,kBACtC,UAAUA,GAAE,eAAe;AAAA,gBAC7B;AAAA,cACF;AAAA,kBACgB,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,aAAa,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBAC9E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,oBAAoB,EAAE,CAAC;AAAA,kBACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,YAC1E;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,UAAU,GAAG,MAAM,EAAE,MAAM;AAClD,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gCAAgC,EAAE,CAAC;AAAA,oBACjE,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,sBACxG,wBAAI,OAAO,SAAS,EAAE,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,sBAC5E,yBAAK,OAAO,SAAS,EAAE,UAAU;AAAA,wBAC/B,wBAAI,OAAO,MAAM,EAAE,OAAO,UAAU,UAAUA,GAAE,iCAAiC,EAAE,CAAC;AAAA,wBACpF,wBAAI,OAAO,MAAM,EAAE,OAAO,YAAY,UAAUA,GAAE,mCAAmC,EAAE,CAAC;AAAA,kBAC1G,EAAE,CAAC;AAAA,gBACL,EAAE,CAAC,EAAE,CAAC;AAAA,oBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,YACgB;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,UAAU,GAAG,MAAM,EAAE,MAAM;AAClD,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,oCAAoC,EAAE,CAAC;AAAA,oBACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,sBACxG,wBAAI,OAAO,SAAS,EAAE,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,sBAC5E,yBAAK,OAAO,SAAS,EAAE,UAAU;AAAA,wBAC/B,wBAAI,OAAO,MAAM,EAAE,OAAO,UAAU,UAAUA,GAAE,qCAAqC,EAAE,CAAC;AAAA,wBACxF,wBAAI,OAAO,MAAM,EAAE,OAAO,YAAY,UAAUA,GAAE,uCAAuC,EAAE,CAAC;AAAA,kBAC9G,EAAE,CAAC;AAAA,gBACL,EAAE,CAAC,EAAE,CAAC;AAAA,oBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,kBAAkB,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7H,oBAAAE,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUF,GAAE,6BAA6B,EAAE,CAAC;AAAA,IACpH,EAAE,CAAC;AAAA,IACH,aAAyB,oBAAAE,KAAK,kBAAkB,EAAE,UAAU,iBAAiB,CAAC;AAAA,EAChF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}