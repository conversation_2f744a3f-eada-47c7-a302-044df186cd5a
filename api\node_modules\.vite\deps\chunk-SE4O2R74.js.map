{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-Q6MSICBU.mjs"], "sourcesContent": ["import {\n  IconAvatar\n} from \"./chunk-EQTBJSBZ.mjs\";\n\n// src/components/common/logo-box/avatar-box.tsx\nimport { motion } from \"motion/react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction AvatarBox({ checked }) {\n  return /* @__PURE__ */ jsxs(\n    IconAvatar,\n    {\n      size: \"xlarge\",\n      className: \"bg-ui-button-neutral shadow-buttons-neutral after:button-neutral-gradient relative mb-4 flex h-[50px] w-[50px] items-center justify-center rounded-xl after:inset-0 after:content-['']\",\n      children: [\n        checked && /* @__PURE__ */ jsx(\n          motion.div,\n          {\n            className: \"absolute -right-[5px] -top-1 flex size-5 items-center justify-center rounded-full border-[0.5px] border-[rgba(3,7,18,0.2)] bg-[#3B82F6] bg-gradient-to-b from-white/0 to-white/20 shadow-[0px_1px_2px_0px_rgba(3,7,18,0.12),0px_1px_2px_0px_rgba(255,255,255,0.10)_inset,0px_-1px_5px_0px_rgba(255,255,255,0.10)_inset,0px_0px_0px_0px_rgba(3,7,18,0.06)_inset]\",\n            initial: { opacity: 0, scale: 0.5 },\n            animate: { opacity: 1, scale: 1 },\n            transition: {\n              duration: 1.2,\n              delay: 0.8,\n              ease: [0, 0.71, 0.2, 1.01]\n            },\n            children: /* @__PURE__ */ jsx(\n              \"svg\",\n              {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 20 20\",\n                fill: \"none\",\n                children: /* @__PURE__ */ jsx(\n                  motion.path,\n                  {\n                    d: \"M5.8335 10.4167L9.16683 13.75L14.1668 6.25\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.5\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    initial: { pathLength: 0, opacity: 0 },\n                    animate: { pathLength: 1, opacity: 1 },\n                    transition: {\n                      duration: 1.3,\n                      delay: 1.1,\n                      bounce: 0.6,\n                      ease: [0.1, 0.8, 0.2, 1.01]\n                    }\n                  }\n                )\n              }\n            )\n          }\n        ),\n        /* @__PURE__ */ jsxs(\n          \"svg\",\n          {\n            className: \"rounded-[10px]\",\n            viewBox: \"0 0 400 400\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n              /* @__PURE__ */ jsx(\"rect\", { width: \"400\", height: \"400\", fill: \"#18181B\" }),\n              /* @__PURE__ */ jsx(\n                \"path\",\n                {\n                  d: \"M238.088 51.1218L238.089 51.1223L310.605 92.8101C334.028 106.308 348.526 131.32 347.868 157.953L347.867 157.966V157.978V241.688C347.867 268.68 333.687 293.362 310.271 306.856L310.269 306.858L237.754 348.878C214.336 362.374 185.643 362.374 162.225 348.878L89.7127 306.859C66.6206 293.361 52.1113 268.674 52.1113 241.688V157.978C52.1113 131.326 66.6211 106.307 89.7088 92.8093C89.7101 92.8085 89.7114 92.8078 89.7127 92.807L162.556 51.1233L162.559 51.1218C185.977 37.6261 214.67 37.6261 238.088 51.1218ZM124.634 200C124.634 241.576 158.502 275.372 200.156 275.372C242.142 275.372 276.013 241.578 276.013 200C276.013 158.419 241.805 124.628 200.156 124.628C158.502 124.628 124.634 158.424 124.634 200Z\",\n                  fill: \"url(#paint0_linear_11869_12671)\",\n                  stroke: \"url(#paint1_linear_11869_12671)\",\n                  strokeWidth: \"2\"\n                }\n              ),\n              /* @__PURE__ */ jsxs(\"defs\", { children: [\n                /* @__PURE__ */ jsxs(\n                  \"linearGradient\",\n                  {\n                    id: \"paint0_linear_11869_12671\",\n                    x1: \"200\",\n                    y1: \"40\",\n                    x2: \"200\",\n                    y2: \"360\",\n                    gradientUnits: \"userSpaceOnUse\",\n                    children: [\n                      /* @__PURE__ */ jsx(\"stop\", { stopColor: \"white\" }),\n                      /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", stopColor: \"white\", stopOpacity: \"0.7\" })\n                    ]\n                  }\n                ),\n                /* @__PURE__ */ jsxs(\n                  \"linearGradient\",\n                  {\n                    id: \"paint1_linear_11869_12671\",\n                    x1: \"200\",\n                    y1: \"40\",\n                    x2: \"200\",\n                    y2: \"360\",\n                    gradientUnits: \"userSpaceOnUse\",\n                    children: [\n                      /* @__PURE__ */ jsx(\"stop\", { stopColor: \"white\", stopOpacity: \"0\" }),\n                      /* @__PURE__ */ jsx(\"stop\", { offset: \"1\", stopColor: \"white\", stopOpacity: \"0.7\" })\n                    ]\n                  }\n                )\n              ] })\n            ]\n          }\n        )\n      ]\n    }\n  );\n}\n\nexport {\n  AvatarBox\n};\n"], "mappings": ";;;;;;;;;;;;;;AAMA,yBAA0B;AAC1B,SAAS,UAAU,EAAE,QAAQ,GAAG;AAC9B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,QACR,eAA2B;AAAA,UACzB,OAAO;AAAA,UACP;AAAA,YACE,WAAW;AAAA,YACX,SAAS,EAAE,SAAS,GAAG,OAAO,IAAI;AAAA,YAClC,SAAS,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,YAChC,YAAY;AAAA,cACV,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM,CAAC,GAAG,MAAM,KAAK,IAAI;AAAA,YAC3B;AAAA,YACA,cAA0B;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,SAAS;AAAA,gBACT,MAAM;AAAA,gBACN,cAA0B;AAAA,kBACxB,OAAO;AAAA,kBACP;AAAA,oBACE,GAAG;AAAA,oBACH,QAAQ;AAAA,oBACR,aAAa;AAAA,oBACb,eAAe;AAAA,oBACf,gBAAgB;AAAA,oBAChB,SAAS,EAAE,YAAY,GAAG,SAAS,EAAE;AAAA,oBACrC,SAAS,EAAE,YAAY,GAAG,SAAS,EAAE;AAAA,oBACrC,YAAY;AAAA,sBACV,UAAU;AAAA,sBACV,OAAO;AAAA,sBACP,QAAQ;AAAA,sBACR,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI;AAAA,oBAC5B;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,kBACQ,wBAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,OAAO,MAAM,UAAU,CAAC;AAAA,kBAC5D;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,kBACgB,yBAAK,QAAQ,EAAE,UAAU;AAAA,oBACvB;AAAA,kBACd;AAAA,kBACA;AAAA,oBACE,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,eAAe;AAAA,oBACf,UAAU;AAAA,0BACQ,wBAAI,QAAQ,EAAE,WAAW,QAAQ,CAAC;AAAA,0BAClC,wBAAI,QAAQ,EAAE,QAAQ,KAAK,WAAW,SAAS,aAAa,MAAM,CAAC;AAAA,oBACrF;AAAA,kBACF;AAAA,gBACF;AAAA,oBACgB;AAAA,kBACd;AAAA,kBACA;AAAA,oBACE,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,eAAe;AAAA,oBACf,UAAU;AAAA,0BACQ,wBAAI,QAAQ,EAAE,WAAW,SAAS,aAAa,IAAI,CAAC;AAAA,0BACpD,wBAAI,QAAQ,EAAE,QAAQ,KAAK,WAAW,SAAS,aAAa,MAAM,CAAC;AAAA,oBACrF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}