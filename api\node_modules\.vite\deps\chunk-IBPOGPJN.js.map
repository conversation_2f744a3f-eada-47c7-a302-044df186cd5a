{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-RWU2ZKWZ.mjs"], "sourcesContent": ["import {\n  reservationItemsQueryKeys\n} from \"./chunk-FVC7M755.mjs\";\nimport {\n  inventoryItemsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/orders.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar ORDERS_QUERY_KEY = \"orders\";\nvar _orderKeys = queryKeysFactory(ORDERS_QUERY_KEY);\n_orderKeys.preview = function(id) {\n  return [this.detail(id), \"preview\"];\n};\n_orderKeys.changes = function(id) {\n  return [this.detail(id), \"changes\"];\n};\n_orderKeys.lineItems = function(id) {\n  return [this.detail(id), \"lineItems\"];\n};\nvar ordersQueryKeys = _orderKeys;\nvar useOrder = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.order.retrieve(id, query),\n    queryKey: ordersQueryKeys.detail(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateOrder = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.order.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useOrderPreview = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.order.retrievePreview(id, query),\n    queryKey: ordersQueryKeys.preview(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useOrders = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.order.list(query),\n    queryKey: ordersQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useOrderChanges = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.order.listChanges(id, query),\n    queryKey: ordersQueryKeys.changes(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useOrderLineItems = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: async () => sdk.admin.order.listLineItems(id, query),\n    queryKey: ordersQueryKeys.lineItems(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateOrderFulfillment = (orderId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.order.createFulfillment(orderId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCancelOrderFulfillment = (orderId, fulfillmentId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.order.cancelFulfillment(orderId, fulfillmentId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateOrderShipment = (orderId, fulfillmentId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.order.createShipment(orderId, fulfillmentId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useMarkOrderFulfillmentAsDelivered = (orderId, fulfillmentId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.order.markAsDelivered(orderId, fulfillmentId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCancelOrder = (orderId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.order.cancel(orderId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.detail(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRequestTransferOrder = (orderId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.order.requestTransfer(orderId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCancelOrderTransfer = (orderId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.order.cancelTransfer(orderId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  ordersQueryKeys,\n  useOrder,\n  useUpdateOrder,\n  useOrderPreview,\n  useOrders,\n  useOrderChanges,\n  useOrderLineItems,\n  useCreateOrderFulfillment,\n  useCancelOrderFulfillment,\n  useCreateOrderShipment,\n  useMarkOrderFulfillmentAsDelivered,\n  useCancelOrder,\n  useRequestTransferOrder,\n  useCancelOrderTransfer\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,mBAAmB;AACvB,IAAI,aAAa,iBAAiB,gBAAgB;AAClD,WAAW,UAAU,SAAS,IAAI;AAChC,SAAO,CAAC,KAAK,OAAO,EAAE,GAAG,SAAS;AACpC;AACA,WAAW,UAAU,SAAS,IAAI;AAChC,SAAO,CAAC,KAAK,OAAO,EAAE,GAAG,SAAS;AACpC;AACA,WAAW,YAAY,SAAS,IAAI;AAClC,SAAO,CAAC,KAAK,OAAO,EAAE,GAAG,WAAW;AACtC;AACA,IAAI,kBAAkB;AACtB,IAAI,WAAW,CAAC,IAAI,OAAO,YAAY;AACrC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,MAAM,SAAS,IAAI,KAAK;AAAA,IACvD,UAAU,gBAAgB,OAAO,IAAI,KAAK;AAAA,IAC1C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,iBAAiB,CAAC,IAAI,YAAY;AACpC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,OAAO,IAAI,OAAO;AAAA,IAC3D,WAAW,CAAC,MAAM,WAAW,YAAY;AA5C7C;AA6CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,OAAO,EAAE;AAAA,MACrC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,MAAM,gBAAgB,IAAI,KAAK;AAAA,IAC9D,UAAU,gBAAgB,QAAQ,EAAE;AAAA,IACpC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,YAAY,CAAC,OAAO,YAAY;AAClC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,MAAM,KAAK,KAAK;AAAA,IAC/C,UAAU,gBAAgB,KAAK,KAAK;AAAA,IACpC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,IAAI,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,MAAM,YAAY,IAAI,KAAK;AAAA,IAC1D,UAAU,gBAAgB,QAAQ,EAAE;AAAA,IACpC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,IAAI,OAAO,YAAY;AAC9C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,YAAY,IAAI,MAAM,MAAM,cAAc,IAAI,KAAK;AAAA,IAC5D,UAAU,gBAAgB,UAAU,EAAE;AAAA,IACtC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,4BAA4B,CAAC,SAAS,YAAY;AACpD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,kBAAkB,SAAS,OAAO;AAAA,IAC3E,WAAW,CAAC,MAAM,WAAW,YAAY;AA3F7C;AA4FM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,MAAM;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,4BAA4B,CAAC,SAAS,eAAe,YAAY;AACnE,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,kBAAkB,SAAS,eAAe,OAAO;AAAA,IAC1F,WAAW,CAAC,MAAM,WAAW,YAAY;AAhH7C;AAiHM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,SAAS,eAAe,YAAY;AAChE,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,eAAe,SAAS,eAAe,OAAO;AAAA,IACvF,WAAW,CAAC,MAAM,WAAW,YAAY;AA/H7C;AAgIM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qCAAqC,CAAC,SAAS,eAAe,YAAY;AAC5E,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,MAAM,gBAAgB,SAAS,aAAa;AAAA,IACxE,WAAW,CAAC,MAAM,WAAW,YAAY;AA9I7C;AA+IM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,iBAAiB,CAAC,SAAS,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,MAAM,OAAO,OAAO;AAAA,IAChD,WAAW,CAAC,MAAM,WAAW,YAAY;AA7J7C;AA8JM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,OAAO,OAAO;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,0BAA0B,CAAC,SAAS,YAAY;AAClD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,MAAM,gBAAgB,SAAS,OAAO;AAAA,IACzE,WAAW,CAAC,MAAM,WAAW,YAAY;AA5K7C;AA6KM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,SAAS,YAAY;AACjD,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,MAAM,eAAe,OAAO;AAAA,IACxD,WAAW,CAAC,MAAM,WAAW,YAAY;AA3L7C;AA4LM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}