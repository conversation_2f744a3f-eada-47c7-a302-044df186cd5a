import {
  DataGridTogglableNumberCell
} from "./chunk-BX5SNYTN.js";
import {
  PRODUCT_VARIANT_IDS_KEY
} from "./chunk-JKTOR6WW.js";
import {
  DataGrid,
  DataGridReadonlyCell,
  DataGridSkeleton,
  createDataGrid<PERSON><PERSON><PERSON>,
  useDataGridDuplicateCell
} from "./chunk-JGBVAJ3K.js";
import "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import {
  ProgressBar
} from "./chunk-YM3FRBGU.js";
import {
  AnimatePresence
} from "./chunk-7M4ICL3D.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-XYBN3KRC.js";
import {
  Skeleton
} from "./chunk-TP2BI5T3.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-NV2N3EWM.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useBatchInventoryItemsLocationLevels
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Await,
  defer,
  useLoaderData
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  InformationCircle,
  Switch,
  Tooltip,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-stock-TZS75EG7.mjs
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
async function getProductStockData(id, productVariantIds) {
  const CHUNK_SIZE = 20;
  let offset = 0;
  let totalCount = 0;
  let allVariants = [];
  do {
    const { variants: chunk, count } = await sdk.admin.product.listVariants(
      id,
      {
        id: productVariantIds,
        offset,
        limit: CHUNK_SIZE,
        fields: "id,title,sku,inventory_items,inventory_items.*,inventory_items.inventory,inventory_items.inventory.id,inventory_items.inventory.title,inventory_items.inventory.sku,*inventory_items.inventory.location_levels,product.thumbnail"
      }
    );
    allVariants = [...allVariants, ...chunk];
    totalCount = count;
    offset += CHUNK_SIZE;
  } while (allVariants.length < totalCount);
  const { stock_locations } = await sdk.admin.stockLocation.list({
    limit: 9999,
    fields: "id,name"
  });
  return {
    variants: allVariants,
    locations: stock_locations
  };
}
var productStockLoader = async ({
  params,
  request
}) => {
  var _a;
  const id = params.id;
  const searchParams = new URLSearchParams(request.url);
  const productVariantIds = ((_a = searchParams.get(PRODUCT_VARIANT_IDS_KEY)) == null ? void 0 : _a.split(",")) || void 0;
  const dataPromise = getProductStockData(id, productVariantIds);
  return defer({
    data: dataPromise
  });
};
var DataGridDuplicateCell = ({
  duplicateOf,
  children
}) => {
  const { watchedValue } = useDataGridDuplicateCell({ duplicateOf });
  return (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-base txt-compact-small text-ui-fg-subtle flex size-full cursor-not-allowed items-center justify-between overflow-hidden px-4 py-2.5 outline-none", children: typeof children === "function" ? children({ value: watchedValue }) : children });
};
function isProductVariant(row) {
  return row.id.startsWith("variant_");
}
function isProductVariantWithInventoryPivot(row) {
  return row.inventory_items && row.inventory_items.length > 0;
}
function getDisabledInventoryRows(variants) {
  const seen = {};
  const disabled = {};
  variants.forEach((variant) => {
    const inventoryItems = variant.inventory_items;
    if (!inventoryItems) {
      return;
    }
    inventoryItems.forEach((item) => {
      const existing = seen[item.inventory_item_id];
      if (existing) {
        disabled[item.inventory_item_id] = {
          id: existing.id,
          title: existing.title || "",
          sku: existing.sku || ""
        };
        return;
      }
      seen[item.inventory_item_id] = variant;
    });
  });
  return disabled;
}
var helper = createDataGridHelper();
var useProductStockColumns = (locations = [], disabled = {}) => {
  const { t: t2 } = useTranslation();
  const getIsDisabled = (0, import_react4.useCallback)(
    (item) => {
      const disabledItem = disabled[item.inventory_item_id];
      const isDisabled = !!disabledItem && disabledItem.id !== item.variant_id;
      if (!isDisabled) {
        return {
          isDisabled: false,
          item: void 0
        };
      }
      return {
        isDisabled,
        item: disabledItem
      };
    },
    [disabled]
  );
  return (0, import_react4.useMemo)(
    () => [
      helper.column({
        id: "title",
        name: "Title",
        header: "Title",
        cell: (context) => {
          var _a, _b, _c, _d;
          const item = context.row.original;
          if (isProductVariant(item)) {
            return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime2.jsx)(Thumbnail, { size: "small", src: (_a = item.product) == null ? void 0 : _a.thumbnail }),
              (0, import_jsx_runtime2.jsx)("span", { children: item.title || "-" })
            ] }) });
          }
          const { isDisabled, item: disabledItem } = getIsDisabled(item);
          if (isDisabled) {
            return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, color: "normal", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex size-full items-center justify-between gap-x-2", children: [
              (0, import_jsx_runtime2.jsx)(
                "span",
                {
                  title: ((_b = item.inventory) == null ? void 0 : _b.title) || void 0,
                  className: "text-ui-fg-disabled",
                  children: ((_c = item.inventory) == null ? void 0 : _c.title) || "-"
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                Tooltip,
                {
                  content: disabledItem.sku ? t2("products.stock.tooltips.alreadyManagedWithSku", {
                    title: disabledItem.title,
                    sku: disabledItem.sku
                  }) : t2("products.stock.tooltips.alreadyManaged", {
                    title: disabledItem.title
                  }),
                  children: (0, import_jsx_runtime2.jsx)(InformationCircle, {})
                }
              )
            ] }) });
          }
          return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, color: "normal", children: ((_d = item.inventory) == null ? void 0 : _d.title) || "-" });
        },
        disableHiding: true
      }),
      helper.column({
        id: "sku",
        name: "SKU",
        header: "SKU",
        cell: (context) => {
          var _a, _b;
          const item = context.row.original;
          if (isProductVariant(item)) {
            return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, children: item.sku || "-" });
          }
          const { isDisabled } = getIsDisabled(item);
          if (isDisabled) {
            return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, color: "normal", children: (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-disabled", children: ((_a = item.inventory) == null ? void 0 : _a.sku) || "-" }) });
          }
          return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context, color: "normal", children: ((_b = item.inventory) == null ? void 0 : _b.sku) || "-" });
        },
        disableHiding: true
      }),
      ...locations.map(
        (location) => helper.column({
          id: `location_${location.id}`,
          name: location.name,
          header: location.name,
          field: (context) => {
            const item = context.row.original;
            if (isProductVariant(item)) {
              return null;
            }
            const { isDisabled } = getIsDisabled(item);
            if (isDisabled) {
              return null;
            }
            return `variants.${item.variant_id}.inventory_items.${item.inventory_item_id}.locations.${location.id}`;
          },
          type: "togglable-number",
          cell: (context) => {
            const item = context.row.original;
            if (isProductVariant(item)) {
              return (0, import_jsx_runtime2.jsx)(DataGridReadonlyCell, { context });
            }
            const { isDisabled, item: disabledItem } = getIsDisabled(item);
            if (isDisabled) {
              return (0, import_jsx_runtime2.jsx)(
                DataGridDuplicateCell,
                {
                  duplicateOf: `variants.${disabledItem.id}.inventory_items.${item.inventory_item_id}.locations.${location.id}`,
                  children: ({ value }) => {
                    const { checked, quantity } = value;
                    return (0, import_jsx_runtime2.jsxs)("div", { className: "flex size-full items-center gap-x-2", children: [
                      (0, import_jsx_runtime2.jsx)(
                        Switch,
                        {
                          className: "shrink-0 cursor-not-allowed",
                          tabIndex: -1,
                          size: "small",
                          checked,
                          disabled: true
                        }
                      ),
                      (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-disabled flex size-full items-center justify-end", children: quantity })
                    ] });
                  }
                }
              );
            }
            return (0, import_jsx_runtime2.jsx)(
              DataGridTogglableNumberCell,
              {
                context,
                disabledToggleTooltip: t2(
                  "inventory.stock.disabledToggleTooltip"
                ),
                placeholder: t2("inventory.stock.placeholder")
              }
            );
          }
        })
      )
    ],
    [locations, getIsDisabled, t2]
  );
};
var LocationQuantitySchema = z.object({
  id: z.string().optional(),
  quantity: z.union([z.number(), z.string()]),
  checked: z.boolean(),
  disabledToggle: z.boolean()
});
var ProductStockLocationsSchema = z.record(LocationQuantitySchema);
var ProductStockInventoryItemSchema = z.object({
  locations: ProductStockLocationsSchema
});
var ProductStockVariantSchema = z.object({
  inventory_items: z.record(ProductStockInventoryItemSchema)
});
var ProductStockSchema = z.object({
  variants: z.record(ProductStockVariantSchema)
});
var ProductStockForm = ({
  variants,
  locations,
  onLoaded
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess, setCloseOnEscape } = useRouteModal();
  const prompt = usePrompt();
  (0, import_react3.useEffect)(() => {
    onLoaded();
  }, [onLoaded]);
  const [isPromptOpen, setIsPromptOpen] = (0, import_react3.useState)(false);
  const form = useForm({
    defaultValues: getDefaultValue(variants, locations),
    resolver: t(ProductStockSchema)
  });
  const initialValues = (0, import_react3.useRef)(getDefaultValue(variants, locations));
  const disabled = (0, import_react3.useMemo)(() => getDisabledInventoryRows(variants), [variants]);
  const columns = useProductStockColumns(locations, disabled);
  const { mutateAsync, isPending } = useBatchInventoryItemsLocationLevels();
  const onSubmit = form.handleSubmit(async (data) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
    const payload = {
      create: [],
      update: [],
      delete: [],
      force: true
    };
    for (const [variantId, variant] of Object.entries(data.variants)) {
      for (const [inventory_item_id, item] of Object.entries(
        variant.inventory_items
      )) {
        for (const [location_id, level] of Object.entries(item.locations)) {
          if (level.id) {
            const wasChecked = (_g = (_f = (_e = (_d = (_c = (_b = (_a = initialValues.current) == null ? void 0 : _a.variants) == null ? void 0 : _b[variantId]) == null ? void 0 : _c.inventory_items) == null ? void 0 : _d[inventory_item_id]) == null ? void 0 : _e.locations) == null ? void 0 : _f[location_id]) == null ? void 0 : _g.checked;
            if (wasChecked && !level.checked) {
              payload.delete.push(level.id);
            } else {
              const newQuantity = level.quantity !== "" ? castNumber(level.quantity) : 0;
              const originalQuantity = (_n = (_m = (_l = (_k = (_j = (_i = (_h = initialValues.current) == null ? void 0 : _h.variants) == null ? void 0 : _i[variantId]) == null ? void 0 : _j.inventory_items) == null ? void 0 : _k[inventory_item_id]) == null ? void 0 : _l.locations) == null ? void 0 : _m[location_id]) == null ? void 0 : _n.quantity;
              if (newQuantity !== originalQuantity) {
                payload.update.push({
                  inventory_item_id,
                  location_id,
                  stocked_quantity: newQuantity
                });
              }
            }
          }
          if (!level.id && level.quantity !== "") {
            payload.create.push({
              inventory_item_id,
              location_id,
              stocked_quantity: castNumber(level.quantity)
            });
          }
        }
      }
    }
    if (payload.delete.length > 0) {
      setIsPromptOpen(true);
      const confirm = await prompt({
        title: t2("general.areYouSure"),
        description: t2("inventory.stock.disablePrompt", {
          count: payload.delete.length
        }),
        confirmText: t2("actions.continue"),
        cancelText: t2("actions.cancel"),
        variant: "confirmation"
      });
      setIsPromptOpen(false);
      if (!confirm) {
        return;
      }
    }
    await mutateAsync(payload, {
      onSuccess: () => {
        toast.success(t2("inventory.stock.successToast"));
        handleSuccess();
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime3.jsxs)(KeyboundForm, { onSubmit, className: "flex size-full flex-col", children: [
    (0, import_jsx_runtime3.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime3.jsx)(RouteFocusModal.Body, { className: "flex flex-col overflow-hidden", children: (0, import_jsx_runtime3.jsx)(
      DataGrid,
      {
        state: form,
        columns,
        data: variants,
        getSubRows,
        onEditingChange: (editing) => setCloseOnEscape(!editing),
        disableInteractions: isPending || isPromptOpen,
        multiColumnSelection: true
      }
    ) }),
    (0, import_jsx_runtime3.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-end gap-2", children: [
      (0, import_jsx_runtime3.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { variant: "secondary", size: "small", type: "button", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime3.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
function getSubRows(row) {
  if (isProductVariantWithInventoryPivot(row)) {
    return row.inventory_items;
  }
}
function getDefaultValue(variants, locations) {
  return {
    variants: variants.reduce((variantAcc, variant) => {
      var _a;
      const inventoryItems = (_a = variant.inventory_items) == null ? void 0 : _a.reduce(
        (itemAcc, item) => {
          const locationsMap = locations.reduce((locationAcc, location) => {
            var _a2, _b;
            const level = (_b = (_a2 = item.inventory) == null ? void 0 : _a2.location_levels) == null ? void 0 : _b.find(
              (level2) => level2.location_id === location.id
            );
            locationAcc[location.id] = {
              id: level == null ? void 0 : level.id,
              quantity: (level == null ? void 0 : level.stocked_quantity) !== void 0 ? level == null ? void 0 : level.stocked_quantity : "",
              checked: !!level,
              disabledToggle: ((level == null ? void 0 : level.incoming_quantity) || 0) > 0 || ((level == null ? void 0 : level.reserved_quantity) || 0) > 0
            };
            return locationAcc;
          }, {});
          itemAcc[item.inventory_item_id] = { locations: locationsMap };
          return itemAcc;
        },
        {}
      );
      variantAcc[variant.id] = { inventory_items: inventoryItems || {} };
      return variantAcc;
    }, {})
  };
}
var ProductStock = () => {
  const { t: t2 } = useTranslation();
  const data = useLoaderData();
  const [isLoading, setIsLoading] = (0, import_react2.useState)(false);
  const timeoutRef = (0, import_react2.useRef)();
  (0, import_react2.useEffect)(() => {
    timeoutRef.current = setTimeout(() => {
      setIsLoading(true);
    }, 200);
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  const onLoaded = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsLoading(false);
  };
  return (0, import_jsx_runtime4.jsxs)("div", { children: [
    (0, import_jsx_runtime4.jsx)("div", { className: "fixed inset-x-0 top-0 z-50 h-1", children: (0, import_jsx_runtime4.jsx)(AnimatePresence, { children: isLoading ? (0, import_jsx_runtime4.jsx)(ProgressBar, { duration: 5 }) : null }) }),
    (0, import_jsx_runtime4.jsxs)(RouteFocusModal, { children: [
      (0, import_jsx_runtime4.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime4.jsx)("span", { className: "sr-only", children: t2("products.stock.heading") }) }),
      (0, import_jsx_runtime4.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime4.jsx)("span", { className: "sr-only", children: t2("products.stock.description") }) }),
      (0, import_jsx_runtime4.jsx)(import_react2.Suspense, { fallback: (0, import_jsx_runtime4.jsx)(ProductStockFallback, {}), children: (0, import_jsx_runtime4.jsx)(Await, { resolve: data.data, children: (data2) => {
        return (0, import_jsx_runtime4.jsx)(
          ProductStockForm,
          {
            variants: data2.variants,
            locations: data2.locations,
            onLoaded
          }
        );
      } }) })
    ] })
  ] });
};
var ProductStockFallback = () => {
  return (0, import_jsx_runtime4.jsx)("div", { className: "relative flex size-full flex-col items-center justify-center divide-y", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex size-full flex-col divide-y", children: [
    (0, import_jsx_runtime4.jsx)("div", { className: "px-4 py-2", children: (0, import_jsx_runtime4.jsx)(Skeleton, { className: "h-7 w-7" }) }),
    (0, import_jsx_runtime4.jsx)("div", { className: "flex-1 overflow-auto", children: (0, import_jsx_runtime4.jsx)(
      DataGridSkeleton,
      {
        columns: Array.from({ length: 10 })
      }
    ) }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "bg-ui-bg-base flex items-center justify-end gap-x-2 p-4", children: [
      (0, import_jsx_runtime4.jsx)(Skeleton, { className: "h-7 w-[59px]" }),
      (0, import_jsx_runtime4.jsx)(Skeleton, { className: "h-7 w-[46px]" })
    ] })
  ] }) });
};
export {
  ProductStock as Component,
  productStockLoader as loader
};
//# sourceMappingURL=product-stock-TZS75EG7-BM25QJCF.js.map
