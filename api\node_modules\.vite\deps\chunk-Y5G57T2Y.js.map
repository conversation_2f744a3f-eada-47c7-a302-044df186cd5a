{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-UAZEQNCO.mjs"], "sourcesContent": ["import {\n  taxRegionsQueryKeys\n} from \"./chunk-LDJKJLBJ.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/tax-rates.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar TAX_RATES_QUERY_KEY = \"tax_rates\";\nvar taxRatesQueryKeys = queryKeysFactory(TAX_RATES_QUERY_KEY);\nvar useTaxRate = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: taxRatesQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.taxRate.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useTaxRates = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.taxRate.list(query),\n    queryKey: taxRatesQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useUpdateTaxRate = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.taxRate.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: taxRatesQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreateTaxRate = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.taxRate.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteTaxRate = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.taxRate.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: taxRatesQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: taxRatesQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useTaxRate,\n  useTaxRates,\n  useUpdateTaxRate,\n  useCreateTaxRate,\n  useDeleteTaxRate\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB,iBAAiB,mBAAmB;AAC5D,IAAI,aAAa,CAAC,IAAI,OAAO,YAAY;AACvC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,kBAAkB,OAAO,EAAE;AAAA,IACrC,SAAS,YAAY,IAAI,MAAM,QAAQ,SAAS,IAAI,KAAK;AAAA,IACzD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,cAAc,CAAC,OAAO,YAAY;AACpC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,QAAQ,KAAK,KAAK;AAAA,IAC3C,UAAU,kBAAkB,KAAK,KAAK;AAAA,IACtC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,mBAAmB,CAAC,IAAI,YAAY;AACtC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,QAAQ,OAAO,IAAI,OAAO;AAAA,IAC7D,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB,EAAE,UAAU,kBAAkB,MAAM,EAAE,CAAC;AACrE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,OAAO,EAAE;AAAA,MACvC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,QAAQ,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mBAAmB,CAAC,YAAY;AAClC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,QAAQ,OAAO,OAAO;AAAA,IACzD,WAAW,CAAC,MAAM,WAAW,YAAY;AArD7C;AAsDM,kBAAY,kBAAkB,EAAE,UAAU,kBAAkB,MAAM,EAAE,CAAC;AACrE,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,QAAQ,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mBAAmB,CAAC,IAAI,YAAY;AACtC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,QAAQ,OAAO,EAAE;AAAA,IAC7C,WAAW,CAAC,MAAM,WAAW,YAAY;AAhE7C;AAiEM,kBAAY,kBAAkB,EAAE,UAAU,kBAAkB,MAAM,EAAE,CAAC;AACrE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,OAAO,EAAE;AAAA,MACvC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,QAAQ,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}