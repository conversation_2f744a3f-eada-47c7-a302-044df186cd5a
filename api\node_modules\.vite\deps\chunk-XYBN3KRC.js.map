{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-MNXC6Q4F.mjs"], "sourcesContent": ["// src/components/common/thumbnail/thumbnail.tsx\nimport { Photo } from \"@medusajs/icons\";\nimport { clx } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Thumbnail = ({ src, alt, size = \"base\" }) => {\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\n        \"bg-ui-bg-component border-ui-border-base flex items-center justify-center overflow-hidden rounded border\",\n        {\n          \"h-8 w-6\": size === \"base\",\n          \"h-5 w-4\": size === \"small\"\n        }\n      ),\n      children: src ? /* @__PURE__ */ jsx(\n        \"img\",\n        {\n          src,\n          alt,\n          className: \"h-full w-full object-cover object-center\"\n        }\n      ) : /* @__PURE__ */ jsx(Photo, { className: \"text-ui-fg-subtle\" })\n    }\n  );\n};\n\nexport {\n  Thumbnail\n};\n"], "mappings": ";;;;;;;;;;;;AAGA,yBAAoB;AACpB,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,OAAO,OAAO,MAAM;AAC/C,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA,QACtB;AAAA,MACF;AAAA,MACA,UAAU,UAAsB;AAAA,QAC9B;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,WAAW;AAAA,QACb;AAAA,MACF,QAAoB,wBAAI,OAAO,EAAE,WAAW,oBAAoB,CAAC;AAAA,IACnE;AAAA,EACF;AACF;", "names": []}