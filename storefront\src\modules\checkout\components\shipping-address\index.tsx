"use client"

import { getCustomLocations } from "@lib/data/vn-locations"
import { HttpTypes } from "@medusajs/types"
import Checkbox from "@modules/common/components/checkbox"
import Divider from "@modules/common/components/divider"
import Input from "@modules/common/components/input"
import SelectNative from "@modules/common/components/select"
import Typography from "components/ui/typography"
import { mapKeys } from "lodash"
import React, { useEffect, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { TDistrict, TProvince, TWard } from "types"
import { TCustomLocation } from "types/custom-locations"
import { REGEX_PHONE } from "utils/constant"
import { useCheckoutStore } from "zustand-store/useCheckoutStore"
import AddressSelect from "../address-select"
import CountrySelect from "../country-select"

type ShippingAddressProps = {
  customer: HttpTypes.StoreCustomer | null
  cart: HttpTypes.StoreCart | null
  checkedSameAsBilling: boolean
  onChangeSameAsBilling: () => void
  generateInvoice: boolean
  toggleGenerateInvoice: () => void
  locationList?: TCustomLocation[]
}

const ShippingAddress: React.FC<ShippingAddressProps> = ({
  customer,
  cart,
  checkedSameAsBilling,
  onChangeSameAsBilling,
  generateInvoice,
  toggleGenerateInvoice,
  locationList,
}) => {
  const { t } = useTranslation("checkout")
  const { shippingAddress, setShippingAddress, saveAddress, setSaveAddress } =
    useCheckoutStore()
  const [provinces, setProvinces] = useState<TProvince[]>([])
  const [districts, setDistricts] = useState<TDistrict[]>([])
  const [wards, setWards] = useState<TWard[]>([])
  const [fullNameInput, setFullNameInput] = useState("")

  const splitFullName = (fullName: string) => {
    const nameParts = fullName.trim().split(/\s+/)
    if (nameParts.length === 0) {
      return { first_name: "", last_name: "" }
    }
    const first_name = nameParts[nameParts.length - 1]
    const last_name = nameParts.slice(0, -1).join(" ")
    return { first_name, last_name }
  }

  // Initialize fullNameInput when component mounts or shipping address changes
  useEffect(() => {
    const { first_name, last_name } = shippingAddress.shipping_address
    const initialFullName =
      [last_name, first_name].filter(Boolean).join(" ") || ""
    setFullNameInput(initialFullName)
  }, [
    shippingAddress.shipping_address.first_name,
    shippingAddress.shipping_address.last_name,
  ])

  const countriesInRegion = useMemo(
    () => cart?.region?.countries?.map((c) => c.iso_2),
    [cart?.region]
  )

  const addressesInRegion = useMemo(
    () =>
      customer?.addresses?.filter(
        (a) => a.country_code && countriesInRegion?.includes(a.country_code)
      ),
    [customer?.addresses, countriesInRegion]
  )

  const [lastCartId, setLastCartId] = useState<string | undefined>(undefined)
  useEffect(() => {
    if (cart?.id && cart.id !== lastCartId) {
      setShippingAddress({
        shipping_address: {
          first_name: cart.shipping_address?.first_name || "",
          last_name: cart.shipping_address?.last_name || "",
          address_1: cart.shipping_address?.address_1 || "",
          company: cart.shipping_address?.company || "",
          postal_code: cart.shipping_address?.postal_code || "",
          province: cart.shipping_address?.province || "",
          country_code: cart.shipping_address?.country_code || "",
          phone: cart.shipping_address?.phone || "",
          metadata: {
            district: cart.shipping_address?.metadata?.district || "",
            ward: cart.shipping_address?.metadata?.ward || "",
            delivery_note: cart.shipping_address?.metadata?.delivery_note || "",
            is_generate_invoice:
              cart.shipping_address?.metadata?.is_generate_invoice || false,
            company_name: cart.shipping_address?.metadata?.company_name || "",
            company_tax_code:
              cart.shipping_address?.metadata?.company_tax_code || "",
            company_address:
              cart.shipping_address?.metadata?.company_address || "",
          },
        },
        email: cart.email || "",
      })
      setLastCartId(cart.id)
    }
  }, [cart?.id])

  // Auto-fill detection and sync
  useEffect(() => {
    const syncAutoFilledValues = () => {
      const inputs = document.querySelectorAll(
        'input[name^="shipping_address"], input[name="email"]'
      )
      let hasChanges = false
      const updates: any = {}

      inputs.forEach((input: any) => {
        if (input.value && input.name) {
          const currentValue = input.value.trim()
          if (!currentValue) return

          if (input.name === "email") {
            if (currentValue !== shippingAddress.email) {
              updates.email = currentValue
              hasChanges = true
            }
          } else if (input.name.startsWith("shipping_address.")) {
            const fieldName = input.name.replace("shipping_address.", "")
            const currentFieldValue =
              shippingAddress.shipping_address[
                fieldName as keyof typeof shippingAddress.shipping_address
              ]

            if (currentValue !== currentFieldValue) {
              if (!updates.shipping_address)
                updates.shipping_address = {
                  ...shippingAddress.shipping_address,
                }
              updates.shipping_address[fieldName] = currentValue
              hasChanges = true
            }
          }
        }
      })

      if (hasChanges) {
        console.log("🚀 ~ Auto-fill detected, syncing values:", updates)
        setShippingAddress(updates)
      }
    }

    // Check for auto-fill after a short delay
    const timeoutId = setTimeout(syncAutoFilledValues, 500)

    // Also check when inputs change (for immediate auto-fill)
    const handleInputChange = () => {
      setTimeout(syncAutoFilledValues, 100)
    }

    document.addEventListener("input", handleInputChange)

    return () => {
      clearTimeout(timeoutId)
      document.removeEventListener("input", handleInputChange)
    }
  }, [shippingAddress])

  useEffect(() => {
    const fetchProvinces = async () => {
      const provinceList = await getCustomLocations()
      setProvinces(provinceList)
    }
    fetchProvinces()
  }, [])

  useEffect(() => {
    if (!shippingAddress.shipping_address.province) {
      setDistricts([])
      setWards([])
      if (
        shippingAddress.shipping_address?.metadata?.district ||
        shippingAddress.shipping_address?.metadata?.ward
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              district: "",
              ward: "",
            },
          },
        })
      }
      return
    }

    const fetchDistricts = async () => {
      const provinceName = shippingAddress.shipping_address.province
      const province = provinces.find((p) => p.name === provinceName)
      const districtList = province?.districts || []
      setDistricts(districtList)
      if (
        districtList.length > 0 &&
        !districtList.some(
          (d) => d.name === shippingAddress.shipping_address?.metadata?.district
        )
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              district: "",
              ward: "",
            },
          },
        })
      }
    }

    fetchDistricts()
  }, [shippingAddress.shipping_address.province, provinces])

  useEffect(() => {
    if (!shippingAddress.shipping_address?.metadata?.district) {
      setWards([])
      if (shippingAddress.shipping_address?.metadata?.ward) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              ward: "",
            },
          },
        })
      }
      return
    }

    const fetchWards = async () => {
      const districtName = shippingAddress.shipping_address?.metadata?.district
      const province = provinces.find((p) =>
        p.districts.some((d) => d.name === districtName)
      )
      const district = province?.districts.find((d) => d.name === districtName)
      const wardList = district?.wards || []
      setWards(wardList)
      if (
        wardList.length > 0 &&
        !wardList.some(
          (w) => w.name === shippingAddress.shipping_address?.metadata?.ward
        )
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              ward: "",
            },
          },
        })
      }
    }

    fetchWards()
  }, [shippingAddress.shipping_address?.metadata?.district, provinces])

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target

    if (name === "full_name") {
      setFullNameInput(value)
    } else if (name === "email") {
      setShippingAddress({
        email: value,
      })
    } else if (name.startsWith("shipping_address.metadata.")) {
      const metadataField = name.replace("shipping_address.metadata.", "")
      setShippingAddress({
        shipping_address: {
          ...shippingAddress?.shipping_address,
          metadata: {
            ...shippingAddress?.shipping_address?.metadata,
            [metadataField]: value,
          },
        },
      })
    } else {
      const fieldName = name.replace("shipping_address.", "")
      setShippingAddress({
        shipping_address: {
          ...shippingAddress?.shipping_address,
          [fieldName]: value,
        },
      })
    }
  }

  const handleFullNameBlur = () => {
    const { first_name, last_name } = splitFullName(fullNameInput)
    setShippingAddress({
      shipping_address: {
        ...shippingAddress?.shipping_address,
        first_name,
        last_name,
      },
    })
  }

  const setFormAddress = (
    address?: HttpTypes.StoreCartAddress,
    email?: string
  ) => {
    setShippingAddress({
      shipping_address: {
        ...shippingAddress.shipping_address,
        ...(address && {
          first_name: address.first_name || "",
          last_name: address.last_name || "",
          address_1: address.address_1 || "",
          company: address.company || "",
          postal_code: address.postal_code || "",
          province: address.province || "",
          country_code: address.country_code || "",
          phone: address.phone || "",
          metadata: {
            district: address.metadata?.district || "",
            ward: address.metadata?.ward || "",
            delivery_note: address.metadata?.delivery_note || "",
            is_generate_invoice: address.metadata?.is_generate_invoice || false,
            company_name: address.metadata?.company_name || "",
            company_tax_code: address.metadata?.company_tax_code || "",
            company_address: address.metadata?.company_address || "",
          },
        }),
      },
      email: email !== undefined ? email : shippingAddress.email,
    })
  }

  const renderAddressSelect = () => {
    if (!customer || (addressesInRegion?.length || 0) === 0) return null

    return (
      <>
        <Typography variant="p" size="sm">
          {t("steps.delivery_address.greeting", { name: customer.first_name })}
        </Typography>
        <AddressSelect
          addresses={addressesInRegion || []}
          addressInput={
            mapKeys(
              shippingAddress.shipping_address,
              (_, key) => key
            ) as HttpTypes.StoreCartAddress
          }
          onSelect={setFormAddress}
        />
        <Divider />
      </>
    )
  }

  return (
    <>
      {renderAddressSelect()}
      <div className="flex flex-col gap-4 lg:gap-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:gap-6">
          <Input
            topLabel={t("steps.delivery_address.form.full_name")}
            name="full_name"
            autoComplete="name"
            value={fullNameInput}
            onChange={handleChange}
            onBlur={handleFullNameBlur}
            required
            data-testid="shipping-full-name-input"
          />
          <Input
            required
            type="tel"
            pattern={`${REGEX_PHONE}`}
            topLabel={t("steps.delivery_address.form.phone")}
            name="shipping_address.phone"
            autoComplete="tel"
            value={shippingAddress.shipping_address.phone || ""}
            onChange={handleChange}
          />
        </div>

        <Input
          required
          topLabel={t("steps.delivery_address.form.email")}
          name="email"
          autoComplete="email"
          value={shippingAddress.email || ""}
          onChange={handleChange}
        />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 md:grid-cols-3">
          <SelectNative
            topLabel={t("steps.delivery_address.form.province")}
            required
            name="shipping_address.province"
            value={shippingAddress.shipping_address.province || ""}
            onChange={handleChange}
            options={
              provinces.map((p) => ({
                value: p.name,
                label: p.name,
              })) || []
            }
          />
          <SelectNative
            topLabel={t("steps.delivery_address.form.district")}
            required
            name="shipping_address.metadata.district"
            value={shippingAddress.shipping_address?.metadata?.district || ""}
            onChange={handleChange}
            options={
              districts.map((d) => ({
                value: d.name,
                label: d.name,
              })) || []
            }
            disabled={!shippingAddress.shipping_address?.province}
          />
          <SelectNative
            topLabel={t("steps.delivery_address.form.ward")}
            required
            name="shipping_address.metadata.ward"
            value={shippingAddress.shipping_address?.metadata?.ward || ""}
            onChange={handleChange}
            options={
              wards.map((w) => ({
                value: w.name,
                label: w.name,
              })) || []
            }
            disabled={!shippingAddress.shipping_address?.metadata?.district}
          />
        </div>

        <Input
          topLabel={t("steps.delivery_address.form.address")}
          name="shipping_address.address_1"
          autoComplete="address-line1"
          value={shippingAddress.shipping_address.address_1 || ""}
          onChange={handleChange}
          required
          data-testid="shipping-address-input"
        />

        <Input
          topLabel={t("steps.delivery_address.form.note")}
          name="shipping_address.metadata.delivery_note"
          value={
            shippingAddress.shipping_address?.metadata?.delivery_note || ""
          }
          onChange={handleChange}
          data-testid="shipping-notes-input"
        />

        <div className="grid grid-cols-2 gap-6">
          <CountrySelect
            required
            name="shipping_address.country_code"
            autoComplete="country"
            region={cart?.region}
            value={shippingAddress.shipping_address.country_code || ""}
            onChange={handleChange}
          />
        </div>
      </div>
      <div className="!-mt-0 grid gap-3">
        <Checkbox
          label={t("steps.delivery_address.form.save_address")}
          name="save_address"
          checked={saveAddress}
          onChange={() => setSaveAddress(!saveAddress)}
          data-testid="save-address-checkbox"
        />

        <Checkbox
          label={t("steps.delivery_address.form.generate_invoice")}
          name="shipping_address.metadata.is_generate_invoice"
          checked={generateInvoice}
          onChange={() => {
            toggleGenerateInvoice()
            setShippingAddress({
              shipping_address: {
                ...shippingAddress.shipping_address,
                metadata: {
                  ...shippingAddress.shipping_address.metadata,
                  is_generate_invoice: !generateInvoice,
                  company_name: !generateInvoice
                    ? shippingAddress.shipping_address.metadata?.company_name
                    : "",
                  company_tax_code: !generateInvoice
                    ? shippingAddress.shipping_address.metadata
                        ?.company_tax_code
                    : "",
                  company_address: !generateInvoice
                    ? shippingAddress.shipping_address.metadata?.company_address
                    : "",
                },
              },
            })
          }}
          data-testid="generate-invoice-checkbox"
        />
      </div>

      {generateInvoice && (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
          <Input
            topLabel={t("steps.delivery_address.form.company_name")}
            name="shipping_address.metadata.company_name"
            value={
              shippingAddress.shipping_address?.metadata?.company_name || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-name-input"
          />
          <Input
            topLabel={t("steps.delivery_address.form.company_tax_code")}
            name="shipping_address.metadata.company_tax_code"
            value={
              shippingAddress.shipping_address?.metadata?.company_tax_code || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-tax-code-input"
          />
          <Input
            topLabel={t("steps.delivery_address.form.company_address")}
            name="shipping_address.metadata.company_address"
            value={
              shippingAddress.shipping_address?.metadata?.company_address || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-address-input"
          />
        </div>
      )}
    </>
  )
}

export default ShippingAddress
