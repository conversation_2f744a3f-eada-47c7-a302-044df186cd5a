import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-F6PXCY3N.mjs
var CUSTOMER_GROUPS_QUERY_KEY = "customer_groups";
var customerGroupsQueryKeys = queryKeysFactory(
  CUSTOMER_GROUPS_QUERY_KEY
);
var useCustomerGroup = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: customerGroupsQueryKeys.detail(id, query),
    queryFn: async () => sdk.admin.customerGroup.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useCustomerGroups = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.customerGroup.list(query),
    queryKey: customerGroupsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateCustomerGroup = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customerGroup.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateCustomerGroup = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customerGroup.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCustomerGroup = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.customerGroup.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCustomerGroupLazy = (options) => {
  return useMutation({
    mutationFn: ({ id }) => sdk.admin.customerGroup.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.detail(variables.id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddCustomersToGroup = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customerGroup.batchCustomers(id, { add: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: customersQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRemoveCustomersFromGroup = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customerGroup.batchCustomers(id, { remove: payload }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: customersQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var CUSTOMERS_QUERY_KEY = "customers";
var customersQueryKeys = queryKeysFactory(CUSTOMERS_QUERY_KEY);
var customerAddressesQueryKeys = queryKeysFactory(
  `${CUSTOMERS_QUERY_KEY}-addresses`
);
var useCustomer = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: customersQueryKeys.detail(id),
    queryFn: async () => sdk.admin.customer.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useCustomers = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.customer.list(query),
    queryKey: customersQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateCustomer = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customer.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateCustomer = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customer.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCustomer = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.customer.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: customersQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useBatchCustomerCustomerGroups = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customer.batchCustomerGroups(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: customerGroupsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customersQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: customersQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateCustomerAddress = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.customer.createAddress(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });
      queryClient.invalidateQueries({
        queryKey: customerAddressesQueryKeys.list(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCustomerAddress = (id, options) => {
  return useMutation({
    mutationFn: (addressId) => sdk.admin.customer.deleteAddress(id, addressId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customersQueryKeys.detail(id) });
      queryClient.invalidateQueries({
        queryKey: customerAddressesQueryKeys.list(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  customerGroupsQueryKeys,
  useCustomerGroup,
  useCustomerGroups,
  useCreateCustomerGroup,
  useUpdateCustomerGroup,
  useDeleteCustomerGroup,
  useDeleteCustomerGroupLazy,
  useAddCustomersToGroup,
  useRemoveCustomersFromGroup,
  useCustomer,
  useCustomers,
  useCreateCustomer,
  useUpdateCustomer,
  useDeleteCustomer,
  useBatchCustomerCustomerGroups,
  useCreateCustomerAddress,
  useDeleteCustomerAddress
};
//# sourceMappingURL=chunk-FSXJE4G7.js.map
