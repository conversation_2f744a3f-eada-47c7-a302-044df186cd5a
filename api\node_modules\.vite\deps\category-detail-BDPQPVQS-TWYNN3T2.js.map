{"version": 3, "sources": ["../../@medusajs/dashboard/dist/category-detail-BDPQPVQS.mjs"], "sourcesContent": ["import {\n  LinkButton\n} from \"./chunk-6WKBBTKM.mjs\";\nimport {\n  useDeleteProductCategoryAction\n} from \"./chunk-TP7N4YBP.mjs\";\nimport {\n  getCategoryChildren,\n  getCategoryPath,\n  getIsActiveProps,\n  getIsInternalProps\n} from \"./chunk-54IEHX46.mjs\";\nimport {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  Skeleton,\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport {\n  categoriesQueryKeys,\n  useProductCategory,\n  useUpdateProductCategoryProducts\n} from \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/categories/category-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CategoryDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { product_category } = useProductCategory(\n    id,\n    {\n      fields: \"name\"\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id)\n    }\n  );\n  if (!product_category) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: product_category.name });\n};\n\n// src/routes/categories/category-detail/category-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/categories/category-detail/components/category-general-section/category-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, Heading, StatusBadge, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar CategoryGeneralSection = ({\n  category\n}) => {\n  const { t } = useTranslation();\n  const activeProps = getIsActiveProps(category.is_active, t);\n  const internalProps = getIsInternalProps(category.is_internal, t);\n  const handleDelete = useDeleteProductCategoryAction(category);\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: category.name }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-4\", children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n          /* @__PURE__ */ jsx2(StatusBadge, { color: activeProps.color, children: activeProps.label }),\n          /* @__PURE__ */ jsx2(StatusBadge, { color: internalProps.color, children: internalProps.label })\n        ] }),\n        /* @__PURE__ */ jsx2(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t(\"actions.edit\"),\n                    icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                    to: \"edit\"\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t(\"actions.delete\"),\n                    icon: /* @__PURE__ */ jsx2(Trash, {}),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 gap-3 px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.description\") }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: category.description || \"-\" })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 gap-3 px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.handle\") }),\n      /* @__PURE__ */ jsxs(Text, { size: \"small\", leading: \"compact\", children: [\n        \"/\",\n        category.handle\n      ] })\n    ] })\n  ] });\n};\n\n// src/routes/categories/category-detail/components/category-organize-section/category-organize-section.tsx\nimport {\n  FolderIllustration,\n  PencilSquare as PencilSquare2,\n  TriangleRightMini\n} from \"@medusajs/icons\";\nimport { Badge, Container as Container2, Heading as Heading2, Text as Text2, Tooltip } from \"@medusajs/ui\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CategoryOrganizeSection = ({\n  category\n}) => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"categories.organize.header\") }),\n      /* @__PURE__ */ jsx3(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"categories.organize.action\"),\n                  icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n                  to: `organize`\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start gap-3 px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"categories.fields.path.label\") }),\n      /* @__PURE__ */ jsx3(PathDisplay, { category })\n    ] }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start gap-3 px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"categories.fields.children.label\") }),\n      /* @__PURE__ */ jsx3(ChildrenDisplay, { category })\n    ] })\n  ] });\n};\nvar PathDisplay = ({\n  category\n}) => {\n  const [expanded, setExpanded] = useState(false);\n  const { t } = useTranslation2();\n  const {\n    product_category: withParents,\n    isLoading,\n    isError,\n    error\n  } = useProductCategory(category.id, {\n    include_ancestors_tree: true,\n    fields: \"id,name,*parent_category\"\n  });\n  const chips = useMemo(() => getCategoryPath(withParents), [withParents]);\n  if (isLoading || !withParents) {\n    return /* @__PURE__ */ jsx3(Skeleton, { className: \"h-5 w-16\" });\n  }\n  if (isError) {\n    throw error;\n  }\n  if (!chips.length) {\n    return /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", children: \"-\" });\n  }\n  if (chips.length > 1 && !expanded) {\n    return /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-[20px_1fr] items-start gap-x-2\", children: [\n      /* @__PURE__ */ jsx3(FolderIllustration, {}),\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex w-full items-center gap-x-0.5 overflow-hidden\", children: [\n        /* @__PURE__ */ jsx3(Tooltip, { content: t(\"categories.fields.path.tooltip\"), children: /* @__PURE__ */ jsx3(\n          \"button\",\n          {\n            className: \"outline-none\",\n            type: \"button\",\n            onClick: () => setExpanded(true),\n            children: /* @__PURE__ */ jsx3(Text2, { size: \"xsmall\", leading: \"compact\", weight: \"plus\", children: \"...\" })\n          }\n        ) }),\n        /* @__PURE__ */ jsx3(\"div\", { className: \"flex size-[15px] shrink-0 items-center justify-center\", children: /* @__PURE__ */ jsx3(TriangleRightMini, {}) }),\n        /* @__PURE__ */ jsx3(\n          Text2,\n          {\n            size: \"xsmall\",\n            leading: \"compact\",\n            weight: \"plus\",\n            className: \"truncate\",\n            children: chips[chips.length - 1].name\n          }\n        )\n      ] })\n    ] });\n  }\n  if (chips.length > 1 && expanded) {\n    return /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-[20px_1fr] items-start gap-x-2\", children: [\n      /* @__PURE__ */ jsx3(FolderIllustration, {}),\n      /* @__PURE__ */ jsx3(\"div\", { className: \"gap- flex flex-wrap items-center gap-x-0.5 gap-y-1\", children: chips.map((chip, index) => {\n        return /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-0.5\", children: [\n          index === chips.length - 1 ? /* @__PURE__ */ jsx3(Text2, { size: \"xsmall\", leading: \"compact\", weight: \"plus\", children: chip.name }) : /* @__PURE__ */ jsx3(\n            LinkButton,\n            {\n              to: `/categories/${chip.id}`,\n              className: \"txt-compact-xsmall-plus text-ui-fg-subtle hover:text-ui-fg-base focus-visible:text-ui-fg-base\",\n              children: chip.name\n            }\n          ),\n          index < chips.length - 1 && /* @__PURE__ */ jsx3(TriangleRightMini, {})\n        ] }, chip.id);\n      }) })\n    ] });\n  }\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"grid grid-cols-1 items-start gap-x-2\", children: chips.map((chip, index) => /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-0.5\", children: [\n    /* @__PURE__ */ jsx3(Text2, { size: \"xsmall\", leading: \"compact\", weight: \"plus\", children: chip.name }),\n    index < chips.length - 1 && /* @__PURE__ */ jsx3(TriangleRightMini, {})\n  ] }, chip.id)) });\n};\nvar ChildrenDisplay = ({\n  category\n}) => {\n  const {\n    product_category: withChildren,\n    isLoading,\n    isError,\n    error\n  } = useProductCategory(category.id, {\n    include_descendants_tree: true,\n    fields: \"id,name,category_children\"\n  });\n  const chips = useMemo(() => getCategoryChildren(withChildren), [withChildren]);\n  if (isLoading || !withChildren) {\n    return /* @__PURE__ */ jsx3(Skeleton, { className: \"h-5 w-16\" });\n  }\n  if (isError) {\n    throw error;\n  }\n  if (!chips.length) {\n    return /* @__PURE__ */ jsx3(Text2, { size: \"small\", leading: \"compact\", children: \"-\" });\n  }\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex w-full flex-wrap gap-1\", children: chips.map((chip) => /* @__PURE__ */ jsx3(Badge, { size: \"2xsmall\", className: \"max-w-full\", asChild: true, children: /* @__PURE__ */ jsx3(Link, { to: `/categories/${chip.id}`, children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: chip.name }) }) }, chip.id)) });\n};\n\n// src/routes/categories/category-detail/components/category-product-section/category-product-section.tsx\nimport { PlusMini } from \"@medusajs/icons\";\nimport {\n  Checkbox,\n  CommandBar,\n  Container as Container3,\n  Heading as Heading3,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo as useMemo2, useState as useState2 } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar CategoryProductSection = ({\n  category\n}) => {\n  const { t } = useTranslation3();\n  const prompt = usePrompt();\n  const [selection, setSelection] = useState2({});\n  const { raw, searchParams } = useProductTableQuery({ pageSize: PAGE_SIZE });\n  const { products, count, isLoading, isError, error } = useProducts(\n    {\n      ...searchParams,\n      category_id: [category.id]\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useProductTableFilters([\"categories\"]);\n  const { table } = useDataTable({\n    data: products || [],\n    columns,\n    count,\n    getRowId: (original) => original.id,\n    pageSize: PAGE_SIZE,\n    enableRowSelection: true,\n    enablePagination: true,\n    rowSelection: {\n      state: selection,\n      updater: setSelection\n    }\n  });\n  const { mutateAsync } = useUpdateProductCategoryProducts(category.id);\n  const handleRemove = async () => {\n    const selected = Object.keys(selection);\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"categories.products.remove.confirmation\", {\n        count: selected.length\n      }),\n      confirmText: t(\"actions.remove\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      {\n        remove: selected\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"categories.products.remove.successToast\", {\n              count: selected.length\n            })\n          );\n          setSelection({});\n        },\n        onError: (error2) => {\n          toast.error(error2.message);\n        }\n      }\n    );\n  };\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Heading3, { level: \"h2\", children: t(\"products.domain\") }),\n      /* @__PURE__ */ jsx4(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.add\"),\n                  icon: /* @__PURE__ */ jsx4(PlusMini, {}),\n                  to: \"products\"\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx4(\n      _DataTable,\n      {\n        table,\n        filters,\n        columns,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        pageSize: PAGE_SIZE,\n        count,\n        navigateTo: (row) => `/products/${row.id}`,\n        isLoading,\n        queryObject: raw,\n        noRecords: {\n          message: t(\"categories.products.list.noRecordsMessage\")\n        }\n      }\n    ),\n    /* @__PURE__ */ jsx4(CommandBar, { open: !!Object.keys(selection).length, children: /* @__PURE__ */ jsxs3(CommandBar.Bar, { children: [\n      /* @__PURE__ */ jsx4(CommandBar.Value, { children: t(\"general.countSelected\", {\n        count: Object.keys(selection).length\n      }) }),\n      /* @__PURE__ */ jsx4(CommandBar.Seperator, {}),\n      /* @__PURE__ */ jsx4(\n        CommandBar.Command,\n        {\n          action: handleRemove,\n          label: t(\"actions.remove\"),\n          shortcut: \"r\"\n        }\n      )\n    ] }) })\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTableColumns();\n  return useMemo2(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx4(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx4(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [base]\n  );\n};\n\n// src/routes/categories/category-detail/category-detail.tsx\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar CategoryDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const { getWidgets } = useExtension();\n  const { product_category, isLoading, isError, error } = useProductCategory(\n    id,\n    void 0,\n    {\n      initialData\n    }\n  );\n  if (isLoading || !product_category) {\n    return /* @__PURE__ */ jsx5(\n      TwoColumnPageSkeleton,\n      {\n        mainSections: 2,\n        sidebarSections: 1,\n        showJSON: true,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs4(\n    TwoColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_category.details.after\"),\n        before: getWidgets(\"product_category.details.before\"),\n        sideAfter: getWidgets(\"product_category.details.side.after\"),\n        sideBefore: getWidgets(\"product_category.details.side.before\")\n      },\n      showJSON: true,\n      showMetadata: true,\n      data: product_category,\n      children: [\n        /* @__PURE__ */ jsxs4(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx5(CategoryGeneralSection, { category: product_category }),\n          /* @__PURE__ */ jsx5(CategoryProductSection, { category: product_category })\n        ] }),\n        /* @__PURE__ */ jsx5(TwoColumnPage.Sidebar, { children: /* @__PURE__ */ jsx5(CategoryOrganizeSection, { category: product_category }) })\n      ]\n    }\n  );\n};\n\n// src/routes/categories/category-detail/loader.ts\nvar categoryDetailQuery = (id) => ({\n  queryKey: categoriesQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.productCategory.retrieve(id)\n});\nvar categoryLoader = async ({ params }) => {\n  const id = params.id;\n  const query = categoryDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  CategoryDetailBreadcrumb as Breadcrumb,\n  CategoryDetail as Component,\n  categoryLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FA,yBAAoB;AA0BpB,IAAAA,sBAAkC;AAgElC,mBAAkC;AAGlC,IAAAC,sBAA2C;AAkJ3C,IAAAC,gBAA2D;AAE3D,IAAAC,sBAA2C;AAkK3C,IAAAA,sBAA2C;AAlZ3C,IAAI,2BAA2B,CAAC,UAAU;AACxC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,iBAAiB,IAAI;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF;AACA,MAAI,CAAC,kBAAkB;AACrB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,iBAAiB,KAAK,CAAC;AACxE;AAUA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAc,iBAAiB,SAAS,WAAW,CAAC;AAC1D,QAAM,gBAAgB,mBAAmB,SAAS,aAAa,CAAC;AAChE,QAAM,eAAe,+BAA+B,QAAQ;AAC5D,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,UAAU,SAAS,KAAK,CAAC;AAAA,UACzC,0BAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC9D,0BAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,cAC9D,oBAAAA,KAAK,aAAa,EAAE,OAAO,YAAY,OAAO,UAAU,YAAY,MAAM,CAAC;AAAA,cAC3E,oBAAAA,KAAK,aAAa,EAAE,OAAO,cAAc,OAAO,UAAU,cAAc,MAAM,CAAC;AAAA,QACjG,EAAE,CAAC;AAAA,YACa,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,cAAc;AAAA,oBACvB,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,IAAI;AAAA,kBACN;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,gBAAgB;AAAA,oBACzB,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,oBACpC,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,0BAAK,OAAO,EAAE,WAAW,sDAAsD,UAAU;AAAA,UACvF,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,oBAAoB,EAAE,CAAC;AAAA,UACnG,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,SAAS,eAAe,IAAI,CAAC;AAAA,IACzG,EAAE,CAAC;AAAA,QACa,0BAAK,OAAO,EAAE,WAAW,sDAAsD,UAAU;AAAA,UACvF,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,UAC9F,0BAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU;AAAA,QACxE;AAAA,QACA,SAAS;AAAA,MACX,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAaA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,4BAA4B,EAAE,CAAC;AAAA,UACzE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,4BAA4B;AAAA,kBACrC,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,kBAC5C,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,kEAAkE,UAAU;AAAA,UACpG,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,8BAA8B,EAAE,CAAC;AAAA,UAC9G,oBAAAA,KAAK,aAAa,EAAE,SAAS,CAAC;AAAA,IAChD,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,kEAAkE,UAAU;AAAA,UACpG,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,kCAAkC,EAAE,CAAC;AAAA,UAClH,oBAAAA,KAAK,iBAAiB,EAAE,SAAS,CAAC;AAAA,IACpD,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,cAAc,CAAC;AAAA,EACjB;AACF,MAAM;AACJ,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,KAAK;AAC9C,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB,SAAS,IAAI;AAAA,IAClC,wBAAwB;AAAA,IACxB,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAQ,sBAAQ,MAAM,gBAAgB,WAAW,GAAG,CAAC,WAAW,CAAC;AACvE,MAAI,aAAa,CAAC,aAAa;AAC7B,eAAuB,oBAAAA,KAAK,UAAU,EAAE,WAAW,WAAW,CAAC;AAAA,EACjE;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,MAAI,CAAC,MAAM,QAAQ;AACjB,eAAuB,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,IAAI,CAAC;AAAA,EACzF;AACA,MAAI,MAAM,SAAS,KAAK,CAAC,UAAU;AACjC,eAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,UAC1F,oBAAAC,KAAK,oBAAoB,CAAC,CAAC;AAAA,UAC3B,oBAAAD,MAAM,OAAO,EAAE,WAAW,sDAAsD,UAAU;AAAA,YACxF,oBAAAC,KAAK,SAAS,EAAE,SAAS,EAAE,gCAAgC,GAAG,cAA0B,oBAAAA;AAAA,UACtG;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,MAAM;AAAA,YACN,SAAS,MAAM,YAAY,IAAI;AAAA,YAC/B,cAA0B,oBAAAA,KAAK,MAAO,EAAE,MAAM,UAAU,SAAS,WAAW,QAAQ,QAAQ,UAAU,MAAM,CAAC;AAAA,UAC/G;AAAA,QACF,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,yDAAyD,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC,EAAE,CAAC;AAAA,YACzI,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,UAAU,MAAM,MAAM,SAAS,CAAC,EAAE;AAAA,UACpC;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL;AACA,MAAI,MAAM,SAAS,KAAK,UAAU;AAChC,eAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,UAC1F,oBAAAC,KAAK,oBAAoB,CAAC,CAAC;AAAA,UAC3B,oBAAAA,KAAK,OAAO,EAAE,WAAW,sDAAsD,UAAU,MAAM,IAAI,CAAC,MAAM,UAAU;AAClI,mBAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,UACxF,UAAU,MAAM,SAAS,QAAoB,oBAAAC,KAAK,MAAO,EAAE,MAAM,UAAU,SAAS,WAAW,QAAQ,QAAQ,UAAU,KAAK,KAAK,CAAC,QAAoB,oBAAAA;AAAA,YACtJ;AAAA,YACA;AAAA,cACE,IAAI,eAAe,KAAK,EAAE;AAAA,cAC1B,WAAW;AAAA,cACX,UAAU,KAAK;AAAA,YACjB;AAAA,UACF;AAAA,UACA,QAAQ,MAAM,SAAS,SAAqB,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,QACxE,EAAE,GAAG,KAAK,EAAE;AAAA,MACd,CAAC,EAAE,CAAC;AAAA,IACN,EAAE,CAAC;AAAA,EACL;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU,MAAM,IAAI,CAAC,MAAM,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,QAC9L,oBAAAC,KAAK,MAAO,EAAE,MAAM,UAAU,SAAS,WAAW,QAAQ,QAAQ,UAAU,KAAK,KAAK,CAAC;AAAA,IACvG,QAAQ,MAAM,SAAS,SAAqB,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,EACxE,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;AAClB;AACA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AACF,MAAM;AACJ,QAAM;AAAA,IACJ,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB,SAAS,IAAI;AAAA,IAClC,0BAA0B;AAAA,IAC1B,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAQ,sBAAQ,MAAM,oBAAoB,YAAY,GAAG,CAAC,YAAY,CAAC;AAC7E,MAAI,aAAa,CAAC,cAAc;AAC9B,eAAuB,oBAAAA,KAAK,UAAU,EAAE,WAAW,WAAW,CAAC;AAAA,EACjE;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,MAAI,CAAC,MAAM,QAAQ;AACjB,eAAuB,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,IAAI,CAAC;AAAA,EACzF;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU,MAAM,IAAI,CAAC,aAAyB,oBAAAA,KAAK,OAAO,EAAE,MAAM,WAAW,WAAW,cAAc,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,eAAe,KAAK,EAAE,IAAI,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;AACnX;AAiBA,IAAI,YAAY;AAChB,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,WAAW,YAAY,QAAI,cAAAC,UAAU,CAAC,CAAC;AAC9C,QAAM,EAAE,KAAK,aAAa,IAAI,qBAAqB,EAAE,UAAU,UAAU,CAAC;AAC1E,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACrD;AAAA,MACE,GAAG;AAAA,MACH,aAAa,CAAC,SAAS,EAAE;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,uBAAuB,CAAC,YAAY,CAAC;AACrD,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,IACA,UAAU,CAAC,aAAa,SAAS;AAAA,IACjC,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,QAAM,EAAE,YAAY,IAAI,iCAAiC,SAAS,EAAE;AACpE,QAAM,eAAe,YAAY;AAC/B,UAAM,WAAW,OAAO,KAAK,SAAS;AACtC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,2CAA2C;AAAA,QACxD,OAAO,SAAS;AAAA,MAClB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJ,EAAE,2CAA2C;AAAA,cAC3C,OAAO,SAAS;AAAA,YAClB,CAAC;AAAA,UACH;AACA,uBAAa,CAAC,CAAC;AAAA,QACjB;AAAA,QACA,SAAS,CAAC,WAAW;AACnB,gBAAM,MAAM,OAAO,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC;AAAA,UAC9D,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,aAAa;AAAA,kBACtB,UAAsB,oBAAAA,KAAK,UAAU,CAAC,CAAC;AAAA,kBACvC,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,QAAQ,aAAa,IAAI,EAAE;AAAA,QACxC;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,2CAA2C;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,QACgB,oBAAAA,KAAK,YAAY,EAAE,MAAM,CAAC,CAAC,OAAO,KAAK,SAAS,EAAE,QAAQ,cAA0B,oBAAAD,MAAM,WAAW,KAAK,EAAE,UAAU;AAAA,UACpH,oBAAAC,KAAK,WAAW,OAAO,EAAE,UAAU,EAAE,yBAAyB;AAAA,QAC5E,OAAO,OAAO,KAAK,SAAS,EAAE;AAAA,MAChC,CAAC,EAAE,CAAC;AAAA,UACY,oBAAAA,KAAK,WAAW,WAAW,CAAC,CAAC;AAAA,UAC7B,oBAAAA;AAAA,QACd,WAAW;AAAA,QACX;AAAA,UACE,QAAQ;AAAA,UACR,OAAO,EAAE,gBAAgB;AAAA,UACzB,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO,cAAAC;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAD;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,QAAM,EAAE,kBAAkB,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,IACA;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,CAAC,kBAAkB;AAClC,eAAuB,oBAAAE;AAAA,MACrB;AAAA,MACA;AAAA,QACE,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,gCAAgC;AAAA,QAClD,QAAQ,WAAW,iCAAiC;AAAA,QACpD,WAAW,WAAW,qCAAqC;AAAA,QAC3D,YAAY,WAAW,sCAAsC;AAAA,MAC/D;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAD,KAAK,wBAAwB,EAAE,UAAU,iBAAiB,CAAC;AAAA,cAC3D,oBAAAA,KAAK,wBAAwB,EAAE,UAAU,iBAAiB,CAAC;AAAA,QAC7E,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,cAAc,SAAS,EAAE,cAA0B,oBAAAA,KAAK,yBAAyB,EAAE,UAAU,iBAAiB,CAAC,EAAE,CAAC;AAAA,MACzI;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,sBAAsB,CAAC,QAAQ;AAAA,EACjC,UAAU,oBAAoB,OAAO,EAAE;AAAA,EACvC,SAAS,YAAY,IAAI,MAAM,gBAAgB,SAAS,EAAE;AAC5D;AACA,IAAI,iBAAiB,OAAO,EAAE,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,oBAAoB,EAAE;AACpC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "useState2", "jsxs3", "jsx4", "useMemo2", "jsx5", "jsxs4"]}