{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-DT7QVGFJ.mjs"], "sourcesContent": ["import {\n  DateCell\n} from \"./chunk-3OHUAQUF.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\n\n// src/hooks/table/columns/use-customer-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\n\n// src/components/table/table-cells/common/email-cell/email-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar EmailCell = ({ email }) => {\n  if (!email) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: email }) });\n};\nvar EmailHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: t(\"fields.email\") }) });\n};\n\n// src/components/table/table-cells/common/name-cell/name-cell.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar NameCell = ({ firstName, lastName }) => {\n  if (!firstName && !lastName) {\n    return /* @__PURE__ */ jsx2(PlaceholderCell, {});\n  }\n  const name = [firstName, lastName].filter(Boolean).join(\" \");\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: name }) });\n};\nvar NameHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: t(\"fields.name\") }) });\n};\n\n// src/components/table/table-cells/customer/account-cell/account-cell.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar AccountCell = ({ hasAccount }) => {\n  const { t } = useTranslation3();\n  const color = hasAccount ? \"green\" : \"orange\";\n  const text = hasAccount ? t(\"customers.fields.registered\") : t(\"customers.fields.guest\");\n  return /* @__PURE__ */ jsx3(StatusCell, { color, children: text });\n};\nvar AccountHeader = () => {\n  const { t } = useTranslation3();\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: t(\"fields.account\") }) });\n};\n\n// src/components/table/table-cells/customer/first-seen-cell/first-seen-cell.tsx\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar FirstSeenCell = ({ createdAt }) => {\n  if (!createdAt) {\n    return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx4(DateCell, { date: createdAt });\n};\nvar FirstSeenHeader = () => {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: t(\"fields.createdAt\") }) });\n};\n\n// src/hooks/table/columns/use-customer-table-columns.tsx\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCustomerTableColumns = () => {\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"email\", {\n        header: () => /* @__PURE__ */ jsx5(EmailHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx5(EmailCell, { email: getValue() })\n      }),\n      columnHelper.display({\n        id: \"name\",\n        header: () => /* @__PURE__ */ jsx5(NameHeader, {}),\n        cell: ({\n          row: {\n            original: { first_name, last_name }\n          }\n        }) => /* @__PURE__ */ jsx5(NameCell, { firstName: first_name, lastName: last_name })\n      }),\n      columnHelper.accessor(\"has_account\", {\n        header: () => /* @__PURE__ */ jsx5(AccountHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx5(AccountCell, { hasAccount: getValue() })\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: () => /* @__PURE__ */ jsx5(FirstSeenHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx5(FirstSeenCell, { createdAt: getValue() })\n      })\n    ],\n    []\n  );\n};\n\nexport {\n  useCustomerTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,mBAAwB;AAIxB,yBAAoB;AAcpB,IAAAA,sBAA4B;AAe5B,IAAAC,sBAA4B;AAc5B,IAAAC,sBAA4B;AAa5B,IAAAA,sBAA4B;AAvD5B,IAAI,YAAY,CAAC,EAAE,MAAM,MAAM;AAC7B,MAAI,CAAC,OAAO;AACV,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,mDAAmD,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,MAAM,CAAC,EAAE,CAAC;AACvL;AACA,IAAI,cAAc,MAAM;AACtB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACnL;AAKA,IAAI,WAAW,CAAC,EAAE,WAAW,SAAS,MAAM;AAC1C,MAAI,CAAC,aAAa,CAAC,UAAU;AAC3B,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,QAAM,OAAO,CAAC,WAAW,QAAQ,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC3D,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AACxL;AACA,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AACpL;AAKA,IAAI,cAAc,CAAC,EAAE,WAAW,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,QAAQ,aAAa,UAAU;AACrC,QAAM,OAAO,aAAa,EAAE,6BAA6B,IAAI,EAAE,wBAAwB;AACvF,aAAuB,oBAAAC,KAAK,YAAY,EAAE,OAAO,UAAU,KAAK,CAAC;AACnE;AACA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AACvL;AAKA,IAAI,gBAAgB,CAAC,EAAE,UAAU,MAAM;AACrC,MAAI,CAAC,WAAW;AACd,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,UAAU,CAAC;AAC3D;AACA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;AACzL;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,0BAA0B,MAAM;AAClC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,UAAsB,oBAAAC,KAAK,aAAa,CAAC,CAAC;AAAA,QAClD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,WAAW,EAAE,OAAO,SAAS,EAAE,CAAC;AAAA,MAC/E,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,CAAC,CAAC;AAAA,QACjD,MAAM,CAAC;AAAA,UACL,KAAK;AAAA,YACH,UAAU,EAAE,YAAY,UAAU;AAAA,UACpC;AAAA,QACF,UAAsB,oBAAAA,KAAK,UAAU,EAAE,WAAW,YAAY,UAAU,UAAU,CAAC;AAAA,MACrF,CAAC;AAAA,MACD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,UAAsB,oBAAAA,KAAK,eAAe,CAAC,CAAC;AAAA,QACpD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,aAAa,EAAE,YAAY,SAAS,EAAE,CAAC;AAAA,MACtF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,UAAsB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,QACtD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,eAAe,EAAE,WAAW,SAAS,EAAE,CAAC;AAAA,MACvF,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4", "jsx5"]}