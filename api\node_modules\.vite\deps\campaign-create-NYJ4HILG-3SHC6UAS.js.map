{"version": 3, "sources": ["../../@medusajs/dashboard/dist/campaign-create-NYJ4HILG.mjs"], "sourcesContent": ["import {\n  CreateCampaignForm\n} from \"./chunk-DXOEDXLQ.mjs\";\nimport \"./chunk-TDDYKNA2.mjs\";\nimport \"./chunk-F6ZOHZVB.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/campaign-create/campaign-create.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CampaignCreate = () => {\n  return /* @__PURE__ */ jsx(RouteFocusModal, { children: /* @__PURE__ */ jsx(CreateCampaignForm, {}) });\n};\nexport {\n  CampaignCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,yBAAoB;AACpB,IAAI,iBAAiB,MAAM;AACzB,aAAuB,wBAAI,iBAAiB,EAAE,cAA0B,wBAAI,oBAAoB,CAAC,CAAC,EAAE,CAAC;AACvG;", "names": []}