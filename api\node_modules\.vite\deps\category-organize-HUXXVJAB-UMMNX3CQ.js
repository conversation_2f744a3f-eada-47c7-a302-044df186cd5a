import {
  CategoryTree
} from "./chunk-DM3HXBNN.js";
import "./chunk-7ALMK6OQ.js";
import {
  RouteFocusModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-DPO7J5IQ.js";
import {
  categoriesQueryKeys,
  useProductCategories
} from "./chunk-ZJNBJBHK.js";
import "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Spinner,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  useMutation
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/category-organize-HUXXVJAB.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var QUERY = {
  fields: "id,name,parent_category_id,rank,*category_children",
  parent_category_id: "null",
  include_descendants_tree: true,
  limit: 9999
};
var OrganizeCategoryForm = () => {
  const {
    product_categories,
    isPending,
    isError,
    error: fetchError
  } = useProductCategories(QUERY);
  const [snapshot, setSnapshot] = (0, import_react.useState)([]);
  const { mutateAsync, isPending: isMutating } = useMutation({
    mutationFn: async ({
      value
    }) => {
      await sdk.admin.productCategory.update(value.id, {
        rank: value.rank ?? 0,
        parent_category_id: value.parent_category_id
      });
    },
    onMutate: async (update) => {
      await queryClient.cancelQueries({
        queryKey: categoriesQueryKeys.list(QUERY)
      });
      const previousValue = queryClient.getQueryData(categoriesQueryKeys.list(QUERY));
      const nextValue = {
        ...previousValue,
        product_categories: update.arr
      };
      queryClient.setQueryData(categoriesQueryKeys.list(QUERY), nextValue);
      return {
        previousValue
      };
    },
    onError: (error, _newValue, context) => {
      queryClient.setQueryData(
        categoriesQueryKeys.list(QUERY),
        context == null ? void 0 : context.previousValue
      );
      toast.error(error.message);
    },
    onSettled: async () => {
      await queryClient.invalidateQueries({
        queryKey: categoriesQueryKeys.all
      });
    }
  });
  const handleRankChange = async (value, arr) => {
    const val = {
      id: value.id,
      parent_category_id: value.parentId,
      rank: value.index
    };
    setSnapshot(arr);
    await mutateAsync({ value: val, arr });
  };
  const loading = isPending || isMutating;
  if (isError) {
    throw fetchError;
  }
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex h-full flex-col overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end", children: loading && (0, import_jsx_runtime.jsx)(Spinner, { className: "animate-spin" }) }) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "bg-ui-bg-subtle flex flex-1 flex-col overflow-y-auto", children: (0, import_jsx_runtime.jsx)(
      CategoryTree,
      {
        renderValue: (item) => item.name,
        value: loading ? snapshot : product_categories || [],
        onChange: handleRankChange
      }
    ) })
  ] });
};
var CategoryOrganize = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(OrganizeCategoryForm, {}) });
};
export {
  CategoryOrganize as Component
};
//# sourceMappingURL=category-organize-HUXXVJAB-UMMNX3CQ.js.map
