import {
  SidebarLink
} from "./chunk-TDOJLFIZ.js";
import "./chunk-73I4NEMG.js";
import {
  getFormattedCountry
} from "./chunk-WBEFJZV3.js";
import {
  PRODUCT_VARIANT_IDS_KEY
} from "./chunk-JKTOR6WW.js";
import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-KTCGZHOS.js";
import {
  PRODUCT_DETAIL_FIELDS
} from "./chunk-Q3ZU474T.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useDataTableDateColumns
} from "./chunk-WNW4SNUS.js";
import {
  DataTable,
  useDataTableDateFilters
} from "./chunk-EPUS4TBC.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-OJUGDQLS.js";
import "./chunk-XQBAAEQQ.js";
import "./chunk-XBF43SLF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-GE5OJZGS.js";
import "./chunk-YM3FRBGU.js";
import "./chunk-7M4ICL3D.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-XGFC5LFP.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-5NX546NL.js";
import "./chunk-GYCHI7LC.js";
import "./chunk-4XXECALA.js";
import "./chunk-XYBN3KRC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import "./chunk-W5LXSWLX.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-DPO7J5IQ.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import {
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import {
  productsQueryKeys,
  useDeleteProduct,
  useDeleteProductOption,
  useDeleteVariantLazy,
  useProduct,
  useProductVariants,
  useUpdateProduct
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLoaderData,
  useNavigate,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Buildings,
  Button,
  Channels,
  Checkbox,
  CommandBar,
  Component,
  Container,
  Heading,
  PencilSquare,
  Plus,
  ShoppingBag,
  StatusBadge,
  Text,
  ThumbnailBadge,
  Tooltip,
  Trash,
  clx,
  createDataTableColumnHelper,
  createDataTableCommandHelper,
  createDataTableFilterHelper,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-detail-TE5NUPNS.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var ProductDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { product } = useProduct(
    id,
    {
      fields: PRODUCT_DETAIL_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!product) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: product.title });
};
var productDetailQuery = (id) => ({
  queryKey: productsQueryKeys.detail(id, { fields: PRODUCT_DETAIL_FIELDS }),
  queryFn: async () => sdk.admin.product.retrieve(id, { fields: PRODUCT_DETAIL_FIELDS })
});
var productLoader = async ({ params }) => {
  const id = params.id;
  const query = productDetailQuery(id);
  const response = await queryClient.ensureQueryData({
    ...query,
    staleTime: 9e4
  });
  return response;
};
var ProductAttributeSection = ({
  product
}) => {
  const { t } = useTranslation();
  const { getDisplays } = useExtension();
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("products.attributes") }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "attributes",
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.height"), value: product.height }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.width"), value: product.width }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.length"), value: product.length }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.weight"), value: product.weight }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.midCode"), value: product.mid_code }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.hsCode"), value: product.hs_code }),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("fields.countryOfOrigin"),
        value: getFormattedCountry(product.origin_country)
      }
    ),
    getDisplays("product", "attributes").map((Component2, i) => {
      return (0, import_jsx_runtime2.jsx)(Component2, { data: product }, i);
    })
  ] });
};
var productStatusColor = (status) => {
  switch (status) {
    case "draft":
      return "grey";
    case "proposed":
      return "orange";
    case "published":
      return "green";
    case "rejected":
      return "red";
    default:
      return "grey";
  }
};
var ProductGeneralSection = ({
  product
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { getDisplays } = useExtension();
  const displays = getDisplays("product", "general");
  const { mutateAsync } = useDeleteProduct(product.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("products.deleteWarning", {
        title: product.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        navigate("..");
      }
    });
  };
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { children: product.title }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime3.jsx)(StatusBadge, { color: productStatusColor(product.status), children: t(`products.productStatus.${product.status}`) }),
        (0, import_jsx_runtime3.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    to: "edit",
                    icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {})
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    onClick: handleDelete,
                    icon: (0, import_jsx_runtime3.jsx)(Trash, {})
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime3.jsx)(SectionRow, { title: t("fields.description"), value: product.description }),
    (0, import_jsx_runtime3.jsx)(SectionRow, { title: t("fields.subtitle"), value: product.subtitle }),
    (0, import_jsx_runtime3.jsx)(SectionRow, { title: t("fields.handle"), value: `/${product.handle}` }),
    (0, import_jsx_runtime3.jsx)(
      SectionRow,
      {
        title: t("fields.discountable"),
        value: product.discountable ? t("fields.true") : t("fields.false")
      }
    ),
    displays.map((Component2, index) => {
      return (0, import_jsx_runtime3.jsx)(Component2, { data: product }, index);
    })
  ] });
};
var ProductMediaSection = ({ product }) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const [selection, setSelection] = (0, import_react.useState)({});
  const media = getMedia(product);
  const handleCheckedChange = (id) => {
    setSelection((prev) => {
      if (prev[id]) {
        const { [id]: _, ...rest } = prev;
        return rest;
      } else {
        return { ...prev, [id]: true };
      }
    });
  };
  const { mutateAsync } = useUpdateProduct(product.id);
  const handleDelete = async () => {
    const ids = Object.keys(selection);
    const includingThumbnail = ids.some(
      (id) => {
        var _a;
        return (_a = media.find((m) => m.id === id)) == null ? void 0 : _a.isThumbnail;
      }
    );
    const res = await prompt({
      title: t("general.areYouSure"),
      description: includingThumbnail ? t("products.media.deleteWarningWithThumbnail", {
        count: ids.length
      }) : t("products.media.deleteWarning", {
        count: ids.length
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    const mediaToKeep = product.images.filter((i) => !ids.includes(i.id)).map((i) => ({ url: i.url }));
    await mutateAsync(
      {
        images: mediaToKeep,
        thumbnail: includingThumbnail ? "" : void 0
      },
      {
        onSuccess: () => {
          setSelection({});
        }
      }
    );
  };
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("products.media.label") }),
      (0, import_jsx_runtime4.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "media?view=edit",
                  icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    media.length > 0 ? (0, import_jsx_runtime4.jsx)("div", { className: "grid grid-cols-[repeat(auto-fill,minmax(96px,1fr))] gap-4 px-6 py-4", children: media.map((i, index) => {
      const isSelected = selection[i.id];
      return (0, import_jsx_runtime4.jsxs)(
        "div",
        {
          className: "shadow-elevation-card-rest hover:shadow-elevation-card-hover transition-fg group relative aspect-square size-full cursor-pointer overflow-hidden rounded-[8px]",
          children: [
            (0, import_jsx_runtime4.jsx)(
              "div",
              {
                className: clx(
                  "transition-fg invisible absolute right-2 top-2 opacity-0 group-hover:visible group-hover:opacity-100",
                  {
                    "visible opacity-100": isSelected
                  }
                ),
                children: (0, import_jsx_runtime4.jsx)(
                  Checkbox,
                  {
                    checked: selection[i.id] || false,
                    onCheckedChange: () => handleCheckedChange(i.id)
                  }
                )
              }
            ),
            i.isThumbnail && (0, import_jsx_runtime4.jsx)("div", { className: "absolute left-2 top-2", children: (0, import_jsx_runtime4.jsx)(Tooltip, { content: t("fields.thumbnail"), children: (0, import_jsx_runtime4.jsx)(ThumbnailBadge, {}) }) }),
            (0, import_jsx_runtime4.jsx)(Link, { to: `media`, state: { curr: index }, children: (0, import_jsx_runtime4.jsx)(
              "img",
              {
                src: i.url,
                alt: `${product.title} image`,
                className: "size-full object-cover"
              }
            ) })
          ]
        },
        i.id
      );
    }) }) : (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col items-center gap-y-4 pb-8 pt-6", children: [
      (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col items-center", children: [
        (0, import_jsx_runtime4.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            weight: "plus",
            className: "text-ui-fg-subtle",
            children: t("products.media.emptyState.header")
          }
        ),
        (0, import_jsx_runtime4.jsx)(Text, { size: "small", className: "text-ui-fg-muted", children: t("products.media.emptyState.description") })
      ] }),
      (0, import_jsx_runtime4.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime4.jsx)(Link, { to: "media?view=edit", children: t("products.media.emptyState.action") }) })
    ] }),
    (0, import_jsx_runtime4.jsx)(CommandBar, { open: !!Object.keys(selection).length, children: (0, import_jsx_runtime4.jsxs)(CommandBar.Bar, { children: [
      (0, import_jsx_runtime4.jsx)(CommandBar.Value, { children: t("general.countSelected", {
        count: Object.keys(selection).length
      }) }),
      (0, import_jsx_runtime4.jsx)(CommandBar.Seperator, {}),
      (0, import_jsx_runtime4.jsx)(
        CommandBar.Command,
        {
          action: handleDelete,
          label: t("actions.delete"),
          shortcut: "d"
        }
      )
    ] }) })
  ] });
};
var getMedia = (product) => {
  const { images = [], thumbnail } = product;
  const media = images.map((image) => ({
    id: image.id,
    url: image.url,
    isThumbnail: image.url === thumbnail
  }));
  if (thumbnail && !media.some((mediaItem) => mediaItem.url === thumbnail)) {
    media.unshift({
      id: "img_thumbnail",
      url: thumbnail,
      isThumbnail: true
    });
  }
  return media;
};
var OptionActions = ({
  product,
  option
}) => {
  const { t } = useTranslation();
  const { mutateAsync } = useDeleteProductOption(product.id, option.id);
  const prompt = usePrompt();
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("products.options.deleteWarning", {
        title: option.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync();
  };
  return (0, import_jsx_runtime5.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              to: `options/${option.id}/edit`,
              icon: (0, import_jsx_runtime5.jsx)(PencilSquare, {})
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              onClick: handleDelete,
              icon: (0, import_jsx_runtime5.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var ProductOptionSection = ({
  product
}) => {
  var _a;
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t("products.options.header") }),
      (0, import_jsx_runtime5.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.create"),
                  to: "options/create",
                  icon: (0, import_jsx_runtime5.jsx)(Plus, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (_a = product.options) == null ? void 0 : _a.map((option) => {
      var _a2;
      return (0, import_jsx_runtime5.jsx)(
        SectionRow,
        {
          title: option.title,
          value: (_a2 = option.values) == null ? void 0 : _a2.map((val) => {
            return (0, import_jsx_runtime5.jsx)(
              Badge,
              {
                size: "2xsmall",
                className: "flex min-w-[20px] items-center justify-center",
                children: val.value
              },
              val.value
            );
          }),
          actions: (0, import_jsx_runtime5.jsx)(OptionActions, { product, option })
        },
        option.id
      );
    })
  ] });
};
var ProductOrganizationSection = ({
  product
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const { getDisplays } = useExtension();
  return (0, import_jsx_runtime6.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime6.jsx)(Heading, { level: "h2", children: t("products.organization.header") }),
      (0, import_jsx_runtime6.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "organization",
                  icon: (0, import_jsx_runtime6.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime6.jsx)(
      SectionRow,
      {
        title: t("fields.tags"),
        value: ((_a = product.tags) == null ? void 0 : _a.length) ? product.tags.map((tag) => (0, import_jsx_runtime6.jsx)(
          OrganizationTag,
          {
            label: tag.value,
            to: `/settings/product-tags/${tag.id}`
          },
          tag.id
        )) : void 0
      }
    ),
    (0, import_jsx_runtime6.jsx)(
      SectionRow,
      {
        title: t("fields.type"),
        value: product.type ? (0, import_jsx_runtime6.jsx)(
          OrganizationTag,
          {
            label: product.type.value,
            to: `/settings/product-types/${product.type_id}`
          }
        ) : void 0
      }
    ),
    (0, import_jsx_runtime6.jsx)(
      SectionRow,
      {
        title: t("fields.collection"),
        value: product.collection ? (0, import_jsx_runtime6.jsx)(
          OrganizationTag,
          {
            label: product.collection.title,
            to: `/collections/${product.collection.id}`
          }
        ) : void 0
      }
    ),
    (0, import_jsx_runtime6.jsx)(
      SectionRow,
      {
        title: t("fields.categories"),
        value: ((_b = product.categories) == null ? void 0 : _b.length) ? product.categories.map((pcat) => (0, import_jsx_runtime6.jsx)(
          OrganizationTag,
          {
            label: pcat.name,
            to: `/categories/${pcat.id}`
          },
          pcat.id
        )) : void 0
      }
    ),
    getDisplays("product", "organize").map((Component2, i) => {
      return (0, import_jsx_runtime6.jsx)(Component2, { data: product }, i);
    })
  ] });
};
var OrganizationTag = ({ label, to }) => {
  return (0, import_jsx_runtime6.jsx)(Tooltip, { content: label, children: (0, import_jsx_runtime6.jsx)(Badge, { size: "2xsmall", className: "block w-fit truncate", asChild: true, children: (0, import_jsx_runtime6.jsx)(Link, { to, children: label }) }) });
};
var ProductSalesChannelSection = ({
  product
}) => {
  var _a;
  const { count } = useSalesChannels();
  const { t } = useTranslation();
  const availableInSalesChannels = ((_a = product.sales_channels) == null ? void 0 : _a.map((sc) => ({
    id: sc.id,
    name: sc.name
  }))) ?? [];
  const firstChannels = availableInSalesChannels.slice(0, 3);
  const restChannels = availableInSalesChannels.slice(3);
  return (0, import_jsx_runtime7.jsxs)(Container, { className: "flex flex-col gap-y-4 px-6 py-4", children: [
    (0, import_jsx_runtime7.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime7.jsx)(Heading, { level: "h2", children: t("fields.sales_channels") }),
      (0, import_jsx_runtime7.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "sales-channels",
                  icon: (0, import_jsx_runtime7.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime7.jsxs)("div", { className: "grid grid-cols-[28px_1fr] items-center gap-x-3", children: [
      (0, import_jsx_runtime7.jsx)("div", { className: "bg-ui-bg-base shadow-borders-base flex size-7 items-center justify-center rounded-md", children: (0, import_jsx_runtime7.jsx)("div", { className: "bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]", children: (0, import_jsx_runtime7.jsx)(Channels, { className: "text-ui-fg-subtle" }) }) }),
      availableInSalesChannels.length > 0 ? (0, import_jsx_runtime7.jsxs)("div", { className: "flex items-center gap-x-1", children: [
        (0, import_jsx_runtime7.jsx)(Text, { size: "small", leading: "compact", children: firstChannels.map((sc) => sc.name).join(", ") }),
        restChannels.length > 0 && (0, import_jsx_runtime7.jsx)(
          Tooltip,
          {
            content: (0, import_jsx_runtime7.jsx)("ul", { children: restChannels.map((sc) => (0, import_jsx_runtime7.jsx)("li", { children: sc.name }, sc.id)) }),
            children: (0, import_jsx_runtime7.jsx)(
              Text,
              {
                size: "small",
                leading: "compact",
                className: "text-ui-fg-subtle",
                children: `+${restChannels.length}`
              }
            )
          }
        )
      ] }) : (0, import_jsx_runtime7.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: t("products.noSalesChannels") })
    ] }),
    (0, import_jsx_runtime7.jsx)("div", { children: (0, import_jsx_runtime7.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", leading: "compact", children: (0, import_jsx_runtime7.jsx)(
      Trans,
      {
        i18nKey: "sales_channels.availableIn",
        values: {
          x: availableInSalesChannels.length,
          y: count ?? 0
        },
        components: [
          (0, import_jsx_runtime7.jsx)(
            "span",
            {
              className: "text-ui-fg-base txt-compact-medium-plus"
            },
            "x"
          ),
          (0, import_jsx_runtime7.jsx)(
            "span",
            {
              className: "text-ui-fg-base txt-compact-medium-plus"
            },
            "y"
          )
        ]
      }
    ) }) })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "pv";
var ProductVariantSection = ({
  product
}) => {
  const { t } = useTranslation();
  const {
    q,
    order,
    offset,
    allow_backorder,
    manage_inventory,
    created_at,
    updated_at
  } = useQueryParams(
    [
      "q",
      "order",
      "offset",
      "manage_inventory",
      "allow_backorder",
      "created_at",
      "updated_at"
    ],
    PREFIX
  );
  const columns = useColumns(product);
  const filters = useFilters();
  const commands = useCommands();
  const { variants, count, isPending, isError, error } = useProductVariants(
    product.id,
    {
      q,
      order: order ? order : "variant_rank",
      offset: offset ? parseInt(offset) : void 0,
      limit: PAGE_SIZE,
      allow_backorder: allow_backorder ? JSON.parse(allow_backorder) : void 0,
      manage_inventory: manage_inventory ? JSON.parse(manage_inventory) : void 0,
      created_at: created_at ? JSON.parse(created_at) : void 0,
      updated_at: updated_at ? JSON.parse(updated_at) : void 0,
      fields: "title,sku,*options,created_at,updated_at,*inventory_items.inventory.location_levels,inventory_quantity,manage_inventory"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime8.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime8.jsx)(
    DataTable,
    {
      data: variants,
      columns,
      filters,
      rowCount: count,
      getRowId: (row) => row.id,
      rowHref: (row) => `/products/${product.id}/variants/${row.id}`,
      pageSize: PAGE_SIZE,
      isLoading: isPending,
      heading: t("products.variants.header"),
      emptyState: {
        empty: {
          heading: t("products.variants.empty.heading"),
          description: t("products.variants.empty.description")
        },
        filtered: {
          heading: t("products.variants.filtered.heading"),
          description: t("products.variants.filtered.description")
        }
      },
      action: {
        label: t("actions.create"),
        to: `variants/create`
      },
      actionMenu: {
        groups: [
          {
            actions: [
              {
                label: t("products.editPrices"),
                to: `prices`,
                icon: (0, import_jsx_runtime8.jsx)(PencilSquare, {})
              },
              {
                label: t("inventory.stock.action"),
                to: `stock`,
                icon: (0, import_jsx_runtime8.jsx)(Buildings, {})
              }
            ]
          }
        ]
      },
      commands,
      prefix: PREFIX
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = (product) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteVariantLazy(product.id);
  const prompt = usePrompt();
  const [searchParams] = useSearchParams();
  const tableSearchParams = (0, import_react2.useMemo)(() => {
    const filtered = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith(`${PREFIX}_`)) {
        filtered.append(key, value);
      }
    }
    return filtered;
  }, [searchParams]);
  const dateColumns = useDataTableDateColumns();
  const handleDelete = (0, import_react2.useCallback)(
    async (id, title) => {
      const res = await prompt({
        title: t("general.areYouSure"),
        description: t("products.deleteVariantWarning", {
          title
        }),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!res) {
        return;
      }
      await mutateAsync({ variantId: id });
    },
    [mutateAsync, prompt, t]
  );
  const optionColumns = (0, import_react2.useMemo)(() => {
    if (!(product == null ? void 0 : product.options)) {
      return [];
    }
    return product.options.map((option) => {
      return columnHelper.display({
        id: option.id,
        header: option.title,
        cell: ({ row }) => {
          var _a;
          const variantOpt = (_a = row.original.options) == null ? void 0 : _a.find(
            (opt) => opt.option_id === option.id
          );
          if (!variantOpt) {
            return (0, import_jsx_runtime8.jsx)("span", { className: "text-ui-fg-muted", children: "-" });
          }
          return (0, import_jsx_runtime8.jsx)("div", { className: "flex items-center", children: (0, import_jsx_runtime8.jsx)(Tooltip, { content: variantOpt.value, children: (0, import_jsx_runtime8.jsx)(
            Badge,
            {
              size: "2xsmall",
              title: variantOpt.value,
              className: "inline-flex min-w-[20px] max-w-[140px] items-center justify-center overflow-hidden truncate",
              children: variantOpt.value
            }
          ) }) });
        }
      });
    });
  }, [product]);
  const getActions = (0, import_react2.useCallback)(
    (ctx) => {
      var _a, _b;
      const variant = ctx.row.original;
      const mainActions = [
        {
          icon: (0, import_jsx_runtime8.jsx)(PencilSquare, {}),
          label: t("actions.edit"),
          onClick: (row) => {
            navigate(
              `edit-variant?variant_id=${row.row.original.id}&${tableSearchParams.toString()}`,
              {
                state: {
                  restore_params: tableSearchParams.toString()
                }
              }
            );
          }
        }
      ];
      const secondaryActions = [
        {
          icon: (0, import_jsx_runtime8.jsx)(Trash, {}),
          label: t("actions.delete"),
          onClick: () => handleDelete(variant.id, variant.title)
        }
      ];
      const inventoryItemsCount = ((_a = variant.inventory_items) == null ? void 0 : _a.length) || 0;
      switch (inventoryItemsCount) {
        case 0:
          break;
        case 1: {
          const inventoryItemLink = `/inventory/${variant.inventory_items[0].inventory.id}`;
          mainActions.push({
            label: t("products.variant.inventory.actions.inventoryItems"),
            onClick: () => {
              navigate(inventoryItemLink);
            },
            icon: (0, import_jsx_runtime8.jsx)(Buildings, {})
          });
          break;
        }
        default: {
          const ids = (_b = variant.inventory_items) == null ? void 0 : _b.map((i) => {
            var _a2;
            return (_a2 = i.inventory) == null ? void 0 : _a2.id;
          });
          if (!ids || ids.length === 0) {
            break;
          }
          const inventoryKitLink = `/inventory?${new URLSearchParams({
            id: ids.join(",")
          }).toString()}`;
          mainActions.push({
            label: t("products.variant.inventory.actions.inventoryKit"),
            onClick: () => {
              navigate(inventoryKitLink);
            },
            icon: (0, import_jsx_runtime8.jsx)(Component, {})
          });
        }
      }
      return [mainActions, secondaryActions];
    },
    [handleDelete, navigate, t, tableSearchParams]
  );
  const getInventory = (0, import_react2.useCallback)(
    (variant) => {
      var _a;
      const castVariant = variant;
      if (!variant.manage_inventory) {
        return {
          text: t("products.variant.inventory.notManaged"),
          hasInventoryKit: false,
          notManaged: true
        };
      }
      const quantity = variant.inventory_quantity;
      const inventoryItems = (_a = castVariant.inventory_items) == null ? void 0 : _a.map((i) => i.inventory).filter(Boolean);
      const hasInventoryKit = inventoryItems.length > 1;
      const locations = {};
      inventoryItems.forEach((i) => {
        var _a2;
        (_a2 = i.location_levels) == null ? void 0 : _a2.forEach((l) => {
          locations[l.id] = true;
        });
      });
      const locationCount = Object.keys(locations).length;
      const text = hasInventoryKit ? t("products.variant.tableItemAvailable", {
        availableCount: quantity
      }) : t("products.variant.tableItem", {
        availableCount: quantity,
        locationCount,
        count: locationCount
      });
      return { text, hasInventoryKit, quantity, notManaged: false };
    },
    [t]
  );
  return (0, import_react2.useMemo)(() => {
    return [
      columnHelper.accessor("title", {
        header: t("fields.title"),
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("sku", {
        header: t("fields.sku"),
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      ...optionColumns,
      columnHelper.display({
        id: "inventory",
        header: t("fields.inventory"),
        cell: ({ row }) => {
          const { text, hasInventoryKit, quantity, notManaged } = getInventory(
            row.original
          );
          return (0, import_jsx_runtime8.jsx)(Tooltip, { content: text, children: (0, import_jsx_runtime8.jsxs)("div", { className: "flex h-full w-full items-center gap-2 overflow-hidden", children: [
            hasInventoryKit && (0, import_jsx_runtime8.jsx)(Component, {}),
            (0, import_jsx_runtime8.jsx)(
              "span",
              {
                className: clx("truncate", {
                  "text-ui-fg-error": !quantity && !notManaged
                }),
                children: text
              }
            )
          ] }) });
        },
        maxSize: 250
      }),
      ...dateColumns,
      columnHelper.action({
        actions: getActions
      })
    ];
  }, [t, optionColumns, dateColumns, getActions, getInventory]);
};
var filterHelper = createDataTableFilterHelper();
var useFilters = () => {
  const { t } = useTranslation();
  const dateFilters = useDataTableDateFilters();
  return (0, import_react2.useMemo)(() => {
    return [
      filterHelper.accessor("allow_backorder", {
        type: "radio",
        label: t("fields.allowBackorder"),
        options: [
          { label: t("filters.radio.yes"), value: "true" },
          { label: t("filters.radio.no"), value: "false" }
        ]
      }),
      filterHelper.accessor("manage_inventory", {
        type: "radio",
        label: t("fields.manageInventory"),
        options: [
          { label: t("filters.radio.yes"), value: "true" },
          { label: t("filters.radio.no"), value: "false" }
        ]
      }),
      ...dateFilters
    ];
  }, [t, dateFilters]);
};
var commandHelper = createDataTableCommandHelper();
var useCommands = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  return [
    commandHelper.command({
      label: t("inventory.stock.action"),
      shortcut: "i",
      action: async (selection) => {
        navigate(
          `stock?${PRODUCT_VARIANT_IDS_KEY}=${Object.keys(selection).join(",")}`
        );
      }
    })
  ];
};
var ProductShippingProfileSection = ({
  product
}) => {
  const { t } = useTranslation();
  const shippingProfile = product.shipping_profile;
  return (0, import_jsx_runtime9.jsxs)(Container, { className: "p-0", children: [
    (0, import_jsx_runtime9.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime9.jsx)(Heading, { level: "h2", children: t("products.shippingProfile.header") }),
      (0, import_jsx_runtime9.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "shipping-profile",
                  icon: (0, import_jsx_runtime9.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    shippingProfile && (0, import_jsx_runtime9.jsx)(
      SidebarLink,
      {
        to: `/settings/locations/shipping-profiles/${shippingProfile.id}`,
        labelKey: shippingProfile.name,
        descriptionKey: shippingProfile.type,
        icon: (0, import_jsx_runtime9.jsx)(ShoppingBag, {})
      }
    )
  ] });
};
var ProductDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const { product, isLoading, isError, error } = useProduct(
    id,
    { fields: PRODUCT_DETAIL_FIELDS },
    {
      initialData
    }
  );
  const { getWidgets } = useExtension();
  const after = getWidgets("product.details.after");
  const before = getWidgets("product.details.before");
  const sideAfter = getWidgets("product.details.side.after");
  const sideBefore = getWidgets("product.details.side.before");
  if (isLoading || !product) {
    return (0, import_jsx_runtime10.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 4,
        sidebarSections: 3,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime10.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after,
        before,
        sideAfter,
        sideBefore
      },
      showJSON: true,
      showMetadata: true,
      data: product,
      children: [
        (0, import_jsx_runtime10.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime10.jsx)(ProductGeneralSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductMediaSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductOptionSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductVariantSection, { product })
        ] }),
        (0, import_jsx_runtime10.jsxs)(TwoColumnPage.Sidebar, { children: [
          (0, import_jsx_runtime10.jsx)(ProductSalesChannelSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductShippingProfileSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductOrganizationSection, { product }),
          (0, import_jsx_runtime10.jsx)(ProductAttributeSection, { product })
        ] })
      ]
    }
  );
};
export {
  ProductDetailBreadcrumb as Breadcrumb,
  ProductDetail as Component,
  productLoader as loader
};
//# sourceMappingURL=product-detail-TE5NUPNS-K5Q4ZNQR.js.map
