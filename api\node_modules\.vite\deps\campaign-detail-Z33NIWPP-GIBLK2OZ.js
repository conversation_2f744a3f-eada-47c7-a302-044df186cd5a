import {
  usePromotionTableColumns,
  usePromotionTableQuery
} from "./chunk-KDZBYWB7.js";
import {
  DateRangeDisplay
} from "./chunk-USN2624C.js";
import "./chunk-KFESVZ55.js";
import "./chunk-7HUCBNCQ.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  TwoColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import {
  usePromotionTableFilters
} from "./chunk-ZIXJCBL3.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-5NX546NL.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import {
  isAfter,
  isBefore
} from "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import {
  campaignsQueryKeys,
  useAddOrRemoveCampaignPromotions,
  useCampaign,
  useDeleteCampaign,
  usePromotions
} from "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link,
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Button,
  ChartPie,
  Checkbox,
  Container,
  CurrencyDollar,
  Heading,
  PencilSquare,
  StatusBadge,
  Text,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/campaign-detail-Z33NIWPP.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var CAMPAIGN_DETAIL_FIELDS = "+promotions.id";
var CampaignDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { campaign } = useCampaign(
    id,
    {
      fields: CAMPAIGN_DETAIL_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!campaign) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: campaign.name });
};
var CampaignBudget = ({ campaign }) => {
  var _a, _b, _c;
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex flex-col gap-y-4 px-6 py-4", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex justify-between", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex-grow", children: [
        (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-base shadow-borders-base float-left flex size-7 items-center justify-center rounded-md", children: (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]", children: (0, import_jsx_runtime2.jsx)(ChartPie, { className: "text-ui-fg-subtle" }) }) }),
        (0, import_jsx_runtime2.jsx)(
          Heading,
          {
            className: "text-ui-fg-subtle ml-10 mt-[1.5px] font-normal",
            level: "h3",
            children: t("campaigns.fields.budget_limit")
          }
        )
      ] }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: `edit-budget`
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { children: (0, import_jsx_runtime2.jsx)(
      Text,
      {
        className: "text-ui-fg-subtle border-ui-border-strong border-l-4 pl-3",
        size: "small",
        leading: "compact",
        children: (0, import_jsx_runtime2.jsx)(
          Trans,
          {
            i18nKey: "campaigns.totalSpend",
            values: {
              amount: ((_a = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _a.limit) || "no limit",
              currency: ((_b = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _b.type) === "spend" && (campaign == null ? void 0 : campaign.budget.limit) ? (_c = campaign.budget) == null ? void 0 : _c.currency_code : ""
            },
            components: [
              (0, import_jsx_runtime2.jsx)(
                "span",
                {
                  className: "text-ui-fg-base txt-compact-medium-plus text-lg"
                },
                "amount"
              ),
              (0, import_jsx_runtime2.jsx)(
                "span",
                {
                  className: "text-ui-fg-base txt-compact-medium-plus text-lg"
                },
                "currency"
              )
            ]
          }
        )
      }
    ) })
  ] });
};
function campaignStatus(campaign) {
  if (campaign.ends_at) {
    if (isBefore(new Date(campaign.ends_at), /* @__PURE__ */ new Date())) {
      return "expired";
    }
  }
  if (campaign.starts_at) {
    if (isAfter(new Date(campaign.starts_at), /* @__PURE__ */ new Date())) {
      return "scheduled";
    }
  }
  return "active";
}
var statusColor = (status) => {
  switch (status) {
    case "expired":
      return "red";
    case "scheduled":
      return "orange";
    case "active":
      return "green";
    default:
      return "grey";
  }
};
var CampaignGeneralSection = ({
  campaign
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteCampaign(campaign.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("campaigns.delete.title"),
      description: t("campaigns.delete.description", {
        name: campaign.name
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("campaigns.delete.successToast", {
            name: campaign.name
          })
        );
        navigate("/campaigns", { replace: true });
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  const status = campaignStatus(campaign);
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { children: campaign.name }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime3.jsx)(StatusBadge, { color: statusColor(status), children: t(`campaigns.status.${status}`) }),
        (0, import_jsx_runtime3.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {}),
                    label: t("actions.edit"),
                    to: `/campaigns/${campaign.id}/edit`
                  }
                ]
              },
              {
                actions: [
                  {
                    icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
                    label: t("actions.delete"),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("campaigns.fields.identifier") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: campaign.campaign_identifier })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.description") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: campaign.description || "-" })
    ] }),
    (campaign == null ? void 0 : campaign.budget) && campaign.budget.type === "spend" && (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.currency") }),
      (0, import_jsx_runtime3.jsxs)("div", { children: [
        (0, import_jsx_runtime3.jsx)(Badge, { size: "xsmall", children: campaign == null ? void 0 : campaign.budget.currency_code }),
        (0, import_jsx_runtime3.jsx)(Text, { className: "inline pl-3", size: "small", leading: "compact", children: (_b = currencies[(_a = campaign == null ? void 0 : campaign.budget.currency_code) == null ? void 0 : _a.toUpperCase()]) == null ? void 0 : _b.name })
      ] })
    ] })
  ] });
};
var PAGE_SIZE = 10;
var CampaignPromotionSection = ({
  campaign
}) => {
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { t } = useTranslation();
  const prompt = usePrompt();
  const columns = useColumns();
  const filters = usePromotionTableFilters();
  const { searchParams, raw } = usePromotionTableQuery({ pageSize: PAGE_SIZE });
  const { promotions, count, isLoading, isError, error } = usePromotions({
    ...searchParams,
    campaign_id: campaign.id
  });
  const { table } = useDataTable({
    data: promotions ?? [],
    columns,
    count,
    getRowId: (row) => row.id,
    enablePagination: true,
    enableRowSelection: true,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    meta: { campaignId: campaign.id }
  });
  if (isError) {
    throw error;
  }
  const { mutateAsync } = useAddOrRemoveCampaignPromotions(campaign.id);
  const handleRemove = async () => {
    const keys = Object.keys(rowSelection);
    const res = await prompt({
      title: t("campaigns.promotions.remove.title", { count: keys.length }),
      description: t("campaigns.promotions.remove.description", {
        count: keys.length
      }),
      confirmText: t("actions.continue"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(
      { remove: keys },
      { onSuccess: () => setRowSelection({}) }
    );
  };
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("promotions.domain") }),
      (0, import_jsx_runtime4.jsx)(Link, { to: `/campaigns/${campaign.id}/add-promotions`, children: (0, import_jsx_runtime4.jsx)(Button, { variant: "secondary", size: "small", children: t("general.add") }) })
    ] }),
    (0, import_jsx_runtime4.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        isLoading,
        count,
        navigateTo: (row) => `/promotions/${row.id}`,
        filters,
        search: true,
        pagination: true,
        orderBy: [
          { key: "code", label: t("fields.code") },
          { key: "type", label: t("fields.type") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        queryObject: raw,
        commands: [
          {
            action: handleRemove,
            label: t("actions.remove"),
            shortcut: "r"
          }
        ],
        noRecords: {
          message: t("campaigns.promotions.list.noRecordsMessage")
        }
      }
    )
  ] });
};
var PromotionActions = ({
  promotion,
  campaignId
}) => {
  const { t } = useTranslation();
  const { mutateAsync } = useAddOrRemoveCampaignPromotions(campaignId);
  const prompt = usePrompt();
  const handleRemove = async () => {
    const res = await prompt({
      title: t("campaigns.promotions.remove.title", {
        count: 1
      }),
      description: t("campaigns.promotions.remove.description", {
        count: 1
      }),
      confirmText: t("actions.continue"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync({
      remove: [promotion.id]
    });
  };
  return (0, import_jsx_runtime4.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `/promotions/${promotion.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime4.jsx)(Trash, {}),
              label: t("actions.remove"),
              onClick: handleRemove
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const columns = usePromotionTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...columns,
      columnHelper.display({
        id: "actions",
        cell: ({ row, table }) => {
          const { campaignId } = table.options.meta;
          return (0, import_jsx_runtime4.jsx)(
            PromotionActions,
            {
              promotion: row.original,
              campaignId
            }
          );
        }
      })
    ],
    [columns]
  );
};
var CampaignSpend = ({ campaign }) => {
  var _a, _b, _c, _d;
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "flex flex-col gap-y-4 px-6 py-4", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "mb-2 grid grid-cols-[28px_1fr] items-center gap-x-3", children: [
      (0, import_jsx_runtime5.jsx)("div", { className: "bg-ui-bg-base shadow-borders-base flex size-7 items-center justify-center rounded-md", children: (0, import_jsx_runtime5.jsx)("div", { className: "bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]", children: (0, import_jsx_runtime5.jsx)(CurrencyDollar, { className: "text-ui-fg-subtle" }) }) }),
      (0, import_jsx_runtime5.jsx)(Heading, { level: "h3", className: "text-ui-fg-subtle font-normal", children: ((_a = campaign.budget) == null ? void 0 : _a.type) === "spend" ? t("campaigns.fields.total_spend") : t("campaigns.fields.total_used") })
    ] }),
    (0, import_jsx_runtime5.jsx)("div", { children: (0, import_jsx_runtime5.jsx)(
      Text,
      {
        className: "text-ui-fg-subtle border-ui-border-strong border-l-4 pl-3",
        size: "small",
        leading: "compact",
        children: (0, import_jsx_runtime5.jsx)(
          Trans,
          {
            i18nKey: "campaigns.totalSpend",
            values: {
              amount: ((_b = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _b.used) || 0,
              currency: ((_c = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _c.type) === "spend" ? (_d = campaign == null ? void 0 : campaign.budget) == null ? void 0 : _d.currency_code : ""
            },
            components: [
              (0, import_jsx_runtime5.jsx)(
                "span",
                {
                  className: "text-ui-fg-base txt-compact-medium-plus text-lg"
                },
                "amount"
              ),
              (0, import_jsx_runtime5.jsx)(
                "span",
                {
                  className: "text-ui-fg-base txt-compact-medium-plus text-lg"
                },
                "currency"
              )
            ]
          }
        )
      }
    ) })
  ] });
};
var CampaignConfigurationSection = ({
  campaign
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime6.jsxs)(Container, { className: "flex flex-col gap-y-4", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime6.jsx)(Heading, { level: "h2", children: t("campaigns.configuration.header") }),
      (0, import_jsx_runtime6.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  icon: (0, import_jsx_runtime6.jsx)(PencilSquare, {}),
                  to: "configuration"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime6.jsx)(
      DateRangeDisplay,
      {
        startsAt: campaign.starts_at,
        endsAt: campaign.ends_at,
        showTime: true
      }
    )
  ] });
};
var CampaignDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const { campaign, isLoading, isError, error } = useCampaign(
    id,
    { fields: CAMPAIGN_DETAIL_FIELDS },
    { initialData }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !campaign) {
    return (0, import_jsx_runtime7.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 2,
        sidebarSections: 3,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime7.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("campaign.details.after"),
        before: getWidgets("campaign.details.before"),
        sideAfter: getWidgets("campaign.details.side.after"),
        sideBefore: getWidgets("campaign.details.side.before")
      },
      hasOutlet: true,
      showJSON: true,
      showMetadata: true,
      data: campaign,
      children: [
        (0, import_jsx_runtime7.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime7.jsx)(CampaignGeneralSection, { campaign }),
          (0, import_jsx_runtime7.jsx)(CampaignPromotionSection, { campaign })
        ] }),
        (0, import_jsx_runtime7.jsxs)(TwoColumnPage.Sidebar, { children: [
          (0, import_jsx_runtime7.jsx)(CampaignConfigurationSection, { campaign }),
          (0, import_jsx_runtime7.jsx)(CampaignSpend, { campaign }),
          (0, import_jsx_runtime7.jsx)(CampaignBudget, { campaign })
        ] })
      ]
    }
  );
};
var campaignDetailQuery = (id) => ({
  queryKey: campaignsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.campaign.retrieve(id, {
    fields: CAMPAIGN_DETAIL_FIELDS
  })
});
var campaignLoader = async ({ params }) => {
  const id = params.id;
  const query = campaignDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  CampaignDetailBreadcrumb as Breadcrumb,
  CampaignDetail as Component,
  campaignLoader as loader
};
//# sourceMappingURL=campaign-detail-Z33NIWPP-GIBLK2OZ.js.map
