{"version": 3, "sources": ["../../@medusajs/dashboard/dist/api-key-management-edit-UX47FHWV.mjs"], "sourcesContent": ["import {\n  VisuallyH<PERSON>den\n} from \"./chunk-F6ZOHZVB.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useApi<PERSON>ey,\n  useUpdateApiKey\n} from \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/api-key-management/api-key-management-edit/api-key-management-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/api-key-management/api-key-management-edit/components/edit-api-key-form/edit-api-key-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditApiKeySchema = zod.object({\n  title: zod.string().min(1)\n});\nvar EditApiKeyForm = ({ apiKey }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      title: apiKey.title\n    },\n    resolver: zodResolver(EditApiKeySchema)\n  });\n  const { mutateAsync, isPending } = useUpdateApiKey(apiKey.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: ({ api_key }) => {\n        toast.success(\n          t(\"apiKeyManagement.edit.successToast\", {\n            title: api_key.title\n          })\n        );\n        handleSuccess();\n      },\n      onError: (err) => {\n        toast.error(err.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsx(\n      Form.Field,\n      {\n        control: form.control,\n        name: \"title\",\n        render: ({ field }) => {\n          return /* @__PURE__ */ jsxs(Form.Item, { children: [\n            /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n          ] });\n        }\n      }\n    ) }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/api-key-management/api-key-management-edit/api-key-management-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ApiKeyManagementEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { api_key, isLoading, isError, error } = useApiKey(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"apiKeyManagement.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { asChild: true, children: /* @__PURE__ */ jsx2(VisuallyHidden, { children: t(\"apiKeyManagement.edit.description\") }) })\n    ] }),\n    !isLoading && !!api_key && /* @__PURE__ */ jsx2(EditApiKeyForm, { apiKey: api_key })\n  ] });\n};\nexport {\n  ApiKeyManagementEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,yBAA0B;AAoD1B,IAAAA,sBAA2C;AAnD3C,IAAI,mBAAuB,WAAO;AAAA,EAChC,OAAW,WAAO,EAAE,IAAI,CAAC;AAC3B,CAAC;AACD,IAAI,iBAAiB,CAAC,EAAE,OAAO,MAAM;AACnC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,OAAO;AAAA,IAChB;AAAA,IACA,UAAU,EAAY,gBAAgB;AAAA,EACxC,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,gBAAgB,OAAO,EAAE;AAC5D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,CAAC,EAAE,QAAQ,MAAM;AAC1B,cAAM;AAAA,UACJA,GAAE,sCAAsC;AAAA,YACtC,OAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,QAAQ;AAChB,cAAM,MAAM,IAAI,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B;AAAA,MAC3I,KAAK;AAAA,MACL;AAAA,QACE,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,gBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,gBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3C,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,UAAU,EAAE;AAC3D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC;AAAA,UACnI,oBAAAE,KAAK,YAAY,aAAa,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,gBAAgB,EAAE,UAAUF,GAAE,mCAAmC,EAAE,CAAC,EAAE,CAAC;AAAA,IACvK,EAAE,CAAC;AAAA,IACH,CAAC,aAAa,CAAC,CAAC,eAA2B,oBAAAE,KAAK,gBAAgB,EAAE,QAAQ,QAAQ,CAAC;AAAA,EACrF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}