{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-2VTICXJR.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/return-reasons.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar RETURN_REASONS_QUERY_KEY = \"return_reasons\";\nvar returnReasonsQueryKeys = queryKeysFactory(RETURN_REASONS_QUERY_KEY);\nvar useReturnReasons = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.returnReason.list(query),\n    queryKey: returnReasonsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useReturnReason = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.returnReason.retrieve(id, query),\n    queryKey: returnReasonsQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateReturnReason = (query, options) => {\n  return useMutation({\n    mutationFn: async (data) => sdk.admin.returnReason.create(data, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateReturnReason = (id, query, options) => {\n  return useMutation({\n    mutationFn: async (data) => sdk.admin.returnReason.update(id, data, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.detail(data.return_reason.id, query)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteReturnReason = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.returnReason.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: returnReasonsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  returnReasonsQueryKeys,\n  useReturnReasons,\n  useReturnReason,\n  useCreateReturnReason,\n  useUpdateReturnReason,\n  useDeleteReturnReason\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,2BAA2B;AAC/B,IAAI,yBAAyB,iBAAiB,wBAAwB;AACtE,IAAI,mBAAmB,CAAC,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,aAAa,KAAK,KAAK;AAAA,IAChD,UAAU,uBAAuB,KAAK,KAAK;AAAA,IAC3C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,IAAI,OAAO,YAAY;AAC5C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,aAAa,SAAS,IAAI,KAAK;AAAA,IACxD,UAAU,uBAAuB,OAAO,EAAE;AAAA,IAC1C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,wBAAwB,CAAC,OAAO,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,OAAO,SAAS,IAAI,MAAM,aAAa,OAAO,MAAM,KAAK;AAAA,IACrE,WAAW,CAAC,MAAM,WAAW,YAAY;AApC7C;AAqCM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,wBAAwB,CAAC,IAAI,OAAO,YAAY;AAClD,SAAO,YAAY;AAAA,IACjB,YAAY,OAAO,SAAS,IAAI,MAAM,aAAa,OAAO,IAAI,MAAM,KAAK;AAAA,IACzE,WAAW,CAAC,MAAM,WAAW,YAAY;AAhD7C;AAiDM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,KAAK,cAAc,IAAI,KAAK;AAAA,MACtE,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,wBAAwB,CAAC,IAAI,YAAY;AAC3C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,aAAa,OAAO,EAAE;AAAA,IAClD,WAAW,CAAC,MAAM,WAAW,YAAY;AA/D7C;AAgEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,OAAO,EAAE;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,QAAQ;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}