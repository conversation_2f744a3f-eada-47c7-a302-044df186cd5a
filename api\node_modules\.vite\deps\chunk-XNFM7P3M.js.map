{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-DLZWPHHO.mjs"], "sourcesContent": ["// src/hooks/table/filters/use-customer-group-table-filters.tsx\nimport { useTranslation } from \"react-i18next\";\nvar useCustomerGroupTableFilters = () => {\n  const { t } = useTranslation();\n  let filters = [];\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  filters = [...filters, ...dateFilters];\n  return filters;\n};\n\nexport {\n  useCustomerGroupTableFilters\n};\n"], "mappings": ";;;;;AAEA,IAAI,+BAA+B,MAAM;AACvC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,MAAI,UAAU,CAAC;AACf,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,YAAU,CAAC,GAAG,SAAS,GAAG,WAAW;AACrC,SAAO;AACT;", "names": []}