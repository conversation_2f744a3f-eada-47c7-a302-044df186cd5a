{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-F6IJV2I2.mjs"], "sourcesContent": ["import {\n  salesChannelsQueryKeys\n} from \"./chunk-PNU5HPGY.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/api-keys.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar API_KEYS_QUERY_KEY = \"api_keys\";\nvar apiKeysQueryKeys = queryKeysFactory(API_KEYS_QUERY_KEY);\nvar useApiKey = (id, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.apiKey.retrieve(id),\n    queryKey: apiKeysQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useApiKeys = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.apiKey.list(query),\n    queryKey: apiKeysQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateApiKey = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.apiKey.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateApiKey = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.apiKey.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRevokeApiKey = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.apiKey.revoke(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    }\n  });\n};\nvar useDeleteApiKey = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.apiKey.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    }\n  });\n};\nvar useBatchRemoveSalesChannelsFromApiKey = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.apiKey.batchSalesChannels(id, { remove: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useBatchAddSalesChannelsToApiKey = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.apiKey.batchSalesChannels(id, { add: payload }),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: apiKeysQueryKeys.detail(id) });\n      queryClient.invalidateQueries({\n        queryKey: salesChannelsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  apiKeysQueryKeys,\n  useApiKey,\n  useApiKeys,\n  useCreateApiKey,\n  useUpdateApiKey,\n  useRevokeApiKey,\n  useDeleteApiKey,\n  useBatchRemoveSalesChannelsFromApiKey,\n  useBatchAddSalesChannelsToApiKey\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,qBAAqB;AACzB,IAAI,mBAAmB,iBAAiB,kBAAkB;AAC1D,IAAI,YAAY,CAAC,IAAI,YAAY;AAC/B,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,OAAO,SAAS,EAAE;AAAA,IAC3C,UAAU,iBAAiB,OAAO,EAAE;AAAA,IACpC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,aAAa,CAAC,OAAO,YAAY;AACnC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK;AAAA,IAC1C,UAAU,iBAAiB,KAAK,KAAK;AAAA,IACrC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,YAAY;AACjC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,IACxD,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,OAAO,IAAI,OAAO;AAAA,IAC5D,WAAW,CAAC,MAAM,WAAW,YAAY;AAjD7C;AAkDM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE;AAAA,IAC5C,WAAW,CAAC,MAAM,WAAW,YAAY;AA5D7C;AA6DM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE;AAAA,IAC5C,WAAW,CAAC,MAAM,WAAW,YAAY;AAtE7C;AAuEM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AACA,IAAI,wCAAwC,CAAC,IAAI,YAAY;AAC3D,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,mBAAmB,IAAI,EAAE,QAAQ,QAAQ,CAAC;AAAA,IACpF,WAAW,CAAC,MAAM,WAAW,YAAY;AAhF7C;AAiFM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mCAAmC,CAAC,IAAI,YAAY;AACtD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,mBAAmB,IAAI,EAAE,KAAK,QAAQ,CAAC;AAAA,IACjF,WAAW,CAAC,MAAM,WAAW,YAAY;AA9F7C;AA+FM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,uBAAuB,MAAM;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}