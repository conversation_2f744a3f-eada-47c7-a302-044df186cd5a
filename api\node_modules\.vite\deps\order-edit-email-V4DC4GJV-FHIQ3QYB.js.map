{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-edit-email-V4DC4GJV.mjs"], "sourcesContent": ["import {\n  DEFAULT_FIELDS\n} from \"./chunk-7I5DQGWY.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport {\n  useOrder,\n  useUpdateOrder\n} from \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/orders/order-edit-email/order-edit-email.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/orders/order-edit-email/components/edit-order-email-form/edit-order-email-form.tsx\nimport * as zod from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditOrderEmailSchema = zod.object({\n  email: zod.string().email()\n});\nfunction EditOrderEmailForm({ order }) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      email: order.email || \"\"\n    },\n    resolver: zodResolver(EditOrderEmailSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateOrder(order.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    try {\n      await mutateAsync({\n        email: data.email\n      });\n      toast.success(\n        t(\"orders.edit.email.requestSuccess\", { email: data.email })\n      );\n      handleSuccess();\n    } catch (error) {\n      toast.error(error.message);\n    }\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex size-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex-1 overflow-auto\", children: /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"email\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.email\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { type: \"email\", ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              isLoading: isPending,\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              children: t(\"actions.save\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/orders/order-edit-email/order-edit-email.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar OrderEditEmail = () => {\n  const { t } = useTranslation2();\n  const params = useParams();\n  const { order, isPending, isError, error } = useOrder(params.id, {\n    fields: DEFAULT_FIELDS\n  });\n  if (!isPending && isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"orders.edit.email.title\") }) }),\n    order && /* @__PURE__ */ jsx2(EditOrderEmailForm, { order })\n  ] });\n};\nexport {\n  OrderEditEmail as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,yBAA0B;AAkE1B,IAAAA,sBAA2C;AAjE3C,IAAI,uBAA2B,WAAO;AAAA,EACpC,OAAW,WAAO,EAAE,MAAM;AAC5B,CAAC;AACD,SAAS,mBAAmB,EAAE,MAAM,GAAG;AACrC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,MAAM,SAAS;AAAA,IACxB;AAAA,IACA,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,eAAe,MAAM,EAAE;AAC1D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,QAAI;AACF,YAAM,YAAY;AAAA,QAChB,OAAO,KAAK;AAAA,MACd,CAAC;AACD,YAAM;AAAA,QACJA,GAAE,oCAAoC,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,MAC7D;AACA,oBAAc;AAAA,IAChB,SAAS,OAAO;AACd,YAAM,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,wBAAwB,cAA0B;AAAA,UACnG,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,oBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,UAAUA,GAAE,cAAc;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,OAAO,WAAW,SAAS,MAAM,IAAI,SAAS,OAAO,IAAI;AAAA,IAC/D,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,yBAAyB,EAAE,CAAC,EAAE,CAAC;AAAA,IAChI,aAAyB,oBAAAE,KAAK,oBAAoB,EAAE,MAAM,CAAC;AAAA,EAC7D,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}