{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-GRT22PE5.mjs"], "sourcesContent": ["import {\n  stockLocationsQueryKeys\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/shipping-options.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar SHIPPING_OPTIONS_QUERY_KEY = \"shipping_options\";\nvar shippingOptionsQueryKeys = queryKeysFactory(\n  SHIPPING_OPTIONS_QUERY_KEY\n);\nvar useShippingOption = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.shippingOption.retrieve(id, query),\n    queryKey: shippingOptionsQueryKeys.detail(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useShippingOptions = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.shippingOption.list(query),\n    queryKey: shippingOptionsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateShippingOptions = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.shippingOption.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: shippingOptionsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateShippingOptions = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.shippingOption.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: shippingOptionsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteShippingOption = (optionId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.shippingOption.delete(optionId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: stockLocationsQueryKeys.all\n      });\n      queryClient.invalidateQueries({\n        queryKey: shippingOptionsQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  shippingOptionsQueryKeys,\n  useShippingOption,\n  useShippingOptions,\n  useCreateShippingOptions,\n  useUpdateShippingOptions,\n  useDeleteShippingOption\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,6BAA6B;AACjC,IAAI,2BAA2B;AAAA,EAC7B;AACF;AACA,IAAI,oBAAoB,CAAC,IAAI,OAAO,YAAY;AAC9C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,eAAe,SAAS,IAAI,KAAK;AAAA,IAC1D,UAAU,yBAAyB,OAAO,EAAE;AAAA,IAC5C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,qBAAqB,CAAC,OAAO,YAAY;AAC3C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,eAAe,KAAK,KAAK;AAAA,IAClD,UAAU,yBAAyB,KAAK,KAAK;AAAA,IAC7C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,2BAA2B,CAAC,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,eAAe,OAAO,OAAO;AAAA,IAChE,WAAW,CAAC,MAAM,WAAW,YAAY;AAzC7C;AA0CM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,eAAe,OAAO,IAAI,OAAO;AAAA,IACpE,WAAW,CAAC,MAAM,WAAW,YAAY;AAxD7C;AAyDM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,0BAA0B,CAAC,UAAU,YAAY;AACnD,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,eAAe,OAAO,QAAQ;AAAA,IAC1D,WAAW,CAAC,MAAM,WAAW,YAAY;AAvE7C;AAwEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,yBAAyB;AAAA,MACrC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}