import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  DescendingSorting,
  DropdownMenu,
  IconButton
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-AOFGTNG6.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var initState = (params, prefix) => {
  const param = prefix ? `${prefix}_order` : "order";
  const sortParam = params.get(param);
  if (!sortParam) {
    return {
      dir: "asc"
      /* ASC */
    };
  }
  const dir = sortParam.startsWith("-") ? "desc" : "asc";
  const key = sortParam.replace("-", "");
  return {
    key,
    dir
  };
};
var DataTableOrderBy = ({
  keys,
  prefix
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [state, setState] = (0, import_react.useState)(initState(searchParams, prefix));
  const param = prefix ? `${prefix}_order` : "order";
  const { t } = useTranslation();
  const handleDirChange = (dir) => {
    setState((prev) => ({
      ...prev,
      dir
    }));
    updateOrderParam({
      key: state.key,
      dir
    });
  };
  const handleKeyChange = (value) => {
    setState((prev) => ({
      ...prev,
      key: value
    }));
    updateOrderParam({
      key: value,
      dir: state.dir
    });
  };
  const updateOrderParam = (state2) => {
    if (!state2.key) {
      setSearchParams((prev) => {
        prev.delete(param);
        return prev;
      });
      return;
    }
    const orderParam = state2.dir === "asc" ? state2.key : `-${state2.key}`;
    setSearchParams((prev) => {
      prev.set(param, orderParam);
      return prev;
    });
  };
  return (0, import_jsx_runtime.jsxs)(DropdownMenu, { children: [
    (0, import_jsx_runtime.jsx)(DropdownMenu.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(IconButton, { size: "small", children: (0, import_jsx_runtime.jsx)(DescendingSorting, {}) }) }),
    (0, import_jsx_runtime.jsxs)(DropdownMenu.Content, { className: "z-[1]", align: "end", children: [
      (0, import_jsx_runtime.jsx)(
        DropdownMenu.RadioGroup,
        {
          value: state.key,
          onValueChange: handleKeyChange,
          children: keys.map((key) => {
            const stringKey = String(key.key);
            return (0, import_jsx_runtime.jsx)(
              DropdownMenu.RadioItem,
              {
                value: stringKey,
                onSelect: (event) => event.preventDefault(),
                children: key.label
              },
              stringKey
            );
          })
        }
      ),
      (0, import_jsx_runtime.jsx)(DropdownMenu.Separator, {}),
      (0, import_jsx_runtime.jsxs)(
        DropdownMenu.RadioGroup,
        {
          value: state.dir,
          onValueChange: handleDirChange,
          children: [
            (0, import_jsx_runtime.jsxs)(
              DropdownMenu.RadioItem,
              {
                className: "flex items-center justify-between",
                value: "asc",
                onSelect: (event) => event.preventDefault(),
                children: [
                  t("general.ascending"),
                  (0, import_jsx_runtime.jsx)(DropdownMenu.Label, { children: "1 - 30" })
                ]
              }
            ),
            (0, import_jsx_runtime.jsxs)(
              DropdownMenu.RadioItem,
              {
                className: "flex items-center justify-between",
                value: "desc",
                onSelect: (event) => event.preventDefault(),
                children: [
                  t("general.descending"),
                  (0, import_jsx_runtime.jsx)(DropdownMenu.Label, { children: "30 - 1" })
                ]
              }
            )
          ]
        }
      )
    ] })
  ] });
};

export {
  DataTableOrderBy
};
//# sourceMappingURL=chunk-CZLMELES.js.map
