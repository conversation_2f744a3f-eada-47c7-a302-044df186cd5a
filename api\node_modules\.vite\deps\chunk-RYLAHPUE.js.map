{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-ZJRFL6ZN.mjs"], "sourcesContent": ["import {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\n\n// src/hooks/table/columns/use-customer-group-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCustomerGroupTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.name\") }),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue() || \"-\" })\n      }),\n      columnHelper.accessor(\"customers\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"customers.domain\") }),\n        cell: ({ getValue }) => {\n          const count = getValue()?.length ?? 0;\n          return /* @__PURE__ */ jsx(TextCell, { text: count });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\nexport {\n  useCustomerGroupTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAOA,mBAAwB;AAExB,yBAAoB;AACpB,IAAI,eAAe,mBAAmB;AACtC,IAAI,+BAA+B,MAAM;AACvC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,QACxE,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,KAAK,IAAI,CAAC;AAAA,MACnF,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;AAAA,QAC7E,MAAM,CAAC,EAAE,SAAS,MAAM;AArBhC;AAsBU,gBAAM,UAAQ,cAAS,MAAT,mBAAY,WAAU;AACpC,qBAAuB,wBAAI,UAAU,EAAE,MAAM,MAAM,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;", "names": []}