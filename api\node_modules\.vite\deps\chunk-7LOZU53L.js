import {
  castNumber
} from "./chunk-EZLR4STK.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";

// node_modules/@medusajs/dashboard/dist/chunk-ZQRKUG6J.mjs
var optionalInt = z.union([z.string(), z.number()]).optional().refine(
  (value) => {
    if (value === "" || value === void 0) {
      return true;
    }
    return Number.isInteger(castNumber(value));
  },
  {
    message: instance.t("validation.mustBeInt")
  }
).refine(
  (value) => {
    if (value === "" || value === void 0) {
      return true;
    }
    return castNumber(value) >= 0;
  },
  {
    message: instance.t("validation.mustBePositive")
  }
);
var optionalFloat = z.union([z.string(), z.number()]).optional().refine(
  (value) => {
    if (value === "" || value === void 0) {
      return true;
    }
    return castNumber(value) >= 0;
  },
  {
    message: instance.t("validation.mustBePositive")
  }
);
var metadataFormSchema = z.array(
  z.object({
    key: z.string(),
    value: z.unknown(),
    isInitial: z.boolean().optional(),
    isDeleted: z.boolean().optional(),
    isIgnored: z.boolean().optional()
  })
);
function partialFormValidation(form, fields, schema) {
  form.clearErrors(fields);
  const values = fields.reduce((acc, key) => {
    acc[key] = form.getValues(key);
    return acc;
  }, {});
  const validationResult = schema.safeParse(values);
  if (!validationResult.success) {
    validationResult.error.errors.forEach(({ path, message, code }) => {
      form.setError(path.join("."), {
        type: code,
        message
      });
    });
    return false;
  }
  return true;
}

export {
  optionalInt,
  optionalFloat,
  partialFormValidation
};
//# sourceMappingURL=chunk-7LOZU53L.js.map
