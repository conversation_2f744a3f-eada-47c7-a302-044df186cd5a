import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-R325LWWE.js";
import "./chunk-WNW4SNUS.js";
import {
  DataTable
} from "./chunk-EPUS4TBC.js";
import "./chunk-32T72GVU.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import {
  useStore
} from "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import {
  useDeleteSalesChannelLazy,
  useSalesChannels
} from "./chunk-3H6LL6QL.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  Trash,
  createDataTableColumnHelper,
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/sales-channel-list-WMLELPSX.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var SalesChannelListTable = () => {
  const { t } = useTranslation();
  const { store } = useStore();
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE
  });
  const { sales_channels, count, isPending, isError, error } = useSalesChannels(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useSalesChannelTableFilters();
  const emptyState = useSalesChannelTableEmptyState();
  const sales_channels_data = (sales_channels == null ? void 0 : sales_channels.map((sales_channel) => {
    return {
      ...sales_channel,
      is_default: (store == null ? void 0 : store.default_sales_channel_id) === sales_channel.id
    };
  })) ?? [];
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime.jsx)(
    DataTable,
    {
      data: sales_channels_data,
      columns,
      rowCount: count,
      getRowId: (row) => row.id,
      pageSize: PAGE_SIZE,
      filters,
      isLoading: isPending,
      emptyState,
      heading: t("salesChannels.domain"),
      subHeading: t("salesChannels.subtitle"),
      action: {
        label: t("actions.create"),
        to: "/settings/sales-channels/create"
      },
      rowHref: (row) => `/settings/sales-channels/${row.id}`
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const base = useSalesChannelTableColumns();
  const { mutateAsync } = useDeleteSalesChannelLazy();
  const handleDelete = (0, import_react.useCallback)(
    async (salesChannel) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("salesChannels.deleteSalesChannelWarning", {
          name: salesChannel.name
        }),
        verificationInstruction: t("general.typeToConfirm"),
        verificationText: salesChannel.name,
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!confirm) {
        return;
      }
      await mutateAsync(salesChannel.id, {
        onSuccess: () => {
          toast.success(t("salesChannels.toast.delete"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    },
    [t, prompt, mutateAsync]
  );
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.action({
        actions: (ctx) => {
          const disabledTooltip = ctx.row.original.is_default ? t("salesChannels.tooltip.cannotDeleteDefault") : void 0;
          return [
            [
              {
                icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                label: t("actions.edit"),
                onClick: () => navigate(
                  `/settings/sales-channels/${ctx.row.original.id}/edit`
                )
              }
            ],
            [
              {
                icon: (0, import_jsx_runtime.jsx)(Trash, {}),
                label: t("actions.delete"),
                onClick: () => handleDelete(ctx.row.original),
                disabled: ctx.row.original.is_default,
                disabledTooltip
              }
            ]
          ];
        }
      })
    ],
    [base, handleDelete, navigate, t]
  );
};
var SalesChannelList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("sales_channel.list.before"),
        after: getWidgets("sales_channel.list.after")
      },
      hasOutlet: true,
      children: (0, import_jsx_runtime2.jsx)(SalesChannelListTable, {})
    }
  );
};
export {
  SalesChannelList as Component
};
//# sourceMappingURL=sales-channel-list-WMLELPSX-JRTG5DZP.js.map
