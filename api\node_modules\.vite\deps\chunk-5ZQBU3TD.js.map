{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-7DXVXBSA.mjs"], "sourcesContent": ["// src/lib/order-helpers.ts\nvar getCanceledOrderStatus = (t, status) => {\n  if (status === \"canceled\") {\n    return { label: t(\"orders.status.canceled\"), color: \"red\" };\n  }\n  return null;\n};\nvar getOrderPaymentStatus = (t, status) => {\n  const [label, color] = {\n    not_paid: [t(\"orders.payment.status.notPaid\"), \"red\"],\n    authorized: [t(\"orders.payment.status.authorized\"), \"orange\"],\n    partially_authorized: [\n      t(\"orders.payment.status.partiallyAuthorized\"),\n      \"red\"\n    ],\n    awaiting: [t(\"orders.payment.status.awaiting\"), \"orange\"],\n    captured: [t(\"orders.payment.status.captured\"), \"green\"],\n    refunded: [t(\"orders.payment.status.refunded\"), \"green\"],\n    partially_refunded: [\n      t(\"orders.payment.status.partiallyRefunded\"),\n      \"orange\"\n    ],\n    partially_captured: [\n      t(\"orders.payment.status.partiallyCaptured\"),\n      \"orange\"\n    ],\n    canceled: [t(\"orders.payment.status.canceled\"), \"red\"],\n    requires_action: [t(\"orders.payment.status.requiresAction\"), \"orange\"]\n  }[status];\n  return { label, color };\n};\nvar getOrderFulfillmentStatus = (t, status) => {\n  const [label, color] = {\n    not_fulfilled: [t(\"orders.fulfillment.status.notFulfilled\"), \"red\"],\n    partially_fulfilled: [\n      t(\"orders.fulfillment.status.partiallyFulfilled\"),\n      \"orange\"\n    ],\n    fulfilled: [t(\"orders.fulfillment.status.fulfilled\"), \"green\"],\n    partially_shipped: [\n      t(\"orders.fulfillment.status.partiallyShipped\"),\n      \"orange\"\n    ],\n    shipped: [t(\"orders.fulfillment.status.shipped\"), \"green\"],\n    delivered: [t(\"orders.fulfillment.status.delivered\"), \"green\"],\n    partially_delivered: [\n      t(\"orders.fulfillment.status.partiallyDelivered\"),\n      \"orange\"\n    ],\n    partially_returned: [\n      t(\"orders.fulfillment.status.partiallyReturned\"),\n      \"orange\"\n    ],\n    returned: [t(\"orders.fulfillment.status.returned\"), \"green\"],\n    canceled: [t(\"orders.fulfillment.status.canceled\"), \"red\"],\n    requires_action: [t(\"orders.fulfillment.status.requiresAction\"), \"orange\"]\n  }[status];\n  return { label, color };\n};\n\nexport {\n  getCanceledOrderStatus,\n  getOrderPaymentStatus,\n  getOrderFulfillmentStatus\n};\n"], "mappings": ";AACA,IAAI,yBAAyB,CAAC,GAAG,WAAW;AAC1C,MAAI,WAAW,YAAY;AACzB,WAAO,EAAE,OAAO,EAAE,wBAAwB,GAAG,OAAO,MAAM;AAAA,EAC5D;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,CAAC,GAAG,WAAW;AACzC,QAAM,CAAC,OAAO,KAAK,IAAI;AAAA,IACrB,UAAU,CAAC,EAAE,+BAA+B,GAAG,KAAK;AAAA,IACpD,YAAY,CAAC,EAAE,kCAAkC,GAAG,QAAQ;AAAA,IAC5D,sBAAsB;AAAA,MACpB,EAAE,2CAA2C;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,UAAU,CAAC,EAAE,gCAAgC,GAAG,QAAQ;AAAA,IACxD,UAAU,CAAC,EAAE,gCAAgC,GAAG,OAAO;AAAA,IACvD,UAAU,CAAC,EAAE,gCAAgC,GAAG,OAAO;AAAA,IACvD,oBAAoB;AAAA,MAClB,EAAE,yCAAyC;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,EAAE,yCAAyC;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,UAAU,CAAC,EAAE,gCAAgC,GAAG,KAAK;AAAA,IACrD,iBAAiB,CAAC,EAAE,sCAAsC,GAAG,QAAQ;AAAA,EACvE,EAAE,MAAM;AACR,SAAO,EAAE,OAAO,MAAM;AACxB;AACA,IAAI,4BAA4B,CAAC,GAAG,WAAW;AAC7C,QAAM,CAAC,OAAO,KAAK,IAAI;AAAA,IACrB,eAAe,CAAC,EAAE,wCAAwC,GAAG,KAAK;AAAA,IAClE,qBAAqB;AAAA,MACnB,EAAE,8CAA8C;AAAA,MAChD;AAAA,IACF;AAAA,IACA,WAAW,CAAC,EAAE,qCAAqC,GAAG,OAAO;AAAA,IAC7D,mBAAmB;AAAA,MACjB,EAAE,4CAA4C;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,SAAS,CAAC,EAAE,mCAAmC,GAAG,OAAO;AAAA,IACzD,WAAW,CAAC,EAAE,qCAAqC,GAAG,OAAO;AAAA,IAC7D,qBAAqB;AAAA,MACnB,EAAE,8CAA8C;AAAA,MAChD;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,EAAE,6CAA6C;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,UAAU,CAAC,EAAE,oCAAoC,GAAG,OAAO;AAAA,IAC3D,UAAU,CAAC,EAAE,oCAAoC,GAAG,KAAK;AAAA,IACzD,iBAAiB,CAAC,EAAE,0CAA0C,GAAG,QAAQ;AAAA,EAC3E,EAAE,MAAM;AACR,SAAO,EAAE,OAAO,MAAM;AACxB;", "names": []}