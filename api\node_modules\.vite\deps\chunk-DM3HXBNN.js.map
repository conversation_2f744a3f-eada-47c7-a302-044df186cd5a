{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-4UY4U2B5.mjs"], "sourcesContent": ["import {\n  CSS\n} from \"./chunk-ACBS6KFT.mjs\";\n\n// src/components/common/sortable-tree/sortable-tree.tsx\nimport {\n  DndContext,\n  DragOverlay,\n  KeyboardSensor,\n  MeasuringStrategy,\n  PointerSensor,\n  closestCenter,\n  defaultDropAnimation,\n  useSensor,\n  useSensors\n} from \"@dnd-kit/core\";\nimport {\n  SortableContext,\n  arrayMove as arrayMove2,\n  verticalListSortingStrategy\n} from \"@dnd-kit/sortable\";\nimport { useEffect, useMemo, useRef, useState } from \"react\";\nimport { createPortal } from \"react-dom\";\n\n// src/components/common/sortable-tree/keyboard-coordinates.ts\nimport {\n  KeyboardCode,\n  closestCorners,\n  getFirstCollision\n} from \"@dnd-kit/core\";\n\n// src/components/common/sortable-tree/utils.ts\nimport { arrayMove } from \"@dnd-kit/sortable\";\nvar iOS = /iPad|iPhone|iPod/.test(navigator.platform);\nfunction getDragDepth(offset, indentationWidth) {\n  return Math.round(offset / indentationWidth);\n}\nfunction getProjection(items, activeId, overId, dragOffset, indentationWidth) {\n  const overItemIndex = items.findIndex(({ id }) => id === overId);\n  const activeItemIndex = items.findIndex(({ id }) => id === activeId);\n  const activeItem = items[activeItemIndex];\n  const newItems = arrayMove(items, activeItemIndex, overItemIndex);\n  const previousItem = newItems[overItemIndex - 1];\n  const nextItem = newItems[overItemIndex + 1];\n  const dragDepth = getDragDepth(dragOffset, indentationWidth);\n  const projectedDepth = activeItem.depth + dragDepth;\n  const maxDepth = getMaxDepth({\n    previousItem\n  });\n  const minDepth = getMinDepth({ nextItem });\n  let depth = projectedDepth;\n  if (projectedDepth >= maxDepth) {\n    depth = maxDepth;\n  } else if (projectedDepth < minDepth) {\n    depth = minDepth;\n  }\n  return { depth, maxDepth, minDepth, parentId: getParentId() };\n  function getParentId() {\n    if (depth === 0 || !previousItem) {\n      return null;\n    }\n    if (depth === previousItem.depth) {\n      return previousItem.parentId;\n    }\n    if (depth > previousItem.depth) {\n      return previousItem.id;\n    }\n    const newParent = newItems.slice(0, overItemIndex).reverse().find((item) => item.depth === depth)?.parentId;\n    return newParent ?? null;\n  }\n}\nfunction getMaxDepth({ previousItem }) {\n  if (previousItem) {\n    return previousItem.depth + 1;\n  }\n  return 0;\n}\nfunction getMinDepth({ nextItem }) {\n  if (nextItem) {\n    return nextItem.depth;\n  }\n  return 0;\n}\nfunction flatten(items, parentId = null, depth = 0, childrenProp) {\n  return items.reduce((acc, item, index) => {\n    const children = item[childrenProp] || [];\n    return [\n      ...acc,\n      { ...item, parentId, depth, index },\n      ...flatten(children, item.id, depth + 1, childrenProp)\n    ];\n  }, []);\n}\nfunction flattenTree(items, childrenProp) {\n  return flatten(items, void 0, void 0, childrenProp);\n}\nfunction buildTree(flattenedItems, newIndex, childrenProp) {\n  const root = { id: \"root\", [childrenProp]: [] };\n  const nodes = { [root.id]: root };\n  const items = flattenedItems.map((item) => ({ ...item, [childrenProp]: [] }));\n  let update = {\n    id: null,\n    parentId: null,\n    index: 0\n  };\n  items.forEach((item, index) => {\n    const {\n      id,\n      index: _index,\n      depth: _depth,\n      parentId: _parentId,\n      ...rest\n    } = item;\n    const children = item[childrenProp] || [];\n    const parentId = _parentId ?? root.id;\n    const parent = nodes[parentId] ?? findItem(items, parentId);\n    nodes[id] = { id, [childrenProp]: children };\n    parent[childrenProp].push({\n      id,\n      ...rest,\n      [childrenProp]: children\n    });\n    if (index === newIndex) {\n      const parentChildren = parent[childrenProp];\n      update = {\n        id: item.id,\n        parentId: parent.id === \"root\" ? null : parent.id,\n        index: parentChildren.length - 1\n      };\n    }\n  });\n  if (!update.id) {\n    throw new Error(\"Could not find item\");\n  }\n  return {\n    items: root[childrenProp],\n    update\n  };\n}\nfunction findItem(items, itemId) {\n  return items.find(({ id }) => id === itemId);\n}\nfunction findItemDeep(items, itemId, childrenProp) {\n  for (const item of items) {\n    const { id } = item;\n    const children = item[childrenProp] || [];\n    if (id === itemId) {\n      return item;\n    }\n    if (children.length) {\n      const child = findItemDeep(children, itemId, childrenProp);\n      if (child) {\n        return child;\n      }\n    }\n  }\n  return void 0;\n}\nfunction countChildren(items, count = 0, childrenProp) {\n  return items.reduce((acc, item) => {\n    const children = item[childrenProp] || [];\n    if (children.length) {\n      return countChildren(children, acc + 1, childrenProp);\n    }\n    return acc + 1;\n  }, count);\n}\nfunction getChildCount(items, id, childrenProp) {\n  const item = findItemDeep(items, id, childrenProp);\n  const children = item?.[childrenProp] || [];\n  return item ? countChildren(children, 0, childrenProp) : 0;\n}\nfunction removeChildrenOf(items, ids, childrenProp) {\n  const excludeParentIds = [...ids];\n  return items.filter((item) => {\n    if (item.parentId && excludeParentIds.includes(item.parentId)) {\n      const children = item[childrenProp] || [];\n      if (children.length) {\n        excludeParentIds.push(item.id);\n      }\n      return false;\n    }\n    return true;\n  });\n}\n\n// src/components/common/sortable-tree/keyboard-coordinates.ts\nvar directions = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left\n];\nvar horizontal = [KeyboardCode.Left, KeyboardCode.Right];\nvar sortableTreeKeyboardCoordinates = (context, indentationWidth) => (event, {\n  currentCoordinates,\n  context: {\n    active,\n    over,\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  }\n}) => {\n  if (directions.includes(event.code)) {\n    if (!active || !collisionRect) {\n      return;\n    }\n    event.preventDefault();\n    const {\n      current: { items, offset }\n    } = context;\n    if (horizontal.includes(event.code) && over?.id) {\n      const { depth, maxDepth, minDepth } = getProjection(\n        items,\n        active.id,\n        over.id,\n        offset,\n        indentationWidth\n      );\n      switch (event.code) {\n        case KeyboardCode.Left:\n          if (depth > minDepth) {\n            return {\n              ...currentCoordinates,\n              x: currentCoordinates.x - indentationWidth\n            };\n          }\n          break;\n        case KeyboardCode.Right:\n          if (depth < maxDepth) {\n            return {\n              ...currentCoordinates,\n              x: currentCoordinates.x + indentationWidth\n            };\n          }\n          break;\n      }\n      return void 0;\n    }\n    const containers = [];\n    droppableContainers.forEach((container) => {\n      if (container?.disabled || container.id === over?.id) {\n        return;\n      }\n      const rect = droppableRects.get(container.id);\n      if (!rect) {\n        return;\n      }\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            containers.push(container);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            containers.push(container);\n          }\n          break;\n      }\n    });\n    const collisions = closestCorners({\n      active,\n      collisionRect,\n      pointerCoordinates: null,\n      droppableRects,\n      droppableContainers: containers\n    });\n    let closestId = getFirstCollision(collisions, \"id\");\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n    if (closestId && over?.id) {\n      const activeRect = droppableRects.get(active.id);\n      const newRect = droppableRects.get(closestId);\n      const newDroppable = droppableContainers.get(closestId);\n      if (activeRect && newRect && newDroppable) {\n        const newIndex = items.findIndex(({ id }) => id === closestId);\n        const newItem = items[newIndex];\n        const activeIndex = items.findIndex(({ id }) => id === active.id);\n        const activeItem = items[activeIndex];\n        if (newItem && activeItem) {\n          const { depth } = getProjection(\n            items,\n            active.id,\n            closestId,\n            (newItem.depth - activeItem.depth) * indentationWidth,\n            indentationWidth\n          );\n          const isBelow = newIndex > activeIndex;\n          const modifier = isBelow ? 1 : -1;\n          const offset2 = 0;\n          const newCoordinates = {\n            x: newRect.left + depth * indentationWidth,\n            y: newRect.top + modifier * offset2\n          };\n          return newCoordinates;\n        }\n      }\n    }\n  }\n  return void 0;\n};\n\n// src/components/common/sortable-tree/sortable-tree-item.tsx\nimport { useSortable } from \"@dnd-kit/sortable\";\n\n// src/components/common/sortable-tree/tree-item.tsx\nimport { forwardRef } from \"react\";\nimport {\n  DotsSix,\n  FolderIllustration,\n  FolderOpenIllustration,\n  TagIllustration,\n  TriangleRightMini\n} from \"@medusajs/icons\";\nimport { Badge, clx, IconButton } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TreeItem = forwardRef(\n  ({\n    childCount,\n    clone,\n    depth,\n    disableSelection,\n    disableInteraction,\n    ghost,\n    handleProps,\n    indentationWidth,\n    collapsed,\n    onCollapse,\n    style,\n    value,\n    disabled,\n    wrapperRef,\n    ...props\n  }, ref) => {\n    return /* @__PURE__ */ jsx(\n      \"li\",\n      {\n        ref: wrapperRef,\n        style: {\n          paddingLeft: `${indentationWidth * depth}px`\n        },\n        className: clx(\"-mb-px list-none\", {\n          \"pointer-events-none\": disableInteraction,\n          \"select-none\": disableSelection,\n          \"[&:first-of-type>div]:border-t-0\": !clone\n        }),\n        ...props,\n        children: /* @__PURE__ */ jsxs(\n          \"div\",\n          {\n            ref,\n            style,\n            className: clx(\n              \"bg-ui-bg-base transition-fg relative flex items-center gap-x-3 border-y px-6 py-2.5\",\n              {\n                \"border-l\": depth > 0,\n                \"shadow-elevation-flyout bg-ui-bg-base w-fit rounded-lg border-none pr-6 opacity-80\": clone,\n                \"bg-ui-bg-base-hover z-[1] opacity-50\": ghost,\n                \"bg-ui-bg-disabled\": disabled\n              }\n            ),\n            children: [\n              /* @__PURE__ */ jsx(Handle, { ...handleProps, disabled }),\n              /* @__PURE__ */ jsx(\n                Collapse,\n                {\n                  collapsed,\n                  onCollapse,\n                  clone\n                }\n              ),\n              /* @__PURE__ */ jsx(\n                Icon,\n                {\n                  childrenCount: childCount,\n                  collapsed,\n                  clone\n                }\n              ),\n              /* @__PURE__ */ jsx(Value, { value }),\n              /* @__PURE__ */ jsx(ChildrenCount, { clone, childrenCount: childCount })\n            ]\n          }\n        )\n      }\n    );\n  }\n);\nTreeItem.displayName = \"TreeItem\";\nvar Handle = ({\n  listeners,\n  attributes,\n  disabled\n}) => {\n  return /* @__PURE__ */ jsx(\n    IconButton,\n    {\n      size: \"small\",\n      variant: \"transparent\",\n      type: \"button\",\n      className: clx(\"cursor-grab\", { \"cursor-not-allowed\": disabled }),\n      disabled,\n      ...attributes,\n      ...listeners,\n      children: /* @__PURE__ */ jsx(DotsSix, {})\n    }\n  );\n};\nvar Icon = ({ childrenCount, collapsed, clone }) => {\n  const isBranch = clone ? childrenCount && childrenCount > 1 : childrenCount;\n  const isOpen = clone ? false : !collapsed;\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-7 items-center justify-center\", children: isBranch ? isOpen ? /* @__PURE__ */ jsx(FolderOpenIllustration, {}) : /* @__PURE__ */ jsx(FolderIllustration, {}) : /* @__PURE__ */ jsx(TagIllustration, {}) });\n};\nvar Collapse = ({ collapsed, onCollapse, clone }) => {\n  if (clone) {\n    return null;\n  }\n  if (!onCollapse) {\n    return /* @__PURE__ */ jsx(\"div\", { className: \"size-7\", role: \"presentation\" });\n  }\n  return /* @__PURE__ */ jsx(\n    IconButton,\n    {\n      size: \"small\",\n      variant: \"transparent\",\n      onClick: onCollapse,\n      type: \"button\",\n      children: /* @__PURE__ */ jsx(\n        TriangleRightMini,\n        {\n          className: clx(\"text-ui-fg-subtle transition-transform\", {\n            \"rotate-90\": !collapsed\n          })\n        }\n      )\n    }\n  );\n};\nvar Value = ({ value }) => {\n  return /* @__PURE__ */ jsx(\"div\", { className: \"txt-compact-small text-ui-fg-subtle flex-grow truncate\", children: value });\n};\nvar ChildrenCount = ({ clone, childrenCount }) => {\n  if (!clone || !childrenCount) {\n    return null;\n  }\n  if (clone && childrenCount <= 1) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(Badge, { size: \"2xsmall\", color: \"blue\", className: \"absolute -right-2 -top-2\", children: childrenCount });\n};\n\n// src/components/common/sortable-tree/sortable-tree-item.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar animateLayoutChanges = ({\n  isSorting,\n  wasDragging\n}) => {\n  return isSorting || wasDragging ? false : true;\n};\nfunction SortableTreeItem({\n  id,\n  depth,\n  disabled,\n  ...props\n}) {\n  const {\n    attributes,\n    isDragging,\n    isSorting,\n    listeners,\n    setDraggableNodeRef,\n    setDroppableNodeRef,\n    transform,\n    transition\n  } = useSortable({\n    id,\n    animateLayoutChanges,\n    disabled\n  });\n  const style = {\n    transform: CSS.Translate.toString(transform),\n    transition\n  };\n  return /* @__PURE__ */ jsx2(\n    TreeItem,\n    {\n      ref: setDraggableNodeRef,\n      wrapperRef: setDroppableNodeRef,\n      style,\n      depth,\n      ghost: isDragging,\n      disableSelection: iOS,\n      disableInteraction: isSorting,\n      disabled,\n      handleProps: {\n        listeners,\n        attributes\n      },\n      ...props\n    }\n  );\n}\n\n// src/components/common/sortable-tree/sortable-tree.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar measuring = {\n  droppable: {\n    strategy: MeasuringStrategy.Always\n  }\n};\nvar dropAnimationConfig = {\n  keyframes({ transform }) {\n    return [\n      { opacity: 1, transform: CSS.Transform.toString(transform.initial) },\n      {\n        opacity: 0,\n        transform: CSS.Transform.toString({\n          ...transform.final,\n          x: transform.final.x + 5,\n          y: transform.final.y + 5\n        })\n      }\n    ];\n  },\n  easing: \"ease-out\",\n  sideEffects({ active }) {\n    active.node.animate([{ opacity: 0 }, { opacity: 1 }], {\n      duration: defaultDropAnimation.duration,\n      easing: defaultDropAnimation.easing\n    });\n  }\n};\nfunction SortableTree({\n  collapsible = true,\n  childrenProp = \"children\",\n  // \"children\" is the default children prop name\n  enableDrag = true,\n  items = [],\n  indentationWidth = 40,\n  onChange,\n  renderValue\n}) {\n  const [collapsedState, setCollapsedState] = useState({});\n  const [activeId, setActiveId] = useState(null);\n  const [overId, setOverId] = useState(null);\n  const [offsetLeft, setOffsetLeft] = useState(0);\n  const [currentPosition, setCurrentPosition] = useState(null);\n  const flattenedItems = useMemo(() => {\n    const flattenedTree = flattenTree(items, childrenProp);\n    const collapsedItems = flattenedTree.reduce(\n      (acc, item) => {\n        const { id } = item;\n        const children = item[childrenProp] || [];\n        const collapsed = collapsedState[id];\n        return collapsed && children.length ? [...acc, id] : acc;\n      },\n      []\n    );\n    return removeChildrenOf(\n      flattenedTree,\n      activeId ? [activeId, ...collapsedItems] : collapsedItems,\n      childrenProp\n    );\n  }, [activeId, items, childrenProp, collapsedState]);\n  const projected = activeId && overId ? getProjection(\n    flattenedItems,\n    activeId,\n    overId,\n    offsetLeft,\n    indentationWidth\n  ) : null;\n  const sensorContext = useRef({\n    items: flattenedItems,\n    offset: offsetLeft\n  });\n  const [coordinateGetter] = useState(\n    () => sortableTreeKeyboardCoordinates(sensorContext, indentationWidth)\n  );\n  const sensors = useSensors(\n    useSensor(PointerSensor),\n    useSensor(KeyboardSensor, {\n      coordinateGetter\n    })\n  );\n  const sortedIds = useMemo(\n    () => flattenedItems.map(({ id }) => id),\n    [flattenedItems]\n  );\n  const activeItem = activeId ? flattenedItems.find(({ id }) => id === activeId) : null;\n  useEffect(() => {\n    sensorContext.current = {\n      items: flattenedItems,\n      offset: offsetLeft\n    };\n  }, [flattenedItems, offsetLeft]);\n  function handleDragStart({ active: { id: activeId2 } }) {\n    setActiveId(activeId2);\n    setOverId(activeId2);\n    const activeItem2 = flattenedItems.find(({ id }) => id === activeId2);\n    if (activeItem2) {\n      setCurrentPosition({\n        parentId: activeItem2.parentId,\n        overId: activeId2\n      });\n    }\n    document.body.style.setProperty(\"cursor\", \"grabbing\");\n  }\n  function handleDragMove({ delta }) {\n    setOffsetLeft(delta.x);\n  }\n  function handleDragOver({ over }) {\n    setOverId(over?.id ?? null);\n  }\n  function handleDragEnd({ active, over }) {\n    resetState();\n    if (projected && over) {\n      const { depth, parentId } = projected;\n      const clonedItems = JSON.parse(\n        JSON.stringify(flattenTree(items, childrenProp))\n      );\n      const overIndex = clonedItems.findIndex(({ id }) => id === over.id);\n      const activeIndex = clonedItems.findIndex(({ id }) => id === active.id);\n      const activeTreeItem = clonedItems[activeIndex];\n      clonedItems[activeIndex] = { ...activeTreeItem, depth, parentId };\n      const sortedItems = arrayMove2(clonedItems, activeIndex, overIndex);\n      const { items: newItems, update } = buildTree(\n        sortedItems,\n        overIndex,\n        childrenProp\n      );\n      onChange(update, newItems);\n    }\n  }\n  function handleDragCancel() {\n    resetState();\n  }\n  function resetState() {\n    setOverId(null);\n    setActiveId(null);\n    setOffsetLeft(0);\n    setCurrentPosition(null);\n    document.body.style.setProperty(\"cursor\", \"\");\n  }\n  function handleCollapse(id) {\n    setCollapsedState((state) => ({\n      ...state,\n      [id]: state[id] ? false : true\n    }));\n  }\n  function getMovementAnnouncement(eventName, activeId2, overId2) {\n    if (overId2 && projected) {\n      if (eventName !== \"onDragEnd\") {\n        if (currentPosition && projected.parentId === currentPosition.parentId && overId2 === currentPosition.overId) {\n          return;\n        } else {\n          setCurrentPosition({\n            parentId: projected.parentId,\n            overId: overId2\n          });\n        }\n      }\n      const clonedItems = JSON.parse(\n        JSON.stringify(flattenTree(items, childrenProp))\n      );\n      const overIndex = clonedItems.findIndex(({ id }) => id === overId2);\n      const activeIndex = clonedItems.findIndex(({ id }) => id === activeId2);\n      const sortedItems = arrayMove2(clonedItems, activeIndex, overIndex);\n      const previousItem = sortedItems[overIndex - 1];\n      let announcement;\n      const movedVerb = eventName === \"onDragEnd\" ? \"dropped\" : \"moved\";\n      const nestedVerb = eventName === \"onDragEnd\" ? \"dropped\" : \"nested\";\n      if (!previousItem) {\n        const nextItem = sortedItems[overIndex + 1];\n        announcement = `${activeId2} was ${movedVerb} before ${nextItem.id}.`;\n      } else {\n        if (projected.depth > previousItem.depth) {\n          announcement = `${activeId2} was ${nestedVerb} under ${previousItem.id}.`;\n        } else {\n          let previousSibling = previousItem;\n          while (previousSibling && projected.depth < previousSibling.depth) {\n            const parentId = previousSibling.parentId;\n            previousSibling = sortedItems.find(({ id }) => id === parentId);\n          }\n          if (previousSibling) {\n            announcement = `${activeId2} was ${movedVerb} after ${previousSibling.id}.`;\n          }\n        }\n      }\n      return announcement;\n    }\n    return;\n  }\n  const announcements = {\n    onDragStart({ active }) {\n      return `Picked up ${active.id}.`;\n    },\n    onDragMove({ active, over }) {\n      return getMovementAnnouncement(\"onDragMove\", active.id, over?.id);\n    },\n    onDragOver({ active, over }) {\n      return getMovementAnnouncement(\"onDragOver\", active.id, over?.id);\n    },\n    onDragEnd({ active, over }) {\n      return getMovementAnnouncement(\"onDragEnd\", active.id, over?.id);\n    },\n    onDragCancel({ active }) {\n      return `Moving was cancelled. ${active.id} was dropped in its original position.`;\n    }\n  };\n  return /* @__PURE__ */ jsx3(\n    DndContext,\n    {\n      accessibility: { announcements },\n      sensors,\n      collisionDetection: closestCenter,\n      measuring,\n      onDragStart: handleDragStart,\n      onDragMove: handleDragMove,\n      onDragOver: handleDragOver,\n      onDragEnd: handleDragEnd,\n      onDragCancel: handleDragCancel,\n      children: /* @__PURE__ */ jsxs2(SortableContext, { items: sortedIds, strategy: verticalListSortingStrategy, children: [\n        flattenedItems.map((item) => {\n          const { id, depth } = item;\n          const children = item[childrenProp] || [];\n          const disabled = typeof enableDrag === \"function\" ? !enableDrag(item) : !enableDrag;\n          return /* @__PURE__ */ jsx3(\n            SortableTreeItem,\n            {\n              id,\n              value: renderValue(item),\n              disabled,\n              depth: id === activeId && projected ? projected.depth : depth,\n              indentationWidth,\n              collapsed: Boolean(collapsedState[id] && children.length),\n              childCount: children.length,\n              onCollapse: collapsible && children.length ? () => handleCollapse(id) : void 0\n            },\n            id\n          );\n        }),\n        createPortal(\n          /* @__PURE__ */ jsx3(DragOverlay, { dropAnimation: dropAnimationConfig, children: activeId && activeItem ? /* @__PURE__ */ jsx3(\n            SortableTreeItem,\n            {\n              id: activeId,\n              depth: activeItem.depth,\n              clone: true,\n              childCount: getChildCount(items, activeId, childrenProp) + 1,\n              value: renderValue(activeItem),\n              indentationWidth: 0\n            }\n          ) : null }),\n          document.body\n        )\n      ] })\n    }\n  );\n}\n\n// src/routes/categories/common/components/category-tree/category-tree.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar CategoryTree = ({\n  value,\n  onChange,\n  renderValue,\n  enableDrag = true,\n  isLoading = false\n}) => {\n  if (isLoading) {\n    return /* @__PURE__ */ jsx4(\"div\", { className: \"txt-compact-small relative flex-1 overflow-y-auto\", children: Array.from({ length: 10 }).map((_, i) => /* @__PURE__ */ jsx4(CategoryLeafPlaceholder, {}, i)) });\n  }\n  return /* @__PURE__ */ jsx4(\n    SortableTree,\n    {\n      items: value,\n      childrenProp: \"category_children\",\n      collapsible: true,\n      enableDrag,\n      onChange,\n      renderValue\n    }\n  );\n};\nvar CategoryLeafPlaceholder = () => {\n  return /* @__PURE__ */ jsx4(\"div\", { className: \"bg-ui-bg-base -mb-px flex h-12 animate-pulse items-center border-y px-6 py-2.5\" });\n};\n\nexport {\n  CategoryTree\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,mBAAqD;AACrD,uBAA6B;AA+R7B,IAAAA,gBAA2B;AAS3B,yBAA0B;AAyI1B,IAAAC,sBAA4B;AAoD5B,IAAAA,sBAA2C;AAiQ3C,IAAAA,sBAA4B;AA3tB5B,IAAI,MAAM,mBAAmB,KAAK,UAAU,QAAQ;AACpD,SAAS,aAAa,QAAQ,kBAAkB;AAC9C,SAAO,KAAK,MAAM,SAAS,gBAAgB;AAC7C;AACA,SAAS,cAAc,OAAO,UAAU,QAAQ,YAAY,kBAAkB;AAC5E,QAAM,gBAAgB,MAAM,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,MAAM;AAC/D,QAAM,kBAAkB,MAAM,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,QAAQ;AACnE,QAAM,aAAa,MAAM,eAAe;AACxC,QAAM,WAAW,UAAU,OAAO,iBAAiB,aAAa;AAChE,QAAM,eAAe,SAAS,gBAAgB,CAAC;AAC/C,QAAM,WAAW,SAAS,gBAAgB,CAAC;AAC3C,QAAM,YAAY,aAAa,YAAY,gBAAgB;AAC3D,QAAM,iBAAiB,WAAW,QAAQ;AAC1C,QAAM,WAAW,YAAY;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,WAAW,YAAY,EAAE,SAAS,CAAC;AACzC,MAAI,QAAQ;AACZ,MAAI,kBAAkB,UAAU;AAC9B,YAAQ;AAAA,EACV,WAAW,iBAAiB,UAAU;AACpC,YAAQ;AAAA,EACV;AACA,SAAO,EAAE,OAAO,UAAU,UAAU,UAAU,YAAY,EAAE;AAC5D,WAAS,cAAc;AAzDzB;AA0DI,QAAI,UAAU,KAAK,CAAC,cAAc;AAChC,aAAO;AAAA,IACT;AACA,QAAI,UAAU,aAAa,OAAO;AAChC,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,QAAQ,aAAa,OAAO;AAC9B,aAAO,aAAa;AAAA,IACtB;AACA,UAAM,aAAY,cAAS,MAAM,GAAG,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,MAA9E,mBAAiF;AACnG,WAAO,aAAa;AAAA,EACtB;AACF;AACA,SAAS,YAAY,EAAE,aAAa,GAAG;AACrC,MAAI,cAAc;AAChB,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,YAAY,EAAE,SAAS,GAAG;AACjC,MAAI,UAAU;AACZ,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,QAAQ,OAAO,WAAW,MAAM,QAAQ,GAAG,cAAc;AAChE,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAU;AACxC,UAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,EAAE,GAAG,MAAM,UAAU,OAAO,MAAM;AAAA,MAClC,GAAG,QAAQ,UAAU,KAAK,IAAI,QAAQ,GAAG,YAAY;AAAA,IACvD;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,YAAY,OAAO,cAAc;AACxC,SAAO,QAAQ,OAAO,QAAQ,QAAQ,YAAY;AACpD;AACA,SAAS,UAAU,gBAAgB,UAAU,cAAc;AACzD,QAAM,OAAO,EAAE,IAAI,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE;AAC9C,QAAM,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK;AAChC,QAAM,QAAQ,eAAe,IAAI,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE,EAAE;AAC5E,MAAI,SAAS;AAAA,IACX,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,UAAM,WAAW,aAAa,KAAK;AACnC,UAAM,SAAS,MAAM,QAAQ,KAAK,SAAS,OAAO,QAAQ;AAC1D,UAAM,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,GAAG,SAAS;AAC3C,WAAO,YAAY,EAAE,KAAK;AAAA,MACxB;AAAA,MACA,GAAG;AAAA,MACH,CAAC,YAAY,GAAG;AAAA,IAClB,CAAC;AACD,QAAI,UAAU,UAAU;AACtB,YAAM,iBAAiB,OAAO,YAAY;AAC1C,eAAS;AAAA,QACP,IAAI,KAAK;AAAA,QACT,UAAU,OAAO,OAAO,SAAS,OAAO,OAAO;AAAA,QAC/C,OAAO,eAAe,SAAS;AAAA,MACjC;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,CAAC,OAAO,IAAI;AACd,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AACA,SAAO;AAAA,IACL,OAAO,KAAK,YAAY;AAAA,IACxB;AAAA,EACF;AACF;AACA,SAAS,SAAS,OAAO,QAAQ;AAC/B,SAAO,MAAM,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,MAAM;AAC7C;AACA,SAAS,aAAa,OAAO,QAAQ,cAAc;AACjD,aAAW,QAAQ,OAAO;AACxB,UAAM,EAAE,GAAG,IAAI;AACf,UAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,QAAI,OAAO,QAAQ;AACjB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAQ;AACnB,YAAM,QAAQ,aAAa,UAAU,QAAQ,YAAY;AACzD,UAAI,OAAO;AACT,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,OAAO,QAAQ,GAAG,cAAc;AACrD,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AACjC,UAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,QAAI,SAAS,QAAQ;AACnB,aAAO,cAAc,UAAU,MAAM,GAAG,YAAY;AAAA,IACtD;AACA,WAAO,MAAM;AAAA,EACf,GAAG,KAAK;AACV;AACA,SAAS,cAAc,OAAO,IAAI,cAAc;AAC9C,QAAM,OAAO,aAAa,OAAO,IAAI,YAAY;AACjD,QAAM,YAAW,6BAAO,kBAAiB,CAAC;AAC1C,SAAO,OAAO,cAAc,UAAU,GAAG,YAAY,IAAI;AAC3D;AACA,SAAS,iBAAiB,OAAO,KAAK,cAAc;AAClD,QAAM,mBAAmB,CAAC,GAAG,GAAG;AAChC,SAAO,MAAM,OAAO,CAAC,SAAS;AAC5B,QAAI,KAAK,YAAY,iBAAiB,SAAS,KAAK,QAAQ,GAAG;AAC7D,YAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,UAAI,SAAS,QAAQ;AACnB,yBAAiB,KAAK,KAAK,EAAE;AAAA,MAC/B;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAGA,IAAI,aAAa;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf;AACA,IAAI,aAAa,CAAC,aAAa,MAAM,aAAa,KAAK;AACvD,IAAI,kCAAkC,CAAC,SAAS,qBAAqB,CAAC,OAAO;AAAA,EAC3E;AAAA,EACA,SAAS;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,MAAM;AACJ,MAAI,WAAW,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,CAAC,UAAU,CAAC,eAAe;AAC7B;AAAA,IACF;AACA,UAAM,eAAe;AACrB,UAAM;AAAA,MACJ,SAAS,EAAE,OAAO,OAAO;AAAA,IAC3B,IAAI;AACJ,QAAI,WAAW,SAAS,MAAM,IAAI,MAAK,6BAAM,KAAI;AAC/C,YAAM,EAAE,OAAO,UAAU,SAAS,IAAI;AAAA,QACpC;AAAA,QACA,OAAO;AAAA,QACP,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,aAAa;AAChB,cAAI,QAAQ,UAAU;AACpB,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG,mBAAmB,IAAI;AAAA,YAC5B;AAAA,UACF;AACA;AAAA,QACF,KAAK,aAAa;AAChB,cAAI,QAAQ,UAAU;AACpB,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG,mBAAmB,IAAI;AAAA,YAC5B;AAAA,UACF;AACA;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AACA,UAAM,aAAa,CAAC;AACpB,wBAAoB,QAAQ,CAAC,cAAc;AACzC,WAAI,uCAAW,aAAY,UAAU,QAAO,6BAAM,KAAI;AACpD;AAAA,MACF;AACA,YAAM,OAAO,eAAe,IAAI,UAAU,EAAE;AAC5C,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,aAAa;AAChB,cAAI,cAAc,MAAM,KAAK,KAAK;AAChC,uBAAW,KAAK,SAAS;AAAA,UAC3B;AACA;AAAA,QACF,KAAK,aAAa;AAChB,cAAI,cAAc,MAAM,KAAK,KAAK;AAChC,uBAAW,KAAK,SAAS;AAAA,UAC3B;AACA;AAAA,MACJ;AAAA,IACF,CAAC;AACD,UAAM,aAAa,eAAe;AAAA,MAChC;AAAA,MACA;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,MACA,qBAAqB;AAAA,IACvB,CAAC;AACD,QAAI,YAAY,kBAAkB,YAAY,IAAI;AAClD,QAAI,eAAc,6BAAM,OAAM,WAAW,SAAS,GAAG;AACnD,kBAAY,WAAW,CAAC,EAAE;AAAA,IAC5B;AACA,QAAI,cAAa,6BAAM,KAAI;AACzB,YAAM,aAAa,eAAe,IAAI,OAAO,EAAE;AAC/C,YAAM,UAAU,eAAe,IAAI,SAAS;AAC5C,YAAM,eAAe,oBAAoB,IAAI,SAAS;AACtD,UAAI,cAAc,WAAW,cAAc;AACzC,cAAM,WAAW,MAAM,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,SAAS;AAC7D,cAAM,UAAU,MAAM,QAAQ;AAC9B,cAAM,cAAc,MAAM,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,OAAO,EAAE;AAChE,cAAM,aAAa,MAAM,WAAW;AACpC,YAAI,WAAW,YAAY;AACzB,gBAAM,EAAE,MAAM,IAAI;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,YACP;AAAA,aACC,QAAQ,QAAQ,WAAW,SAAS;AAAA,YACrC;AAAA,UACF;AACA,gBAAM,UAAU,WAAW;AAC3B,gBAAM,WAAW,UAAU,IAAI;AAC/B,gBAAM,UAAU;AAChB,gBAAM,iBAAiB;AAAA,YACrB,GAAG,QAAQ,OAAO,QAAQ;AAAA,YAC1B,GAAG,QAAQ,MAAM,WAAW;AAAA,UAC9B;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAgBA,IAAI,eAAW;AAAA,EACb,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,GAAG,QAAQ;AACT,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,OAAO;AAAA,UACL,aAAa,GAAG,mBAAmB,KAAK;AAAA,QAC1C;AAAA,QACA,WAAW,IAAI,oBAAoB;AAAA,UACjC,uBAAuB;AAAA,UACvB,eAAe;AAAA,UACf,oCAAoC,CAAC;AAAA,QACvC,CAAC;AAAA,QACD,GAAG;AAAA,QACH,cAA0B;AAAA,UACxB;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,WAAW;AAAA,cACT;AAAA,cACA;AAAA,gBACE,YAAY,QAAQ;AAAA,gBACpB,sFAAsF;AAAA,gBACtF,wCAAwC;AAAA,gBACxC,qBAAqB;AAAA,cACvB;AAAA,YACF;AAAA,YACA,UAAU;AAAA,kBACQ,wBAAI,QAAQ,EAAE,GAAG,aAAa,SAAS,CAAC;AAAA,kBACxC;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,eAAe;AAAA,kBACf;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB,wBAAI,OAAO,EAAE,MAAM,CAAC;AAAA,kBACpB,wBAAI,eAAe,EAAE,OAAO,eAAe,WAAW,CAAC;AAAA,YACzE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc;AACvB,IAAI,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW,IAAI,eAAe,EAAE,sBAAsB,SAAS,CAAC;AAAA,MAChE;AAAA,MACA,GAAG;AAAA,MACH,GAAG;AAAA,MACH,cAA0B,wBAAI,SAAS,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;AACA,IAAI,OAAO,CAAC,EAAE,eAAe,WAAW,MAAM,MAAM;AAClD,QAAM,WAAW,QAAQ,iBAAiB,gBAAgB,IAAI;AAC9D,QAAM,SAAS,QAAQ,QAAQ,CAAC;AAChC,aAAuB,wBAAI,OAAO,EAAE,WAAW,2CAA2C,UAAU,WAAW,aAAyB,wBAAI,wBAAwB,CAAC,CAAC,QAAoB,wBAAI,oBAAoB,CAAC,CAAC,QAAoB,wBAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC;AACpQ;AACA,IAAI,WAAW,CAAC,EAAE,WAAW,YAAY,MAAM,MAAM;AACnD,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AACA,MAAI,CAAC,YAAY;AACf,eAAuB,wBAAI,OAAO,EAAE,WAAW,UAAU,MAAM,eAAe,CAAC;AAAA,EACjF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,cAA0B;AAAA,QACxB;AAAA,QACA;AAAA,UACE,WAAW,IAAI,0CAA0C;AAAA,YACvD,aAAa,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,QAAQ,CAAC,EAAE,MAAM,MAAM;AACzB,aAAuB,wBAAI,OAAO,EAAE,WAAW,0DAA0D,UAAU,MAAM,CAAC;AAC5H;AACA,IAAI,gBAAgB,CAAC,EAAE,OAAO,cAAc,MAAM;AAChD,MAAI,CAAC,SAAS,CAAC,eAAe;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,SAAS,iBAAiB,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,OAAO,EAAE,MAAM,WAAW,OAAO,QAAQ,WAAW,4BAA4B,UAAU,cAAc,CAAC;AACtI;AAIA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AACJ,SAAO,aAAa,cAAc,QAAQ;AAC5C;AACA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,WAAW,IAAI,UAAU,SAAS,SAAS;AAAA,IAC3C;AAAA,EACF;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,KAAK;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB;AAAA,MACA,aAAa;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAAA,EACd,WAAW;AAAA,IACT,UAAU,kBAAkB;AAAA,EAC9B;AACF;AACA,IAAI,sBAAsB;AAAA,EACxB,UAAU,EAAE,UAAU,GAAG;AACvB,WAAO;AAAA,MACL,EAAE,SAAS,GAAG,WAAW,IAAI,UAAU,SAAS,UAAU,OAAO,EAAE;AAAA,MACnE;AAAA,QACE,SAAS;AAAA,QACT,WAAW,IAAI,UAAU,SAAS;AAAA,UAChC,GAAG,UAAU;AAAA,UACb,GAAG,UAAU,MAAM,IAAI;AAAA,UACvB,GAAG,UAAU,MAAM,IAAI;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,EAAE,OAAO,GAAG;AACtB,WAAO,KAAK,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,GAAG;AAAA,MACpD,UAAU,kCAAqB;AAAA,MAC/B,QAAQ,kCAAqB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa;AAAA,EACpB,cAAc;AAAA,EACd,eAAe;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,QAAQ,CAAC;AAAA,EACT,mBAAmB;AAAA,EACnB;AAAA,EACA;AACF,GAAG;AACD,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,uBAAS,CAAC,CAAC;AACvD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,IAAI;AAC7C,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAAS,IAAI;AACzC,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,CAAC;AAC9C,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,uBAAS,IAAI;AAC3D,QAAM,qBAAiB,sBAAQ,MAAM;AACnC,UAAM,gBAAgB,YAAY,OAAO,YAAY;AACrD,UAAM,iBAAiB,cAAc;AAAA,MACnC,CAAC,KAAK,SAAS;AACb,cAAM,EAAE,GAAG,IAAI;AACf,cAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,cAAM,YAAY,eAAe,EAAE;AACnC,eAAO,aAAa,SAAS,SAAS,CAAC,GAAG,KAAK,EAAE,IAAI;AAAA,MACvD;AAAA,MACA,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL;AAAA,MACA,WAAW,CAAC,UAAU,GAAG,cAAc,IAAI;AAAA,MAC3C;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,OAAO,cAAc,cAAc,CAAC;AAClD,QAAM,YAAY,YAAY,SAAS;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,oBAAgB,qBAAO;AAAA,IAC3B,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,CAAC,gBAAgB,QAAI;AAAA,IACzB,MAAM,gCAAgC,eAAe,gBAAgB;AAAA,EACvE;AACA,QAAM,UAAU;AAAA,IACd,UAAU,aAAa;AAAA,IACvB,UAAU,gBAAgB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,gBAAY;AAAA,IAChB,MAAM,eAAe,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;AAAA,IACvC,CAAC,cAAc;AAAA,EACjB;AACA,QAAM,aAAa,WAAW,eAAe,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,QAAQ,IAAI;AACjF,8BAAU,MAAM;AACd,kBAAc,UAAU;AAAA,MACtB,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF,GAAG,CAAC,gBAAgB,UAAU,CAAC;AAC/B,WAAS,gBAAgB,EAAE,QAAQ,EAAE,IAAI,UAAU,EAAE,GAAG;AACtD,gBAAY,SAAS;AACrB,cAAU,SAAS;AACnB,UAAM,cAAc,eAAe,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,SAAS;AACpE,QAAI,aAAa;AACf,yBAAmB;AAAA,QACjB,UAAU,YAAY;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,aAAS,KAAK,MAAM,YAAY,UAAU,UAAU;AAAA,EACtD;AACA,WAAS,eAAe,EAAE,MAAM,GAAG;AACjC,kBAAc,MAAM,CAAC;AAAA,EACvB;AACA,WAAS,eAAe,EAAE,KAAK,GAAG;AAChC,eAAU,6BAAM,OAAM,IAAI;AAAA,EAC5B;AACA,WAAS,cAAc,EAAE,QAAQ,KAAK,GAAG;AACvC,eAAW;AACX,QAAI,aAAa,MAAM;AACrB,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,cAAc,KAAK;AAAA,QACvB,KAAK,UAAU,YAAY,OAAO,YAAY,CAAC;AAAA,MACjD;AACA,YAAM,YAAY,YAAY,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,EAAE;AAClE,YAAM,cAAc,YAAY,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,OAAO,EAAE;AACtE,YAAM,iBAAiB,YAAY,WAAW;AAC9C,kBAAY,WAAW,IAAI,EAAE,GAAG,gBAAgB,OAAO,SAAS;AAChE,YAAM,cAAc,UAAW,aAAa,aAAa,SAAS;AAClE,YAAM,EAAE,OAAO,UAAU,OAAO,IAAI;AAAA,QAClC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,eAAS,QAAQ,QAAQ;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,eAAW;AAAA,EACb;AACA,WAAS,aAAa;AACpB,cAAU,IAAI;AACd,gBAAY,IAAI;AAChB,kBAAc,CAAC;AACf,uBAAmB,IAAI;AACvB,aAAS,KAAK,MAAM,YAAY,UAAU,EAAE;AAAA,EAC9C;AACA,WAAS,eAAe,IAAI;AAC1B,sBAAkB,CAAC,WAAW;AAAA,MAC5B,GAAG;AAAA,MACH,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,QAAQ;AAAA,IAC5B,EAAE;AAAA,EACJ;AACA,WAAS,wBAAwB,WAAW,WAAW,SAAS;AAC9D,QAAI,WAAW,WAAW;AACxB,UAAI,cAAc,aAAa;AAC7B,YAAI,mBAAmB,UAAU,aAAa,gBAAgB,YAAY,YAAY,gBAAgB,QAAQ;AAC5G;AAAA,QACF,OAAO;AACL,6BAAmB;AAAA,YACjB,UAAU,UAAU;AAAA,YACpB,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,cAAc,KAAK;AAAA,QACvB,KAAK,UAAU,YAAY,OAAO,YAAY,CAAC;AAAA,MACjD;AACA,YAAM,YAAY,YAAY,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,OAAO;AAClE,YAAM,cAAc,YAAY,UAAU,CAAC,EAAE,GAAG,MAAM,OAAO,SAAS;AACtE,YAAM,cAAc,UAAW,aAAa,aAAa,SAAS;AAClE,YAAM,eAAe,YAAY,YAAY,CAAC;AAC9C,UAAI;AACJ,YAAM,YAAY,cAAc,cAAc,YAAY;AAC1D,YAAM,aAAa,cAAc,cAAc,YAAY;AAC3D,UAAI,CAAC,cAAc;AACjB,cAAM,WAAW,YAAY,YAAY,CAAC;AAC1C,uBAAe,GAAG,SAAS,QAAQ,SAAS,WAAW,SAAS,EAAE;AAAA,MACpE,OAAO;AACL,YAAI,UAAU,QAAQ,aAAa,OAAO;AACxC,yBAAe,GAAG,SAAS,QAAQ,UAAU,UAAU,aAAa,EAAE;AAAA,QACxE,OAAO;AACL,cAAI,kBAAkB;AACtB,iBAAO,mBAAmB,UAAU,QAAQ,gBAAgB,OAAO;AACjE,kBAAM,WAAW,gBAAgB;AACjC,8BAAkB,YAAY,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,QAAQ;AAAA,UAChE;AACA,cAAI,iBAAiB;AACnB,2BAAe,GAAG,SAAS,QAAQ,SAAS,UAAU,gBAAgB,EAAE;AAAA,UAC1E;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA;AAAA,EACF;AACA,QAAM,gBAAgB;AAAA,IACpB,YAAY,EAAE,OAAO,GAAG;AACtB,aAAO,aAAa,OAAO,EAAE;AAAA,IAC/B;AAAA,IACA,WAAW,EAAE,QAAQ,KAAK,GAAG;AAC3B,aAAO,wBAAwB,cAAc,OAAO,IAAI,6BAAM,EAAE;AAAA,IAClE;AAAA,IACA,WAAW,EAAE,QAAQ,KAAK,GAAG;AAC3B,aAAO,wBAAwB,cAAc,OAAO,IAAI,6BAAM,EAAE;AAAA,IAClE;AAAA,IACA,UAAU,EAAE,QAAQ,KAAK,GAAG;AAC1B,aAAO,wBAAwB,aAAa,OAAO,IAAI,6BAAM,EAAE;AAAA,IACjE;AAAA,IACA,aAAa,EAAE,OAAO,GAAG;AACvB,aAAO,yBAAyB,OAAO,EAAE;AAAA,IAC3C;AAAA,EACF;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,eAAe,EAAE,cAAc;AAAA,MAC/B;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,MACA,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAA0B,oBAAAC,MAAM,iBAAiB,EAAE,OAAO,WAAW,UAAU,6BAA6B,UAAU;AAAA,QACpH,eAAe,IAAI,CAAC,SAAS;AAC3B,gBAAM,EAAE,IAAI,MAAM,IAAI;AACtB,gBAAM,WAAW,KAAK,YAAY,KAAK,CAAC;AACxC,gBAAM,WAAW,OAAO,eAAe,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC;AACzE,qBAAuB,oBAAAD;AAAA,YACrB;AAAA,YACA;AAAA,cACE;AAAA,cACA,OAAO,YAAY,IAAI;AAAA,cACvB;AAAA,cACA,OAAO,OAAO,YAAY,YAAY,UAAU,QAAQ;AAAA,cACxD;AAAA,cACA,WAAW,QAAQ,eAAe,EAAE,KAAK,SAAS,MAAM;AAAA,cACxD,YAAY,SAAS;AAAA,cACrB,YAAY,eAAe,SAAS,SAAS,MAAM,eAAe,EAAE,IAAI;AAAA,YAC1E;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,YACD;AAAA,cACkB,oBAAAA,KAAK,aAAa,EAAE,eAAe,qBAAqB,UAAU,YAAY,iBAA6B,oBAAAA;AAAA,YACzH;AAAA,YACA;AAAA,cACE,IAAI;AAAA,cACJ,OAAO,WAAW;AAAA,cAClB,OAAO;AAAA,cACP,YAAY,cAAc,OAAO,UAAU,YAAY,IAAI;AAAA,cAC3D,OAAO,YAAY,UAAU;AAAA,cAC7B,kBAAkB;AAAA,YACpB;AAAA,UACF,IAAI,KAAK,CAAC;AAAA,UACV,SAAS;AAAA,QACX;AAAA,MACF,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AACF;AAIA,IAAI,eAAe,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,YAAY;AACd,MAAM;AACJ,MAAI,WAAW;AACb,eAAuB,oBAAAE,KAAK,OAAO,EAAE,WAAW,qDAAqD,UAAU,MAAM,KAAK,EAAE,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,UAAsB,oBAAAA,KAAK,yBAAyB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AAAA,EACjN;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,0BAA0B,MAAM;AAClC,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,iFAAiF,CAAC;AACpI;", "names": ["import_react", "import_jsx_runtime", "jsx2", "jsx3", "jsxs2", "jsx4"]}