import {
  useDate
} from "./chunk-AGRADJYQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Text,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-FOD6BULO.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DateRangeDisplay = ({
  startsAt,
  endsAt,
  showTime = false
}) => {
  const startDate = startsAt ? new Date(startsAt) : null;
  const endDate = endsAt ? new Date(endsAt) : null;
  const { t } = useTranslation();
  const { getFullDate } = useDate();
  return (0, import_jsx_runtime.jsxs)("div", { className: "grid gap-3 md:grid-cols-2", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component text-ui-fg-subtle flex items-center gap-x-3 rounded-md px-3 py-1.5", children: [
      (0, import_jsx_runtime.jsx)(Bar, { date: startDate }),
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Text, { weight: "plus", size: "small", children: t("fields.startDate") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "tabular-nums", children: startDate ? getFullDate({
          date: startDate,
          includeTime: showTime
        }) : "-" })
      ] })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component text-ui-fg-subtle flex items-center gap-x-3 rounded-md px-3 py-1.5", children: [
      (0, import_jsx_runtime.jsx)(Bar, { date: endDate }),
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Text, { size: "small", weight: "plus", children: t("fields.endDate") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "tabular-nums", children: endDate ? getFullDate({
          date: endDate,
          includeTime: showTime
        }) : "-" })
      ] })
    ] })
  ] });
};
var Bar = ({ date }) => {
  const now = /* @__PURE__ */ new Date();
  const isDateInFuture = date && date > now;
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx("bg-ui-tag-neutral-icon h-8 w-1 rounded-full", {
        "bg-ui-tag-orange-icon": isDateInFuture
      })
    }
  );
};

export {
  DateRangeDisplay
};
//# sourceMappingURL=chunk-USN2624C.js.map
