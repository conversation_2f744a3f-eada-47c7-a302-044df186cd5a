{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-MW4K5NNY.mjs"], "sourcesContent": ["import {\n  countries\n} from \"./chunk-VDBOSWVE.mjs\";\n\n// src/components/inputs/country-select/country-select.tsx\nimport {\n  forwardRef,\n  useImperativeHandle,\n  useRef\n} from \"react\";\nimport { TrianglesMini } from \"@medusajs/icons\";\nimport { clx } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CountrySelect = forwardRef(\n  ({ className, disabled, placeholder, value, defaultValue, ...props }, ref) => {\n    const { t } = useTranslation();\n    const innerRef = useRef(null);\n    useImperativeHandle(ref, () => innerRef.current);\n    const isPlaceholder = innerRef.current?.value === \"\";\n    return /* @__PURE__ */ jsxs(\"div\", { className: \"relative\", children: [\n      /* @__PURE__ */ jsx(\n        TrianglesMini,\n        {\n          className: clx(\n            \"text-ui-fg-muted transition-fg pointer-events-none absolute right-2 top-1/2 -translate-y-1/2\",\n            {\n              \"text-ui-fg-disabled\": disabled\n            }\n          )\n        }\n      ),\n      /* @__PURE__ */ jsxs(\n        \"select\",\n        {\n          value: value !== void 0 ? value.toLowerCase() : void 0,\n          defaultValue: defaultValue ? defaultValue.toLowerCase() : void 0,\n          disabled,\n          className: clx(\n            \"bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 outline-none\",\n            \"placeholder:text-ui-fg-muted text-ui-fg-base\",\n            \"hover:bg-ui-bg-field-hover\",\n            \"focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active\",\n            \"aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error\",\n            \"invalid::border-ui-border-error invalid:shadow-borders-error\",\n            \"disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled\",\n            {\n              \"text-ui-fg-muted\": isPlaceholder\n            },\n            className\n          ),\n          ...props,\n          ref: innerRef,\n          children: [\n            /* @__PURE__ */ jsx(\"option\", { value: \"\", disabled: true, className: \"text-ui-fg-muted\", children: placeholder || t(\"fields.selectCountry\") }),\n            countries.map((country) => {\n              return /* @__PURE__ */ jsx(\"option\", { value: country.iso_2.toLowerCase(), children: country.display_name }, country.iso_2);\n            })\n          ]\n        }\n      )\n    ] });\n  }\n);\nCountrySelect.displayName = \"CountrySelect\";\n\nexport {\n  CountrySelect\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAKA,mBAIO;AAIP,yBAA0B;AAC1B,IAAI,oBAAgB;AAAA,EAClB,CAAC,EAAE,WAAW,UAAU,aAAa,OAAO,cAAc,GAAG,MAAM,GAAG,QAAQ;AAfhF;AAgBI,UAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,UAAM,eAAW,qBAAO,IAAI;AAC5B,0CAAoB,KAAK,MAAM,SAAS,OAAO;AAC/C,UAAM,kBAAgB,cAAS,YAAT,mBAAkB,WAAU;AAClD,eAAuB,yBAAK,OAAO,EAAE,WAAW,YAAY,UAAU;AAAA,UACpD;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,YACT;AAAA,YACA;AAAA,cACE,uBAAuB;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd;AAAA,QACA;AAAA,UACE,OAAO,UAAU,SAAS,MAAM,YAAY,IAAI;AAAA,UAChD,cAAc,eAAe,aAAa,YAAY,IAAI;AAAA,UAC1D;AAAA,UACA,WAAW;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,cACE,oBAAoB;AAAA,YACtB;AAAA,YACA;AAAA,UACF;AAAA,UACA,GAAG;AAAA,UACH,KAAK;AAAA,UACL,UAAU;AAAA,gBACQ,wBAAI,UAAU,EAAE,OAAO,IAAI,UAAU,MAAM,WAAW,oBAAoB,UAAU,eAAe,EAAE,sBAAsB,EAAE,CAAC;AAAA,YAC9I,UAAU,IAAI,CAAC,YAAY;AACzB,yBAAuB,wBAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,YAAY,GAAG,UAAU,QAAQ,aAAa,GAAG,QAAQ,KAAK;AAAA,YAC5H,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL;AACF;AACA,cAAc,cAAc;", "names": []}