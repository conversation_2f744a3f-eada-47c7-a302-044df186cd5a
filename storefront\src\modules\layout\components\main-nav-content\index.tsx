"use client"

import { getCustomLocations } from "@lib/data/custom-locations"
import { retrieveCustomer } from "@lib/data/customer"
import { listCartShippingMethods } from "@lib/data/fulfillment"
import { listCartPaymentMethods } from "@lib/data/payment"
import { getPromotions } from "@lib/data/promotions"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import Logo from "@modules/layout/components/logo"
import MenuCategory from "@modules/layout/components/menu-category"
import ErrorBoundary from "components/ui/error-boundary"
import { Separator } from "components/ui/separator"
import { useEffect, useMemo, useState } from "react"
import {
  ICategoryWithProducts,
  T_MenuByHandleResponse,
  T_SettingPreferences,
  TCartCustomField,
} from "types"
import { cn } from "utils"
import { PAGE_PATH } from "utils/path"
import { useCartStateStore } from "zustand-store/useCartStore"
import CartDialog from "../cart-dialog"
import SearchInput from "../search-content/input-search"
import ContactInfo from "./header/contact-info"
import LanguageSelect from "./header/language-select"
import SaleButton from "./header/pre-order-button"
import MobileMenu from "./mobile-menu"

type MainNavContentProps = {
  dataHeaderMenu: T_MenuByHandleResponse
  settings: T_SettingPreferences
  cart: TCartCustomField | null
  isHome?: boolean
  categoriesWithProducts?: ICategoryWithProducts[]
  countryCode?: string
}

export default function MainNavContent({
  dataHeaderMenu,
  settings,
  cart,
  isHome,
  categoriesWithProducts,
  countryCode,
}: MainNavContentProps) {
  const logoUrl = useMemo(() => settings?.logo?.url, [settings])
  const [checkoutData, setCheckoutData] = useState<any>(null)
  const { fetchCart, setCart } = useCartStateStore()

  useEffect(() => {
    if (cart) {
      setCart(cart)
    }
  }, [cart, setCart])

  useEffect(() => {
    if (!cart) return

    const fetchData = async () => {
      try {
        const customer = await retrieveCustomer()
        const data = await Promise.all([
          Promise.resolve(customer),
          getPromotions({}),
          listCartShippingMethods(cart.id),
          listCartPaymentMethods(cart.region?.id ?? "")
            .then((res) => {
              console.log("🚀 ~ listCartPaymentMethods ~ res:", res)
              return res // Return the result!
            })
            .catch((err) => {
              console.log("🚀 ~ listCartPaymentMethods ~ err:", err)
              return [] // Return empty array on error
            }),
          getCustomLocations(),
        ])

        setCheckoutData(data)
      } catch (error) {
        console.error("Error fetching checkout data:", error)
      }
    }

    fetchData()
  }, [cart])

  // Render cart dialog component
  const renderCartDialog = () => {
    return (
      <CartDialog
        cart={cart}
        customer={checkoutData?.[0] ?? {}}
        promotions={checkoutData?.[1]?.promotions ?? []}
        shippingMethods={checkoutData?.[2] ?? []}
        paymentMethods={checkoutData?.[3] ?? []}
        locationList={checkoutData?.[4] ?? []}
      />
    )
  }

  return (
    <nav
      className={cn(
        "txt-xsmall-plus h-full px-4 py-2 text-ui-fg-subtle shadow-[0_8px_24px_rgba(149,157,165,0.1)] lg:px-10 lg:pt-3"
      )}
    >
      {/* Mobile Navigation */}
      <div className="flex items-center justify-between lg:hidden">
        <LocalizedClientLink href={PAGE_PATH.HOME}>
          <Logo size={60} />
        </LocalizedClientLink>
        <div className="flex items-center justify-end space-x-2 sm:space-x-3">
          <SaleButton />

          {renderCartDialog()}
          <MobileMenu
            dataMenuLeft={dataHeaderMenu}
            dataMenuRight={null}
            isHomePage={!!isHome}
            scrolled={false}
            logoUrl={logoUrl}
            categoriesWithProducts={categoriesWithProducts}
            countryCode={countryCode}
          />
        </div>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden grid-cols-2 lg:grid">
        <div className="flex items-center gap-x-4">
          <LocalizedClientLink href={PAGE_PATH.HOME}>
            <Logo size={80} />
          </LocalizedClientLink>
          <Separator orientation="vertical" className="h-1/3 bg-primary-main" />
          <SearchInput />
        </div>
        <div className="flex items-center justify-end space-x-4">
          <ContactInfo />
          <Separator
            orientation="vertical"
            className="mx-3 h-1/3 bg-primary-main"
          />
          <ErrorBoundary>
            <SaleButton />
          </ErrorBoundary>

          <LanguageSelect />

          {renderCartDialog()}
        </div>
      </div>

      <Separator
        orientation="horizontal"
        className="mt-2 hidden bg-gray-200 lg:block"
      />
      <div className="w-full overflow-hidden">
        <div className="hidden items-center justify-center overflow-x-auto py-4 lg:flex">
          <MenuCategory
            dataMenu={dataHeaderMenu}
            isHome={isHome}
            categoriesWithProducts={categoriesWithProducts}
            countryCode={countryCode}
          />
        </div>
      </div>
    </nav>
  )
}
