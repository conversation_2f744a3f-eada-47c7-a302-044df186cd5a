{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-tag-detail-DEEKOXLB.mjs"], "sourcesContent": ["import {\n  useDeleteProductTagAction\n} from \"./chunk-7MM5L2ZM.mjs\";\nimport \"./chunk-YOVJWH6O.mjs\";\nimport \"./chunk-SYQ6IA6C.mjs\";\nimport \"./chunk-DGRTPKQC.mjs\";\nimport \"./chunk-DT7QVGFJ.mjs\";\nimport \"./chunk-I3VB6NM2.mjs\";\nimport \"./chunk-ZJRFL6ZN.mjs\";\nimport \"./chunk-DFA6WGYO.mjs\";\nimport \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-FHSC5X62.mjs\";\nimport \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-DM7MO4FV.mjs\";\nimport \"./chunk-OMC5JCQH.mjs\";\nimport \"./chunk-3IRPEKIV.mjs\";\nimport \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-3OHUAQUF.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport {\n  productTagsQueryKeys,\n  useProductTag\n} from \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/product-tags/product-tag-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductTagDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { product_tag } = useProductTag(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!product_tag) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: product_tag.value });\n};\n\n// src/routes/product-tags/product-tag-detail/loader.ts\nvar productTagDetailQuery = (id) => ({\n  queryKey: productTagsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.productTag.retrieve(id)\n});\nvar productTagLoader = async ({ params }) => {\n  const id = params.id;\n  const query = productTagDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/product-tags/product-tag-detail/product-tag-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/product-tags/product-tag-detail/components/product-tag-general-section/product-tag-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar ProductTagGeneralSection = ({\n  productTag\n}) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeleteProductTagAction({ productTag });\n  return /* @__PURE__ */ jsxs(Container, { className: \"flex items-center justify-between\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-1.5\", children: [\n      /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-muted h1-core\", children: \"#\" }),\n      /* @__PURE__ */ jsx2(Heading, { children: productTag.value })\n    ] }),\n    /* @__PURE__ */ jsx2(\n      ActionMenu,\n      {\n        groups: [\n          {\n            actions: [\n              {\n                icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                label: t(\"actions.edit\"),\n                to: \"edit\"\n              }\n            ]\n          },\n          {\n            actions: [\n              {\n                icon: /* @__PURE__ */ jsx2(Trash, {}),\n                label: t(\"actions.delete\"),\n                onClick: handleDelete\n              }\n            ]\n          }\n        ]\n      }\n    )\n  ] });\n};\n\n// src/routes/product-tags/product-tag-detail/components/product-tag-product-section/product-tag-product-section.tsx\nimport { Container as Container2, Heading as Heading2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar PREFIX = \"pt\";\nvar ProductTagProductSection = ({\n  productTag\n}) => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useProductTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { products, count, isPending, isError, error } = useProducts({\n    ...searchParams,\n    tag_id: productTag.id\n  });\n  const filters = useProductTableFilters([\"product_tags\"]);\n  const columns = useProductTableColumns();\n  const { table } = useDataTable({\n    data: products,\n    count,\n    columns,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y px-0 py-0\", children: [\n    /* @__PURE__ */ jsx3(\"div\", { className: \"px-6 py-4\", children: /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"products.domain\") }) }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        filters,\n        queryObject: raw,\n        isLoading: isPending,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        navigateTo: (row) => `/products/${row.original.id}`,\n        search: true,\n        pagination: true,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"status\", label: t(\"fields.status\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ]\n      }\n    )\n  ] });\n};\n\n// src/routes/product-tags/product-tag-detail/product-tag-detail.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ProductTagDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const { getWidgets } = useExtension();\n  const { product_tag, isPending, isError, error } = useProductTag(\n    id,\n    void 0,\n    {\n      initialData\n    }\n  );\n  if (isPending || !product_tag) {\n    return /* @__PURE__ */ jsx4(SingleColumnPageSkeleton, { showJSON: true, sections: 2 });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_tag.details.after\"),\n        before: getWidgets(\"product_tag.details.before\")\n      },\n      showJSON: true,\n      data: product_tag,\n      children: [\n        /* @__PURE__ */ jsx4(ProductTagGeneralSection, { productTag: product_tag }),\n        /* @__PURE__ */ jsx4(ProductTagProductSection, { productTag: product_tag })\n      ]\n    }\n  );\n};\nexport {\n  ProductTagDetailBreadcrumb as Breadcrumb,\n  ProductTagDetail as Component,\n  productTagLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsHA,yBAAoB;AA+BpB,IAAAA,sBAAkC;AA0ClC,IAAAC,sBAA2C;AAuD3C,IAAAA,sBAA2C;AA/H3C,IAAI,6BAA6B,CAAC,UAAU;AAC1C,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI,QAAQ;AAAA,IAChD,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,YAAY,MAAM,CAAC;AACpE;AAGA,IAAI,wBAAwB,CAAC,QAAQ;AAAA,EACnC,UAAU,qBAAqB,OAAO,EAAE;AAAA,EACxC,SAAS,YAAY,IAAI,MAAM,WAAW,SAAS,EAAE;AACvD;AACA,IAAI,mBAAmB,OAAO,EAAE,OAAO,MAAM;AAC3C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,sBAAsB,EAAE;AACtC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAUA,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,0BAA0B,EAAE,WAAW,CAAC;AAC7D,aAAuB,0BAAK,WAAW,EAAE,WAAW,qCAAqC,UAAU;AAAA,QACjF,0BAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,UAChE,oBAAAC,KAAK,QAAQ,EAAE,WAAW,4BAA4B,UAAU,IAAI,CAAC;AAAA,UACrE,oBAAAA,KAAK,SAAS,EAAE,UAAU,WAAW,MAAM,CAAC;AAAA,IAC9D,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,OAAO,EAAE,cAAc;AAAA,gBACvB,IAAI;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,gBACpC,OAAO,EAAE,gBAAgB;AAAA,gBACzB,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAMA,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI,YAAY;AAAA,IACjE,GAAG;AAAA,IACH,QAAQ,WAAW;AAAA,EACrB,CAAC;AACD,QAAM,UAAU,uBAAuB,CAAC,cAAc,CAAC;AACvD,QAAM,UAAU,uBAAuB;AACvC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,sBAAsB,UAAU;AAAA,QACpE,oBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,cAA0B,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;AAAA,QACjI,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,QACX;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,QAAQ,aAAa,IAAI,SAAS,EAAE;AAAA,QACjD,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,UAAU,OAAO,EAAE,eAAe,EAAE;AAAA,UAC3C,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,QAAM,EAAE,aAAa,WAAW,SAAS,MAAM,IAAI;AAAA,IACjD;AAAA,IACA;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,CAAC,aAAa;AAC7B,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,MAAM,UAAU,EAAE,CAAC;AAAA,EACvF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,2BAA2B;AAAA,QAC7C,QAAQ,WAAW,4BAA4B;AAAA,MACjD;AAAA,MACA,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAD,KAAK,0BAA0B,EAAE,YAAY,YAAY,CAAC;AAAA,YAC1D,oBAAAA,KAAK,0BAA0B,EAAE,YAAY,YAAY,CAAC;AAAA,MAC5E;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsx4", "jsxs3"]}