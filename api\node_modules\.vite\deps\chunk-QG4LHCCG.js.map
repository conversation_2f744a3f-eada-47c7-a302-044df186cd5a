{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-WRSGHGAT.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/query/use-customer-table-query.tsx\nvar useCustomerTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\n      \"offset\",\n      \"q\",\n      \"has_account\",\n      \"groups\",\n      \"order\",\n      \"created_at\",\n      \"updated_at\"\n    ],\n    prefix\n  );\n  const { offset, groups, created_at, updated_at, has_account, q, order } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    groups: groups?.split(\",\"),\n    has_account: has_account ? has_account === \"true\" : void 0,\n    order,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useCustomerTableQuery\n};\n"], "mappings": ";;;;;AAKA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,QAAQ,YAAY,YAAY,aAAa,GAAG,MAAM,IAAI;AAC1E,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC,QAAQ,iCAAQ,MAAM;AAAA,IACtB,aAAa,cAAc,gBAAgB,SAAS;AAAA,IACpD;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}