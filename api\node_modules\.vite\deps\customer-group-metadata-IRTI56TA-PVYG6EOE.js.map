{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-group-metadata-IRTI56TA.mjs"], "sourcesContent": ["import \"./chunk-XRTVFYCW.mjs\";\nimport {\n  MetadataForm\n} from \"./chunk-AL4WDQTN.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport \"./chunk-ZCO6EK4W.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCustomerGroup,\n  useUpdateCustomerGroup\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/customer-groups/customer-group-metadata/customer-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CustomerGroupMetadata = () => {\n  const { id } = useParams();\n  const { customer_group, isPending, isError, error } = useCustomerGroup(id);\n  const { mutateAsync, isPending: isMutating } = useUpdateCustomerGroup(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      metadata: customer_group?.metadata,\n      hook: mutateAsync,\n      isPending,\n      isMutating\n    }\n  );\n};\nexport {\n  CustomerGroupMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,yBAAoB;AACpB,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,gBAAgB,WAAW,SAAS,MAAM,IAAI,iBAAiB,EAAE;AACzE,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,uBAAuB,EAAE;AACxE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,UAAU,iDAAgB;AAAA,MAC1B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}