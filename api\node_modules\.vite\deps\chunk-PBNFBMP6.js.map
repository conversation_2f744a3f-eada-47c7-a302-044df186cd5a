{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LDJKJLBJ.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/tax-regions.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar TAX_REGIONS_QUERY_KEY = \"tax_regions\";\nvar taxRegionsQueryKeys = queryKeysFactory(TAX_REGIONS_QUERY_KEY);\nvar useTaxRegion = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: taxRegionsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.taxRegion.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useTaxRegions = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.taxRegion.list(query),\n    queryKey: taxRegionsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateTaxRegion = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.taxRegion.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteTaxRegion = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.taxRegion.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: taxRegionsQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: taxRegionsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  taxRegionsQueryKeys,\n  useTaxRegion,\n  useTaxRegions,\n  useCreateTaxRegion,\n  useDeleteTaxRegion\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB,iBAAiB,qBAAqB;AAChE,IAAI,eAAe,CAAC,IAAI,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,oBAAoB,OAAO,EAAE;AAAA,IACvC,SAAS,YAAY,IAAI,MAAM,UAAU,SAAS,IAAI,KAAK;AAAA,IAC3D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,gBAAgB,CAAC,OAAO,YAAY;AACtC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,UAAU,KAAK,KAAK;AAAA,IAC7C,UAAU,oBAAoB,KAAK,KAAK;AAAA,IACxC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,qBAAqB,CAAC,YAAY;AACpC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,OAAO,OAAO;AAAA,IAC3D,WAAW,CAAC,MAAM,WAAW,YAAY;AApC7C;AAqCM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,IAAI,CAAC;AACnE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,IAAI,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,UAAU,OAAO,EAAE;AAAA,IAC/C,WAAW,CAAC,MAAM,WAAW,YAAY;AA9C7C;AA+CM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,QAAQ,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}