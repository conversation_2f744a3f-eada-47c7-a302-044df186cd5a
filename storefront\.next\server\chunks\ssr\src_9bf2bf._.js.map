{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/categories/components/auto-scroll/index.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\n\ninterface AutoScrollProps {\n  targetId: string\n  offset?: number\n}\n\nexport default function AutoScroll({ targetId, offset = 0 }: AutoScrollProps) {\n  useEffect(() => {\n    // Always scroll to products section when component mounts\n    const timer = setTimeout(() => {\n      const targetElement = document.getElementById(targetId)\n      if (targetElement) {\n        const elementPosition =\n          targetElement.getBoundingClientRect().top + window.pageYOffset\n        const offsetPosition = elementPosition - offset\n\n        window.scrollTo({\n          top: offsetPosition,\n          behavior: \"smooth\",\n        })\n      }\n    }, 200) // Small delay to ensure the page is fully rendered\n\n    return () => clearTimeout(timer)\n  }, [targetId, offset])\n\n  return null\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AASe,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAmB;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,MAAM,QAAQ,WAAW;YACvB,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,IAAI,eAAe;gBACjB,MAAM,kBACJ,cAAc,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW;gBAChE,MAAM,iBAAiB,kBAAkB;gBAEzC,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF;QACF,GAAG,KAAK,mDAAmD;;QAE3D,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAO;IAErB,OAAO;AACT"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/constant.ts"], "sourcesContent": ["export type MenuFilterItem = {\r\n  id: string\r\n  name: string\r\n  value: string\r\n  image?: ImageData | null\r\n  children: MenuFilterChild[]\r\n}\r\n\r\nexport type ImageData = {\r\n  url: string\r\n  id: string\r\n}\r\n\r\nexport type MenuFilterChild = {\r\n  id: string\r\n  name: string\r\n  value: string\r\n  image?: ImageData | null\r\n  children?: MenuFilterChild[]\r\n}\r\n\r\nexport const MENU_FILTER_ITEMS: MenuFilterItem[] = [\r\n  // {\r\n  //   id: \"category-1\",\r\n  //   name: \"<PERSON><PERSON> mục\",\r\n  //   value: \"category\",\r\n  //   children: [],\r\n  // },\r\n  // {\r\n  //   id: \"program-promotion-1\",\r\n  //   name: \"<PERSON><PERSON><PERSON>ng trình khuyến mãi\",\r\n  //   value: \"program\",\r\n  //   children: [\r\n  //     {\r\n  //       id: \"program-promotion-1-1\",\r\n  //       name: \"Black Friday\",\r\n  //       value: \"black-friday\",\r\n  //     },\r\n  //     {\r\n  //       id: \"program-promotion-1-2\",\r\n  //       name: \"Red Friday\",\r\n  //       value: \"red-friday\",\r\n  //     },\r\n  //   ],\r\n  // },\r\n  // {\r\n  //   id: \"size-1\",\r\n  //   name: \"Kích cỡ\",\r\n  //   value: \"size\",\r\n  //   children: [\r\n  //     {\r\n  //       id: \"size-1-1\",\r\n  //       name: \"S\",\r\n  //       value: \"s\",\r\n  //     },\r\n  //     {\r\n  //       id: \"size-1-2\",\r\n  //       name: \"M\",\r\n  //       value: \"m\",\r\n  //     },\r\n  //     {\r\n  //       id: \"size-1-3\",\r\n  //       name: \"L\",\r\n  //       value: \"l\",\r\n  //     },\r\n  //   ],\r\n  // },\r\n  // {\r\n  //   id: \"color-1\",\r\n  //   name: \"Màu sắc\",\r\n  //   value: \"color\",\r\n  //   children: [\r\n  //     {\r\n  //       id: \"color-1-1\",\r\n  //       name: \"Đen\",\r\n  //       value: \"black\",\r\n  //     },\r\n  //     {\r\n  //       id: \"color-1-2\",\r\n  //       name: \"Trắng\",\r\n  //       value: \"white\",\r\n  //     },\r\n  //     {\r\n  //       id: \"color-1-3\",\r\n  //       name: \"Xanh\",\r\n  //       value: \"blue\",\r\n  //     },\r\n  //     {\r\n  //       id: \"color-1-4\",\r\n  //       name: \"Đỏ\",\r\n  //       value: \"red\",\r\n  //     },\r\n  //   ],\r\n  // },\r\n  {\r\n    id: \"price-1\",\r\n    name: \"Giá\",\r\n    value: \"price\",\r\n    children: [\r\n      {\r\n        id: \"price-1-1\",\r\n        name: \"Dưới 100.000đ\",\r\n        value: \"lt100000\",\r\n      },\r\n      {\r\n        id: \"price-1-2\",\r\n        name: \"Từ 100.000đ đến 200.000đ\",\r\n        value: \"100000-200000\",\r\n      },\r\n      {\r\n        id: \"price-1-3\",\r\n        name: \"Trên 200.000đ\",\r\n        value: \"gt200000\",\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\nexport const SORT_FILTER_ITEMS: {\r\n  id: number\r\n  value: string\r\n  name: string\r\n}[] = [\r\n  { id: 0, value: \"newest\", name: \"Mới nhất\" },\r\n  { id: 1, value: \"featured\", name: \"Nổi bật\" },\r\n  { id: 2, value: \"price_asc\", name: \"Giá tăng dần\" },\r\n  { id: 3, value: \"price_desc\", name: \"Giá giảm dần\" },\r\n]\r\n"], "names": [], "mappings": ";;;;AAqBO,MAAM,oBAAsC;IACjD,IAAI;IACJ,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,kBAAkB;IAClB,KAAK;IACL,IAAI;IACJ,+BAA+B;IAC/B,qCAAqC;IACrC,sBAAsB;IACtB,gBAAgB;IAChB,QAAQ;IACR,qCAAqC;IACrC,8BAA8B;IAC9B,+BAA+B;IAC/B,SAAS;IACT,QAAQ;IACR,qCAAqC;IACrC,4BAA4B;IAC5B,6BAA6B;IAC7B,SAAS;IACT,OAAO;IACP,KAAK;IACL,IAAI;IACJ,kBAAkB;IAClB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,QAAQ;IACR,wBAAwB;IACxB,mBAAmB;IACnB,oBAAoB;IACpB,SAAS;IACT,QAAQ;IACR,wBAAwB;IACxB,mBAAmB;IACnB,oBAAoB;IACpB,SAAS;IACT,QAAQ;IACR,wBAAwB;IACxB,mBAAmB;IACnB,oBAAoB;IACpB,SAAS;IACT,OAAO;IACP,KAAK;IACL,IAAI;IACJ,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,yBAAyB;IACzB,qBAAqB;IACrB,wBAAwB;IACxB,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,uBAAuB;IACvB,wBAAwB;IACxB,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,sBAAsB;IACtB,uBAAuB;IACvB,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,oBAAoB;IACpB,sBAAsB;IACtB,SAAS;IACT,OAAO;IACP,KAAK;IACL;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;YACT;SACD;IACH;CACD;AAEM,MAAM,oBAIP;IACJ;QAAE,IAAI;QAAG,OAAO;QAAU,MAAM;IAAW;IAC3C;QAAE,IAAI;QAAG,OAAO;QAAY,MAAM;IAAU;IAC5C;QAAE,IAAI;QAAG,OAAO;QAAa,MAAM;IAAe;IAClD;QAAE,IAAI;QAAG,OAAO;QAAc,MAAM;IAAe;CACpD"}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"shadow-md z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverAnchor, PopoverContent, PopoverTrigger }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AAHA;AAFA;;;;;AAOA,MAAM,UAAU,oKAAiB,IAAI;AAErC,MAAM,iBAAiB,oKAAiB,OAAO;AAE/C,MAAM,gBAAgB,oKAAiB,MAAM;AAE7C,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,oKAAiB,MAAM;kBACtB,cAAA,8OAAC,oKAAiB,OAAO;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,oKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/dual-range-slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\ninterface DualRangeSliderProps\r\n  extends React.ComponentProps<typeof SliderPrimitive.Root> {\r\n  labelPosition?: \"top\" | \"bottom\"\r\n  label?: (value: number | undefined) => React.ReactNode\r\n  isLabelStatic?: boolean\r\n}\r\n\r\nconst DualRangeSlider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  DualRangeSliderProps\r\n>(\r\n  (\r\n    {\r\n      className,\r\n      label,\r\n      labelPosition = \"top\",\r\n      isLabelStatic = false,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const initialValue = React.useMemo(() => {\r\n      return Array.isArray(props.value)\r\n        ? props.value\r\n        : [props.min || 0, props.max || 100]\r\n    }, [props.value, props.min, props.max])\r\n\r\n    return (\r\n      <SliderPrimitive.Root\r\n        ref={ref}\r\n        className={cn(\r\n          \"relative flex w-full touch-none select-none items-center\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <SliderPrimitive.Track className=\"relative h-1 w-full grow overflow-hidden rounded-[55px] bg-secondary\">\r\n          <SliderPrimitive.Range className=\"absolute h-full bg-primary-main\" />\r\n        </SliderPrimitive.Track>\r\n        {initialValue.map((value, index) => (\r\n          <React.Fragment key={index}>\r\n            <SliderPrimitive.Thumb className=\"relative block h-4 w-4 cursor-pointer rounded-full border-2 border-primary-main bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">\r\n              {label && (\r\n                <span\r\n                  className={cn(\r\n                    \"absolute flex w-full justify-center\",\r\n                    labelPosition === \"top\" && \"-top-7\",\r\n                    labelPosition === \"bottom\" && \"top-4\"\r\n                  )}\r\n                >\r\n                  {label(value)}\r\n                </span>\r\n              )}\r\n            </SliderPrimitive.Thumb>\r\n          </React.Fragment>\r\n        ))}\r\n      </SliderPrimitive.Root>\r\n    )\r\n  }\r\n)\r\nDualRangeSlider.displayName = \"DualRangeSlider\"\r\n\r\nexport { DualRangeSlider }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAHA;AAFA;;;;;AAcA,MAAM,gCAAkB,sMAAM,UAAU,CAItC,CACE,EACE,SAAS,EACT,KAAK,EACL,gBAAgB,KAAK,EACrB,gBAAgB,KAAK,EACrB,GAAG,OACJ,EACD;IAEA,MAAM,eAAe,sMAAM,OAAO,CAAC;QACjC,OAAO,MAAM,OAAO,CAAC,MAAM,KAAK,IAC5B,MAAM,KAAK,GACX;YAAC,MAAM,GAAG,IAAI;YAAG,MAAM,GAAG,IAAI;SAAI;IACxC,GAAG;QAAC,MAAM,KAAK;QAAE,MAAM,GAAG;QAAE,MAAM,GAAG;KAAC;IAEtC,qBACE,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,mKAAgB,KAAK;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,mKAAgB,KAAK;oBAAC,WAAU;;;;;;;;;;;YAElC,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,sMAAM,QAAQ;8BACb,cAAA,8OAAC,mKAAgB,KAAK;wBAAC,WAAU;kCAC9B,uBACC,8OAAC;4BACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,uCACA,kBAAkB,SAAS,UAC3B,kBAAkB,YAAY;sCAG/B,MAAM;;;;;;;;;;;mBAVM;;;;;;;;;;;AAkB7B;AAEF,gBAAgB,WAAW,GAAG"}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-form-mobile.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useRouter, useSearchParams } from \"next/navigation\"\r\nimport { Controller, useForm } from \"react-hook-form\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { convertCurrencyToLocale } from \"@lib/util/money\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\nimport { But<PERSON> } from \"components/ui/button\"\r\nimport { DualRangeSlider } from \"components/ui/dual-range-slider\"\r\nimport Typography from \"components/ui/typography\"\r\n\r\nimport React, { useEffect, useState } from \"react\"\r\nimport { cn } from \"utils\"\r\n\r\nconst minPrice = 0\r\nconst maxPrice = 5000000\r\n\r\nconst defaultPriceRange = [\r\n  {\r\n    min: 10000,\r\n    max: 100000,\r\n    label: \"10,000 đ - 100,000 đ\",\r\n  },\r\n  {\r\n    min: 100000,\r\n    max: 200000,\r\n    label: \"100,000 đ - 200,000 đ\",\r\n  },\r\n  {\r\n    min: 200000,\r\n    max: 400000,\r\n    label: \"200,000 đ - 400,000 đ\",\r\n  },\r\n  {\r\n    min: 400000,\r\n    max: 600000,\r\n    label: \"400,000 đ - 600,000 đ\",\r\n  },\r\n  {\r\n    min: 600000,\r\n    max: 800000,\r\n    label: \"600,000 đ - 800,000 đ\",\r\n  },\r\n  {\r\n    min: 800000,\r\n    max: maxPrice,\r\n    label: \"Trên 800,000 đ\",\r\n  },\r\n]\r\n\r\ntype FilterFormMobileProps = {\r\n  handle: string\r\n  onApply?: () => void\r\n}\r\n\r\nconst FilterFormMobile = ({ handle, onApply }: FilterFormMobileProps) => {\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const params = new URLSearchParams(searchParams.toString())\r\n  const { t } = useTranslation(\"templates\")\r\n\r\n  const priceRangeURL = params.get(\"priceRange\")\r\n  const priceRangeQuery = priceRangeURL ? priceRangeURL.split(\",\") : null\r\n\r\n  const [selectedPriceRange, setSelectedPriceRange] = useState<string>(\"\")\r\n\r\n  const initialPriceRange = React.useMemo(() => {\r\n    if (priceRangeQuery) {\r\n      return [Number(priceRangeQuery[0]), Number(priceRangeQuery[1])]\r\n    }\r\n    return [minPrice, maxPrice]\r\n  }, [priceRangeQuery])\r\n\r\n  const { control, handleSubmit, watch, setValue } = useForm({\r\n    defaultValues: {\r\n      priceRange: initialPriceRange,\r\n    },\r\n  })\r\n\r\n  const priceRangeForm = watch(\"priceRange\")\r\n\r\n  const onSubmit = (data: { priceRange: number[] }) => {\r\n    if (data.priceRange[0] === minPrice && data.priceRange[1] === maxPrice) {\r\n      params.delete(\"priceRange\")\r\n    } else {\r\n      params.set(\"priceRange\", data.priceRange.join(\",\"))\r\n    }\r\n    router.push(`${PAGE_PATH.CATEGORIES.detail(handle)}?${params.toString()}`, {\r\n      scroll: false,\r\n    })\r\n    onApply?.()\r\n  }\r\n\r\n  const handleSelectedPriceRange = (\r\n    min: number,\r\n    max: number,\r\n    label: string\r\n  ) => {\r\n    setSelectedPriceRange(label)\r\n    setValue(\"priceRange\", [min, max])\r\n  }\r\n\r\n  const handleReset = () => {\r\n    setSelectedPriceRange(\"\")\r\n    setValue(\"priceRange\", [minPrice, maxPrice])\r\n    params.delete(\"priceRange\")\r\n    router.push(`${PAGE_PATH.CATEGORIES.detail(handle)}?${params.toString()}`, {\r\n      scroll: false,\r\n    })\r\n    onApply?.()\r\n  }\r\n\r\n  // Initialize selected range from URL\r\n  useEffect(() => {\r\n    if (priceRangeQuery && !selectedPriceRange) {\r\n      const newSelectedPriceRange = defaultPriceRange.find(\r\n        (item) =>\r\n          item.min === Number(priceRangeQuery[0]) &&\r\n          item.max === Number(priceRangeQuery[1])\r\n      )\r\n      if (newSelectedPriceRange) {\r\n        setSelectedPriceRange(newSelectedPriceRange.label)\r\n      }\r\n    }\r\n  }, [priceRangeQuery, selectedPriceRange])\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\r\n      <div className=\"space-y-4\">\r\n        {/* Slider */}\r\n        <Controller\r\n          name=\"priceRange\"\r\n          control={control}\r\n          render={({ field: { onChange, value } }) => (\r\n            <DualRangeSlider\r\n              value={value}\r\n              onValueChange={onChange}\r\n              min={minPrice}\r\n              max={maxPrice}\r\n              step={50000}\r\n            />\r\n          )}\r\n        />\r\n\r\n        {/* Price Display */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <Typography\r\n            variant=\"p\"\r\n            size=\"sm\"\r\n            className=\"font-medium text-gray-600\"\r\n          >\r\n            {convertCurrencyToLocale({\r\n              amount: priceRangeForm[0],\r\n              currency_code: \"VND\",\r\n            })}\r\n          </Typography>\r\n          <Typography\r\n            variant=\"p\"\r\n            size=\"sm\"\r\n            className=\"font-medium text-gray-600\"\r\n          >\r\n            {convertCurrencyToLocale({\r\n              amount: priceRangeForm[1],\r\n              currency_code: \"VND\",\r\n            })}\r\n          </Typography>\r\n        </div>\r\n\r\n        {/* Preset Price Ranges */}\r\n        <div className=\"grid grid-cols-1 gap-2\">\r\n          {defaultPriceRange.map((item) => (\r\n            <button\r\n              type=\"button\"\r\n              onClick={() =>\r\n                handleSelectedPriceRange(item.min, item.max, item.label)\r\n              }\r\n              key={item.label}\r\n              className={cn(\r\n                \"flex h-10 items-center justify-center rounded-md border text-sm transition-colors\",\r\n                selectedPriceRange === item.label\r\n                  ? \"border-primary-main bg-primary-main/10 text-primary-main\"\r\n                  : \"border-gray-300 hover:border-gray-400\"\r\n              )}\r\n            >\r\n              {item.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Action Buttons */}\r\n        <div className=\"flex gap-2 pt-4\">\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={handleReset}\r\n            className=\"flex-1\"\r\n          >\r\n            {t(\"reset\")}\r\n          </Button>\r\n          <Button type=\"submit\" className=\"flex-1\">\r\n            {t(\"apply\")}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  )\r\n}\r\n\r\nexport default FilterFormMobile\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAVA;AADA;AAHA;;;;;;;;;;;;AAgBA,MAAM,WAAW;AACjB,MAAM,WAAW;AAEjB,MAAM,oBAAoB;IACxB;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;CACD;AAOD,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAyB;IAClE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;IACxD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,gBAAgB,OAAO,GAAG,CAAC;IACjC,MAAM,kBAAkB,gBAAgB,cAAc,KAAK,CAAC,OAAO;IAEnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErE,MAAM,oBAAoB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACtC,IAAI,iBAAiB;YACnB,OAAO;gBAAC,OAAO,eAAe,CAAC,EAAE;gBAAG,OAAO,eAAe,CAAC,EAAE;aAAE;QACjE;QACA,OAAO;YAAC;YAAU;SAAS;IAC7B,GAAG;QAAC;KAAgB;IAEpB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACzD,eAAe;YACb,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB,MAAM;IAE7B,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,UAAU,CAAC,EAAE,KAAK,YAAY,KAAK,UAAU,CAAC,EAAE,KAAK,UAAU;YACtE,OAAO,MAAM,CAAC;QAChB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc,KAAK,UAAU,CAAC,IAAI,CAAC;QAChD;QACA,OAAO,IAAI,CAAC,GAAG,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACzE,QAAQ;QACV;QACA;IACF;IAEA,MAAM,2BAA2B,CAC/B,KACA,KACA;QAEA,sBAAsB;QACtB,SAAS,cAAc;YAAC;YAAK;SAAI;IACnC;IAEA,MAAM,cAAc;QAClB,sBAAsB;QACtB,SAAS,cAAc;YAAC;YAAU;SAAS;QAC3C,OAAO,MAAM,CAAC;QACd,OAAO,IAAI,CAAC,GAAG,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACzE,QAAQ;QACV;QACA;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,CAAC,oBAAoB;YAC1C,MAAM,wBAAwB,kBAAkB,IAAI,CAClD,CAAC,OACC,KAAK,GAAG,KAAK,OAAO,eAAe,CAAC,EAAE,KACtC,KAAK,GAAG,KAAK,OAAO,eAAe,CAAC,EAAE;YAE1C,IAAI,uBAAuB;gBACzB,sBAAsB,sBAAsB,KAAK;YACnD;QACF;IACF,GAAG;QAAC;QAAiB;KAAmB;IAExC,qBACE,8OAAC;QAAK,UAAU,aAAa;QAAW,WAAU;kBAChD,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,8JAAA,CAAA,aAAU;oBACT,MAAK;oBACL,SAAS;oBACT,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,iBACrC,8OAAC,mJAAA,CAAA,kBAAe;4BACd,OAAO;4BACP,eAAe;4BACf,KAAK;4BACL,KAAK;4BACL,MAAM;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAET,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;gCACvB,QAAQ,cAAc,CAAC,EAAE;gCACzB,eAAe;4BACjB;;;;;;sCAEF,8OAAC,sIAAA,CAAA,UAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAET,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;gCACvB,QAAQ,cAAc,CAAC,EAAE;gCACzB,eAAe;4BACjB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,qBACtB,8OAAC;4BACC,MAAK;4BACL,SAAS,IACP,yBAAyB,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK;4BAGzD,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,qFACA,uBAAuB,KAAK,KAAK,GAC7B,6DACA;sCAGL,KAAK,KAAK;2BARN,KAAK,KAAK;;;;;;;;;;8BAcrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;sCAET,EAAE;;;;;;sCAEL,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;sCAC7B,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;uCAEe"}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-close.tsx"], "sourcesContent": ["type TCloseIconProps = {\r\n  className?: string\r\n  color?: string\r\n}\r\nexport const CloseIcon = ({ className, color }: TCloseIconProps) => {\r\n  return (\r\n    <svg\r\n      width={28}\r\n      height={28}\r\n      viewBox=\"0 0 28 28\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n    >\r\n      <path\r\n        d=\"M1.57134 28L0 26.4287L12.4287 14L0 1.57134L1.57134 0L14 12.4287L26.4287 0L28 1.57134L15.5713 14L28 26.4287L26.4287 28L14 15.5713L1.57134 28Z\"\r\n        fill={color}\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAIO,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,KAAK,EAAmB;IAC7D,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;kBAEX,cAAA,8OAAC;YACC,GAAE;YACF,MAAM;;;;;;;;;;;AAId"}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-results-tags/index.tsx"], "sourcesContent": ["import { useRouter, useSearchParams } from \"next/navigation\"\r\n\r\nimport Typography from \"components/ui/typography\"\r\nimport { Button } from \"components/ui/button\"\r\n\r\nimport { CloseIcon } from \"components/svg/ic-close\"\r\nimport { Toolt<PERSON>, TooltipContent, TooltipTrigger } from \"components/ui/tooltip\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport Divider from \"@modules/common/components/divider\"\r\nimport { convertCurrencyToLocale } from \"@lib/util/money\"\r\n\r\nexport default function FilterResultsTags() {\r\n  const { t } = useTranslation(\"collections\")\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const queryParams = Object.fromEntries(searchParams.entries())\r\n\r\n  const optionList = Object.entries(queryParams)\r\n\r\n  const handleRemoveFilter = (key: string) => {\r\n    const newSearchParams = new URLSearchParams(searchParams)\r\n    newSearchParams.delete(key)\r\n    router.push(`?${newSearchParams.toString()}`, { scroll: false })\r\n  }\r\n\r\n  const handleRemoveAllFilter = () => {\r\n    router.push(`?${new URLSearchParams().toString()}`, { scroll: false })\r\n  }\r\n\r\n  if (!optionList.length) return null\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex items-center gap-2 flex-wrap\">\r\n        {optionList.map(([key, value]) =>\r\n          key === \"priceRange\" ? (\r\n            <div\r\n              key={key}\r\n              className=\"flex items-center gap-2 border-2 bg-primary-lighter border-primary-main rounded-lg px-2 py-1\"\r\n            >\r\n              <Tooltip>\r\n                <TooltipTrigger>\r\n                  <div\r\n                    onClick={() => handleRemoveFilter(key)}\r\n                    className=\"cursor-pointer bg-primary-main rounded-full p-1\"\r\n                  >\r\n                    <CloseIcon className=\"w-2 h-2\" color=\"#fff\" />\r\n                  </div>\r\n                </TooltipTrigger>\r\n                {/* <TooltipContent>\r\n                {t(\"remove\")} {t(`filter_menu.${key}`)}\r\n              </TooltipContent> */}\r\n              </Tooltip>\r\n              <Typography variant=\"p\" size=\"base\">\r\n                {/* {t(`filter_menu.${key}`)}: {value} */}\r\n                {convertCurrencyToLocale({\r\n                  amount: Number(value.split(\",\")[0]),\r\n                  currency_code: \"VND\",\r\n                })}\r\n                &nbsp;-&nbsp;\r\n                {convertCurrencyToLocale({\r\n                  amount: Number(value.split(\",\")[1]),\r\n                  currency_code: \"VND\",\r\n                })}\r\n              </Typography>\r\n            </div>\r\n          ) : null\r\n        )}\r\n\r\n        {/* {optionList.length > 1 && (\r\n          <Button\r\n            variant=\"link\"\r\n            onClick={handleRemoveAllFilter}\r\n            className=\"text-sm !h-fit\"\r\n          >\r\n            {t(\"filter_menu.remove_all\")}\r\n          </Button>\r\n        )} */}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAGA;AACA;AACA;AAEA;AAFA;;;;;;;;AAIe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,OAAO,WAAW,CAAC,aAAa,OAAO;IAE3D,MAAM,aAAa,OAAO,OAAO,CAAC;IAElC,MAAM,qBAAqB,CAAC;QAC1B,MAAM,kBAAkB,IAAI,gBAAgB;QAC5C,gBAAgB,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,gBAAgB,QAAQ,IAAI,EAAE;YAAE,QAAQ;QAAM;IAChE;IAEA,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,kBAAkB,QAAQ,IAAI,EAAE;YAAE,QAAQ;QAAM;IACtE;IAEA,IAAI,CAAC,WAAW,MAAM,EAAE,OAAO;IAE/B,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,WAAU;sBACZ,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAC3B,QAAQ,6BACN,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC,mIAAA,CAAA,UAAO;sCACN,cAAA,8OAAC,mIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAEV,cAAA,8OAAC,wIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,OAAM;;;;;;;;;;;;;;;;;;;;;sCAO3C,8OAAC,sIAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAI,MAAK;;gCAE1B,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;oCACvB,QAAQ,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;oCAClC,eAAe;gCACjB;gCAAG;gCAEF,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;oCACvB,QAAQ,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;oCAClC,eAAe;gCACjB;;;;;;;;mBA1BG;;;;2BA6BL;;;;;;;;;;;AAed"}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-dialog-mobile/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Settings2 } from \"lucide-react\"\r\nimport { useSearchParams } from \"next/navigation\"\r\nimport { useState } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport {\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"components/ui/accordion\"\r\nimport { Button } from \"components/ui/button\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"components/ui/dialog\"\r\nimport Typography from \"components/ui/typography\"\r\n\r\nimport Divider from \"@modules/common/components/divider\"\r\nimport { MenuFilterItem } from \"../../constant\"\r\nimport FilterFormMobile from \"../filter-form-mobile\"\r\nimport FilterResultsTags from \"../filter-results-tags\"\r\n\r\ntype DialogFilterProps = {\r\n  menuFilter: MenuFilterItem[]\r\n  handle: string\r\n}\r\n\r\nexport default function DialogMobileFilter({\r\n  menuFilter,\r\n  handle,\r\n}: DialogFilterProps) {\r\n  const { t } = useTranslation([\"collections\", \"templates\"])\r\n  const [open, setOpen] = useState(false)\r\n\r\n  const searchParams = useSearchParams()\r\n  const queryParams = Object.fromEntries(searchParams.entries())\r\n\r\n  const accordionItems = (item: MenuFilterItem) => (\r\n    <AccordionItem key={item.id} value={item.id.toString()}>\r\n      <AccordionTrigger>\r\n        <Typography variant=\"p\" size=\"base\">\r\n          {t(`collections:filter_menu.${item.value}`)}\r\n        </Typography>\r\n      </AccordionTrigger>\r\n      <AccordionContent className=\"ml-2 flex flex-col gap-2\">\r\n        {item.children.map((child) => (\r\n          <Typography key={child.id} variant=\"p\" className=\"text-sm\">\r\n            {child.name}\r\n          </Typography>\r\n        ))}\r\n      </AccordionContent>\r\n    </AccordionItem>\r\n  )\r\n\r\n  const handleApply = () => {\r\n    setOpen(false)\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button variant=\"outline\" className=\"relative !h-9\">\r\n          <Settings2 className=\"mr-2 h-4 w-4\" />\r\n          <span className=\"text-sm\">{t(\"collections:filter\")}</span>\r\n          {Object.keys(queryParams).length > 0 && (\r\n            <div className=\"absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary-main text-xs text-white\">\r\n              {Object.keys(queryParams).length}\r\n            </div>\r\n          )}\r\n        </Button>\r\n      </DialogTrigger>\r\n\r\n      <DialogContent className=\"mx-auto flex max-h-[90vh] w-[calc(100%-2rem)] max-w-md flex-col overflow-hidden rounded-lg p-4\">\r\n        <DialogHeader className=\"flex-shrink-0\">\r\n          <DialogTitle className=\"flex items-center justify-between\">\r\n            <Typography\r\n              variant=\"p\"\r\n              size=\"lg\"\r\n              color=\"primary\"\r\n              className=\"font-bold\"\r\n            >\r\n              {t(\"collections:filter\")}\r\n            </Typography>\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex-1 space-y-4 overflow-y-auto\">\r\n          <FilterResultsTags />\r\n\r\n          <Divider />\r\n\r\n          {/* Price Filter Section */}\r\n          <div className=\"space-y-2\">\r\n            <Typography variant=\"p\" size=\"base\" className=\"font-semibold\">\r\n              {t(\"templates:price_range\")}\r\n            </Typography>\r\n            <FilterFormMobile handle={handle} onApply={handleApply} />\r\n          </div>\r\n\r\n          <Divider />\r\n\r\n          {/* Category Filters */}\r\n          {/* {menuFilter.length > 0 && (\r\n            <Accordion type=\"single\" collapsible className=\"w-full\">\r\n              {menuFilter.map((item) => {\r\n                if (item.children) {\r\n                  return accordionItems(item)\r\n                }\r\n                return null\r\n              })}\r\n            </Accordion>\r\n          )} */}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AAKA;AACA;AAOA;AAEA;AAEA;AACA;AApBA;AAHA;AAFA;;;;;;;;;;;;;AAgCe,SAAS,mBAAmB,EACzC,UAAU,EACV,MAAM,EACY;IAClB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAe;KAAY;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,OAAO,WAAW,CAAC,aAAa,OAAO;IAE3D,MAAM,iBAAiB,CAAC,qBACtB,8OAAC,qIAAA,CAAA,gBAAa;YAAe,OAAO,KAAK,EAAE,CAAC,QAAQ;;8BAClD,8OAAC,qIAAA,CAAA,mBAAgB;8BACf,cAAA,8OAAC,sIAAA,CAAA,UAAU;wBAAC,SAAQ;wBAAI,MAAK;kCAC1B,EAAE,CAAC,wBAAwB,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;8BAG9C,8OAAC,qIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BACzB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,sIAAA,CAAA,UAAU;4BAAgB,SAAQ;4BAAI,WAAU;sCAC9C,MAAM,IAAI;2BADI,MAAM,EAAE;;;;;;;;;;;WARX,KAAK,EAAE;;;;;IAgB7B,MAAM,cAAc;QAClB,QAAQ;IACV;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAU;;sCAClC,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAK,WAAU;sCAAW,EAAE;;;;;;wBAC5B,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,mBACjC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,IAAI,CAAC,aAAa,MAAM;;;;;;;;;;;;;;;;;0BAMxC,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC,sIAAA,CAAA,UAAU;gCACT,SAAQ;gCACR,MAAK;gCACL,OAAM;gCACN,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kLAAA,CAAA,UAAiB;;;;;0CAElB,8OAAC,2JAAA,CAAA,UAAO;;;;;0CAGR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sIAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAI,MAAK;wCAAO,WAAU;kDAC3C,EAAE;;;;;;kDAEL,8OAAC,wKAAA,CAAA,UAAgB;wCAAC,QAAQ;wCAAQ,SAAS;;;;;;;;;;;;0CAG7C,8OAAC,2JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;AAiBlB"}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useRouter, useSearchParams } from \"next/navigation\"\r\nimport { Controller, useForm } from \"react-hook-form\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { convertCurrencyToLocale } from \"@lib/util/money\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\nimport { But<PERSON> } from \"components/ui/button\"\r\nimport { DualRangeSlider } from \"components/ui/dual-range-slider\"\r\nimport Typography from \"components/ui/typography\"\r\n\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"components/ui/card\"\r\nimport { useEffect, useState } from \"react\"\r\nimport { cn } from \"utils\"\r\nconst minPrice = 0\r\nconst maxPrice = 5000000\r\n\r\nconst defaultPriceRange = [\r\n  {\r\n    min: 10000,\r\n    max: 100000,\r\n    label: \"10,000 đ - 100,000 đ\",\r\n  },\r\n  {\r\n    min: 100000,\r\n    max: 200000,\r\n    label: \"100,000 đ - 200,000 đ\",\r\n  },\r\n  {\r\n    min: 200000,\r\n    max: 400000,\r\n    label: \"200,000 đ - 400,000 đ\",\r\n  },\r\n  {\r\n    min: 400000,\r\n    max: 600000,\r\n    label: \"400,000 đ - 600,000 đ\",\r\n  },\r\n  {\r\n    min: 600000,\r\n    max: 800000,\r\n    label: \"600,000 đ - 800,000 đ\",\r\n  },\r\n  {\r\n    min: 800000,\r\n    max: maxPrice,\r\n    label: \"Trên 800,000 đ\",\r\n  },\r\n]\r\n\r\nconst FilterForm = ({ handle }: { handle: string }) => {\r\n  const router = useRouter()\r\n  const [chosenPriceRange, setChosenPriceRange] = useState<{\r\n    min: number\r\n    max: number\r\n  }>({\r\n    min: minPrice,\r\n    max: maxPrice,\r\n  })\r\n  const [selectedPriceRange, setSelectedPriceRange] = useState<string>(\"\")\r\n  const searchParams = useSearchParams()\r\n  const params = new URLSearchParams(searchParams.toString())\r\n  const { t } = useTranslation(\"templates\")\r\n\r\n  const isDisabled = params.get(\"priceRange\") ? false : true\r\n  const priceRangeURL = params.get(\"priceRange\")\r\n  const priceRangeQuery = priceRangeURL ? priceRangeURL.split(\",\") : null\r\n\r\n  const { control, handleSubmit, watch, reset, setValue, getValues } = useForm({\r\n    defaultValues: {\r\n      priceRange: priceRangeQuery\r\n        ? [Number(priceRangeQuery[0]), Number(priceRangeQuery[1])]\r\n        : [chosenPriceRange.min, chosenPriceRange.max],\r\n    },\r\n  })\r\n\r\n  const priceRangeForm = watch(\"priceRange\")\r\n\r\n  const onSubmit = (data: { priceRange: number[] }) => {\r\n    if (data.priceRange[0] === minPrice && data.priceRange[1] === maxPrice) {\r\n      params.delete(\"priceRange\")\r\n    } else {\r\n      params.set(\"priceRange\", data.priceRange.join(\",\"))\r\n    }\r\n    router.push(`${PAGE_PATH.CATEGORIES.detail(handle)}?${params.toString()}`, {\r\n      scroll: false,\r\n    })\r\n  }\r\n\r\n  const handleSelectedPriceRange = (\r\n    min: number,\r\n    max: number,\r\n    label: string\r\n  ) => {\r\n    setChosenPriceRange({ min, max })\r\n    setSelectedPriceRange(label)\r\n  }\r\n\r\n  //   const handleReset = () => {\r\n  //     reset({ priceRange: [minPrice, maxPrice] })\r\n  //     params.delete(\"priceRange\")\r\n  //     router.push(`${PAGE_PATH.CATEGORIES.detail(handle)}?${params.toString()}`, {\r\n  //       scroll: false,\r\n  //     })\r\n  //   }\r\n\r\n  //   console.log(\"priceRangeQuery\", priceRangeQuery)\r\n\r\n  useEffect(() => {\r\n    if (priceRangeQuery && !selectedPriceRange) {\r\n      const newSelectedPriceRange = defaultPriceRange.find(\r\n        (item) =>\r\n          item.min === Number(priceRangeQuery[0]) &&\r\n          item.max === Number(priceRangeQuery[1])\r\n      )\r\n      setValue(\"priceRange\", [\r\n        newSelectedPriceRange?.min ?? Number(priceRangeQuery[0]),\r\n        newSelectedPriceRange?.max ?? Number(priceRangeQuery[1]),\r\n      ])\r\n      setSelectedPriceRange(newSelectedPriceRange?.label ?? \"\")\r\n    } else {\r\n      setValue(\"priceRange\", [chosenPriceRange.min, chosenPriceRange.max])\r\n    }\r\n  }, [chosenPriceRange])\r\n\r\n  //   useEffect(() => {\r\n  //     if (priceRangeQuery && !selectedPriceRange) {\r\n  //       const min = Number(priceRangeQuery[0])\r\n  //       const max = Number(priceRangeQuery[1])\r\n\r\n  //       setChosenPriceRange({ min, max })\r\n  //       setValue(\"priceRange\", [min, max])\r\n\r\n  //       const selectedLabel = defaultPriceRange.find(\r\n  //         (range) => range.min === min && range.max === max\r\n  //       )?.label\r\n\r\n  //       setSelectedPriceRange(selectedLabel ?? \"\")\r\n  //     } else {\r\n  //       setValue(\"priceRange\", [chosenPriceRange.min, chosenPriceRange.max])\r\n  //     }\r\n  //   }, [chosenPriceRange, selectedPriceRange])\r\n\r\n  return (\r\n    <Card className=\"border-none\">\r\n      <CardHeader className=\"!pb-0\">\r\n        <CardTitle>\r\n          <div className=\"flex items-center justify-between\">\r\n            <Typography\r\n              variant=\"h6\"\r\n              size=\"base\"\r\n              color=\"black\"\r\n              className=\"font-semibold\"\r\n            >\r\n              {t(\"price_from\")}\r\n            </Typography>\r\n            {/* <Button\r\n              variant=\"ghost\"\r\n              className=\"text-gray-600 font-bold px-0 hover:bg-transparent\"\r\n              onClick={handleReset}\r\n              disabled={isDisabled}\r\n            >\r\n              {t(\"all_products.reset_filter\")}\r\n            </Button> */}\r\n          </div>\r\n        </CardTitle>\r\n      </CardHeader>\r\n\r\n      <form onSubmit={handleSubmit(onSubmit)} className=\"flex flex-col\">\r\n        <CardContent>\r\n          <div className=\"flex flex-col gap-4\">\r\n            {/* <Typography\r\n              variant=\"p\"\r\n              size=\"md\"\r\n              className=\"mb-4 text-primary-main font-bold\"\r\n            >\r\n              {t(\"all_products.price\")}\r\n            </Typography> */}\r\n            <Controller\r\n              name=\"priceRange\"\r\n              control={control}\r\n              render={({ field: { onChange, value } }) => (\r\n                <DualRangeSlider\r\n                  value={value}\r\n                  onValueChange={onChange}\r\n                  min={minPrice}\r\n                  max={maxPrice}\r\n                  step={50000}\r\n                />\r\n              )}\r\n            />\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <Typography\r\n                variant=\"p\"\r\n                size=\"sm\"\r\n                className=\"mb-4 font-normal text-gray-500\"\r\n              >\r\n                {convertCurrencyToLocale({\r\n                  amount: priceRangeForm[0],\r\n                  currency_code: \"VND\",\r\n                })}\r\n              </Typography>\r\n              <Typography\r\n                variant=\"p\"\r\n                size=\"sm\"\r\n                className=\"mb-4 font-normal text-gray-450\"\r\n              >\r\n                {convertCurrencyToLocale({\r\n                  amount: priceRangeForm[1],\r\n                  currency_code: \"VND\",\r\n                })}\r\n              </Typography>\r\n            </div>\r\n          </div>\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            {defaultPriceRange.map((item) => (\r\n              <div\r\n                onClick={() =>\r\n                  handleSelectedPriceRange(item.min, item.max, item.label)\r\n                }\r\n                key={item.label}\r\n                className={cn(\r\n                  \"flex h-[40px] min-w-[210px] cursor-pointer items-center justify-center rounded-xs border\",\r\n                  selectedPriceRange === item.label\r\n                    ? \"border-primary-main\"\r\n                    : \"border-gray-450\"\r\n                )}\r\n              >\r\n                <Typography\r\n                  variant=\"p\"\r\n                  size=\"sm\"\r\n                  className={cn(\r\n                    \"font-medium\",\r\n                    selectedPriceRange === item.label\r\n                      ? \"text-primary-main\"\r\n                      : \"text-gray-450\"\r\n                  )}\r\n                >\r\n                  {item.label}\r\n                </Typography>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </CardContent>\r\n\r\n        <CardFooter>\r\n          <Button type=\"submit\" size=\"lg\" className=\"w-full rounded-[55px]\">\r\n            {t(\"all_products.apply\")}\r\n          </Button>\r\n        </CardFooter>\r\n      </form>\r\n    </Card>\r\n  )\r\n}\r\n\r\nexport default FilterForm\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAOA;AACA;AAjBA;AADA;AAHA;;;;;;;;;;;;;AAsBA,MAAM,WAAW;AACjB,MAAM,WAAW;AAEjB,MAAM,oBAAoB;IACxB;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;CACD;AAED,MAAM,aAAa,CAAC,EAAE,MAAM,EAAsB;IAChD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGpD;QACD,KAAK;QACL,KAAK;IACP;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;IACxD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,aAAa,OAAO,GAAG,CAAC,gBAAgB,QAAQ;IACtD,MAAM,gBAAgB,OAAO,GAAG,CAAC;IACjC,MAAM,kBAAkB,gBAAgB,cAAc,KAAK,CAAC,OAAO;IAEnE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QAC3E,eAAe;YACb,YAAY,kBACR;gBAAC,OAAO,eAAe,CAAC,EAAE;gBAAG,OAAO,eAAe,CAAC,EAAE;aAAE,GACxD;gBAAC,iBAAiB,GAAG;gBAAE,iBAAiB,GAAG;aAAC;QAClD;IACF;IAEA,MAAM,iBAAiB,MAAM;IAE7B,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,UAAU,CAAC,EAAE,KAAK,YAAY,KAAK,UAAU,CAAC,EAAE,KAAK,UAAU;YACtE,OAAO,MAAM,CAAC;QAChB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc,KAAK,UAAU,CAAC,IAAI,CAAC;QAChD;QACA,OAAO,IAAI,CAAC,GAAG,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACzE,QAAQ;QACV;IACF;IAEA,MAAM,2BAA2B,CAC/B,KACA,KACA;QAEA,oBAAoB;YAAE;YAAK;QAAI;QAC/B,sBAAsB;IACxB;IAEA,gCAAgC;IAChC,kDAAkD;IAClD,kCAAkC;IAClC,mFAAmF;IACnF,uBAAuB;IACvB,SAAS;IACT,MAAM;IAEN,oDAAoD;IAEpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,CAAC,oBAAoB;YAC1C,MAAM,wBAAwB,kBAAkB,IAAI,CAClD,CAAC,OACC,KAAK,GAAG,KAAK,OAAO,eAAe,CAAC,EAAE,KACtC,KAAK,GAAG,KAAK,OAAO,eAAe,CAAC,EAAE;YAE1C,SAAS,cAAc;gBACrB,uBAAuB,OAAO,OAAO,eAAe,CAAC,EAAE;gBACvD,uBAAuB,OAAO,OAAO,eAAe,CAAC,EAAE;aACxD;YACD,sBAAsB,uBAAuB,SAAS;QACxD,OAAO;YACL,SAAS,cAAc;gBAAC,iBAAiB,GAAG;gBAAE,iBAAiB,GAAG;aAAC;QACrE;IACF,GAAG;QAAC;KAAiB;IAErB,sBAAsB;IACtB,oDAAoD;IACpD,+CAA+C;IAC/C,+CAA+C;IAE/C,0CAA0C;IAC1C,2CAA2C;IAE3C,sDAAsD;IACtD,4DAA4D;IAC5D,iBAAiB;IAEjB,mDAAmD;IACnD,eAAe;IACf,6EAA6E;IAC7E,QAAQ;IACR,+CAA+C;IAE/C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;8BACR,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,OAAM;4BACN,WAAU;sCAET,EAAE;;;;;;;;;;;;;;;;;;;;;0BAcX,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAChD,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDAQb,8OAAC,8JAAA,CAAA,aAAU;wCACT,MAAK;wCACL,SAAS;wCACT,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,iBACrC,8OAAC,mJAAA,CAAA,kBAAe;gDACd,OAAO;gDACP,eAAe;gDACf,KAAK;gDACL,KAAK;gDACL,MAAM;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,UAAU;gDACT,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAET,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;oDACvB,QAAQ,cAAc,CAAC,EAAE;oDACzB,eAAe;gDACjB;;;;;;0DAEF,8OAAC,sIAAA,CAAA,UAAU;gDACT,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAET,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;oDACvB,QAAQ,cAAc,CAAC,EAAE;oDACzB,eAAe;gDACjB;;;;;;;;;;;;;;;;;;0CAIN,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,qBACtB,8OAAC;wCACC,SAAS,IACP,yBAAyB,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK;wCAGzD,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uBAAuB,KAAK,KAAK,GAC7B,wBACA;kDAGN,cAAA,8OAAC,sIAAA,CAAA,UAAU;4CACT,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,eACA,uBAAuB,KAAK,KAAK,GAC7B,sBACA;sDAGL,KAAK,KAAK;;;;;;uCAlBR,KAAK,KAAK;;;;;;;;;;;;;;;;kCAyBvB,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,MAAK;4BAAK,WAAU;sCACvC,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;uCAEe"}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-sort/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ArrowDownWideNarrow } from \"lucide-react\"\r\nimport { usePathname, useRouter, useSearchParams } from \"next/navigation\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport useMediaQuery from \"hooks/use-media-query\"\r\nimport { SORT_FILTER_ITEMS } from \"../../constant\"\r\n\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"components/ui/select\"\r\nimport Typography from \"components/ui/typography\"\r\n\r\nimport { SortOptions } from \"@modules/store/components/refinement-list/sort-products\"\r\nimport { cn } from \"utils\"\r\n\r\nexport default function SortFilter() {\r\n  const isMobile = useMediaQuery(\"(max-width: 768px)\")\r\n  const { t } = useTranslation(\"collections\")\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const sortByUrl = searchParams.get(\"sort_by\") as SortOptions | null\r\n  const pathname = usePathname()\r\n\r\n  const handleSortBy = (value: SortOptions) => {\r\n    const params = new URLSearchParams(searchParams.toString())\r\n    params.set(\"sort_by\", value)\r\n    router.replace(`${pathname}?${params.toString()}`, {\r\n      scroll: false,\r\n    })\r\n  }\r\n\r\n  return (\r\n    <Select onValueChange={handleSortBy} defaultValue={sortByUrl || \"\"}>\r\n      <SelectTrigger\r\n        className={cn(\r\n          \"shadow-sm !h-fit gap-2 border border-gray-300 bg-transparent bg-white px-3 font-medium text-gray-700 hover:border-primary-main hover:bg-primary-lighter hover:text-primary-main\",\r\n          \"\",\r\n          !isMobile && \"h-full rounded-md\"\r\n        )}\r\n        isShowIcon={!isMobile}\r\n      >\r\n        {isMobile ? (\r\n          <ArrowDownWideNarrow className=\"h-4 w-4\" />\r\n        ) : (\r\n          <SelectValue placeholder={t(\"sort_menu.title\")} className=\"text-xs\" />\r\n        )}\r\n      </SelectTrigger>\r\n      <SelectContent>\r\n        <SelectGroup>\r\n          <SelectLabel className=\"text-sm\">\r\n            <Typography variant=\"p\" size=\"sm\">\r\n              {t(\"sort_menu.title\")}\r\n            </Typography>\r\n          </SelectLabel>\r\n          {SORT_FILTER_ITEMS.map((item) => (\r\n            <SelectItem key={item.id} value={item.value}>\r\n              <Typography variant=\"p\" size=\"sm\">\r\n                {t(`sort_menu.${item.value}`)}\r\n              </Typography>\r\n            </SelectItem>\r\n          ))}\r\n        </SelectGroup>\r\n      </SelectContent>\r\n    </Select>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AAEA;AASA;AAGA;AAjBA;AAFA;AAFA;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAC/B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,WAAW;QACtB,OAAO,OAAO,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACjD,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,eAAe;QAAc,cAAc,aAAa;;0BAC9D,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,mLACA,IACA,CAAC,YAAY;gBAEf,YAAY,CAAC;0BAEZ,yBACC,8OAAC,4OAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;yCAE/B,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAa,EAAE;oBAAoB,WAAU;;;;;;;;;;;0BAG9D,8OAAC,kIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;sCACV,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC,sIAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAI,MAAK;0CAC1B,EAAE;;;;;;;;;;;wBAGN,yIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC,kIAAA,CAAA,aAAU;gCAAe,OAAO,KAAK,KAAK;0CACzC,cAAA,8OAAC,sIAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAI,MAAK;8CAC1B,EAAE,CAAC,UAAU,EAAE,KAAK,KAAK,EAAE;;;;;;+BAFf,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAUpC"}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/filter-section/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useMemo, useState } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { ICategoryListWithMetadata } from \"types/category\"\r\n\r\nimport useMediaQuery from \"hooks/use-media-query\"\r\nimport { MENU_FILTER_ITEMS, MenuFilterItem } from \"../../constant\"\r\n\r\nimport { Button } from \"components/ui/button\"\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"components/ui/popover\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\nimport DialogMobileFilter from \"../filter-dialog-mobile\"\r\nimport FilterForm from \"../filter-form\"\r\nimport FilterResultsTags from \"../filter-results-tags\"\r\nimport SortFilter from \"../filter-sort\"\r\n\r\ntype FilterSectionProps = {\r\n  categoryList: ICategoryListWithMetadata[]\r\n  // regions: HttpTypes.StoreRegion\r\n  handle: string\r\n}\r\n\r\n// FORMAT MENU CATEGORY\r\nconst getMenuFilterItems = (categoryList: ICategoryListWithMetadata[]) => {\r\n  return MENU_FILTER_ITEMS.map((item) => {\r\n    if (item.value === \"category\") {\r\n      const mainMenu = categoryList.map((category) => {\r\n        const children =\r\n          category.category_children?.map((child) => ({\r\n            id: child.id,\r\n            name: child.name,\r\n            value: child.handle,\r\n            image: child.metadata?.data?.image || null,\r\n          })) || []\r\n\r\n        return {\r\n          id: category.id,\r\n          name: category.name,\r\n          value: category.handle,\r\n          image: category.metadata?.data?.image || null,\r\n          children,\r\n        }\r\n      })\r\n\r\n      return { ...item, children: mainMenu }\r\n    }\r\n    return item\r\n  })\r\n}\r\n\r\nexport default function FilterSection({\r\n  categoryList,\r\n  // regions,\r\n  handle,\r\n}: FilterSectionProps) {\r\n  const { t } = useTranslation(\"templates\")\r\n  const isMobile = useMediaQuery(\"(max-width: 768px)\")\r\n  const [isOpen, setIsOpen] = useState(false)\r\n\r\n  const MenuFilter: MenuFilterItem[] = useMemo(\r\n    () => getMenuFilterItems(categoryList),\r\n    [categoryList]\r\n  )\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-between gap-x-5\">\r\n      <div className=\"flex flex-grow items-center gap-6\">\r\n        {isMobile ? (\r\n          <DialogMobileFilter menuFilter={MenuFilter} handle={handle} />\r\n        ) : (\r\n          <div className=\"flex items-center gap-x-6\">\r\n            {/* Filter button list */}\r\n            <FilterResultsTags />\r\n            <div className=\"flex flex-wrap items-center gap-2\">\r\n              {/* {MenuFilter.map((item) => (\r\n                <FilterButtonDropdownMenu\r\n                  key={item.id}\r\n                  item={item}\r\n                  regions={regions}\r\n                  handle={handle}\r\n                />\r\n              ))} */}\r\n              <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n                <PopoverTrigger asChild>\r\n                  <Button variant=\"outline\" className=\"!h-9 text-sm\">\r\n                    <p>{t(\"price_range\")}</p>\r\n                    <ChevronDownIcon className=\"h-3 w-3\" />\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent side=\"bottom\" className=\"w-fit\">\r\n                  <div>\r\n                    <FilterForm handle={handle} />\r\n                  </div>\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n\r\n            {/* Filter button list */}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"w-fit\">\r\n        <SortFilter />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAbA;AASA;AAZA;;;;;;;;;;;;;AAwBA,uBAAuB;AACvB,MAAM,qBAAqB,CAAC;IAC1B,OAAO,yIAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,KAAK,KAAK,KAAK,YAAY;YAC7B,MAAM,WAAW,aAAa,GAAG,CAAC,CAAC;gBACjC,MAAM,WACJ,SAAS,iBAAiB,EAAE,IAAI,CAAC,QAAU,CAAC;wBAC1C,IAAI,MAAM,EAAE;wBACZ,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,MAAM;wBACnB,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACxC,CAAC,MAAM,EAAE;gBAEX,OAAO;oBACL,IAAI,SAAS,EAAE;oBACf,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,MAAM;oBACtB,OAAO,SAAS,QAAQ,EAAE,MAAM,SAAS;oBACzC;gBACF;YACF;YAEA,OAAO;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAS;QACvC;QACA,OAAO;IACT;AACF;AAEe,SAAS,cAAc,EACpC,YAAY,EACZ,WAAW;AACX,MAAM,EACa;IACnB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAA+B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACzC,IAAM,mBAAmB,eACzB;QAAC;KAAa;IAGhB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,yBACC,8OAAC,mLAAA,CAAA,UAAkB;oBAAC,YAAY;oBAAY,QAAQ;;;;;yCAEpD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kLAAA,CAAA,UAAiB;;;;;sCAClB,8OAAC;4BAAI,WAAU;sCASb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAQ,cAAc;;kDACnC,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC,wNAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG/B,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,MAAK;wCAAS,WAAU;kDACtC,cAAA,8OAAC;sDACC,cAAA,8OAAC,8JAAA,CAAA,UAAU;gDAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uKAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInB"}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-empty-product.tsx"], "sourcesContent": ["type IcEmptyProductProps = {\r\n  size?: number | string\r\n  className?: string\r\n}\r\n\r\nexport default function IcEmptyProduct({\r\n  size = 200,\r\n  className,\r\n}: IcEmptyProductProps) {\r\n  return (\r\n    <svg\r\n      width={size}\r\n      height={size}\r\n      viewBox=\"0 0 354 290\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n    >\r\n      <g clipPath=\"url(#clip0_302_1804)\">\r\n        <path\r\n          d=\"M11.6055 133V136\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M4.86719 134.625L7.31862 137.149\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M2 141.343H5.46629\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M4.58594 148.18L7.03737 145.656\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M11.6055 149V146\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M16.6055 147L14.6055 145\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M20.6055 142H17.6055\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M18.6055 135L16.6055 137\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M172.605 2V4\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M166.605 8H168.605\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M172.605 14V12\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M177.605 8H175.605\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M56.6055 86V89\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M47.6055 95H50.6055\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M56.6055 103V100\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M64.6055 95H61.6055\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          d=\"M271.859 195.039C274.249 195.039 276.187 196.978 276.187 199.368C276.187 201.76 274.249 203.697 271.859 203.697C269.469 203.697 267.531 201.76 267.531 199.368\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M291.666 155.764C294.236 154.962 298.175 151.819 299.087 148.438C299.914 151.427 303.38 154.962 306.508 155.229C302.987 156.476 299.621 160.179 299.087 163.089C298.723 160.123 294.068 156.196 291.666 155.764\"\r\n          fill=\"#EBECEE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M27.877 193.706C29.1623 193.305 31.131 191.734 31.5869 190.043C32.0003 191.537 33.7331 193.305 35.2968 193.439C33.537 194.061 31.8543 195.913 31.5869 197.368C31.4052 195.885 29.0774 193.921 27.877 193.706\"\r\n          fill=\"#EBECEE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M51.7294 54.5972C51.7294 56.4518 50.2268 57.954 48.3735 57.954C46.5194 57.954 45.0176 56.4518 45.0176 54.5972C45.0176 52.7434 46.5194 51.2404 48.3735 51.2404C50.2268 51.2404 51.7294 52.7434 51.7294 54.5972\"\r\n          fill=\"#D1D8DF\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M275.824 63.6107C275.824 66.7093 273.312 69.2212 270.215 69.2212C267.117 69.2212 264.605 66.7093 264.605 63.6107C264.605 60.512 267.117 58.0001 270.215 58.0001C273.312 58.0001 275.824 60.512 275.824 63.6107\"\r\n          fill=\"#EBECEE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M353.519 48.4097C353.519 50.868 351.527 52.8602 349.07 52.8602C346.613 52.8602 344.621 50.868 344.621 48.4097C344.621 45.9513 346.613 43.9591 349.07 43.9591C351.527 43.9591 353.519 45.9513 353.519 48.4097\"\r\n          fill=\"#EBECEE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M114.449 256.268C114.449 257.817 113.193 259.073 111.644 259.073C110.095 259.073 108.84 257.817 108.84 256.268C108.84 254.718 110.095 253.462 111.644 253.462C113.193 253.462 114.449 254.718 114.449 256.268Z\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M112.78 33.0317C112.78 34.8863 111.278 36.3885 109.424 36.3885C107.571 36.3885 106.068 34.8863 106.068 33.0317C106.068 31.178 107.571 29.6749 109.424 29.6749C111.278 29.6749 112.78 31.178 112.78 33.0317Z\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M198.383 287.336C196.239 288.152 193.839 287.075 193.023 284.931C192.207 282.785 193.283 280.384 195.429 279.569C197.572 278.753 199.972 279.829 200.788 281.975C201.605 284.12 200.527 286.52 198.383 287.336Z\"\r\n          stroke=\"#D2D8DF\"\r\n          strokeWidth=\"2.921\"\r\n          strokeLinecap=\"round\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M197.105 208H115.1C109.328 208 104.604 203.261 104.604 197.47V80.5275C104.604 74.7362 109.328 70 115.1 70H197.105C202.879 70 207.604 74.7362 207.604 80.5275V197.47C207.604 203.261 202.879 208 197.105 208\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M190.105 213H108.1C102.328 213 97.6035 208.261 97.6035 202.47V85.5275C97.6035 79.7362 102.328 75 108.1 75H190.105C195.879 75 200.604 79.7362 200.604 85.5275V202.47C200.604 208.261 195.879 213 190.105 213\"\r\n          fill=\"#E8EBF2\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M183.296 216.947H101.122C95.3379 216.947 90.6035 212.228 90.6035 206.461V90.0025C90.6035 84.2352 95.3379 79.5186 101.122 79.5186H183.296C189.082 79.5186 193.816 84.2352 193.816 90.0025V206.461C193.816 212.228 189.082 216.947 183.296 216.947\"\r\n          fill=\"#D8DBEA\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M183.296 216.947H101.122C95.3379 216.947 90.6035 212.228 90.6035 206.461V90.0025C90.6035 84.2352 95.3379 79.5186 101.122 79.5186H183.296C189.082 79.5186 193.816 84.2352 193.816 90.0025V206.461C193.816 212.228 189.082 216.947 183.296 216.947\"\r\n          fill=\"#F1F2F7\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M171.451 115H110.756C109.022 115 107.604 113.65 107.604 112.001C107.604 110.35 109.022 109 110.756 109H171.451C173.185 109 174.604 110.35 174.604 112.001C174.604 113.65 173.185 115 171.451 115\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M171.451 133H110.756C109.022 133 107.604 131.65 107.604 130.001C107.604 128.35 109.022 127 110.756 127H171.451C173.185 127 174.604 128.35 174.604 130.001C174.604 131.65 173.185 133 171.451 133\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M171.451 151H110.756C109.022 151 107.604 149.65 107.604 148.001C107.604 146.352 109.022 145 110.756 145H171.451C173.185 145 174.604 146.352 174.604 148.001C174.604 149.65 173.185 151 171.451 151\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M171.451 169H110.756C109.022 169 107.604 167.65 107.604 166.001C107.604 164.35 109.022 163 110.756 163H171.451C173.185 163 174.604 164.35 174.604 166.001C174.604 167.65 173.185 169 171.451 169\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M142.438 187H110.769C109.028 187 107.604 185.65 107.604 184.001C107.604 182.35 109.028 181 110.769 181H142.438C144.179 181 145.604 182.35 145.604 184.001C145.604 185.65 144.179 187 142.438 187\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M206.358 163.737C194.222 175.871 175.433 177.24 161.792 167.872C160.049 166.653 158.389 165.284 156.842 163.737C156.045 162.943 155.295 162.124 154.587 161.261C153.172 159.538 151.933 157.701 150.917 155.803C149.128 152.597 147.912 149.195 147.248 145.679C145.105 134.651 148.309 122.781 156.842 114.252C165.396 105.697 177.268 102.514 188.297 104.636C191.812 105.321 195.217 106.537 198.423 108.305C200.324 109.343 202.136 110.583 203.862 111.997C204.722 112.702 205.542 113.455 206.336 114.252C207.885 115.799 209.275 117.455 210.471 119.201C219.844 132.837 218.472 151.625 206.358 163.737\"\r\n          fill=\"white\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M202.962 161.347C190.336 173.976 169.873 173.973 157.244 161.347C144.632 148.734 144.632 128.27 157.261 115.643C169.873 103.03 190.336 103.03 202.948 115.643C215.575 128.27 215.575 148.734 202.962 161.347M209.442 109.156C193.235 92.9482 166.972 92.9482 150.764 109.156C134.559 125.361 134.545 151.64 150.75 167.848C165.501 182.594 188.621 183.924 204.882 171.816C206.47 170.632 208 169.305 209.459 167.848C210.916 166.389 212.243 164.859 213.425 163.271C225.532 147.007 224.191 123.904 209.442 109.156\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M273.226 228.942L272.545 229.622C268.044 234.126 260.672 234.126 256.168 229.622L214.604 188.057L231.661 171L273.226 212.565C277.729 217.069 277.729 224.438 273.226 228.942\"\r\n          fill=\"url(#paint0_linear_302_1804)\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M214.321 162L226.604 174.283L217.884 183L205.604 170.717C207.224 169.509 208.783 168.154 210.274 166.668C211.76 165.182 213.115 163.621 214.321 162\"\r\n          fill=\"#E0E2EE\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M272.604 212.054L255.655 229L252.604 225.946L269.55 209L272.604 212.054Z\"\r\n          fill=\"#64C726\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M232.604 171.587L215.191 189L212.604 186.413L230.016 169L232.604 171.587Z\"\r\n          fill=\"url(#paint1_linear_302_1804)\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M194.604 111.299L151.9 154C150.292 151.118 149.198 148.059 148.604 144.898L185.501 108C188.66 108.618 191.721 109.709 194.604 111.299\"\r\n          fill=\"white\"\r\n          fillOpacity=\"0.5\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M208.604 121.448L162.436 165C160.783 163.912 159.209 162.687 157.742 161.302C156.987 160.59 156.272 159.859 155.604 159.087L202.333 115C203.151 115.633 203.928 116.307 204.681 117.02C206.149 118.404 207.469 119.886 208.604 121.448\"\r\n          fill=\"white\"\r\n          fillOpacity=\"0.5\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <linearGradient\r\n          id=\"paint0_linear_302_1804\"\r\n          x1=\"245.604\"\r\n          y1=\"140\"\r\n          x2=\"183.604\"\r\n          y2=\"202\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#CCFBAE\" />\r\n          <stop offset=\"1\" stopColor=\"#75CE3E\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_302_1804\"\r\n          x1=\"222.604\"\r\n          y1=\"159\"\r\n          x2=\"202.604\"\r\n          y2=\"179\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#67C22E\" />\r\n          <stop offset=\"1\" stopColor=\"#6FBE40\" />\r\n        </linearGradient>\r\n        <clipPath id=\"clip0_302_1804\">\r\n          <rect width=\"354\" height=\"290\" fill=\"white\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,eAAe,EACrC,OAAO,GAAG,EACV,SAAS,EACW;IACpB,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;;;;;;kCAEhB,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;wBACL,aAAY;;;;;;;;;;;;0BAGhB,8OAAC;;kCACC,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,8OAAC;gCAAK,WAAU;;;;;;0CAChB,8OAAC;gCAAK,QAAO;gCAAI,WAAU;;;;;;;;;;;;kCAE7B,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,8OAAC;gCAAK,WAAU;;;;;;0CAChB,8OAAC;gCAAK,QAAO;gCAAI,WAAU;;;;;;;;;;;;kCAE7B,8OAAC;wBAAS,IAAG;kCACX,cAAA,8OAAC;4BAAK,OAAM;4BAAM,QAAO;4BAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;AAK9C"}}, {"offset": {"line": 2080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/no-products.tsx"], "sourcesContent": ["\"use client\"\r\nimport IcEmptyProduct from \"components/svg/ic-empty-product\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nexport default function NoProducts() {\r\n  const { t } = useTranslation(\"templates\")\r\n  return (\r\n    <div className=\"mx-auto flex w-full max-w-md flex-col items-center\">\r\n      <IcEmptyProduct size={220} />\r\n      <Typography\r\n        variant=\"h6\"\r\n        className=\"text-center font-semibold text-gray-600\"\r\n      >\r\n        {t(\"no_products_found\")}\r\n      </Typography>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;0BACtB,8OAAC,sIAAA,CAAA,UAAU;gBACT,SAAQ;gBACR,WAAU;0BAET,EAAE;;;;;;;;;;;;AAIX"}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"utils\"\r\nimport { ChevronLeftIcon, ChevronRightIcon, DotsHorizontalIcon } from \"@radix-ui/react-icons\"\r\nimport { buttonVariants } from \"./button\"\r\nimport { ButtonProps } from \"./button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"paginationActive\" : \"pagination\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeftIcon className=\"h-4 w-4\" />\r\n    {/* <span>Previous</span> */}\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    {/* <span>Next</span> */}\r\n    <ChevronRightIcon className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <DotsHorizontalIcon className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAEA;AADA;;;;;;AAIA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,8OAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;AAEvD,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB,iBACpB,8OAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,qBAAqB;YACzC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACyC,iBAC5C,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAI/B,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,GAAG,OACyC,iBAC5C,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;kBAGT,cAAA,8OAAC,gLAAA,CAAA,mBAAgB;YAAC,WAAU;;;;;;;;;;;AAGhC,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,8OAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,8OAAC,gLAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG"}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2266, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/custom-pagination.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"./ui/pagination\"\r\n\r\ntype PaginationProps = {\r\n  currentPage: number\r\n  totalPage: number\r\n  onChange: (page: number) => void\r\n}\r\n\r\nexport default function CustomPagination({\r\n  currentPage,\r\n  totalPage,\r\n  onChange,\r\n}: PaginationProps) {\r\n  const handlePageChange = (page: number) => {\r\n    onChange(page)\r\n  }\r\n\r\n  const getPaginationItems = () => {\r\n    const paginationItems = []\r\n    const startPage = Math.max(1, currentPage - 2)\r\n    const endPage = Math.min(totalPage, currentPage + 2)\r\n\r\n    if (startPage > 1) {\r\n      paginationItems.push(\r\n        <PaginationItem key={1}>\r\n          <PaginationLink\r\n            className=\"cursor-pointer\"\r\n            onClick={() => handlePageChange(1)}\r\n          >\r\n            1\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n      if (startPage > 2) {\r\n        paginationItems.push(\r\n          <PaginationItem key=\"ellipsis-start\" className=\"cursor-pointer\">\r\n            <PaginationEllipsis />\r\n          </PaginationItem>\r\n        )\r\n      }\r\n    }\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      paginationItems.push(\r\n        <PaginationItem key={i}>\r\n          <PaginationLink\r\n            isActive={i === currentPage}\r\n            onClick={() => handlePageChange(i)}\r\n            className=\"cursor-pointer\"\r\n          >\r\n            {i}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n    }\r\n\r\n    if (endPage < totalPage) {\r\n      if (endPage < totalPage - 1) {\r\n        paginationItems.push(\r\n          <PaginationItem key=\"ellipsis-end\" className=\"cursor-pointer\">\r\n            <PaginationEllipsis />\r\n          </PaginationItem>\r\n        )\r\n      }\r\n      paginationItems.push(\r\n        <PaginationItem key={totalPage}>\r\n          <PaginationLink\r\n            className=\"cursor-pointer\"\r\n            onClick={() => handlePageChange(totalPage)}\r\n          >\r\n            {totalPage}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n    }\r\n\r\n    return paginationItems\r\n  }\r\n\r\n  return (\r\n    <Pagination>\r\n      <PaginationContent>\r\n        <PaginationItem>\r\n          <PaginationPrevious\r\n            className={`cursor-pointer ${currentPage <= 1 ? \"cursor-not-allowed opacity-50\" : \"\"}`}\r\n            onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}\r\n          />\r\n        </PaginationItem>\r\n        {getPaginationItems()}\r\n        <PaginationItem>\r\n          <PaginationNext\r\n            className={`cursor-pointer ${currentPage >= totalPage ? \"cursor-not-allowed opacity-50\" : \"\"}`}\r\n            onClick={() =>\r\n              currentPage < totalPage && handlePageChange(currentPage + 1)\r\n            }\r\n          />\r\n        </PaginationItem>\r\n      </PaginationContent>\r\n    </Pagination>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS,iBAAiB,EACvC,WAAW,EACX,SAAS,EACT,QAAQ,EACQ;IAChB,MAAM,mBAAmB,CAAC;QACxB,SAAS;IACX;IAEA,MAAM,qBAAqB;QACzB,MAAM,kBAAkB,EAAE;QAC1B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC5C,MAAM,UAAU,KAAK,GAAG,CAAC,WAAW,cAAc;QAElD,IAAI,YAAY,GAAG;YACjB,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,SAAS,IAAM,iBAAiB;8BACjC;;;;;;eAJkB;;;;;YASvB,IAAI,YAAY,GAAG;gBACjB,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;oBAAsB,WAAU;8BAC7C,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;mBADD;;;;;YAIxB;QACF;QAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,UAAU,MAAM;oBAChB,SAAS,IAAM,iBAAiB;oBAChC,WAAU;8BAET;;;;;;eANgB;;;;;QAUzB;QAEA,IAAI,UAAU,WAAW;YACvB,IAAI,UAAU,YAAY,GAAG;gBAC3B,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;oBAAoB,WAAU;8BAC3C,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;mBADD;;;;;YAIxB;YACA,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,SAAS,IAAM,iBAAiB;8BAE/B;;;;;;eALgB;;;;;QASzB;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC,sIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;8BAChB,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;wBACjB,WAAW,CAAC,eAAe,EAAE,eAAe,IAAI,kCAAkC,IAAI;wBACtF,SAAS,IAAM,cAAc,KAAK,iBAAiB,cAAc;;;;;;;;;;;gBAGpE;8BACD,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wBACb,WAAW,CAAC,eAAe,EAAE,eAAe,YAAY,kCAAkC,IAAI;wBAC9F,SAAS,IACP,cAAc,aAAa,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;AAOxE"}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/pagination.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport CustomPagination from \"components/custom-pagination\"\r\nimport { useRouter, useSearchParams } from \"next/navigation\"\r\n\r\ntype CollectionPaginationProps = {\r\n  currentPage: number\r\n  totalPage: number\r\n}\r\n\r\nexport default function CollectionPagination({\r\n  currentPage,\r\n  totalPage,\r\n}: CollectionPaginationProps) {\r\n  const { push } = useRouter()\r\n  const searchParams = useSearchParams()\r\n\r\n  const handlePageChange = (newPage: number) => {\r\n    const params = new URLSearchParams(searchParams.toString())\r\n    params.set(\"page\", newPage.toString())\r\n    push(`?${params.toString()}`)\r\n  }\r\n\r\n  return (\r\n    <CustomPagination\r\n      currentPage={currentPage}\r\n      totalPage={totalPage}\r\n      onChange={handlePageChange}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,qBAAqB,EAC3C,WAAW,EACX,SAAS,EACiB;IAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ,QAAQ,QAAQ;QACnC,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC9B;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAgB;QACf,aAAa;QACb,WAAW;QACX,UAAU;;;;;;AAGhB"}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/collections/components/product-collection-wrapper/index.tsx"], "sourcesContent": ["\"use client\"\r\n// import Image from \"next/image\"\r\n\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\nimport NoProducts from \"../no-products\"\r\n// import FilterProduct from \"../filter-product\"\r\nimport ProductItem from \"@modules/products/components/product-item\"\r\nimport CollectionPagination from \"../pagination\"\r\n// import Typography from \"components/ui/typography\"\r\nimport {\r\n  filterProductsWithVariants,\r\n  getFirstVariant,\r\n  getProductPrice as getProductPriceHelper,\r\n  hasValidVariants,\r\n} from \"@lib/util/product-helpers\"\r\nimport { translateText } from \"@lib/util/text-translator\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { useSearchParams } from \"next/navigation\"\r\nimport { useMemo } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { TStoreProductWithCustomField } from \"types/product\"\r\n\r\ntype ProductBoxProps = {\r\n  page: number\r\n  countryCode: string\r\n  products: TStoreProductWithCustomField[]\r\n  totalPage: number\r\n  region: HttpTypes.StoreRegion\r\n  // sortBy?: SortOptions\r\n  // handle?: string\r\n  // priceRange?: string\r\n}\r\n\r\n// type PaginatedProductsParams = {\r\n//   limit: number\r\n//   collection_id?: string[]\r\n//   category_id?: string[]\r\n//   id?: string[]\r\n//   order?: string\r\n// }\r\n\r\n// const LIMIT_PRODUCT = 16\r\n\r\n// const prepareQueryParams = (\r\n//   handle?: string,\r\n//   sortBy?: SortOptions,\r\n//   currentCategory?: any\r\n// ): PaginatedProductsParams => {\r\n//   const queryParams: PaginatedProductsParams = {\r\n//     limit: LIMIT_PRODUCT,\r\n//   }\r\n\r\n//   if (handle && handle !== \"all-product\") {\r\n//     queryParams[\"category_id\"] = Array.isArray(currentCategory?.id)\r\n//       ? currentCategory?.id\r\n//       : [currentCategory?.id]\r\n//   }\r\n\r\n//   if (sortBy === \"created_at\") {\r\n//     queryParams[\"order\"] = \"created_at\"\r\n//   }\r\n\r\n//   return queryParams\r\n// }\r\n\r\nexport default function ProductCollectionWrapper({\r\n  page,\r\n  countryCode,\r\n  products,\r\n  totalPage,\r\n  region,\r\n}: ProductBoxProps) {\r\n  const { i18n } = useTranslation()\r\n  const params = useSearchParams()\r\n  const priceRange = params.get(\"priceRange\")\r\n  const sortBy = params.get(\"sort_by\") || params.get(\"sortBy\")\r\n\r\n  const filteredProducts = useMemo(() => {\r\n    if (!priceRange && !sortBy) return products\r\n\r\n    let newProductsList = products\r\n\r\n    // Apply price filter if exists\r\n    if (priceRange) {\r\n      const startPrice = priceRange.split(\",\")[0]\r\n      const endPrice = priceRange.split(\",\")[1]\r\n      newProductsList = products.filter(\r\n        (product): product is TStoreProductWithCustomField => {\r\n          return (\r\n            hasValidVariants(product) &&\r\n            product.variants.some((variant) => {\r\n              return (\r\n                variant.calculated_price?.calculated_amount &&\r\n                variant.calculated_price?.calculated_amount >=\r\n                  Number(startPrice) &&\r\n                variant.calculated_price?.calculated_amount <= Number(endPrice)\r\n              )\r\n            })\r\n          )\r\n        }\r\n      )\r\n    }\r\n\r\n    // Apply sorting\r\n    if (sortBy) {\r\n      switch (sortBy) {\r\n        case \"featured\":\r\n          // Keep original order for featured\r\n          break\r\n        case \"price_asc\":\r\n          newProductsList = [...newProductsList].sort((a, b) => {\r\n            const priceA = getProductPriceHelper(a).calculated_amount\r\n            const priceB = getProductPriceHelper(b).calculated_amount\r\n            return priceA - priceB\r\n          })\r\n          break\r\n        case \"price_desc\":\r\n          newProductsList = [...newProductsList].sort((a, b) => {\r\n            const priceA = getProductPriceHelper(a).calculated_amount\r\n            const priceB = getProductPriceHelper(b).calculated_amount\r\n            return priceB - priceA\r\n          })\r\n          break\r\n        case \"newest\":\r\n          newProductsList = [...newProductsList].sort((a, b) => {\r\n            const dateA = new Date(a.created_at ?? \"\")\r\n            const dateB = new Date(b.created_at ?? \"\")\r\n            return dateB.getTime() - dateA.getTime()\r\n          })\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    }\r\n    return newProductsList\r\n  }, [products, priceRange, sortBy])\r\n\r\n  // Calculate pagination based on filtered products\r\n  const ITEMS_PER_PAGE = 16\r\n  const filteredTotalPage = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE)\r\n  const shouldShowPagination = filteredProducts.length > ITEMS_PER_PAGE\r\n\r\n  // Get products for current page (client-side pagination when filtering)\r\n  const paginatedProducts = useMemo(() => {\r\n    if (!priceRange && !sortBy) {\r\n      // Server-side pagination - use all products\r\n      return filteredProducts\r\n    }\r\n\r\n    // Client-side pagination for filtered results\r\n    const startIndex = (page - 1) * ITEMS_PER_PAGE\r\n    const endIndex = startIndex + ITEMS_PER_PAGE\r\n    return filteredProducts.slice(startIndex, endIndex)\r\n  }, [filteredProducts, page, priceRange, sortBy])\r\n\r\n  // Filter products with valid variants and log warnings\r\n  const validProducts = filterProductsWithVariants(paginatedProducts)\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-10 mt-8 grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:mb-20 xl:grid-cols-3 xl:gap-8 2xl:grid-cols-4 2xl:gap-4\">\r\n        {validProducts && validProducts.length > 0 ? (\r\n          validProducts.map((product) => {\r\n            const firstVariant = getFirstVariant(product)!\r\n            const productPrice = getProductPriceHelper(product)\r\n\r\n            // Translate title and description using translateText\r\n            const translatedTitle = product.title\r\n              ? translateText(product.title, i18n.language).text_locale ||\r\n                product.title\r\n              : \"\"\r\n            const translatedDescription = product.description\r\n              ? translateText(product.description, i18n.language).text_locale ||\r\n                product.description\r\n              : \"\"\r\n\r\n            return (\r\n              <ProductItem\r\n                isSmallCard\r\n                countryCode={countryCode}\r\n                fullProduct={product}\r\n                variant={firstVariant.id}\r\n                description={translatedDescription}\r\n                key={product.id}\r\n                href={PAGE_PATH.PRODUCT.detail(product.handle)}\r\n                image={product.thumbnail || \"/images/no-image.svg\"}\r\n                title={translatedTitle}\r\n                price={productPrice.calculated_amount}\r\n                original_price={productPrice.original_amount}\r\n                content_on_picture={\r\n                  <ProductDiscountOverlay product={product} />\r\n                }\r\n                discountable={product.discountable}\r\n                currency_code={region.currency_code}\r\n                product_id={product.id}\r\n              />\r\n            )\r\n          })\r\n        ) : (\r\n          <div className=\"col-span-4\">\r\n            <NoProducts />\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {(priceRange || sortBy ? shouldShowPagination : totalPage > 1) && (\r\n        <CollectionPagination\r\n          currentPage={page}\r\n          totalPage={priceRange || sortBy ? filteredTotalPage : totalPage}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n\r\nconst ProductDiscountOverlay = ({ product }: { product: any }) => (\r\n  <>\r\n    {/* {product.discountable ? (\r\n      <div\r\n        className={`absolute top-2 left-2 w-fit rounded-md bg-red text-white p-2 font-semibold ${\r\n          product.discountable ? \"bg-red-500\" : \"bg-yellow-500\"\r\n        }`}\r\n      >\r\n        <Typography variant=\"p\" size=\"sm\" color=\"white\">\r\n          {20} %\r\n        </Typography>\r\n      </div>\r\n    ) : null} */}\r\n\r\n    {/* <div className=\"absolute top-2 right-2\">\r\n      <Image\r\n        src={\"/images/logo-atsh.png\"}\r\n        alt=\"heart\"\r\n        width={40}\r\n        height={50}\r\n        className=\"rounded-full\"\r\n      />\r\n    </div>\r\n\r\n    <div className=\"absolute bottom-0 left-0 w-full h-16 z-10\">\r\n      <Image\r\n        src={\"/images/logo-atsh-1.png\"}\r\n        alt=\"heart\"\r\n        layout=\"fill\"\r\n        className=\"w-full h-full\"\r\n      />\r\n    </div> */}\r\n  </>\r\n)\r\n"], "names": [], "mappings": ";;;;AACA,iCAAiC;AAEjC;AAEA;AACA,gDAAgD;AAChD;AACA;AACA,oDAAoD;AACpD;AAMA;AAEA;AACA;AACA;AAAA;AApBA;;;;;;;;;;;AAkEe,SAAS,yBAAyB,EAC/C,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,MAAM,EACU;IAChB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,aAAa,OAAO,GAAG,CAAC;IAC9B,MAAM,SAAS,OAAO,GAAG,CAAC,cAAc,OAAO,GAAG,CAAC;IAEnD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,cAAc,CAAC,QAAQ,OAAO;QAEnC,IAAI,kBAAkB;QAEtB,+BAA+B;QAC/B,IAAI,YAAY;YACd,MAAM,aAAa,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3C,MAAM,WAAW,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC,kBAAkB,SAAS,MAAM,CAC/B,CAAC;gBACC,OACE,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,YACjB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACrB,OACE,QAAQ,gBAAgB,EAAE,qBAC1B,QAAQ,gBAAgB,EAAE,qBACxB,OAAO,eACT,QAAQ,gBAAgB,EAAE,qBAAqB,OAAO;gBAE1D;YAEJ;QAEJ;QAEA,gBAAgB;QAChB,IAAI,QAAQ;YACV,OAAQ;gBACN,KAAK;oBAEH;gBACF,KAAK;oBACH,kBAAkB;2BAAI;qBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;wBAC9C,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,kBAAqB,AAAD,EAAE,GAAG,iBAAiB;wBACzD,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,kBAAqB,AAAD,EAAE,GAAG,iBAAiB;wBACzD,OAAO,SAAS;oBAClB;oBACA;gBACF,KAAK;oBACH,kBAAkB;2BAAI;qBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;wBAC9C,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,kBAAqB,AAAD,EAAE,GAAG,iBAAiB;wBACzD,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,kBAAqB,AAAD,EAAE,GAAG,iBAAiB;wBACzD,OAAO,SAAS;oBAClB;oBACA;gBACF,KAAK;oBACH,kBAAkB;2BAAI;qBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;wBAC9C,MAAM,QAAQ,IAAI,KAAK,EAAE,UAAU,IAAI;wBACvC,MAAM,QAAQ,IAAI,KAAK,EAAE,UAAU,IAAI;wBACvC,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;oBACxC;oBACA;gBACF;oBACE;YACJ;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAU;QAAY;KAAO;IAEjC,kDAAkD;IAClD,MAAM,iBAAiB;IACvB,MAAM,oBAAoB,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;IAC9D,MAAM,uBAAuB,iBAAiB,MAAM,GAAG;IAEvD,wEAAwE;IACxE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC1B,4CAA4C;YAC5C,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,OAAO,iBAAiB,KAAK,CAAC,YAAY;IAC5C,GAAG;QAAC;QAAkB;QAAM;QAAY;KAAO;IAE/C,uDAAuD;IACvD,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,6BAA0B,AAAD,EAAE;IAEjD,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,cAAc,MAAM,GAAG,IACvC,cAAc,GAAG,CAAC,CAAC;oBACjB,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;oBACrC,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAqB,AAAD,EAAE;oBAE3C,sDAAsD;oBACtD,MAAM,kBAAkB,QAAQ,KAAK,GACjC,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE,WAAW,IACvD,QAAQ,KAAK,GACb;oBACJ,MAAM,wBAAwB,QAAQ,WAAW,GAC7C,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,WAAW,EAAE,KAAK,QAAQ,EAAE,WAAW,IAC7D,QAAQ,WAAW,GACnB;oBAEJ,qBACE,8OAAC,qKAAA,CAAA,UAAW;wBACV,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,SAAS,aAAa,EAAE;wBACxB,aAAa;wBAEb,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,MAAM;wBAC7C,OAAO,QAAQ,SAAS,IAAI;wBAC5B,OAAO;wBACP,OAAO,aAAa,iBAAiB;wBACrC,gBAAgB,aAAa,eAAe;wBAC5C,kCACE,8OAAC;4BAAuB,SAAS;;;;;;wBAEnC,cAAc,QAAQ,YAAY;wBAClC,eAAe,OAAO,aAAa;wBACnC,YAAY,QAAQ,EAAE;uBAXjB,QAAQ,EAAE;;;;;gBAcrB,mBAEA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8JAAA,CAAA,UAAU;;;;;;;;;;;;;;;YAKhB,CAAC,cAAc,SAAS,uBAAuB,YAAY,CAAC,mBAC3D,8OAAC,0JAAA,CAAA,UAAoB;gBACnB,aAAa;gBACb,WAAW,cAAc,SAAS,oBAAoB;;;;;;;;AAKhE;AAEA,MAAM,yBAAyB,CAAC,EAAE,OAAO,EAAoB,iBAC3D"}}, {"offset": {"line": 2623, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/Logo.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ImageProps } from \"next/image\"\r\nimport { useEffect, useState } from \"react\"\r\n\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\nimport { ResponsiveImage } from \"./custom-image\"\r\n\r\nconst logoBlack = \"/images/main_logo.png\"\r\n\r\ntype TPropsLogoStorefront = {\r\n  isLink?: boolean\r\n  src?: string\r\n  ratio?: string\r\n} & Omit<ImageProps, \"src\">\r\n\r\nconst LogoStorefront = ({\r\n  isLink = false,\r\n  src,\r\n  alt = \"Logo\",\r\n  // ratio = \"450/108\",\r\n  ...props\r\n}: TPropsLogoStorefront) => {\r\n  const [imageSrc, setImageSrc] = useState(src || logoBlack)\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setImageSrc(window.innerWidth < 768 ? logoBlack : src || logoBlack)\r\n    }\r\n\r\n    handleResize() // Set initial state\r\n    window.addEventListener(\"resize\", handleResize)\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize)\r\n    }\r\n  }, [src])\r\n\r\n  const renderImage = () =>\r\n    typeof src === \"string\" ? (\r\n      <div className=\"w-40\">\r\n        <ResponsiveImage src={imageSrc} alt={alt} {...props} />\r\n      </div>\r\n    ) : (\r\n      <div className=\"w-40\">\r\n        <ResponsiveImage src={logoBlack} alt={alt} {...props} />\r\n      </div>\r\n    )\r\n\r\n  return isLink ? (\r\n    <LocalizedClientLink href={PAGE_PATH.HOME}>\r\n      {renderImage()}\r\n    </LocalizedClientLink>\r\n  ) : (\r\n    <div>{renderImage()}</div>\r\n  )\r\n}\r\n\r\nexport default LogoStorefront\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AAAA;AAPA;;;;;;AASA,MAAM,YAAY;AAQlB,MAAM,iBAAiB,CAAC,EACtB,SAAS,KAAK,EACd,GAAG,EACH,MAAM,MAAM,EACZ,qBAAqB;AACrB,GAAG,OACkB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,UAAU,GAAG,MAAM,YAAY,OAAO;QAC3D;QAEA,eAAe,oBAAoB;;QACnC,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;KAAI;IAER,MAAM,cAAc,IAClB,OAAO,QAAQ,yBACb,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0MAAA,CAAA,kBAAe;gBAAC,KAAK;gBAAU,KAAK;gBAAM,GAAG,KAAK;;;;;;;;;;iCAGrD,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0MAAA,CAAA,kBAAe;gBAAC,KAAK;gBAAW,KAAK;gBAAM,GAAG,KAAK;;;;;;;;;;;IAI1D,OAAO,uBACL,8OAAC,+KAAA,CAAA,UAAmB;QAAC,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI;kBACtC;;;;;6BAGH,8OAAC;kBAAK;;;;;;AAEV;uCAEe"}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/loading/loading-screen.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport LogoStorefront from \"components/Logo\"\r\nimport { useEffect, useState } from \"react\"\r\n\r\nexport default function SplashScreen() {\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  if (!mounted) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-[9998] flex flex-col items-center justify-center bg-background\">\r\n      <LogoStorefront alt=\"Logo Efruit\" width={200} height={100} />\r\n      <div className=\"mt-2 flex space-x-2\">\r\n        <div className=\"h-2 w-2 animate-bounce rounded-full bg-black md:h-3 md:w-3\" />\r\n        <div className=\"h-2 w-2 animate-bounce rounded-full bg-black delay-200 md:h-3 md:w-3\" />\r\n        <div className=\"delay-400 h-2 w-2 animate-bounce rounded-full bg-black md:h-3 md:w-3\" />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAc;gBAAC,KAAI;gBAAc,OAAO;gBAAK,QAAQ;;;;;;0BACtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 2781, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/error-boundary/index.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"components/ui/button\"\nimport { Component, ErrorInfo, ReactNode } from \"react\"\n\ninterface Props {\n  children: ReactNode\n  fallback?: ReactNode\n  onError?: (error: Error, errorInfo: ErrorInfo) => void\n}\n\ninterface State {\n  hasError: boolean\n  error?: Error\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  public state: State = {\n    hasError: false,\n  }\n\n  public static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo)\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo)\n    }\n  }\n\n  private handleReset = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  public render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback\n      }\n\n      // Default fallback UI\n      return (\n        <div className=\"flex min-h-[400px] flex-col items-center justify-center px-4\">\n          <div className=\"w-full max-w-md text-center\">\n            <div className=\"mb-6\">\n              <h2 className=\"mb-2 text-2xl font-semibold text-gray-900\">\n                Đã xảy ra lỗi\n              </h2>\n              <p className=\"text-gray-600\">\n                Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.\n              </p>\n            </div>\n\n            <div className=\"space-y-3\">\n              <Button onClick={this.handleReset} className=\"w-full\">\n                Thử lại\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                onClick={() => window.location.reload()}\n                className=\"w-full\"\n              >\n                Tải lại trang\n              </Button>\n            </div>\n\n            {process.env.NODE_ENV === \"development\" && this.state.error && (\n              <details className=\"mt-6 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500\">\n                  Chi tiết lỗi (Development)\n                </summary>\n                <pre className=\"rounded mt-2 overflow-auto bg-gray-100 p-4 text-xs\">\n                  {this.state.error.message}\n                  {this.state.error.stack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IAC5B,QAAe;QACpB,UAAU;IACZ,EAAC;IAED,OAAc,yBAAyB,KAAY,EAAS;QAC1D,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEO,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QAC3D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEQ,cAAc;QACpB,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAEM,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,sBAAsB;YACtB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAI,CAAC,WAAW;oCAAE,WAAU;8CAAS;;;;;;8CAItD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAAuC;;;;;;8CAG1D,8OAAC;oCAAI,WAAU;;wCACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;wCACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe"}}, {"offset": {"line": 2929, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/categories/templates/category-header.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { translateText } from \"@lib/util/text-translator\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useParams } from \"next/navigation\"\r\n\r\ntype CategoryHeaderProps = {\r\n  category: HttpTypes.StoreProductCategory\r\n}\r\n\r\nexport default function CategoryHeader({ category }: CategoryHeaderProps) {\r\n  const { localeLanguage } = useParams()\r\n\r\n  const categoryName = category.name\r\n    ? translateText(category.name, localeLanguage as string).text_locale ||\r\n      category.name\r\n    : \"Category\"\r\n\r\n  return (\r\n    <div className=\"content-container flex w-full flex-col items-center gap-y-6 py-6 pt-10\">\r\n      <Typography variant=\"h1\" className=\"font-bold text-primary-dark\">\r\n        {categoryName}\r\n      </Typography>\r\n      {category.description && (\r\n        <Typography variant=\"p\" className=\"mt-2 text-gray-600\">\r\n          {translateText(category.description, localeLanguage as string)\r\n            .text_locale || category.description}\r\n        </Typography>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEnC,MAAM,eAAe,SAAS,IAAI,GAC9B,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,EAAE,gBAA0B,WAAW,IAClE,SAAS,IAAI,GACb;IAEJ,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAU;gBAAC,SAAQ;gBAAK,WAAU;0BAChC;;;;;;YAEF,SAAS,WAAW,kBACnB,8OAAC,sIAAA,CAAA,UAAU;gBAAC,SAAQ;gBAAI,WAAU;0BAC/B,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,EAAE,gBAClC,WAAW,IAAI,SAAS,WAAW;;;;;;;;;;;;AAKhD"}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}