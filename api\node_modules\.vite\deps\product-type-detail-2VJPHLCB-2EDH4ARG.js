import {
  useDeleteProductTypeAction
} from "./chunk-AMEHXNKS.js";
import {
  useProductTableColumns
} from "./chunk-MWHYWZYO.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-EUULPFXI.js";
import "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-VIMVKTIR.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import "./chunk-XYBN3KRC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-3HVD2KMV.js";
import "./chunk-H3VZFY73.js";
import "./chunk-RX237AWS.js";
import "./chunk-Y5G57T2Y.js";
import "./chunk-PBNFBMP6.js";
import "./chunk-BEVRGZXI.js";
import {
  productTypesQueryKeys,
  useProductType
} from "./chunk-HREJMEGI.js";
import "./chunk-VFNUYVQL.js";
import "./chunk-KRJAVNJS.js";
import "./chunk-NAULBNIN.js";
import "./chunk-6Q7JSKEY.js";
import "./chunk-HQL74AJG.js";
import "./chunk-7UV5UA6G.js";
import "./chunk-XBIMCDU6.js";
import "./chunk-FSXJE4G7.js";
import "./chunk-E4TFG45M.js";
import "./chunk-S32V3COL.js";
import "./chunk-BZZVTH5X.js";
import "./chunk-CXC4I63N.js";
import "./chunk-IBPOGPJN.js";
import "./chunk-OX66UHHF.js";
import "./chunk-ZJNBJBHK.js";
import "./chunk-RWC53K5F.js";
import "./chunk-AKXAI3UV.js";
import "./chunk-662EXSHO.js";
import "./chunk-3H6LL6QL.js";
import {
  useProducts
} from "./chunk-FBCJANGI.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare,
  Trash
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-type-detail-2VJPHLCB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ProductTypeDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { product_type } = useProductType(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!product_type) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: product_type.value });
};
var productTypeDetailQuery = (id) => ({
  queryKey: productTypesQueryKeys.detail(id),
  queryFn: async () => sdk.admin.productType.retrieve(id)
});
var productTypeLoader = async ({ params }) => {
  const id = params.id;
  const query = productTypeDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ProductTypeGeneralSection = ({
  productType
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTypeAction(
    productType.id,
    productType.value
  );
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex items-center justify-between", children: [
    (0, import_jsx_runtime2.jsx)(Heading, { children: productType.value }),
    (0, import_jsx_runtime2.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                label: t("actions.edit"),
                icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                to: "edit"
              }
            ]
          },
          {
            actions: [
              {
                label: t("actions.delete"),
                icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                onClick: handleDelete
              }
            ]
          }
        ]
      }
    )
  ] });
};
var PAGE_SIZE = 10;
var ProductTypeProductSection = ({
  productType
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE
  });
  const { products, count, isPending, isError, error } = useProducts({
    ...searchParams,
    type_id: [productType.id]
  });
  const filters = useProductTableFilters(["product_types"]);
  const columns = useProductTableColumns();
  const { table } = useDataTable({
    columns,
    data: products,
    count: (products == null ? void 0 : products.length) || 0,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsx)("div", { className: "px-6 py-4", children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("products.domain") }) }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        filters,
        isLoading: isPending,
        columns,
        count,
        pageSize: PAGE_SIZE,
        navigateTo: ({ original }) => `/products/${original.id}`,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        queryObject: raw,
        search: true,
        pagination: true
      }
    )
  ] });
};
var ProductTypeDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const { product_type, isPending, isError, error } = useProductType(
    id,
    void 0,
    {
      initialData
    }
  );
  const { getWidgets } = useExtension();
  if (isPending || !product_type) {
    return (0, import_jsx_runtime4.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_type.details.after"),
        before: getWidgets("product_type.details.before")
      },
      showJSON: true,
      showMetadata: true,
      data: product_type,
      children: [
        (0, import_jsx_runtime4.jsx)(ProductTypeGeneralSection, { productType: product_type }),
        (0, import_jsx_runtime4.jsx)(ProductTypeProductSection, { productType: product_type })
      ]
    }
  );
};
export {
  ProductTypeDetailBreadcrumb as Breadcrumb,
  ProductTypeDetail as Component,
  productTypeLoader as loader
};
//# sourceMappingURL=product-type-detail-2VJPHLCB-2EDH4ARG.js.map
