import "@medusajs/framework/types"
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: Date | string; output: Date | string; }
  JSON: { input: Record<string, unknown>; output: Record<string, unknown>; }
};

export type StockLocationAddress = {
  __typename?: 'StockLocationAddress';
  id: Maybe<Scalars['ID']['output']>;
  address_1: Scalars['String']['output'];
  address_2: Maybe<Scalars['String']['output']>;
  company: Maybe<Scalars['String']['output']>;
  country_code: Scalars['String']['output'];
  city: Maybe<Scalars['String']['output']>;
  phone: Maybe<Scalars['String']['output']>;
  postal_code: Maybe<Scalars['String']['output']>;
  province: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type StockLocation = {
  __typename?: 'StockLocation';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  address_id: Scalars['ID']['output'];
  address: Maybe<StockLocationAddress>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  fulfillment_provider_link: Maybe<Array<Maybe<LinkLocationFulfillmentProvider>>>;
  fulfillment_providers: Maybe<Array<Maybe<FulfillmentProvider>>>;
  fulfillment_set_link: Maybe<Array<Maybe<LinkLocationFulfillmentSet>>>;
  fulfillment_sets: Maybe<Array<Maybe<FulfillmentSet>>>;
  sales_channels_link: Maybe<Array<Maybe<LinkSalesChannelStockLocation>>>;
  sales_channels: Maybe<Array<Maybe<SalesChannel>>>;
};

export type InventoryItem = {
  __typename?: 'InventoryItem';
  id: Scalars['ID']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  sku: Maybe<Scalars['String']['output']>;
  origin_country: Maybe<Scalars['String']['output']>;
  hs_code: Maybe<Scalars['String']['output']>;
  mid_code: Maybe<Scalars['String']['output']>;
  material: Maybe<Scalars['String']['output']>;
  weight: Maybe<Scalars['Int']['output']>;
  length: Maybe<Scalars['Int']['output']>;
  height: Maybe<Scalars['Int']['output']>;
  width: Maybe<Scalars['Int']['output']>;
  requires_shipping: Scalars['Boolean']['output'];
  description: Maybe<Scalars['String']['output']>;
  title: Maybe<Scalars['String']['output']>;
  thumbnail: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  location_levels: Maybe<Array<Maybe<InventoryLevel>>>;
  variant_link: Maybe<Array<Maybe<LinkProductVariantInventoryItem>>>;
  variants: Maybe<Array<Maybe<ProductVariant>>>;
};

export type InventoryLevel = {
  __typename?: 'InventoryLevel';
  id: Scalars['ID']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  inventory_item_id: Scalars['String']['output'];
  inventory_item: InventoryItem;
  location_id: Scalars['String']['output'];
  stocked_quantity: Scalars['Int']['output'];
  reserved_quantity: Scalars['Int']['output'];
  incoming_quantity: Scalars['Int']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  stock_locations: Maybe<Array<Maybe<StockLocation>>>;
};

export type ReservationItem = {
  __typename?: 'ReservationItem';
  id: Scalars['ID']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  line_item_id: Maybe<Scalars['String']['output']>;
  inventory_item_id: Scalars['String']['output'];
  inventory_item: InventoryItem;
  location_id: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  external_id: Maybe<Scalars['String']['output']>;
  description: Maybe<Scalars['String']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
};

export enum ProductStatus {
  Draft = 'draft',
  Proposed = 'proposed',
  Published = 'published',
  Rejected = 'rejected'
}

export type Product = {
  __typename?: 'Product';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  handle: Scalars['String']['output'];
  subtitle: Maybe<Scalars['String']['output']>;
  description: Maybe<Scalars['String']['output']>;
  is_giftcard: Scalars['Boolean']['output'];
  status: ProductStatus;
  thumbnail: Maybe<Scalars['String']['output']>;
  width: Maybe<Scalars['Float']['output']>;
  weight: Maybe<Scalars['Float']['output']>;
  length: Maybe<Scalars['Float']['output']>;
  height: Maybe<Scalars['Float']['output']>;
  origin_country: Maybe<Scalars['String']['output']>;
  hs_code: Maybe<Scalars['String']['output']>;
  mid_code: Maybe<Scalars['String']['output']>;
  material: Maybe<Scalars['String']['output']>;
  collection: Maybe<ProductCollection>;
  collection_id: Maybe<Scalars['String']['output']>;
  categories: Maybe<Array<Maybe<ProductCategory>>>;
  type: Maybe<ProductType>;
  type_id: Maybe<Scalars['String']['output']>;
  tags: Array<ProductTag>;
  variants: Array<ProductVariant>;
  options: Array<ProductOption>;
  images: Array<ProductImage>;
  discountable: Maybe<Scalars['Boolean']['output']>;
  external_id: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  sales_channels_link: Maybe<Array<Maybe<LinkProductSalesChannel>>>;
  sales_channels: Maybe<Array<Maybe<SalesChannel>>>;
  shipping_profiles_link: Maybe<LinkProductShippingProfile>;
  shipping_profile: Maybe<ShippingProfile>;
  product_mix_match_link: Maybe<Array<Maybe<LinkProductMixMatchProductMixMatchProductProduct>>>;
  product_mix_matches: Maybe<Array<Maybe<ProductMixMatch>>>;
  product_rating_link: Maybe<LinkProductReviewProductRatingProductProduct>;
  product_rating: Maybe<ProductRating>;
  translation_link: Maybe<Array<Maybe<LinkProductProductLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
  wishlist_item_link: Maybe<LinkWishlistModuleWishlistItemProductProduct>;
  wishlist_item: Maybe<WishlistItem>;
};

export type ProductVariant = {
  __typename?: 'ProductVariant';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  sku: Maybe<Scalars['String']['output']>;
  barcode: Maybe<Scalars['String']['output']>;
  ean: Maybe<Scalars['String']['output']>;
  upc: Maybe<Scalars['String']['output']>;
  allow_backorder: Scalars['Boolean']['output'];
  manage_inventory: Scalars['Boolean']['output'];
  requires_shipping: Scalars['Boolean']['output'];
  hs_code: Maybe<Scalars['String']['output']>;
  origin_country: Maybe<Scalars['String']['output']>;
  mid_code: Maybe<Scalars['String']['output']>;
  material: Maybe<Scalars['String']['output']>;
  weight: Maybe<Scalars['Float']['output']>;
  length: Maybe<Scalars['Float']['output']>;
  height: Maybe<Scalars['Float']['output']>;
  width: Maybe<Scalars['Float']['output']>;
  options: Array<ProductOptionValue>;
  metadata: Maybe<Scalars['JSON']['output']>;
  product: Maybe<Product>;
  product_id: Maybe<Scalars['String']['output']>;
  variant_rank: Maybe<Scalars['Int']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  inventory_items: Maybe<Array<Maybe<LinkProductVariantInventoryItem>>>;
  inventory: Maybe<Array<Maybe<InventoryItem>>>;
  price_set_link: Maybe<LinkProductVariantPriceSet>;
  price_set: Maybe<PriceSet>;
  order_items: Maybe<Array<Maybe<OrderLineItem>>>;
  product_variant_image_link: Maybe<Array<Maybe<LinkProductProductVariantProductVariantImageProductVariantImage>>>;
  product_variant_images: Maybe<Array<Maybe<ProductVariantImage>>>;
};

export type ProductCategory = {
  __typename?: 'ProductCategory';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  description: Scalars['String']['output'];
  handle: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  is_internal: Scalars['Boolean']['output'];
  rank: Scalars['Int']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  parent_category: Maybe<ProductCategory>;
  parent_category_id: Maybe<Scalars['String']['output']>;
  category_children: Array<ProductCategory>;
  products: Array<Product>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  translation_link: Maybe<Array<Maybe<LinkProductProductCategoryLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
};

export type ProductTag = {
  __typename?: 'ProductTag';
  id: Scalars['ID']['output'];
  value: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  products: Maybe<Array<Maybe<Product>>>;
};

export type ProductCollection = {
  __typename?: 'ProductCollection';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  handle: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  products: Maybe<Array<Maybe<Product>>>;
  translation_link: Maybe<Array<Maybe<LinkProductProductCollectionLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
};

export type ProductType = {
  __typename?: 'ProductType';
  id: Scalars['ID']['output'];
  value: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ProductOption = {
  __typename?: 'ProductOption';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  product: Maybe<Product>;
  product_id: Maybe<Scalars['String']['output']>;
  values: Array<ProductOptionValue>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ProductImage = {
  __typename?: 'ProductImage';
  id: Scalars['ID']['output'];
  url: Scalars['String']['output'];
  rank: Scalars['Int']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ProductOptionValue = {
  __typename?: 'ProductOptionValue';
  id: Scalars['ID']['output'];
  value: Scalars['String']['output'];
  option: Maybe<ProductOption>;
  option_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_option_image_link: Maybe<Array<Maybe<LinkProductProductOptionValueProductOptionImageProductOptionImage>>>;
  product_option_images: Maybe<Array<Maybe<ProductOptionImage>>>;
};

export type PriceSet = {
  __typename?: 'PriceSet';
  id: Scalars['ID']['output'];
  prices: Maybe<Array<Maybe<Price>>>;
  calculated_price: Maybe<CalculatedPriceSet>;
  variant_link: Maybe<LinkProductVariantPriceSet>;
  variant: Maybe<ProductVariant>;
  shipping_option_link: Maybe<LinkShippingOptionPriceSet>;
  shipping_option: Maybe<ShippingOption>;
};

export type Price = {
  __typename?: 'Price';
  id: Scalars['ID']['output'];
  currency_code: Maybe<Scalars['String']['output']>;
  amount: Maybe<Scalars['Float']['output']>;
  min_quantity: Maybe<Scalars['Float']['output']>;
  max_quantity: Maybe<Scalars['Float']['output']>;
  rules_count: Maybe<Scalars['Int']['output']>;
  price_rules: Maybe<Array<Maybe<PriceRule>>>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type PriceRule = {
  __typename?: 'PriceRule';
  id: Scalars['ID']['output'];
  price_set_id: Scalars['String']['output'];
  price_set: Maybe<PriceSet>;
  attribute: Scalars['String']['output'];
  value: Scalars['String']['output'];
  priority: Scalars['Int']['output'];
  price_id: Scalars['String']['output'];
  price_list_id: Scalars['String']['output'];
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type CalculatedPriceSet = {
  __typename?: 'CalculatedPriceSet';
  id: Scalars['ID']['output'];
  is_calculated_price_price_list: Maybe<Scalars['Boolean']['output']>;
  is_calculated_price_tax_inclusive: Maybe<Scalars['Boolean']['output']>;
  calculated_amount: Maybe<Scalars['Float']['output']>;
  raw_calculated_amount: Maybe<Scalars['JSON']['output']>;
  is_original_price_price_list: Maybe<Scalars['Boolean']['output']>;
  is_original_price_tax_inclusive: Maybe<Scalars['Boolean']['output']>;
  original_amount: Maybe<Scalars['Float']['output']>;
  raw_original_amount: Maybe<Scalars['JSON']['output']>;
  currency_code: Maybe<Scalars['String']['output']>;
  calculated_price: Maybe<PriceDetails>;
  original_price: Maybe<PriceDetails>;
};

export type PriceDetails = {
  __typename?: 'PriceDetails';
  id: Maybe<Scalars['ID']['output']>;
  price_list_id: Maybe<Scalars['String']['output']>;
  price_list_type: Maybe<Scalars['String']['output']>;
  min_quantity: Maybe<Scalars['Float']['output']>;
  max_quantity: Maybe<Scalars['Float']['output']>;
};

export enum PromotionTypeValues {
  Standard = 'standard',
  Buyget = 'buyget'
}

export enum PromotionRuleOperatorValues {
  Gt = 'gt',
  Lt = 'lt',
  Eq = 'eq',
  Ne = 'ne',
  In = 'in',
  Lte = 'lte',
  Gte = 'gte'
}

export enum CampaignBudgetTypeValues {
  Spend = 'spend',
  Usage = 'usage'
}

export enum ApplicationMethodTypeValues {
  Fixed = 'fixed',
  Percentage = 'percentage'
}

export enum ApplicationMethodTargetTypeValues {
  Order = 'order',
  ShippingMethods = 'shipping_methods',
  Items = 'items'
}

export enum ApplicationMethodAllocationValues {
  Each = 'each',
  Across = 'across'
}

export type Promotion = {
  __typename?: 'Promotion';
  id: Scalars['ID']['output'];
  code: Maybe<Scalars['String']['output']>;
  type: Maybe<PromotionTypeValues>;
  is_automatic: Maybe<Scalars['Boolean']['output']>;
  application_method: Maybe<ApplicationMethod>;
  rules: Maybe<Array<Maybe<PromotionRule>>>;
  campaign_id: Maybe<Scalars['String']['output']>;
  campaign: Maybe<Campaign>;
  order_link: Maybe<LinkOrderPromotion>;
};

export type PromotionRule = {
  __typename?: 'PromotionRule';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  attribute: Maybe<Scalars['String']['output']>;
  operator: Maybe<PromotionRuleOperatorValues>;
  values: Array<PromotionRuleValue>;
};

export type PromotionRuleValue = {
  __typename?: 'PromotionRuleValue';
  id: Scalars['ID']['output'];
  value: Maybe<Scalars['String']['output']>;
};

export type Campaign = {
  __typename?: 'Campaign';
  id: Scalars['ID']['output'];
  name: Maybe<Scalars['String']['output']>;
  description: Maybe<Scalars['String']['output']>;
  campaign_identifier: Maybe<Scalars['String']['output']>;
  starts_at: Maybe<Scalars['DateTime']['output']>;
  ends_at: Maybe<Scalars['DateTime']['output']>;
  budget: Maybe<CampaignBudget>;
  promotions: Maybe<Array<Maybe<Promotion>>>;
};

export type CampaignBudget = {
  __typename?: 'CampaignBudget';
  id: Scalars['ID']['output'];
  type: Maybe<CampaignBudgetTypeValues>;
  limit: Maybe<Scalars['Int']['output']>;
  used: Maybe<Scalars['Int']['output']>;
  currency_code: Maybe<Scalars['String']['output']>;
};

export type ApplicationMethod = {
  __typename?: 'ApplicationMethod';
  id: Scalars['ID']['output'];
  type: Maybe<ApplicationMethodTypeValues>;
  target_type: Maybe<ApplicationMethodTargetTypeValues>;
  allocation: Maybe<ApplicationMethodAllocationValues>;
  value: Maybe<Scalars['Float']['output']>;
  currency_code: Maybe<Scalars['String']['output']>;
  max_quantity: Maybe<Scalars['Int']['output']>;
  buy_rules_min_quantity: Maybe<Scalars['Int']['output']>;
  apply_to_quantity: Maybe<Scalars['Int']['output']>;
  promotion: Maybe<Promotion>;
  target_rules: Maybe<Array<Maybe<PromotionRule>>>;
  buy_rules: Maybe<Array<Maybe<PromotionRule>>>;
};

export type CustomerAddress = {
  __typename?: 'CustomerAddress';
  id: Scalars['ID']['output'];
  address_name: Maybe<Scalars['String']['output']>;
  is_default_shipping: Scalars['Boolean']['output'];
  is_default_billing: Scalars['Boolean']['output'];
  company: Maybe<Scalars['String']['output']>;
  first_name: Maybe<Scalars['String']['output']>;
  last_name: Maybe<Scalars['String']['output']>;
  address_1: Maybe<Scalars['String']['output']>;
  address_2: Maybe<Scalars['String']['output']>;
  city: Maybe<Scalars['String']['output']>;
  country_code: Maybe<Scalars['String']['output']>;
  province: Maybe<Scalars['String']['output']>;
  postal_code: Maybe<Scalars['String']['output']>;
  phone: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  customer_id: Scalars['String']['output'];
  customer: Customer;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type CustomerGroupCustomer = {
  __typename?: 'CustomerGroupCustomer';
  id: Scalars['ID']['output'];
  created_by: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  customer_id: Scalars['String']['output'];
  customer: Customer;
  customer_group_id: Scalars['String']['output'];
  customer_group: CustomerGroup;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type CustomerGroup = {
  __typename?: 'CustomerGroup';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  customers: Array<Maybe<Customer>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Customer = {
  __typename?: 'Customer';
  id: Scalars['ID']['output'];
  company_name: Maybe<Scalars['String']['output']>;
  first_name: Maybe<Scalars['String']['output']>;
  last_name: Maybe<Scalars['String']['output']>;
  email: Maybe<Scalars['String']['output']>;
  phone: Maybe<Scalars['String']['output']>;
  has_account: Scalars['Boolean']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  groups: Array<Maybe<CustomerGroup>>;
  addresses: Array<Maybe<CustomerAddress>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  carts: Maybe<Array<Maybe<Cart>>>;
  orders: Maybe<Array<Maybe<Order>>>;
  account_holder_link: Maybe<Array<Maybe<LinkCustomerAccountHolder>>>;
  product_review_link: Maybe<Array<Maybe<LinkProductReviewProductReviewCustomerCustomer>>>;
  product_reviews: Maybe<Array<Maybe<ProductReview>>>;
  wishlist_link: Maybe<Array<Maybe<LinkCustomerCustomerWishlistModuleWishlist>>>;
  wishlists: Maybe<Array<Maybe<Wishlist>>>;
};

export type SalesChannel = {
  __typename?: 'SalesChannel';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  is_disabled: Scalars['Boolean']['output'];
  created_at: Scalars['DateTime']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  products_link: Maybe<Array<Maybe<LinkProductSalesChannel>>>;
  api_keys_link: Maybe<Array<Maybe<LinkPublishableApiKeySalesChannel>>>;
  publishable_api_keys: Maybe<Array<Maybe<ApiKey>>>;
  carts: Maybe<Array<Maybe<Cart>>>;
  orders: Maybe<Array<Maybe<Order>>>;
  locations_link: Maybe<Array<Maybe<LinkSalesChannelStockLocation>>>;
  stock_locations: Maybe<Array<Maybe<StockLocation>>>;
};

export type Cart = {
  __typename?: 'Cart';
  id: Scalars['ID']['output'];
  region_id: Maybe<Scalars['String']['output']>;
  customer_id: Maybe<Scalars['String']['output']>;
  sales_channel_id: Maybe<Scalars['String']['output']>;
  email: Maybe<Scalars['String']['output']>;
  currency_code: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  completed_at: Maybe<Scalars['DateTime']['output']>;
  shipping_address_id: Maybe<Scalars['String']['output']>;
  shipping_address: Maybe<Address>;
  billing_address_id: Maybe<Scalars['String']['output']>;
  billing_address: Maybe<Address>;
  items: Array<Maybe<LineItem>>;
  credit_lines: Array<Maybe<CreditLine>>;
  shipping_methods: Array<Maybe<ShippingMethod>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  payment_collection_link: Maybe<LinkCartPaymentCollection>;
  payment_collection: Maybe<PaymentCollection>;
  cart_link: Maybe<Array<Maybe<LinkCartPromotion>>>;
  promotions: Maybe<Array<Maybe<Promotion>>>;
  order_link: Maybe<LinkOrderCart>;
  order: Maybe<Order>;
  customer: Maybe<Customer>;
  region: Maybe<Region>;
  sales_channel: Maybe<SalesChannel>;
};

export type CreditLine = {
  __typename?: 'CreditLine';
  id: Scalars['ID']['output'];
  cart_id: Scalars['String']['output'];
  cart: Cart;
  reference: Maybe<Scalars['String']['output']>;
  reference_id: Maybe<Scalars['String']['output']>;
  amount: Scalars['String']['output'];
  raw_amount: Scalars['JSON']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Address = {
  __typename?: 'Address';
  id: Scalars['ID']['output'];
  customer_id: Maybe<Scalars['String']['output']>;
  company: Maybe<Scalars['String']['output']>;
  first_name: Maybe<Scalars['String']['output']>;
  last_name: Maybe<Scalars['String']['output']>;
  address_1: Maybe<Scalars['String']['output']>;
  address_2: Maybe<Scalars['String']['output']>;
  city: Maybe<Scalars['String']['output']>;
  country_code: Maybe<Scalars['String']['output']>;
  province: Maybe<Scalars['String']['output']>;
  postal_code: Maybe<Scalars['String']['output']>;
  phone: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type LineItem = {
  __typename?: 'LineItem';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  subtitle: Maybe<Scalars['String']['output']>;
  thumbnail: Maybe<Scalars['String']['output']>;
  quantity: Scalars['Int']['output'];
  variant_id: Maybe<Scalars['String']['output']>;
  product_id: Maybe<Scalars['String']['output']>;
  product_title: Maybe<Scalars['String']['output']>;
  product_description: Maybe<Scalars['String']['output']>;
  product_subtitle: Maybe<Scalars['String']['output']>;
  product_type: Maybe<Scalars['String']['output']>;
  product_type_id: Maybe<Scalars['String']['output']>;
  product_collection: Maybe<Scalars['String']['output']>;
  product_handle: Maybe<Scalars['String']['output']>;
  variant_sku: Maybe<Scalars['String']['output']>;
  variant_barcode: Maybe<Scalars['String']['output']>;
  variant_title: Maybe<Scalars['String']['output']>;
  variant_option_values: Maybe<Scalars['JSON']['output']>;
  requires_shipping: Scalars['Boolean']['output'];
  is_discountable: Scalars['Boolean']['output'];
  is_giftcard: Scalars['Boolean']['output'];
  is_tax_inclusive: Scalars['Boolean']['output'];
  is_custom_price: Scalars['Boolean']['output'];
  compare_at_unit_price: Maybe<Scalars['String']['output']>;
  unit_price: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  adjustments: Array<Maybe<LineItemAdjustment>>;
  tax_lines: Array<Maybe<LineItemTaxLine>>;
  cart_id: Scalars['String']['output'];
  cart: Cart;
  raw_compare_at_unit_price: Maybe<Scalars['JSON']['output']>;
  raw_unit_price: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  variant: Maybe<ProductVariant>;
};

export type LineItemAdjustment = {
  __typename?: 'LineItemAdjustment';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  code: Maybe<Scalars['String']['output']>;
  amount: Scalars['String']['output'];
  provider_id: Maybe<Scalars['String']['output']>;
  promotion_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  item_id: Scalars['String']['output'];
  item: LineItem;
  raw_amount: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  promotion: Maybe<Promotion>;
};

export type LineItemTaxLine = {
  __typename?: 'LineItemTaxLine';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  rate: Scalars['String']['output'];
  provider_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  tax_rate_id: Maybe<Scalars['String']['output']>;
  item_id: Scalars['String']['output'];
  item: LineItem;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingMethod = {
  __typename?: 'ShippingMethod';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  description: Maybe<Scalars['JSON']['output']>;
  amount: Scalars['String']['output'];
  is_tax_inclusive: Scalars['Boolean']['output'];
  shipping_option_id: Maybe<Scalars['String']['output']>;
  data: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  cart_id: Scalars['String']['output'];
  cart: Cart;
  tax_lines: Array<Maybe<ShippingMethodTaxLine>>;
  adjustments: Array<Maybe<ShippingMethodAdjustment>>;
  raw_amount: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingMethodAdjustment = {
  __typename?: 'ShippingMethodAdjustment';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  code: Maybe<Scalars['String']['output']>;
  amount: Scalars['String']['output'];
  provider_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  promotion_id: Maybe<Scalars['String']['output']>;
  shipping_method_id: Scalars['String']['output'];
  shipping_method: ShippingMethod;
  raw_amount: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingMethodTaxLine = {
  __typename?: 'ShippingMethodTaxLine';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  rate: Scalars['String']['output'];
  provider_id: Maybe<Scalars['String']['output']>;
  tax_rate_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  shipping_method_id: Scalars['String']['output'];
  shipping_method: ShippingMethod;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Region = {
  __typename?: 'Region';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  currency_code: Scalars['String']['output'];
  automatic_taxes: Scalars['Boolean']['output'];
  countries: Array<Maybe<Country>>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  carts: Maybe<Array<Maybe<Cart>>>;
  orders: Maybe<Array<Maybe<Order>>>;
  payment_provider_link: Maybe<Array<Maybe<LinkRegionPaymentProvider>>>;
  payment_providers: Maybe<Array<Maybe<PaymentProvider>>>;
};

export type Country = {
  __typename?: 'Country';
  iso_2: Scalars['ID']['output'];
  iso_3: Scalars['String']['output'];
  num_code: Scalars['String']['output'];
  name: Scalars['String']['output'];
  display_name: Scalars['String']['output'];
  region_id: Maybe<Scalars['String']['output']>;
  region: Maybe<Region>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export enum ApiKeyTypeEnum {
  Publishable = 'PUBLISHABLE',
  Secret = 'SECRET'
}

export type ApiKey = {
  __typename?: 'ApiKey';
  id: Scalars['ID']['output'];
  token: Scalars['String']['output'];
  salt: Scalars['String']['output'];
  redacted: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: ApiKeyTypeEnum;
  last_used_at: Maybe<Scalars['DateTime']['output']>;
  created_by: Scalars['String']['output'];
  revoked_by: Maybe<Scalars['String']['output']>;
  revoked_at: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  sales_channels_link: Maybe<Array<Maybe<LinkPublishableApiKeySalesChannel>>>;
  sales_channels: Maybe<Array<Maybe<SalesChannel>>>;
};

export type Store = {
  __typename?: 'Store';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  default_sales_channel_id: Maybe<Scalars['String']['output']>;
  default_region_id: Maybe<Scalars['String']['output']>;
  default_location_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  supported_currencies: Array<Maybe<StoreCurrency>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  currency: Maybe<Currency>;
};

export type StoreCurrency = {
  __typename?: 'StoreCurrency';
  id: Scalars['ID']['output'];
  currency_code: Scalars['String']['output'];
  is_default: Scalars['Boolean']['output'];
  store_id: Maybe<Scalars['String']['output']>;
  store: Maybe<Store>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type TaxRate = {
  __typename?: 'TaxRate';
  id: Scalars['ID']['output'];
  rate: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
  is_default: Scalars['Boolean']['output'];
  is_combinable: Scalars['Boolean']['output'];
  tax_region_id: Scalars['String']['output'];
  tax_region: TaxRegion;
  rules: Array<Maybe<TaxRateRule>>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type TaxRegion = {
  __typename?: 'TaxRegion';
  id: Scalars['ID']['output'];
  country_code: Scalars['String']['output'];
  province_code: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  provider_id: Maybe<Scalars['String']['output']>;
  provider: Maybe<TaxProvider>;
  parent_id: Maybe<Scalars['String']['output']>;
  parent: Maybe<TaxRegion>;
  children: Array<Maybe<TaxRegion>>;
  tax_rates: Array<Maybe<TaxRate>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type TaxRateRule = {
  __typename?: 'TaxRateRule';
  id: Scalars['ID']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  tax_rate_id: Scalars['String']['output'];
  tax_rate: TaxRate;
  reference: Scalars['String']['output'];
  reference_id: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type TaxProvider = {
  __typename?: 'TaxProvider';
  id: Scalars['ID']['output'];
  is_enabled: Scalars['Boolean']['output'];
  regions: Array<Maybe<TaxRegion>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Currency = {
  __typename?: 'Currency';
  code: Scalars['ID']['output'];
  symbol: Scalars['String']['output'];
  symbol_native: Scalars['String']['output'];
  name: Scalars['String']['output'];
  decimal_digits: Scalars['Int']['output'];
  rounding: Scalars['String']['output'];
  raw_rounding: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export enum PaymentCollectionStatus {
  NotPaid = 'not_paid',
  Awaiting = 'awaiting',
  Authorized = 'authorized',
  PartiallyAuthorized = 'partially_authorized',
  Canceled = 'canceled'
}

export enum PaymentSessionStatus {
  Authorized = 'authorized',
  Captured = 'captured',
  Pending = 'pending',
  RequiresMore = 'requires_more',
  Error = 'error',
  Canceled = 'canceled'
}

export type PaymentCollection = {
  __typename?: 'PaymentCollection';
  id: Scalars['ID']['output'];
  currency_code: Scalars['String']['output'];
  region_id: Scalars['String']['output'];
  amount: Scalars['Float']['output'];
  authorized_amount: Maybe<Scalars['Float']['output']>;
  refunded_amount: Maybe<Scalars['Float']['output']>;
  captured_amount: Maybe<Scalars['Float']['output']>;
  completed_at: Maybe<Scalars['DateTime']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  status: PaymentCollectionStatus;
  cart_link: Maybe<LinkCartPaymentCollection>;
  cart: Maybe<Cart>;
  order_link: Maybe<LinkOrderPaymentCollection>;
  order: Maybe<Order>;
};

export type Payment = {
  __typename?: 'Payment';
  id: Scalars['ID']['output'];
  amount: Scalars['Float']['output'];
  raw_amount: Maybe<Scalars['Float']['output']>;
  authorized_amount: Maybe<Scalars['Float']['output']>;
  raw_authorized_amount: Maybe<Scalars['Float']['output']>;
  currency_code: Scalars['String']['output'];
  provider_id: Scalars['String']['output'];
  cart_id: Maybe<Scalars['String']['output']>;
  order_id: Maybe<Scalars['String']['output']>;
  order_edit_id: Maybe<Scalars['String']['output']>;
  customer_id: Maybe<Scalars['String']['output']>;
  data: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  captured_at: Maybe<Scalars['DateTime']['output']>;
  canceled_at: Maybe<Scalars['DateTime']['output']>;
  captured_amount: Maybe<Scalars['Float']['output']>;
  raw_captured_amount: Maybe<Scalars['Float']['output']>;
  refunded_amount: Maybe<Scalars['Float']['output']>;
  raw_refunded_amount: Maybe<Scalars['Float']['output']>;
  payment_collection_id: Scalars['String']['output'];
};

export type Capture = {
  __typename?: 'Capture';
  id: Scalars['ID']['output'];
  amount: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  created_by: Maybe<Scalars['String']['output']>;
  payment: Payment;
};

export type Refund = {
  __typename?: 'Refund';
  id: Scalars['ID']['output'];
  amount: Scalars['Float']['output'];
  refund_reason_id: Maybe<Scalars['String']['output']>;
  refund_reason: Maybe<RefundReason>;
  note: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  created_by: Maybe<Scalars['String']['output']>;
  payment: Payment;
};

export type PaymentSession = {
  __typename?: 'PaymentSession';
  id: Scalars['ID']['output'];
  amount: Scalars['Float']['output'];
  currency_code: Scalars['String']['output'];
  provider_id: Scalars['String']['output'];
  data: Scalars['JSON']['output'];
  context: Maybe<Scalars['JSON']['output']>;
  status: PaymentSessionStatus;
  authorized_at: Maybe<Scalars['DateTime']['output']>;
  payment_collection_id: Scalars['String']['output'];
  payment_collection: Maybe<PaymentCollection>;
  payment: Maybe<Payment>;
};

export type PaymentProvider = {
  __typename?: 'PaymentProvider';
  id: Scalars['ID']['output'];
  is_enabled: Scalars['String']['output'];
  region_link: Maybe<Array<Maybe<LinkRegionPaymentProvider>>>;
  regions: Maybe<Array<Maybe<Region>>>;
};

export type RefundReason = {
  __typename?: 'RefundReason';
  id: Scalars['ID']['output'];
  label: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export enum ChangeActionType {
  CancelReturnItem = 'CANCEL_RETURN_ITEM',
  FulfillItem = 'FULFILL_ITEM',
  CancelItemFulfillment = 'CANCEL_ITEM_FULFILLMENT',
  ItemAdd = 'ITEM_ADD',
  ItemRemove = 'ITEM_REMOVE',
  ItemUpdate = 'ITEM_UPDATE',
  ReceiveDamagedReturnItem = 'RECEIVE_DAMAGED_RETURN_ITEM',
  ReceiveReturnItem = 'RECEIVE_RETURN_ITEM',
  ReturnItem = 'RETURN_ITEM',
  ShippingAdd = 'SHIPPING_ADD',
  ShippingRemove = 'SHIPPING_REMOVE',
  ShipItem = 'SHIP_ITEM',
  WriteOffItem = 'WRITE_OFF_ITEM',
  ReinstateItem = 'REINSTATE_ITEM'
}

export type OrderSummary = {
  __typename?: 'OrderSummary';
  pending_difference: Maybe<Scalars['Float']['output']>;
  current_order_total: Maybe<Scalars['Float']['output']>;
  original_order_total: Maybe<Scalars['Float']['output']>;
  transaction_total: Maybe<Scalars['Float']['output']>;
  paid_total: Maybe<Scalars['Float']['output']>;
  refunded_total: Maybe<Scalars['Float']['output']>;
  credit_line_total: Maybe<Scalars['Float']['output']>;
  accounting_total: Maybe<Scalars['Float']['output']>;
  raw_pending_difference: Maybe<Scalars['JSON']['output']>;
  raw_current_order_total: Maybe<Scalars['JSON']['output']>;
  raw_original_order_total: Maybe<Scalars['JSON']['output']>;
  raw_transaction_total: Maybe<Scalars['JSON']['output']>;
  raw_paid_total: Maybe<Scalars['JSON']['output']>;
  raw_refunded_total: Maybe<Scalars['JSON']['output']>;
  raw_credit_line_total: Maybe<Scalars['JSON']['output']>;
  raw_accounting_total: Maybe<Scalars['JSON']['output']>;
};

export type OrderShippingMethodAdjustment = {
  __typename?: 'OrderShippingMethodAdjustment';
  id: Scalars['ID']['output'];
  code: Maybe<Scalars['String']['output']>;
  amount: Maybe<Scalars['Float']['output']>;
  order_id: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  promotion_id: Maybe<Scalars['String']['output']>;
  provider_id: Maybe<Scalars['String']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  shipping_method: Maybe<OrderShippingMethod>;
  shipping_method_id: Scalars['String']['output'];
};

export type OrderLineItemAdjustment = {
  __typename?: 'OrderLineItemAdjustment';
  id: Scalars['ID']['output'];
  code: Maybe<Scalars['String']['output']>;
  amount: Maybe<Scalars['Float']['output']>;
  order_id: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  promotion_id: Maybe<Scalars['String']['output']>;
  provider_id: Maybe<Scalars['String']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  item: Maybe<OrderLineItem>;
  item_id: Scalars['String']['output'];
};

export type OrderShippingMethodTaxLine = {
  __typename?: 'OrderShippingMethodTaxLine';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  tax_rate_id: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  rate: Maybe<Scalars['Float']['output']>;
  provider_id: Maybe<Scalars['String']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  shipping_method: Maybe<OrderShippingMethod>;
  shipping_method_id: Scalars['String']['output'];
  total: Maybe<Scalars['Float']['output']>;
  subtotal: Maybe<Scalars['Float']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
};

export type OrderLineItemTaxLine = {
  __typename?: 'OrderLineItemTaxLine';
  id: Scalars['ID']['output'];
  description: Maybe<Scalars['String']['output']>;
  tax_rate_id: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  rate: Maybe<Scalars['Float']['output']>;
  provider_id: Maybe<Scalars['String']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  item: Maybe<OrderLineItem>;
  item_id: Scalars['String']['output'];
  total: Maybe<Scalars['Float']['output']>;
  subtotal: Maybe<Scalars['Float']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
};

export type OrderAddress = {
  __typename?: 'OrderAddress';
  id: Scalars['ID']['output'];
  customer_id: Maybe<Scalars['String']['output']>;
  first_name: Maybe<Scalars['String']['output']>;
  last_name: Maybe<Scalars['String']['output']>;
  phone: Maybe<Scalars['String']['output']>;
  company: Maybe<Scalars['String']['output']>;
  address_1: Maybe<Scalars['String']['output']>;
  address_2: Maybe<Scalars['String']['output']>;
  city: Maybe<Scalars['String']['output']>;
  country_code: Maybe<Scalars['String']['output']>;
  province: Maybe<Scalars['String']['output']>;
  postal_code: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
};

export type OrderShippingMethod = {
  __typename?: 'OrderShippingMethod';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  amount: Maybe<Scalars['Float']['output']>;
  raw_amount: Maybe<Scalars['JSON']['output']>;
  is_tax_inclusive: Maybe<Scalars['Boolean']['output']>;
  shipping_option_id: Maybe<Scalars['String']['output']>;
  data: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  tax_lines: Maybe<Array<Maybe<OrderShippingMethodTaxLine>>>;
  adjustments: Maybe<Array<Maybe<OrderShippingMethodAdjustment>>>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
  original_total: Maybe<Scalars['Float']['output']>;
  original_subtotal: Maybe<Scalars['Float']['output']>;
  original_tax_total: Maybe<Scalars['Float']['output']>;
  total: Maybe<Scalars['Float']['output']>;
  subtotal: Maybe<Scalars['Float']['output']>;
  tax_total: Maybe<Scalars['Float']['output']>;
  discount_total: Maybe<Scalars['Float']['output']>;
  discount_tax_total: Maybe<Scalars['Float']['output']>;
  raw_original_total: Maybe<Scalars['JSON']['output']>;
  raw_original_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_tax_total: Maybe<Scalars['JSON']['output']>;
};

export type OrderLineItem = {
  __typename?: 'OrderLineItem';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  subtitle: Maybe<Scalars['String']['output']>;
  thumbnail: Maybe<Scalars['String']['output']>;
  variant_id: Maybe<Scalars['String']['output']>;
  product_id: Maybe<Scalars['String']['output']>;
  product_title: Maybe<Scalars['String']['output']>;
  product_description: Maybe<Scalars['String']['output']>;
  product_subtitle: Maybe<Scalars['String']['output']>;
  product_type: Maybe<Scalars['String']['output']>;
  product_type_id: Maybe<Scalars['String']['output']>;
  product_collection: Maybe<Scalars['String']['output']>;
  product_handle: Maybe<Scalars['String']['output']>;
  variant_sku: Maybe<Scalars['String']['output']>;
  variant_barcode: Maybe<Scalars['String']['output']>;
  variant_title: Maybe<Scalars['String']['output']>;
  variant_option_values: Maybe<Scalars['JSON']['output']>;
  requires_shipping: Scalars['Boolean']['output'];
  is_discountable: Scalars['Boolean']['output'];
  is_tax_inclusive: Scalars['Boolean']['output'];
  compare_at_unit_price: Maybe<Scalars['Float']['output']>;
  raw_compare_at_unit_price: Maybe<Scalars['JSON']['output']>;
  unit_price: Scalars['Float']['output'];
  raw_unit_price: Maybe<Scalars['JSON']['output']>;
  quantity: Scalars['Int']['output'];
  raw_quantity: Maybe<Scalars['JSON']['output']>;
  tax_lines: Maybe<Array<Maybe<OrderLineItemTaxLine>>>;
  adjustments: Maybe<Array<Maybe<OrderLineItemAdjustment>>>;
  detail: OrderItem;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  original_total: Maybe<Scalars['Float']['output']>;
  original_subtotal: Maybe<Scalars['Float']['output']>;
  original_tax_total: Maybe<Scalars['Float']['output']>;
  item_total: Maybe<Scalars['Float']['output']>;
  item_subtotal: Maybe<Scalars['Float']['output']>;
  item_tax_total: Maybe<Scalars['Float']['output']>;
  total: Maybe<Scalars['Float']['output']>;
  subtotal: Maybe<Scalars['Float']['output']>;
  tax_total: Maybe<Scalars['Float']['output']>;
  discount_total: Maybe<Scalars['Float']['output']>;
  discount_tax_total: Maybe<Scalars['Float']['output']>;
  refundable_total: Maybe<Scalars['Float']['output']>;
  refundable_total_per_unit: Maybe<Scalars['Float']['output']>;
  raw_original_total: Maybe<Scalars['JSON']['output']>;
  raw_original_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_item_total: Maybe<Scalars['JSON']['output']>;
  raw_item_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_item_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_refundable_total: Maybe<Scalars['JSON']['output']>;
  raw_refundable_total_per_unit: Maybe<Scalars['JSON']['output']>;
  product: Maybe<Product>;
  variant: Maybe<ProductVariant>;
};

export type OrderItem = {
  __typename?: 'OrderItem';
  id: Scalars['ID']['output'];
  item_id: Scalars['String']['output'];
  item: OrderLineItem;
  quantity: Scalars['Int']['output'];
  raw_quantity: Maybe<Scalars['JSON']['output']>;
  fulfilled_quantity: Scalars['Int']['output'];
  raw_fulfilled_quantity: Maybe<Scalars['JSON']['output']>;
  shipped_quantity: Scalars['Int']['output'];
  raw_shipped_quantity: Maybe<Scalars['JSON']['output']>;
  return_requested_quantity: Scalars['Int']['output'];
  raw_return_requested_quantity: Maybe<Scalars['JSON']['output']>;
  return_received_quantity: Scalars['Int']['output'];
  raw_return_received_quantity: Maybe<Scalars['JSON']['output']>;
  return_dismissed_quantity: Scalars['Int']['output'];
  raw_return_dismissed_quantity: Maybe<Scalars['JSON']['output']>;
  written_off_quantity: Scalars['Int']['output'];
  raw_written_off_quantity: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export enum OrderStatus {
  Pending = 'pending',
  Completed = 'completed',
  Draft = 'draft',
  Archived = 'archived',
  Canceled = 'canceled',
  RequiresAction = 'requires_action'
}

export type Order = {
  __typename?: 'Order';
  id: Scalars['ID']['output'];
  version: Scalars['Int']['output'];
  order_change: Maybe<OrderChange>;
  status: OrderStatus;
  region_id: Maybe<Scalars['String']['output']>;
  customer_id: Maybe<Scalars['String']['output']>;
  sales_channel_id: Maybe<Scalars['String']['output']>;
  email: Maybe<Scalars['String']['output']>;
  currency_code: Scalars['String']['output'];
  shipping_address: Maybe<OrderAddress>;
  billing_address: Maybe<OrderAddress>;
  items: Maybe<Array<Maybe<OrderLineItem>>>;
  shipping_methods: Maybe<Array<Maybe<OrderShippingMethod>>>;
  transactions: Maybe<Array<Maybe<OrderTransaction>>>;
  summary: Maybe<OrderSummary>;
  metadata: Maybe<Scalars['JSON']['output']>;
  canceled_at: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  original_item_total: Scalars['Float']['output'];
  original_item_subtotal: Scalars['Float']['output'];
  original_item_tax_total: Scalars['Float']['output'];
  item_total: Scalars['Float']['output'];
  item_subtotal: Scalars['Float']['output'];
  item_tax_total: Scalars['Float']['output'];
  original_total: Scalars['Float']['output'];
  original_subtotal: Scalars['Float']['output'];
  original_tax_total: Scalars['Float']['output'];
  total: Scalars['Float']['output'];
  subtotal: Scalars['Float']['output'];
  tax_total: Scalars['Float']['output'];
  discount_total: Scalars['Float']['output'];
  discount_tax_total: Scalars['Float']['output'];
  gift_card_total: Scalars['Float']['output'];
  gift_card_tax_total: Scalars['Float']['output'];
  shipping_total: Scalars['Float']['output'];
  shipping_subtotal: Scalars['Float']['output'];
  shipping_tax_total: Scalars['Float']['output'];
  original_shipping_total: Scalars['Float']['output'];
  original_shipping_subtotal: Scalars['Float']['output'];
  original_shipping_tax_total: Scalars['Float']['output'];
  raw_original_item_total: Maybe<Scalars['JSON']['output']>;
  raw_original_item_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_item_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_item_total: Maybe<Scalars['JSON']['output']>;
  raw_item_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_item_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_original_total: Maybe<Scalars['JSON']['output']>;
  raw_original_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_gift_card_total: Maybe<Scalars['JSON']['output']>;
  raw_gift_card_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_shipping_total: Maybe<Scalars['JSON']['output']>;
  raw_shipping_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_shipping_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_total: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_tax_total: Maybe<Scalars['JSON']['output']>;
  cart_link: Maybe<LinkOrderCart>;
  cart: Maybe<Cart>;
  fulfillment_link: Maybe<Array<Maybe<LinkOrderFulfillment>>>;
  fulfillments: Maybe<Array<Maybe<Fulfillment>>>;
  payment_collections_link: Maybe<LinkOrderPaymentCollection>;
  payment_collections: Maybe<Array<Maybe<PaymentCollection>>>;
  promotion_link: Maybe<LinkOrderPromotion>;
  promotion: Maybe<Array<Maybe<Promotion>>>;
  customer: Maybe<Customer>;
  region: Maybe<Region>;
  sales_channel: Maybe<SalesChannel>;
  product_review_link: Maybe<Array<Maybe<LinkProductReviewProductReviewOrderOrder>>>;
  product_reviews: Maybe<Array<Maybe<ProductReview>>>;
};

export enum ReturnStatus {
  Requested = 'requested',
  Received = 'received',
  PartiallyReceived = 'partially_received',
  Canceled = 'canceled'
}

export type Return = {
  __typename?: 'Return';
  id: Scalars['ID']['output'];
  status: ReturnStatus;
  refund_amount: Maybe<Scalars['Float']['output']>;
  order_id: Scalars['String']['output'];
  items: Array<Maybe<OrderReturnItem>>;
  return_fulfillment_link: Maybe<Array<Maybe<LinkReturnFulfillment>>>;
  fulfillments: Maybe<Array<Maybe<Fulfillment>>>;
};

export type OrderReturnItem = {
  __typename?: 'OrderReturnItem';
  id: Scalars['ID']['output'];
  return_id: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  item_id: Scalars['String']['output'];
  reason_id: Maybe<Scalars['String']['output']>;
  quantity: Scalars['Int']['output'];
  raw_quantity: Maybe<Scalars['JSON']['output']>;
  received_quantity: Maybe<Scalars['Int']['output']>;
  raw_received_quantity: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
};

export type OrderClaimItem = {
  __typename?: 'OrderClaimItem';
  id: Scalars['ID']['output'];
  claim_id: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  item_id: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  images: Maybe<Array<Maybe<OrderClaimItemImage>>>;
  raw_quantity: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
};

export type OrderClaimItemImage = {
  __typename?: 'OrderClaimItemImage';
  id: Scalars['ID']['output'];
  claim_item_id: Scalars['String']['output'];
  item: OrderClaimItem;
  url: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
};

export type OrderExchangeItem = {
  __typename?: 'OrderExchangeItem';
  id: Scalars['ID']['output'];
  exchange_id: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  item_id: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  raw_quantity: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Maybe<Scalars['DateTime']['output']>;
  updated_at: Maybe<Scalars['DateTime']['output']>;
};

export type OrderClaim = {
  __typename?: 'OrderClaim';
  order_id: Scalars['String']['output'];
  claim_items: Array<Maybe<OrderClaimItem>>;
  additional_items: Array<Maybe<OrderClaimItem>>;
  return: Maybe<Return>;
  return_id: Maybe<Scalars['String']['output']>;
  no_notification: Maybe<Scalars['Boolean']['output']>;
  refund_amount: Maybe<Scalars['Float']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
};

export type OrderExchange = {
  __typename?: 'OrderExchange';
  order_id: Scalars['String']['output'];
  return_items: Array<Maybe<OrderReturnItem>>;
  additional_items: Array<Maybe<OrderExchangeItem>>;
  no_notification: Maybe<Scalars['Boolean']['output']>;
  difference_due: Maybe<Scalars['Float']['output']>;
  return: Maybe<Return>;
  return_id: Maybe<Scalars['String']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
};

export enum PaymentStatus {
  NotPaid = 'not_paid',
  Awaiting = 'awaiting',
  Authorized = 'authorized',
  PartiallyAuthorized = 'partially_authorized',
  Captured = 'captured',
  PartiallyCaptured = 'partially_captured',
  PartiallyRefunded = 'partially_refunded',
  Refunded = 'refunded',
  Canceled = 'canceled',
  RequiresAction = 'requires_action'
}

export enum FulfillmentStatus {
  NotFulfilled = 'not_fulfilled',
  PartiallyFulfilled = 'partially_fulfilled',
  Fulfilled = 'fulfilled',
  PartiallyShipped = 'partially_shipped',
  Shipped = 'shipped',
  PartiallyDelivered = 'partially_delivered',
  Delivered = 'delivered',
  Canceled = 'canceled'
}

export type OrderDetail = {
  __typename?: 'OrderDetail';
  id: Scalars['ID']['output'];
  version: Scalars['Int']['output'];
  order_change: Maybe<OrderChange>;
  status: OrderStatus;
  region_id: Maybe<Scalars['String']['output']>;
  customer_id: Maybe<Scalars['String']['output']>;
  sales_channel_id: Maybe<Scalars['String']['output']>;
  email: Maybe<Scalars['String']['output']>;
  currency_code: Scalars['String']['output'];
  shipping_address: Maybe<OrderAddress>;
  billing_address: Maybe<OrderAddress>;
  items: Maybe<Array<Maybe<OrderLineItem>>>;
  shipping_methods: Maybe<Array<Maybe<OrderShippingMethod>>>;
  transactions: Maybe<Array<Maybe<OrderTransaction>>>;
  summary: Maybe<OrderSummary>;
  metadata: Maybe<Scalars['JSON']['output']>;
  canceled_at: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  original_item_total: Scalars['Float']['output'];
  original_item_subtotal: Scalars['Float']['output'];
  original_item_tax_total: Scalars['Float']['output'];
  item_total: Scalars['Float']['output'];
  item_subtotal: Scalars['Float']['output'];
  item_tax_total: Scalars['Float']['output'];
  original_total: Scalars['Float']['output'];
  original_subtotal: Scalars['Float']['output'];
  original_tax_total: Scalars['Float']['output'];
  total: Scalars['Float']['output'];
  subtotal: Scalars['Float']['output'];
  tax_total: Scalars['Float']['output'];
  discount_total: Scalars['Float']['output'];
  discount_tax_total: Scalars['Float']['output'];
  gift_card_total: Scalars['Float']['output'];
  gift_card_tax_total: Scalars['Float']['output'];
  shipping_total: Scalars['Float']['output'];
  shipping_subtotal: Scalars['Float']['output'];
  shipping_tax_total: Scalars['Float']['output'];
  original_shipping_total: Scalars['Float']['output'];
  original_shipping_subtotal: Scalars['Float']['output'];
  original_shipping_tax_total: Scalars['Float']['output'];
  raw_original_item_total: Maybe<Scalars['JSON']['output']>;
  raw_original_item_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_item_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_item_total: Maybe<Scalars['JSON']['output']>;
  raw_item_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_item_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_original_total: Maybe<Scalars['JSON']['output']>;
  raw_original_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_total: Maybe<Scalars['JSON']['output']>;
  raw_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_total: Maybe<Scalars['JSON']['output']>;
  raw_discount_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_gift_card_total: Maybe<Scalars['JSON']['output']>;
  raw_gift_card_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_shipping_total: Maybe<Scalars['JSON']['output']>;
  raw_shipping_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_shipping_tax_total: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_total: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_subtotal: Maybe<Scalars['JSON']['output']>;
  raw_original_shipping_tax_total: Maybe<Scalars['JSON']['output']>;
  payment_collections: Maybe<Array<Maybe<PaymentCollection>>>;
  payment_status: PaymentStatus;
  fulfillments: Maybe<Array<Maybe<Fulfillment>>>;
  fulfillment_status: FulfillmentStatus;
};

export type OrderChange = {
  __typename?: 'OrderChange';
  id: Scalars['ID']['output'];
  version: Scalars['Int']['output'];
  change_type: Maybe<Scalars['String']['output']>;
  order_id: Scalars['String']['output'];
  return_id: Maybe<Scalars['String']['output']>;
  exchange_id: Maybe<Scalars['String']['output']>;
  claim_id: Maybe<Scalars['String']['output']>;
  order: Order;
  return_order: Maybe<Return>;
  exchange: Maybe<OrderExchange>;
  claim: Maybe<OrderClaim>;
  actions: Array<Maybe<OrderChangeAction>>;
  status: Scalars['String']['output'];
  requested_by: Maybe<Scalars['String']['output']>;
  requested_at: Maybe<Scalars['DateTime']['output']>;
  confirmed_by: Maybe<Scalars['String']['output']>;
  confirmed_at: Maybe<Scalars['DateTime']['output']>;
  declined_by: Maybe<Scalars['String']['output']>;
  declined_reason: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  declined_at: Maybe<Scalars['DateTime']['output']>;
  canceled_by: Maybe<Scalars['String']['output']>;
  canceled_at: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type OrderChangeAction = {
  __typename?: 'OrderChangeAction';
  id: Scalars['ID']['output'];
  order_change_id: Maybe<Scalars['String']['output']>;
  order_change: Maybe<OrderChange>;
  order_id: Maybe<Scalars['String']['output']>;
  return_id: Maybe<Scalars['String']['output']>;
  claim_id: Maybe<Scalars['String']['output']>;
  exchange_id: Maybe<Scalars['String']['output']>;
  order: Maybe<Order>;
  reference: Scalars['String']['output'];
  reference_id: Scalars['String']['output'];
  action: ChangeActionType;
  details: Maybe<Scalars['JSON']['output']>;
  internal_note: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type OrderTransaction = {
  __typename?: 'OrderTransaction';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  order: Order;
  amount: Scalars['Float']['output'];
  raw_amount: Maybe<Scalars['JSON']['output']>;
  currency_code: Scalars['String']['output'];
  reference: Scalars['String']['output'];
  reference_id: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type AuthIdentity = {
  __typename?: 'AuthIdentity';
  id: Scalars['ID']['output'];
  provider_identities: Array<Maybe<ProviderIdentity>>;
  app_metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ProviderIdentity = {
  __typename?: 'ProviderIdentity';
  id: Scalars['ID']['output'];
  entity_id: Scalars['String']['output'];
  provider: Scalars['String']['output'];
  auth_identity_id: Scalars['String']['output'];
  auth_identity: AuthIdentity;
  user_metadata: Maybe<Scalars['JSON']['output']>;
  provider_metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type User = {
  __typename?: 'User';
  id: Scalars['ID']['output'];
  first_name: Maybe<Scalars['String']['output']>;
  last_name: Maybe<Scalars['String']['output']>;
  email: Scalars['String']['output'];
  avatar_url: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Invite = {
  __typename?: 'Invite';
  id: Scalars['ID']['output'];
  email: Scalars['String']['output'];
  accepted: Scalars['Boolean']['output'];
  token: Scalars['String']['output'];
  expires_at: Scalars['DateTime']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export enum GeoZoneType {
  Country = 'country',
  Province = 'province',
  City = 'city',
  Zip = 'zip'
}

export enum ShippingOptionPriceType {
  Calculated = 'calculated',
  Flat = 'flat'
}

export type FulfillmentItem = {
  __typename?: 'FulfillmentItem';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  sku: Scalars['String']['output'];
  barcode: Scalars['String']['output'];
  line_item_id: Maybe<Scalars['String']['output']>;
  inventory_item_id: Maybe<Scalars['String']['output']>;
  fulfillment_id: Scalars['String']['output'];
  fulfillment: Fulfillment;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type FulfillmentLabel = {
  __typename?: 'FulfillmentLabel';
  id: Scalars['ID']['output'];
  tracking_number: Scalars['String']['output'];
  tracking_url: Scalars['String']['output'];
  label_url: Scalars['String']['output'];
  fulfillment_id: Scalars['String']['output'];
  fulfillment: Fulfillment;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type FulfillmentProvider = {
  __typename?: 'FulfillmentProvider';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  shipping_options: Array<ShippingOption>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  locations_link: Maybe<Array<Maybe<LinkLocationFulfillmentProvider>>>;
  locations: Maybe<Array<Maybe<StockLocation>>>;
};

export type FulfillmentSet = {
  __typename?: 'FulfillmentSet';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  service_zones: Array<ServiceZone>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  locations_link: Maybe<LinkLocationFulfillmentSet>;
  location: Maybe<StockLocation>;
};

export type Fulfillment = {
  __typename?: 'Fulfillment';
  id: Scalars['ID']['output'];
  location_id: Scalars['String']['output'];
  packed_at: Maybe<Scalars['DateTime']['output']>;
  shipped_at: Maybe<Scalars['DateTime']['output']>;
  delivered_at: Maybe<Scalars['DateTime']['output']>;
  canceled_at: Maybe<Scalars['DateTime']['output']>;
  marked_shipped_by: Maybe<Scalars['String']['output']>;
  created_by: Maybe<Scalars['String']['output']>;
  data: Maybe<Scalars['JSON']['output']>;
  provider_id: Scalars['String']['output'];
  shipping_option_id: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  shipping_option: Maybe<ShippingOption>;
  provider: FulfillmentProvider;
  items: Array<FulfillmentItem>;
  labels: Array<FulfillmentLabel>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  order_link: Maybe<LinkOrderFulfillment>;
  order: Maybe<Order>;
  return_link: Maybe<LinkReturnFulfillment>;
};

export type GeoZone = {
  __typename?: 'GeoZone';
  id: Scalars['ID']['output'];
  type: GeoZoneType;
  country_code: Scalars['String']['output'];
  province_code: Maybe<Scalars['String']['output']>;
  city: Maybe<Scalars['String']['output']>;
  postal_expression: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ServiceZone = {
  __typename?: 'ServiceZone';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  fulfillment_set: FulfillmentSet;
  fulfillment_set_id: Scalars['String']['output'];
  geo_zones: Array<GeoZone>;
  shipping_options: Array<ShippingOption>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingOptionRule = {
  __typename?: 'ShippingOptionRule';
  id: Scalars['ID']['output'];
  attribute: Scalars['String']['output'];
  operator: Scalars['String']['output'];
  value: Maybe<Scalars['JSON']['output']>;
  shipping_option_id: Scalars['String']['output'];
  shipping_option: ShippingOption;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingOptionType = {
  __typename?: 'ShippingOptionType';
  id: Scalars['ID']['output'];
  label: Scalars['String']['output'];
  description: Scalars['String']['output'];
  code: Scalars['String']['output'];
  shipping_option_id: Scalars['String']['output'];
  shipping_option: ShippingOption;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ShippingOption = {
  __typename?: 'ShippingOption';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  price_type: ShippingOptionPriceType;
  service_zone_id: Scalars['String']['output'];
  shipping_profile_id: Scalars['String']['output'];
  provider_id: Scalars['String']['output'];
  shipping_option_type_id: Maybe<Scalars['String']['output']>;
  data: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  service_zone: ServiceZone;
  shipping_profile: ShippingProfile;
  fulfillment_provider: FulfillmentProvider;
  type: ShippingOptionType;
  rules: Array<ShippingOptionRule>;
  fulfillments: Array<Fulfillment>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  price_set_link: Maybe<LinkShippingOptionPriceSet>;
};

export type ShippingProfile = {
  __typename?: 'ShippingProfile';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  shipping_options: Array<ShippingOption>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  products_link: Maybe<Array<Maybe<LinkProductShippingProfile>>>;
};

export enum NotificationStatusEnum {
  Pending = 'PENDING',
  Success = 'SUCCESS',
  Failure = 'FAILURE'
}

export type Notification = {
  __typename?: 'Notification';
  id: Scalars['ID']['output'];
  to: Scalars['String']['output'];
  channel: Scalars['String']['output'];
  template: Scalars['String']['output'];
  data: Maybe<Scalars['JSON']['output']>;
  trigger_type: Maybe<Scalars['String']['output']>;
  resource_id: Maybe<Scalars['String']['output']>;
  resource_type: Maybe<Scalars['String']['output']>;
  receiver_id: Maybe<Scalars['String']['output']>;
  original_notification_id: Maybe<Scalars['String']['output']>;
  idempotency_key: Maybe<Scalars['String']['output']>;
  external_id: Maybe<Scalars['String']['output']>;
  status: NotificationStatusEnum;
  provider_id: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export enum WorkflowExecutionStateEnum {
  NotStarted = 'NOT_STARTED',
  Invoking = 'INVOKING',
  WaitingToCompensate = 'WAITING_TO_COMPENSATE',
  Compensating = 'COMPENSATING',
  Done = 'DONE',
  Reverted = 'REVERTED',
  Failed = 'FAILED'
}

export type WorkflowExecution = {
  __typename?: 'WorkflowExecution';
  id: Scalars['ID']['output'];
  workflow_id: Scalars['ID']['output'];
  transaction_id: Scalars['ID']['output'];
  execution: Maybe<Scalars['JSON']['output']>;
  context: Maybe<Scalars['JSON']['output']>;
  state: WorkflowExecutionStateEnum;
  retention_time: Maybe<Scalars['Int']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Page = {
  __typename?: 'Page';
  id: Scalars['ID']['output'];
  title: Scalars['JSON']['output'];
  content: Scalars['JSON']['output'];
  handle: Scalars['String']['output'];
  is_visible: Scalars['Boolean']['output'];
  template: Scalars['String']['output'];
  metadata: Scalars['JSON']['output'];
  image: Maybe<Scalars['String']['output']>;
  image_cover: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  layout_link: Maybe<LinkCmsModulePageLayoutModulelayout>;
  layout: Maybe<Layout>;
  translation_link: Maybe<Array<Maybe<LinkCmsModulePageLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
};

export type PostCategory = {
  __typename?: 'PostCategory';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  description: Scalars['String']['output'];
  handle: Scalars['String']['output'];
  mpath: Maybe<Scalars['String']['output']>;
  is_active: Scalars['Boolean']['output'];
  is_internal: Scalars['Boolean']['output'];
  rank: Scalars['Int']['output'];
  image: Maybe<Scalars['String']['output']>;
  image_cover: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  parent_id: Maybe<Scalars['String']['output']>;
  parent: Maybe<PostCategory>;
  children: Array<Maybe<PostCategory>>;
  posts: Array<Maybe<Post>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  translation_link: Maybe<Array<Maybe<LinkCmsModulePostCategoryLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
};

export type Post = {
  __typename?: 'Post';
  id: Scalars['ID']['output'];
  title: Scalars['JSON']['output'];
  excerpt: Scalars['JSON']['output'];
  content: Scalars['JSON']['output'];
  handle: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  is_internal: Scalars['Boolean']['output'];
  featured: Scalars['Boolean']['output'];
  rank: Scalars['Int']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  post_categories: Array<Maybe<PostCategory>>;
  image: Maybe<Scalars['String']['output']>;
  image_cover: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  translation_link: Maybe<Array<Maybe<LinkCmsModulePostLanguageModuleTranslation>>>;
  translations: Maybe<Array<Maybe<Translation>>>;
};

export type Banner = {
  __typename?: 'Banner';
  id: Scalars['ID']['output'];
  title: Scalars['JSON']['output'];
  description: Scalars['JSON']['output'];
  image: Maybe<Scalars['String']['output']>;
  link: Maybe<Scalars['String']['output']>;
  button_text: Maybe<Scalars['JSON']['output']>;
  position: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  rank: Scalars['Int']['output'];
  start_date: Maybe<Scalars['DateTime']['output']>;
  end_date: Maybe<Scalars['DateTime']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Layout = {
  __typename?: 'Layout';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  sections: Array<Maybe<LayoutSection>>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  page_link: Maybe<LinkCmsModulePageLayoutModulelayout>;
  page: Maybe<Page>;
};

export type LayoutSection = {
  __typename?: 'LayoutSection';
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  description: Maybe<Scalars['String']['output']>;
  content: Maybe<Scalars['JSON']['output']>;
  section_type: Scalars['String']['output'];
  section_rank: Maybe<Scalars['Int']['output']>;
  section_position: Maybe<Scalars['String']['output']>;
  layout_id: Scalars['String']['output'];
  layout: Layout;
  metadata: Maybe<Scalars['JSON']['output']>;
  mpath: Maybe<Scalars['String']['output']>;
  parent_id: Maybe<Scalars['String']['output']>;
  parent: Maybe<LayoutSection>;
  children: Array<Maybe<LayoutSection>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ContactForm = {
  __typename?: 'ContactForm';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  handle: Scalars['String']['output'];
  fields: Array<Maybe<ContactFormField>>;
  contact_data: Array<Maybe<ContactFormSubmit>>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ContactFormField = {
  __typename?: 'ContactFormField';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
  is_required: Scalars['Boolean']['output'];
  default_value: Maybe<Scalars['String']['output']>;
  options: Maybe<Scalars['JSON']['output']>;
  contact_form_id: Scalars['String']['output'];
  contact_form: ContactForm;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ContactFormSubmit = {
  __typename?: 'ContactFormSubmit';
  id: Scalars['ID']['output'];
  contact_form_id: Scalars['String']['output'];
  contact_form: ContactForm;
  form_data: Scalars['JSON']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type ProductVariantImage = {
  __typename?: 'ProductVariantImage';
  id: Scalars['ID']['output'];
  image_id: Maybe<Scalars['String']['output']>;
  image_url: Maybe<Scalars['String']['output']>;
  image_rank: Maybe<Scalars['Int']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_variant_link: Maybe<LinkProductProductVariantProductVariantImageProductVariantImage>;
  product_variant: Maybe<ProductVariant>;
};

export type ProductOptionImage = {
  __typename?: 'ProductOptionImage';
  id: Scalars['ID']['output'];
  image_id: Maybe<Scalars['String']['output']>;
  image_url: Maybe<Scalars['String']['output']>;
  image_rank: Maybe<Scalars['Int']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_option_value_link: Maybe<LinkProductProductOptionValueProductOptionImageProductOptionImage>;
  product_option_value: Maybe<ProductOptionValue>;
};

export type ProductReview = {
  __typename?: 'ProductReview';
  id: Scalars['ID']['output'];
  customer_id: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  order_item_id: Scalars['String']['output'];
  product_id: Scalars['String']['output'];
  rating: Scalars['Int']['output'];
  images: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  content: Maybe<Scalars['String']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  customer_link: Maybe<Array<Maybe<LinkProductReviewProductReviewCustomerCustomer>>>;
  customers: Maybe<Array<Maybe<Customer>>>;
  order_link: Maybe<Array<Maybe<LinkProductReviewProductReviewOrderOrder>>>;
  orders: Maybe<Array<Maybe<Order>>>;
};

export type ProductRating = {
  __typename?: 'ProductRating';
  id: Scalars['ID']['output'];
  rating_options: Scalars['JSON']['output'];
  stats: Scalars['JSON']['output'];
  review_ids: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_link: Maybe<LinkProductReviewProductRatingProductProduct>;
  product: Maybe<Product>;
};

export type ProductMixMatch = {
  __typename?: 'ProductMixMatch';
  id: Scalars['ID']['output'];
  title: Maybe<Scalars['String']['output']>;
  content: Maybe<Scalars['String']['output']>;
  images: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rank: Maybe<Scalars['Int']['output']>;
  is_visible: Scalars['Boolean']['output'];
  customer_metadata: Maybe<Scalars['JSON']['output']>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_link: Maybe<Array<Maybe<LinkProductMixMatchProductMixMatchProductProduct>>>;
  products: Maybe<Array<Maybe<Product>>>;
};

export type Wishlist = {
  __typename?: 'Wishlist';
  id: Scalars['ID']['output'];
  customer_id: Scalars['String']['output'];
  items: Array<Maybe<WishlistItem>>;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  customer_link: Maybe<LinkCustomerCustomerWishlistModuleWishlist>;
  customer: Maybe<Customer>;
};

export type WishlistItem = {
  __typename?: 'WishlistItem';
  id: Scalars['ID']['output'];
  wishlist_id: Scalars['String']['output'];
  wishlist: Wishlist;
  product_id: Scalars['String']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  product_link: Maybe<LinkWishlistModuleWishlistItemProductProduct>;
  product: Maybe<Product>;
};

export type Language = {
  __typename?: 'Language';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  code: Scalars['String']['output'];
  is_visible: Scalars['Boolean']['output'];
  metadata: Maybe<Scalars['JSON']['output']>;
  icon: Maybe<Scalars['JSON']['output']>;
  translations: Array<Maybe<Translation>>;
  is_default: Scalars['Boolean']['output'];
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
};

export type Translation = {
  __typename?: 'Translation';
  id: Scalars['ID']['output'];
  reference_id: Maybe<Scalars['String']['output']>;
  object_type: Maybe<Scalars['String']['output']>;
  attribute_key: Maybe<Scalars['String']['output']>;
  attribute_value: Maybe<Scalars['String']['output']>;
  language_code: Scalars['String']['output'];
  language_id: Scalars['String']['output'];
  language: Language;
  metadata: Maybe<Scalars['JSON']['output']>;
  created_at: Scalars['DateTime']['output'];
  updated_at: Scalars['DateTime']['output'];
  deleted_at: Maybe<Scalars['DateTime']['output']>;
  page_link: Maybe<LinkCmsModulePageLanguageModuleTranslation>;
  page: Maybe<Page>;
  post_category_link: Maybe<LinkCmsModulePostCategoryLanguageModuleTranslation>;
  post_category: Maybe<PostCategory>;
  post_link: Maybe<LinkCmsModulePostLanguageModuleTranslation>;
  post: Maybe<Post>;
  product_category_link: Maybe<LinkProductProductCategoryLanguageModuleTranslation>;
  product_category: Maybe<ProductCategory>;
  product_collection_link: Maybe<LinkProductProductCollectionLanguageModuleTranslation>;
  product_collection: Maybe<ProductCollection>;
  product_link: Maybe<LinkProductProductLanguageModuleTranslation>;
  product: Maybe<Product>;
};

export type LinkCartPaymentCollection = {
  __typename?: 'LinkCartPaymentCollection';
  cart_id: Scalars['String']['output'];
  payment_collection_id: Scalars['String']['output'];
  cart: Maybe<Cart>;
  payment_collection: Maybe<Payment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCartPromotion = {
  __typename?: 'LinkCartPromotion';
  cart_id: Scalars['String']['output'];
  promotion_id: Scalars['String']['output'];
  cart: Maybe<Cart>;
  promotions: Maybe<Promotion>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkLocationFulfillmentProvider = {
  __typename?: 'LinkLocationFulfillmentProvider';
  stock_location_id: Scalars['String']['output'];
  fulfillment_provider_id: Scalars['String']['output'];
  location: Maybe<StockLocation>;
  fulfillment_provider: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkLocationFulfillmentSet = {
  __typename?: 'LinkLocationFulfillmentSet';
  stock_location_id: Scalars['String']['output'];
  fulfillment_set_id: Scalars['String']['output'];
  location: Maybe<StockLocation>;
  fulfillment_set: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkOrderCart = {
  __typename?: 'LinkOrderCart';
  order_id: Scalars['String']['output'];
  cart_id: Scalars['String']['output'];
  order: Maybe<Order>;
  cart: Maybe<Cart>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkOrderFulfillment = {
  __typename?: 'LinkOrderFulfillment';
  order_id: Scalars['String']['output'];
  fulfillment_id: Scalars['String']['output'];
  order: Maybe<Order>;
  fulfillments: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkOrderPaymentCollection = {
  __typename?: 'LinkOrderPaymentCollection';
  order_id: Scalars['String']['output'];
  payment_collection_id: Scalars['String']['output'];
  order: Maybe<Order>;
  payment_collection: Maybe<Payment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkOrderPromotion = {
  __typename?: 'LinkOrderPromotion';
  order_id: Scalars['String']['output'];
  promotion_id: Scalars['String']['output'];
  order: Maybe<Order>;
  promotion: Maybe<Promotion>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkReturnFulfillment = {
  __typename?: 'LinkReturnFulfillment';
  return_id: Scalars['String']['output'];
  fulfillment_id: Scalars['String']['output'];
  return: Maybe<Order>;
  fulfillments: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductSalesChannel = {
  __typename?: 'LinkProductSalesChannel';
  product_id: Scalars['String']['output'];
  sales_channel_id: Scalars['String']['output'];
  product: Maybe<Product>;
  sales_channel: Maybe<SalesChannel>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductVariantInventoryItem = {
  __typename?: 'LinkProductVariantInventoryItem';
  variant_id: Scalars['String']['output'];
  inventory_item_id: Scalars['String']['output'];
  required_quantity: Scalars['Int']['output'];
  variant: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductVariantPriceSet = {
  __typename?: 'LinkProductVariantPriceSet';
  variant_id: Scalars['String']['output'];
  price_set_id: Scalars['String']['output'];
  variant: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkPublishableApiKeySalesChannel = {
  __typename?: 'LinkPublishableApiKeySalesChannel';
  publishable_key_id: Scalars['String']['output'];
  sales_channel_id: Scalars['String']['output'];
  api_key: Maybe<ApiKey>;
  sales_channel: Maybe<SalesChannel>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkRegionPaymentProvider = {
  __typename?: 'LinkRegionPaymentProvider';
  region_id: Scalars['String']['output'];
  payment_provider_id: Scalars['String']['output'];
  region: Maybe<Region>;
  payment_provider: Maybe<Payment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkSalesChannelStockLocation = {
  __typename?: 'LinkSalesChannelStockLocation';
  sales_channel_id: Scalars['String']['output'];
  stock_location_id: Scalars['String']['output'];
  sales_channel: Maybe<SalesChannel>;
  location: Maybe<StockLocation>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkShippingOptionPriceSet = {
  __typename?: 'LinkShippingOptionPriceSet';
  shipping_option_id: Scalars['String']['output'];
  price_set_id: Scalars['String']['output'];
  shipping_option: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductShippingProfile = {
  __typename?: 'LinkProductShippingProfile';
  product_id: Scalars['String']['output'];
  shipping_profile_id: Scalars['String']['output'];
  product: Maybe<Product>;
  shipping_profile: Maybe<Fulfillment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCustomerAccountHolder = {
  __typename?: 'LinkCustomerAccountHolder';
  customer_id: Scalars['String']['output'];
  account_holder_id: Scalars['String']['output'];
  customer: Maybe<Customer>;
  account_holder: Maybe<Payment>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCmsModulePageLayoutModulelayout = {
  __typename?: 'LinkCmsModulePageLayoutModulelayout';
  page_id: Scalars['String']['output'];
  layout_id: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCmsModulePageLanguageModuleTranslation = {
  __typename?: 'LinkCmsModulePageLanguageModuleTranslation';
  page_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCmsModulePostCategoryLanguageModuleTranslation = {
  __typename?: 'LinkCmsModulePostCategoryLanguageModuleTranslation';
  post_category_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCmsModulePostLanguageModuleTranslation = {
  __typename?: 'LinkCmsModulePostLanguageModuleTranslation';
  post_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductProductCategoryLanguageModuleTranslation = {
  __typename?: 'LinkProductProductCategoryLanguageModuleTranslation';
  product_category_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  product_category: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductProductCollectionLanguageModuleTranslation = {
  __typename?: 'LinkProductProductCollectionLanguageModuleTranslation';
  product_collection_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  product_collection: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductMixMatchProductMixMatchProductProduct = {
  __typename?: 'LinkProductMixMatchProductMixMatchProductProduct';
  product_mix_match_id: Scalars['String']['output'];
  product_id: Scalars['String']['output'];
  product_mix_match: Maybe<ProductMixMatch>;
  product: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductProductOptionValueProductOptionImageProductOptionImage = {
  __typename?: 'LinkProductProductOptionValueProductOptionImageProductOptionImage';
  product_option_value_id: Scalars['String']['output'];
  product_option_image_id: Scalars['String']['output'];
  product_option_value: Maybe<Product>;
  product_option_image: Maybe<ProductOptionImage>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductReviewProductRatingProductProduct = {
  __typename?: 'LinkProductReviewProductRatingProductProduct';
  product_rating_id: Scalars['String']['output'];
  product_id: Scalars['String']['output'];
  product_rating: Maybe<ProductReview>;
  product: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductProductLanguageModuleTranslation = {
  __typename?: 'LinkProductProductLanguageModuleTranslation';
  product_id: Scalars['String']['output'];
  translation_id: Scalars['String']['output'];
  product: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductProductVariantProductVariantImageProductVariantImage = {
  __typename?: 'LinkProductProductVariantProductVariantImageProductVariantImage';
  product_variant_id: Scalars['String']['output'];
  product_variant_image_id: Scalars['String']['output'];
  product_variant: Maybe<Product>;
  product_variant_image: Maybe<ProductVariantImage>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductReviewProductReviewCustomerCustomer = {
  __typename?: 'LinkProductReviewProductReviewCustomerCustomer';
  product_review_id: Scalars['String']['output'];
  customer_id: Scalars['String']['output'];
  product_review: Maybe<ProductReview>;
  customer: Maybe<Customer>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkProductReviewProductReviewOrderOrder = {
  __typename?: 'LinkProductReviewProductReviewOrderOrder';
  product_review_id: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  product_review: Maybe<ProductReview>;
  order: Maybe<Order>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkWishlistModuleWishlistItemProductProduct = {
  __typename?: 'LinkWishlistModuleWishlistItemProductProduct';
  wishlist_item_id: Scalars['String']['output'];
  product_id: Scalars['String']['output'];
  product: Maybe<Product>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

export type LinkCustomerCustomerWishlistModuleWishlist = {
  __typename?: 'LinkCustomerCustomerWishlistModuleWishlist';
  customer_id: Scalars['String']['output'];
  wishlist_id: Scalars['String']['output'];
  customer: Maybe<Customer>;
  createdAt: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  deletedAt: Maybe<Scalars['String']['output']>;
};

declare module '@medusajs/framework/types' {
  interface RemoteQueryEntryPoints {
    stock_location_address: StockLocationAddress
    stock_location_addresses: StockLocationAddress
    stock_location: StockLocation
    stock_locations: StockLocation
    inventory_items: InventoryItem
    inventory_item: InventoryItem
    inventory: InventoryItem
    reservation: ReservationItem
    reservations: ReservationItem
    reservation_item: ReservationItem
    reservation_items: ReservationItem
    inventory_level: InventoryLevel
    inventory_levels: InventoryLevel
    product_variant: ProductVariant
    product_variants: ProductVariant
    variant: ProductVariant
    variants: ProductVariant
    product: Product
    products: Product
    product_option: ProductOption
    product_options: ProductOption
    product_option_value: ProductOptionValue
    product_option_values: ProductOptionValue
    product_type: ProductType
    product_types: ProductType
    product_tag: ProductTag
    product_tags: ProductTag
    product_collection: ProductCollection
    product_collections: ProductCollection
    product_category: ProductCategory
    product_categories: ProductCategory
    price_set: PriceSet
    price_sets: PriceSet
    price_list: any
    price_lists: any
    price: Price
    prices: Price
    price_preference: any
    price_preferences: any
    promotion: Promotion
    promotions: Promotion
    campaign: Campaign
    campaigns: Campaign
    promotion_rule: PromotionRule
    promotion_rules: PromotionRule
    customer_address: CustomerAddress
    customer_addresses: CustomerAddress
    customer_group_customer: CustomerGroupCustomer
    customer_group_customers: CustomerGroupCustomer
    customer_group: CustomerGroup
    customer_groups: CustomerGroup
    customer: Customer
    customers: Customer
    sales_channel: SalesChannel
    sales_channels: SalesChannel
    cart: Cart
    carts: Cart
    credit_line: CreditLine
    credit_lines: CreditLine
    address: Address
    addresses: Address
    line_item: LineItem
    line_items: LineItem
    line_item_adjustment: LineItemAdjustment
    line_item_adjustments: LineItemAdjustment
    line_item_tax_line: LineItemTaxLine
    line_item_tax_lines: LineItemTaxLine
    shipping_method: ShippingMethod
    shipping_methods: ShippingMethod
    shipping_method_adjustment: ShippingMethodAdjustment
    shipping_method_adjustments: ShippingMethodAdjustment
    shipping_method_tax_line: ShippingMethodTaxLine
    shipping_method_tax_lines: ShippingMethodTaxLine
    region: Region
    regions: Region
    country: Country
    countries: Country
    api_key: ApiKey
    api_keys: ApiKey
    store: Store
    stores: Store
    store_currency: StoreCurrency
    store_currencies: StoreCurrency
    tax_rate: TaxRate
    tax_rates: TaxRate
    tax_region: TaxRegion
    tax_regions: TaxRegion
    tax_rate_rule: TaxRateRule
    tax_rate_rules: TaxRateRule
    tax_provider: TaxProvider
    tax_providers: TaxProvider
    currency: Currency
    currencies: Currency
    payment_method: any
    payment_methods: any
    payment: Payment
    payments: Payment
    payment_collection: PaymentCollection
    payment_collections: PaymentCollection
    payment_provider: PaymentProvider
    payment_providers: PaymentProvider
    payment_session: PaymentSession
    payment_sessions: PaymentSession
    refund_reason: RefundReason
    refund_reasons: RefundReason
    account_holder: any
    account_holders: any
    order: Order
    orders: Order
    order_address: OrderAddress
    order_addresses: OrderAddress
    order_change: OrderChange
    order_changes: OrderChange
    order_claim: OrderClaim
    order_claims: OrderClaim
    order_exchange: OrderExchange
    order_exchanges: OrderExchange
    order_item: OrderItem
    order_items: OrderItem
    order_line_item: OrderLineItem
    order_line_items: OrderLineItem
    order_shipping_method: OrderShippingMethod
    order_shipping_methods: OrderShippingMethod
    order_transaction: OrderTransaction
    order_transactions: OrderTransaction
    return: Return
    returns: Return
    return_reason: any
    return_reasons: any
    auth_identity: AuthIdentity
    auth_identities: AuthIdentity
    provider_identity: ProviderIdentity
    provider_identities: ProviderIdentity
    user: User
    users: User
    invite: Invite
    invites: Invite
    fulfillment_address: any
    fulfillment_addresses: any
    fulfillment_item: FulfillmentItem
    fulfillment_items: FulfillmentItem
    fulfillment_label: FulfillmentLabel
    fulfillment_labels: FulfillmentLabel
    fulfillment_provider: FulfillmentProvider
    fulfillment_providers: FulfillmentProvider
    fulfillment_set: FulfillmentSet
    fulfillment_sets: FulfillmentSet
    fulfillment: Fulfillment
    fulfillments: Fulfillment
    geo_zone: GeoZone
    geo_zones: GeoZone
    service_zone: ServiceZone
    service_zones: ServiceZone
    shipping_option_rule: ShippingOptionRule
    shipping_option_rules: ShippingOptionRule
    shipping_option_type: ShippingOptionType
    shipping_option_types: ShippingOptionType
    shipping_option: ShippingOption
    shipping_options: ShippingOption
    shipping_profile: ShippingProfile
    shipping_profiles: ShippingProfile
    notification: Notification
    notifications: Notification
    workflow_execution: WorkflowExecution
    workflow_executions: WorkflowExecution
    file: any
    files: any
    page: Page
    pages: Page
    post_category: PostCategory
    post_categories: PostCategory
    post: Post
    posts: Post
    banner: Banner
    banners: Banner
    menu: any
    menus: any
    layout: Layout
    layouts: Layout
    layout_section: LayoutSection
    layout_sections: LayoutSection
    contact_form: ContactForm
    contact_forms: ContactForm
    contact_form_field: ContactFormField
    contact_form_fields: ContactFormField
    contact_form_submit: ContactFormSubmit
    contact_form_submits: ContactFormSubmit
    product_variant_image: ProductVariantImage
    product_variant_images: ProductVariantImage
    product_option_image: ProductOptionImage
    product_option_images: ProductOptionImage
    product_review: ProductReview
    product_reviews: ProductReview
    product_rating: ProductRating
    product_ratings: ProductRating
    product_mix_match: ProductMixMatch
    product_mix_matches: ProductMixMatch
    wishlist: Wishlist
    wishlists: Wishlist
    wishlist_item: WishlistItem
    wishlist_items: WishlistItem
    language: Language
    languages: Language
    translation: Translation
    translations: Translation
    cart_payment_collection: LinkCartPaymentCollection
    cart_payment_collections: LinkCartPaymentCollection
    cart_promotion: LinkCartPromotion
    cart_promotions: LinkCartPromotion
    location_fulfillment_provider: LinkLocationFulfillmentProvider
    location_fulfillment_providers: LinkLocationFulfillmentProvider
    location_fulfillment_set: LinkLocationFulfillmentSet
    location_fulfillment_sets: LinkLocationFulfillmentSet
    order_cart: LinkOrderCart
    order_carts: LinkOrderCart
    order_fulfillment: LinkOrderFulfillment
    order_fulfillments: LinkOrderFulfillment
    order_payment_collection: LinkOrderPaymentCollection
    order_payment_collections: LinkOrderPaymentCollection
    order_promotion: LinkOrderPromotion
    order_promotions: LinkOrderPromotion
    return_fulfillment: LinkReturnFulfillment
    return_fulfillments: LinkReturnFulfillment
    product_sales_channel: LinkProductSalesChannel
    product_sales_channels: LinkProductSalesChannel
    product_variant_inventory_item: LinkProductVariantInventoryItem
    product_variant_inventory_items: LinkProductVariantInventoryItem
    product_variant_price_set: LinkProductVariantPriceSet
    product_variant_price_sets: LinkProductVariantPriceSet
    publishable_api_key_sales_channel: LinkPublishableApiKeySalesChannel
    publishable_api_key_sales_channels: LinkPublishableApiKeySalesChannel
    region_payment_provider: LinkRegionPaymentProvider
    region_payment_providers: LinkRegionPaymentProvider
    sales_channel_location: LinkSalesChannelStockLocation
    sales_channel_locations: LinkSalesChannelStockLocation
    shipping_option_price_set: LinkShippingOptionPriceSet
    shipping_option_price_sets: LinkShippingOptionPriceSet
    product_shipping_profile: LinkProductShippingProfile
    product_shipping_profiles: LinkProductShippingProfile
    customer_account_holder: LinkCustomerAccountHolder
    customer_account_holders: LinkCustomerAccountHolder
    page_layout: LinkCmsModulePageLayoutModulelayout
    page_translation: LinkCmsModulePageLanguageModuleTranslation
    post_category_translation: LinkCmsModulePostCategoryLanguageModuleTranslation
    post_translation: LinkCmsModulePostLanguageModuleTranslation
    product_category_translation: LinkProductProductCategoryLanguageModuleTranslation
    product_collection_translation: LinkProductProductCollectionLanguageModuleTranslation
    product_mix_match_product: LinkProductMixMatchProductMixMatchProductProduct
    product_option_value_product_option_image: LinkProductProductOptionValueProductOptionImageProductOptionImage
    product_rating_product: LinkProductReviewProductRatingProductProduct
    product_translation: LinkProductProductLanguageModuleTranslation
    product_variant_product_variant_image: LinkProductProductVariantProductVariantImageProductVariantImage
    product_review_customer: LinkProductReviewProductReviewCustomerCustomer
    product_review_order: LinkProductReviewProductReviewOrderOrder
    wishlist_item_product: LinkWishlistModuleWishlistItemProductProduct
    customer_wishlist: LinkCustomerCustomerWishlistModuleWishlist
  }
}