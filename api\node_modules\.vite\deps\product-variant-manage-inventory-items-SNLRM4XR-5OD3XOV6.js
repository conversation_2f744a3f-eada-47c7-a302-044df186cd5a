import {
  VARIANT_DETAIL_FIELDS
} from "./chunk-VPOHTWLW.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-MLCYGAA2.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  ZodIssueCode,
  arrayType,
  numberType,
  objectType,
  stringType,
  unionType
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useProductVariant,
  useProductVariantsInventoryItemsBatch
} from "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  IconButton,
  Input,
  Label,
  XMarkMini,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-variant-manage-inventory-items-SNLRM4XR.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ManageVariantInventoryItemsSchema = objectType({
  inventory: arrayType(
    objectType({
      inventory_item_id: stringType().min(1, instance.t("products.variant.inventory.validation.itemId")),
      required_quantity: unionType([numberType(), stringType()])
    }).superRefine((data, ctx) => {
      const quantity = data.required_quantity ? castNumber(data.required_quantity) : 0;
      if (quantity < 1) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: instance.t(
            "products.variant.inventory.validation.quantity"
          ),
          path: ["required_quantity"]
        });
      }
    })
  )
});
function ManageVariantInventoryItemsForm({
  variant
}) {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      inventory: variant.inventory_items.length ? variant.inventory_items.map((i) => ({
        required_quantity: i.required_quantity,
        inventory_item_id: i.inventory.id
      })) : [
        {
          inventory_item_id: "",
          required_quantity: ""
        }
      ]
    },
    resolver: t(ManageVariantInventoryItemsSchema)
  });
  const inventory = useFieldArray({
    control: form.control,
    name: `inventory`
  });
  const hasKit = inventory.fields.length > 1;
  const items = useComboboxData({
    queryKey: ["inventory_items"],
    queryFn: (params) => sdk.admin.inventoryItem.list(params),
    getOptions: (data) => data.inventory_items.map((item) => ({
      label: `${item.title} ${item.sku ? `(${item.sku})` : ""}`,
      value: item.id
    })),
    defaultValue: (_b = (_a = variant.inventory_items) == null ? void 0 : _a[0]) == null ? void 0 : _b.inventory_item_id
  });
  const { mutateAsync, isPending } = useProductVariantsInventoryItemsBatch(
    variant == null ? void 0 : variant.product_id
  );
  const handleSubmit = form.handleSubmit(async (values) => {
    const existingItems = {};
    const selectedItems = {};
    variant.inventory_items.forEach(
      (i) => existingItems[i.inventory.id] = i.required_quantity
    );
    values.inventory.forEach((i) => selectedItems[i.inventory_item_id] = true);
    const payload = {};
    values.inventory.forEach((v) => {
      if (v.inventory_item_id in existingItems) {
        if (v.required_quantity !== existingItems[v.inventory_item_id]) {
          payload.update = payload.update || [];
          payload.update.push({
            required_quantity: castNumber(v.required_quantity),
            inventory_item_id: v.inventory_item_id,
            variant_id: variant.id
          });
        }
      } else {
        payload.create = payload.create || [];
        payload.create.push({
          required_quantity: castNumber(v.required_quantity),
          inventory_item_id: v.inventory_item_id,
          variant_id: variant.id
        });
      }
    });
    variant.inventory_items.forEach((i) => {
      if (!(i.inventory.id in selectedItems)) {
        payload.delete = payload.delete || [];
        payload.delete.push({
          inventory_item_id: i.inventory.id,
          variant_id: variant.id
        });
      }
    });
    await mutateAsync(payload, {
      onSuccess: () => {
        toast.success(t2("products.variant.inventory.toast.itemsManageSuccess"));
        handleSuccess();
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex justify-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col gap-y-8 px-6 pt-12 md:w-[720px] md:pt-24", children: [
          (0, import_jsx_runtime.jsx)(Heading, { children: t2(
            hasKit ? "products.create.inventory.heading" : "fields.inventoryItems"
          ) }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid gap-y-4", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between gap-x-4", children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: variant.title }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2(
                  hasKit ? "products.create.inventory.label" : "fields.inventoryItem"
                ) })
              ] }),
              (0, import_jsx_runtime.jsx)(
                Button,
                {
                  size: "small",
                  variant: "secondary",
                  type: "button",
                  onClick: () => {
                    inventory.append({
                      inventory_item_id: "",
                      required_quantity: ""
                    });
                  },
                  children: t2("actions.add")
                }
              )
            ] }),
            inventory.fields.map((inventoryItem, inventoryIndex) => (0, import_jsx_runtime.jsxs)(
              "li",
              {
                className: "bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",
                children: [
                  (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-[min-content,1fr] items-center gap-1.5", children: [
                    (0, import_jsx_runtime.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime.jsx)(
                      Label,
                      {
                        size: "xsmall",
                        weight: "plus",
                        className: "text-ui-fg-subtle",
                        htmlFor: `inventory.${inventoryIndex}.inventory_item_id`,
                        children: t2("fields.item")
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(
                      Form.Field,
                      {
                        control: form.control,
                        name: `inventory.${inventoryIndex}.inventory_item_id`,
                        render: ({ field }) => {
                          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                              Combobox,
                              {
                                ...field,
                                options: items.options,
                                searchValue: items.searchValue,
                                onSearchValueChange: items.onSearchValueChange,
                                fetchNextPage: items.fetchNextPage,
                                className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                                placeholder: t2(
                                  "products.create.inventory.itemPlaceholder"
                                )
                              }
                            ) }),
                            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                          ] });
                        }
                      }
                    ),
                    (0, import_jsx_runtime.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime.jsx)(
                      Label,
                      {
                        size: "xsmall",
                        weight: "plus",
                        className: "text-ui-fg-subtle",
                        htmlFor: `inventory.${inventoryIndex}.required_quantity`,
                        children: t2("fields.quantity")
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(
                      Form.Field,
                      {
                        control: form.control,
                        name: `inventory.${inventoryIndex}.required_quantity`,
                        render: ({ field: { onChange, value, ...field } }) => {
                          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                              Input,
                              {
                                type: "number",
                                className: "bg-ui-bg-field-component",
                                min: 0,
                                value,
                                onChange,
                                ...field,
                                placeholder: t2(
                                  "products.create.inventory.quantityPlaceholder"
                                )
                              }
                            ) }),
                            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                          ] });
                        }
                      }
                    )
                  ] }),
                  (0, import_jsx_runtime.jsx)(
                    IconButton,
                    {
                      type: "button",
                      size: "small",
                      variant: "transparent",
                      className: "text-ui-fg-muted",
                      onClick: () => inventory.remove(inventoryIndex),
                      children: (0, import_jsx_runtime.jsx)(XMarkMini, {})
                    }
                  )
                ]
              },
              inventoryItem.id
            ))
          ] })
        ] }) })
      ]
    }
  ) });
}
function ProductVariantManageInventoryItems() {
  const { id, variant_id } = useParams();
  const {
    variant,
    isPending: isLoading,
    isError,
    error
  } = useProductVariant(id, variant_id, {
    fields: VARIANT_DETAIL_FIELDS
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: !isLoading && variant && (0, import_jsx_runtime2.jsx)(ManageVariantInventoryItemsForm, { variant }) });
}
export {
  ProductVariantManageInventoryItems as Component
};
//# sourceMappingURL=product-variant-manage-inventory-items-SNLRM4XR-5OD3XOV6.js.map
