import {
  RulesFormField
} from "./chunk-3FCV2SD6.js";
import "./chunk-CFEMRZCK.js";
import "./chunk-MLCYGAA2.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  usePromotion,
  usePromotionAddRules,
  usePromotionRemoveRules,
  usePromotionUpdateRules,
  useUpdatePromotion
} from "./chunk-S32V3COL.js";
import "./chunk-RWC53K5F.js";
import "./chunk-662EXSHO.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/edit-rules-7MMNZKEQ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var EditRules = z.object({
  type: z.string().optional(),
  rules: z.array(
    z.object({
      id: z.string().optional(),
      attribute: z.string().min(1, { message: instance.t("promotions.form.required") }),
      operator: z.string().min(1, { message: instance.t("promotions.form.required") }),
      values: z.union([
        z.number().min(1, { message: instance.t("promotions.form.required") }),
        z.string().min(1, { message: instance.t("promotions.form.required") }),
        z.array(z.string()).min(1, { message: instance.t("promotions.form.required") })
      ]),
      required: z.boolean().optional(),
      disguised: z.boolean().optional(),
      field_type: z.string().optional()
    })
  )
});
var EditRulesForm = ({
  promotion,
  ruleType,
  handleSubmit,
  isSubmitting
}) => {
  const { t: t2 } = useTranslation();
  const [rulesToRemove, setRulesToRemove] = (0, import_react.useState)([]);
  const form = useForm({
    defaultValues: { rules: [], type: promotion.type },
    resolver: t(EditRules)
  });
  const handleFormSubmit = form.handleSubmit(handleSubmit(rulesToRemove));
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleFormSubmit,
      className: "flex h-full flex-col",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsx)(
          RulesFormField,
          {
            form,
            ruleType,
            setRulesToRemove,
            rulesToRemove,
            promotion
          }
        ) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", disabled: isSubmitting, children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isSubmitting, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var getRuleValue = (rule) => {
  if (rule.field_type === "number") {
    return parseInt(rule.values);
  }
  return rule.values;
};
var EditRulesWrapper = ({
  promotion,
  rules,
  ruleType
}) => {
  const { handleSuccess } = useRouteModal();
  const { mutateAsync: updatePromotion } = useUpdatePromotion(promotion.id);
  const { mutateAsync: addPromotionRules } = usePromotionAddRules(
    promotion.id,
    ruleType
  );
  const { mutateAsync: removePromotionRules } = usePromotionRemoveRules(
    promotion.id,
    ruleType
  );
  const { mutateAsync: updatePromotionRules, isPending } = usePromotionUpdateRules(promotion.id, ruleType);
  const handleSubmit = (rulesToRemove) => {
    return async function(data) {
      const applicationMethodData = {};
      const { rules: allRules = [] } = data;
      const disguisedRules = allRules.filter((rule) => rule.disguised);
      const disguisedRulesToRemove = (rulesToRemove == null ? void 0 : rulesToRemove.filter((r) => r.disguised)) || [];
      for (const rule of disguisedRules) {
        applicationMethodData[rule.attribute] = getRuleValue(rule);
      }
      for (const rule of disguisedRulesToRemove) {
        applicationMethodData[rule.attribute] = null;
      }
      const rulesData = allRules.filter((rule) => !rule.disguised);
      const rulesToCreate = rulesData.filter(
        (rule) => !("id" in rule)
      );
      const rulesToUpdate = rulesData.filter(
        (rule) => typeof rule.id === "string"
      );
      if (Object.keys(applicationMethodData).length) {
        await updatePromotion({
          application_method: applicationMethodData
        });
      }
      rulesToCreate.length && await addPromotionRules({
        rules: rulesToCreate.map((rule) => {
          return {
            attribute: rule.attribute,
            operator: rule.operator,
            values: rule.values
          };
        })
      });
      (rulesToRemove == null ? void 0 : rulesToRemove.length) && await removePromotionRules({
        rule_ids: rulesToRemove.map((r) => r.id).filter(Boolean)
      });
      rulesToUpdate.length && await updatePromotionRules({
        rules: rulesToUpdate.map((rule) => {
          return {
            id: rule.id,
            attribute: rule.attribute,
            operator: rule.operator,
            values: rule.values
          };
        })
      });
      handleSuccess();
    };
  };
  return (0, import_jsx_runtime2.jsx)(
    EditRulesForm,
    {
      promotion,
      rules,
      ruleType,
      handleSubmit,
      isSubmitting: isPending
    }
  );
};
var EditRules2 = () => {
  var _a, _b;
  const params = useParams();
  const allowedParams = [
    "rules",
    "buy-rules",
    "target-rules"
    /* TARGET_RULES */
  ];
  if (!allowedParams.includes(params.ruleType)) {
    throw "invalid page";
  }
  const { t: t2 } = useTranslation();
  const ruleType = params.ruleType;
  const id = params.id;
  const rules = [];
  const { promotion, isPending: isLoading, isError, error } = usePromotion(id);
  if (promotion) {
    if (ruleType === "rules") {
      rules.push(...promotion.rules || []);
    } else if (ruleType === "target-rules") {
      rules.push(...((_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.target_rules) || []);
    } else if (ruleType === "buy-rules") {
      rules.push(...((_b = promotion.application_method) == null ? void 0 : _b.buy_rules) || []);
    }
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime3.jsx)(Heading, { children: t2(`promotions.edit.${ruleType}.title`) }) }),
    !isLoading && promotion && (0, import_jsx_runtime3.jsx)(
      EditRulesWrapper,
      {
        promotion,
        rules,
        ruleType
      }
    )
  ] });
};
export {
  EditRules2 as Component
};
//# sourceMappingURL=edit-rules-7MMNZKEQ-CXZZN7TV.js.map
