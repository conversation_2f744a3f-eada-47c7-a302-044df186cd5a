{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-list-TFAHMX6Y.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-CQXEEXNP.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-2WQFRVK5.mjs\";\nimport \"./chunk-B646R3EG.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  productsQueryKeys,\n  useDeleteProduct,\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/products/product-list/loader.ts\nvar productsListQuery = () => ({\n  queryKey: productsQueryKeys.list({\n    limit: 20,\n    offset: 0,\n    is_giftcard: false\n  }),\n  queryFn: async () => sdk.admin.product.list({ limit: 20, offset: 0, is_giftcard: false })\n});\nvar productsLoader = (client) => {\n  return async () => {\n    const query = productsListQuery();\n    return queryClient.getQueryData(\n      query.queryKey\n    ) ?? await client.fetchQuery(query);\n  };\n};\n\n// src/routes/products/product-list/components/product-list-table/product-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading, toast, usePrompt } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link, Outlet, useLoaderData, useLocation } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ProductListTable = () => {\n  const { t } = useTranslation();\n  const location = useLocation();\n  const initialData = useLoaderData();\n  const { searchParams, raw } = useProductTableQuery({ pageSize: PAGE_SIZE });\n  const { products, count, isLoading, isError, error } = useProducts(\n    {\n      ...searchParams,\n      is_giftcard: false\n    },\n    {\n      initialData,\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useProductTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: products ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    pageSize: PAGE_SIZE,\n    getRowId: (row) => row.id\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Heading, { level: \"h2\", children: t(\"products.domain\") }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: `export${location.search}`, children: t(\"actions.export\") }) }),\n        /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: \"import\", children: t(\"actions.import\") }) }),\n        /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: \"create\", children: t(\"actions.create\") }) })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        filters,\n        search: true,\n        pagination: true,\n        isLoading,\n        queryObject: raw,\n        navigateTo: (row) => `${row.original.id}`,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        noRecords: {\n          message: t(\"products.list.noRecordsMessage\")\n        }\n      }\n    ),\n    /* @__PURE__ */ jsx(Outlet, {})\n  ] });\n};\nvar ProductActions = ({ product }) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteProduct(product.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"products.deleteWarning\", {\n        title: product.title\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"products.toasts.delete.success.header\"), {\n          description: t(\"products.toasts.delete.success.description\", {\n            title: product.title\n          })\n        });\n      },\n      onError: (e) => {\n        toast.error(t(\"products.toasts.delete.error.header\"), {\n          description: e.message\n        });\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `/products/${product.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTableColumns();\n  const columns = useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(ProductActions, { product: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n  return columns;\n};\n\n// src/routes/products/product-list/product-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProductList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product.list.after\"),\n        before: getWidgets(\"product.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(ProductListTable, {})\n    }\n  );\n};\nexport {\n  ProductList as Component,\n  productsLoader as productLoader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA,mBAAwB;AAGxB,yBAA0B;AA6I1B,IAAAA,sBAA4B;AAtK5B,IAAI,oBAAoB,OAAO;AAAA,EAC7B,UAAU,kBAAkB,KAAK;AAAA,IAC/B,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,EACf,CAAC;AAAA,EACD,SAAS,YAAY,IAAI,MAAM,QAAQ,KAAK,EAAE,OAAO,IAAI,QAAQ,GAAG,aAAa,MAAM,CAAC;AAC1F;AACA,IAAI,iBAAiB,CAAC,WAAW;AAC/B,SAAO,YAAY;AACjB,UAAM,QAAQ,kBAAkB;AAChC,WAAO,YAAY;AAAA,MACjB,MAAM;AAAA,IACR,KAAK,MAAM,OAAO,WAAW,KAAK;AAAA,EACpC;AACF;AAWA,IAAI,YAAY;AAChB,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB,EAAE,UAAU,UAAU,CAAC;AAC1E,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACrD;AAAA,MACE,GAAG;AAAA,MACH,aAAa;AAAA,IACf;AAAA,IACA;AAAA,MACE;AAAA,MACA,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,uBAAuB;AACvC,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU,CAAC,QAAQ,IAAI;AAAA,EACzB,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,wBAAI,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC;AAAA,UAC5D,yBAAK,OAAO,EAAE,WAAW,4CAA4C,UAAU;AAAA,YAC7E,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,SAAS,SAAS,MAAM,IAAI,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,YAC1K,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,YACxJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,MAC1K,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ;AAAA,QACA,aAAa;AAAA,QACb,YAAY,CAAC,QAAQ,GAAG,IAAI,SAAS,EAAE;AAAA,QACvC,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,WAAW;AAAA,UACT,SAAS,EAAE,gCAAgC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,QAAQ,CAAC,CAAC;AAAA,EAChC,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC,EAAE,QAAQ,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,iBAAiB,QAAQ,EAAE;AACnD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,0BAA0B;AAAA,QACvC,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,uCAAuC,GAAG;AAAA,UACxD,aAAa,EAAE,8CAA8C;AAAA,YAC3D,OAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,qCAAqC,GAAG;AAAA,UACpD,aAAa,EAAE;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,aAAa,QAAQ,EAAE;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,QAAM,cAAU;AAAA,IACd,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,gBAAgB,EAAE,SAAS,IAAI,SAAS,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACA,SAAO;AACT;AAIA,IAAI,cAAc,MAAM;AACtB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,oBAAoB;AAAA,QACtC,QAAQ,WAAW,qBAAqB;AAAA,MAC1C;AAAA,MACA,cAA0B,oBAAAA,KAAK,kBAAkB,CAAC,CAAC;AAAA,IACrD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}