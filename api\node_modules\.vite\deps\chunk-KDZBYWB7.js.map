{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LADMQDYD.mjs"], "sourcesContent": ["import {\n  getPromotionStatus\n} from \"./chunk-KD3INMVA.mjs\";\nimport {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/columns/use-promotion-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\n// src/components/table/table-cells/common/code-cell/code-cell.tsx\nimport { Badge } from \"@medusajs/ui\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CodeCell = ({ code }) => {\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center gap-x-3 overflow-hidden\", children: /* @__PURE__ */ jsx(Badge, { size: \"2xsmall\", className: \"truncate\", children: code }) });\n};\nvar CodeHeader = ({ text }) => {\n  return /* @__PURE__ */ jsx(\"div\", { className: \" flex h-full w-full items-center \", children: /* @__PURE__ */ jsx(\"span\", { children: text }) });\n};\n\n// src/components/table/table-cells/promotion/status-cell/status-cell.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar StatusCell2 = ({ promotion }) => {\n  const [color, text] = getPromotionStatus(promotion);\n  return /* @__PURE__ */ jsx2(StatusCell, { color, children: text });\n};\n\n// src/hooks/table/columns/use-promotion-table-columns.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar usePromotionTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"code\",\n        header: () => /* @__PURE__ */ jsx3(CodeHeader, { text: t(\"fields.code\") }),\n        cell: ({ row }) => /* @__PURE__ */ jsx3(CodeCell, { code: row.original.code })\n      }),\n      columnHelper.display({\n        id: \"method\",\n        header: () => /* @__PURE__ */ jsx3(TextHeader, { text: t(\"promotions.fields.method\") }),\n        cell: ({ row }) => {\n          const text = row.original.is_automatic ? t(\"promotions.form.method.automatic.title\") : t(\"promotions.form.method.code.title\");\n          return /* @__PURE__ */ jsx3(TextCell, { text });\n        }\n      }),\n      columnHelper.display({\n        id: \"status\",\n        header: () => /* @__PURE__ */ jsx3(TextHeader, { text: t(\"fields.status\") }),\n        cell: ({ row }) => /* @__PURE__ */ jsx3(StatusCell2, { promotion: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/hooks/table/query/use-promotion-table-query.tsx\nvar usePromotionTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\"offset\", \"q\", \"created_at\", \"updated_at\"],\n    prefix\n  );\n  const { offset, q, created_at, updated_at } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    offset: offset ? Number(offset) : 0,\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  usePromotionTableColumns,\n  usePromotionTableQuery\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,mBAAwB;AAKxB,yBAAoB;AASpB,IAAAA,sBAA4B;AAO5B,IAAAA,sBAA4B;AAf5B,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM;AAC3B,aAAuB,wBAAI,OAAO,EAAE,WAAW,2DAA2D,cAA0B,wBAAI,OAAO,EAAE,MAAM,WAAW,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AAC9M;AACA,IAAI,aAAa,CAAC,EAAE,KAAK,MAAM;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,qCAAqC,cAA0B,wBAAI,QAAQ,EAAE,UAAU,KAAK,CAAC,EAAE,CAAC;AACjJ;AAIA,IAAI,cAAc,CAAC,EAAE,UAAU,MAAM;AACnC,QAAM,CAAC,OAAO,IAAI,IAAI,mBAAmB,SAAS;AAClD,aAAuB,oBAAAC,KAAK,YAAY,EAAE,OAAO,UAAU,KAAK,CAAC;AACnE;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,2BAA2B,MAAM;AACnC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAC,KAAK,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,QACzE,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,UAAU,EAAE,MAAM,IAAI,SAAS,KAAK,CAAC;AAAA,MAC/E,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,0BAA0B,EAAE,CAAC;AAAA,QACtF,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,OAAO,IAAI,SAAS,eAAe,EAAE,wCAAwC,IAAI,EAAE,mCAAmC;AAC5H,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,KAAK,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;AAAA,QAC3E,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,aAAa,EAAE,WAAW,IAAI,SAAS,CAAC;AAAA,MAClF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB,CAAC,UAAU,KAAK,cAAc,YAAY;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,YAAY,WAAW,IAAI;AAC9C,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}