import {
  useDeleteProductCategory
} from "./chunk-ZJNBJBHK.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  toast,
  usePrompt
} from "./chunk-RJPG7Y6U.js";

// node_modules/@medusajs/dashboard/dist/chunk-TP7N4YBP.mjs
var useDeleteProductCategoryAction = (category) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteProductCategory(category.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("categories.delete.confirmation", {
        name: category.name
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("categories.delete.successToast", {
            name: category.name
          })
        );
        navigate("/categories", {
          replace: true
        });
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return handleDelete;
};

export {
  useDeleteProductCategoryAction
};
//# sourceMappingURL=chunk-TM7HU4GE.js.map
