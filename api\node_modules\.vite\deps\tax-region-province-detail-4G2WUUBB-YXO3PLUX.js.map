{"version": 3, "sources": ["../../@medusajs/dashboard/dist/tax-region-province-detail-4G2WUUBB.mjs"], "sourcesContent": ["import {\n  TaxOverrideTable,\n  TaxRateLine,\n  useTaxOverrideTable\n} from \"./chunk-2MPQMCLO.mjs\";\nimport {\n  TaxRegionCard\n} from \"./chunk-X2Y4KNQI.mjs\";\nimport {\n  getProvinceByIso2,\n  isProvinceInCountry\n} from \"./chunk-THZJC662.mjs\";\nimport \"./chunk-V3MOBCDF.mjs\";\nimport {\n  useTaxRateTableQuery\n} from \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-3WXBLS2P.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-VDBOSWVE.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport {\n  useTaxRates\n} from \"./chunk-UAZEQNCO.mjs\";\nimport {\n  taxRegionsQueryKeys,\n  useTaxRegion\n} from \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/tax-regions/tax-region-province-detail/tax-region-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/tax-regions/tax-region-province-detail/components/tax-region-province-detail-section/tax-region-detail-section.tsx\nimport { Badge, Container, Tooltip } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionProvinceDetailSection = ({\n  taxRegion\n}) => {\n  const { t } = useTranslation();\n  const defaultRates = taxRegion.tax_rates.filter((r) => r.is_default === true);\n  const showBage = defaultRates.length === 0;\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsx(\n      TaxRegionCard,\n      {\n        taxRegion,\n        type: \"header\",\n        asLink: false,\n        badge: showBage && /* @__PURE__ */ jsx(Tooltip, { content: t(\"taxRegions.fields.noDefaultRate.tooltip\"), children: /* @__PURE__ */ jsx(Badge, { color: \"orange\", size: \"2xsmall\", className: \"cursor-default\", children: t(\"taxRegions.fields.noDefaultRate.label\") }) })\n      }\n    ),\n    defaultRates.map((rate) => {\n      return /* @__PURE__ */ jsx(TaxRateLine, { taxRate: rate, isSublevelTaxRate: true }, rate.id);\n    })\n  ] });\n};\n\n// src/routes/tax-regions/tax-region-province-detail/components/tax-region-province-override-section/tax-region-province-override-section.tsx\nimport { Container as Container2, Heading } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar PREFIX = \"o\";\nvar TaxRegionProvinceOverrideSection = ({\n  taxRegion\n}) => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useTaxRateTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { tax_rates, count, isPending, isError, error } = useTaxRates(\n    {\n      ...searchParams,\n      tax_region_id: taxRegion.id,\n      is_default: false\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const { table } = useTaxOverrideTable({\n    count,\n    data: tax_rates,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(Container2, { className: \"p-0\", children: /* @__PURE__ */ jsx2(\n    TaxOverrideTable,\n    {\n      isPending,\n      table,\n      count,\n      action: {\n        label: t(\"actions.create\"),\n        to: \"overrides/create\"\n      },\n      queryObject: raw,\n      prefix: PREFIX,\n      children: /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"taxRegions.taxOverrides.header\") })\n    }\n  ) });\n};\n\n// src/routes/tax-regions/tax-region-province-detail/tax-region-detail.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar TaxRegionDetail = () => {\n  const { province_id } = useParams();\n  const initialData = useLoaderData();\n  const {\n    tax_region: taxRegion,\n    isLoading,\n    isError,\n    error\n  } = useTaxRegion(province_id, void 0, { initialData });\n  const { getWidgets } = useExtension();\n  if (isLoading || !taxRegion) {\n    return /* @__PURE__ */ jsx3(SingleColumnPageSkeleton, { sections: 2, showJSON: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(\n    SingleColumnPage,\n    {\n      data: taxRegion,\n      showJSON: true,\n      widgets: {\n        after: getWidgets(\"tax.details.after\"),\n        before: getWidgets(\"tax.details.before\")\n      },\n      children: [\n        /* @__PURE__ */ jsx3(TaxRegionProvinceDetailSection, { taxRegion }),\n        /* @__PURE__ */ jsx3(TaxRegionProvinceOverrideSection, { taxRegion })\n      ]\n    }\n  );\n};\n\n// src/routes/tax-regions/tax-region-province-detail/breadcrumb.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar TaxRegionDetailBreadcrumb = (props) => {\n  const { province_id } = props.params || {};\n  const { tax_region } = useTaxRegion(province_id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(province_id)\n  });\n  if (!tax_region) {\n    return null;\n  }\n  const countryCode = tax_region.country_code?.toUpperCase();\n  const provinceCode = tax_region.province_code?.toUpperCase();\n  const isValid = isProvinceInCountry(countryCode, provinceCode);\n  return /* @__PURE__ */ jsx4(\"span\", { children: isValid ? getProvinceByIso2(provinceCode) : provinceCode });\n};\n\n// src/routes/tax-regions/tax-region-province-detail/loader.ts\nvar taxRegionDetailQuery = (id) => ({\n  queryKey: taxRegionsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.taxRegion.retrieve(id)\n});\nvar taxRegionLoader = async ({ params }) => {\n  const id = params.province_id;\n  const query = taxRegionDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  TaxRegionDetailBreadcrumb as Breadcrumb,\n  TaxRegionDetail as Component,\n  TaxRegionDetail,\n  taxRegionLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA,yBAA0B;AA2B1B,IAAAA,sBAA4B;AAgD5B,IAAAA,sBAA2C;AAmC3C,IAAAA,sBAA4B;AA7G5B,IAAI,iCAAiC,CAAC;AAAA,EACpC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,UAAU,UAAU,OAAO,CAAC,MAAM,EAAE,eAAe,IAAI;AAC5E,QAAM,WAAW,aAAa,WAAW;AACzC,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO,gBAA4B,wBAAI,SAAS,EAAE,SAAS,EAAE,yCAAyC,GAAG,cAA0B,wBAAI,OAAO,EAAE,OAAO,UAAU,MAAM,WAAW,WAAW,kBAAkB,UAAU,EAAE,uCAAuC,EAAE,CAAC,EAAE,CAAC;AAAA,MAC1Q;AAAA,IACF;AAAA,IACA,aAAa,IAAI,CAAC,SAAS;AACzB,iBAAuB,wBAAI,aAAa,EAAE,SAAS,MAAM,mBAAmB,KAAK,GAAG,KAAK,EAAE;AAAA,IAC7F,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AAOA,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,mCAAmC,CAAC;AAAA,EACtC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,WAAW,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,MACE,GAAG;AAAA,MACH,eAAe,UAAU;AAAA,MACzB,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,EAAE,MAAM,IAAI,oBAAoB;AAAA,IACpC;AAAA,IACA,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,WAAY,EAAE,WAAW,OAAO,cAA0B,oBAAAA;AAAA,IACpF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,EAAE,gBAAgB;AAAA,QACzB,IAAI;AAAA,MACN;AAAA,MACA,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,cAA0B,oBAAAA,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,gCAAgC,EAAE,CAAC;AAAA,IACxG;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,YAAY,IAAI,UAAU;AAClC,QAAM,cAAc,cAAc;AAClC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,aAAa,aAAa,QAAQ,EAAE,YAAY,CAAC;AACrD,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,WAAW;AAC3B,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,KAAK,CAAC;AAAA,EACvF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,QACP,OAAO,WAAW,mBAAmB;AAAA,QACrC,QAAQ,WAAW,oBAAoB;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,YACQ,oBAAAD,KAAK,gCAAgC,EAAE,UAAU,CAAC;AAAA,YAClD,oBAAAA,KAAK,kCAAkC,EAAE,UAAU,CAAC;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,4BAA4B,CAAC,UAAU;AAjM3C;AAkME,QAAM,EAAE,YAAY,IAAI,MAAM,UAAU,CAAC;AACzC,QAAM,EAAE,WAAW,IAAI,aAAa,aAAa,QAAQ;AAAA,IACvD,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,WAAW;AAAA,EAC9B,CAAC;AACD,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,QAAM,eAAc,gBAAW,iBAAX,mBAAyB;AAC7C,QAAM,gBAAe,gBAAW,kBAAX,mBAA0B;AAC/C,QAAM,UAAU,oBAAoB,aAAa,YAAY;AAC7D,aAAuB,oBAAAE,KAAK,QAAQ,EAAE,UAAU,UAAU,kBAAkB,YAAY,IAAI,aAAa,CAAC;AAC5G;AAGA,IAAI,uBAAuB,CAAC,QAAQ;AAAA,EAClC,UAAU,oBAAoB,OAAO,EAAE;AAAA,EACvC,SAAS,YAAY,IAAI,MAAM,UAAU,SAAS,EAAE;AACtD;AACA,IAAI,kBAAkB,OAAO,EAAE,OAAO,MAAM;AAC1C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,qBAAqB,EAAE;AACrC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "jsx2", "jsx3", "jsxs2", "jsx4"]}