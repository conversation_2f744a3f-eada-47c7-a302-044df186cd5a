import {
  useQueryParams
} from "./chunk-32T72GVU.js";

// node_modules/@medusajs/dashboard/dist/chunk-FHSC5X62.mjs
var useUserInviteTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "order", "created_at", "updated_at"],
    prefix
  );
  const { offset, created_at, updated_at, q, order } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    order,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};

export {
  useUserInviteTableQuery
};
//# sourceMappingURL=chunk-AYBSQXJR.js.map
