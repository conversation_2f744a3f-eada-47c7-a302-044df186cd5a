{"version": 3, "sources": ["../../@medusajs/dashboard/dist/campaign-configuration-QV3OHWBZ.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-ZCO6EK4W.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCampaign,\n  useUpdateCampaign\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/campaigns/campaign-configuration/campaign-configuration.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/campaigns/campaign-configuration/components/campaign-configuration-form/campaign-configuration-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { <PERSON><PERSON>, DatePicker, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CampaignConfigurationSchema = z.object({\n  starts_at: z.date().nullable(),\n  ends_at: z.date().nullable()\n});\nvar CampaignConfigurationForm = ({\n  campaign\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      starts_at: campaign.starts_at ? new Date(campaign.starts_at) : void 0,\n      ends_at: campaign.ends_at ? new Date(campaign.ends_at) : void 0\n    },\n    resolver: zodResolver(CampaignConfigurationSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateCampaign(campaign.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        starts_at: data.starts_at || null,\n        ends_at: data.ends_at || null\n      },\n      {\n        onSuccess: ({ campaign: campaign2 }) => {\n          toast.success(\n            t(\"campaigns.configuration.edit.successToast\", {\n              name: campaign2.name\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"starts_at\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"campaigns.fields.start_date\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                DatePicker,\n                {\n                  granularity: \"minute\",\n                  hourCycle: 12,\n                  shouldCloseOnSelect: false,\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"ends_at\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"campaigns.fields.end_date\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                DatePicker,\n                {\n                  granularity: \"minute\",\n                  shouldCloseOnSelect: false,\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(\n        Button,\n        {\n          isLoading: isPending,\n          type: \"submit\",\n          variant: \"primary\",\n          size: \"small\",\n          children: t(\"actions.save\")\n        }\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/campaigns/campaign-configuration/campaign-configuration.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CampaignConfiguration = () => {\n  const { t } = useTranslation2();\n  const { id } = useParams();\n  const { campaign, isLoading, isError, error } = useCampaign(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"campaigns.configuration.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"campaigns.configuration.edit.description\") })\n    ] }),\n    !isLoading && campaign && /* @__PURE__ */ jsx2(CampaignConfigurationForm, { campaign })\n  ] });\n};\nexport {\n  CampaignConfiguration as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,yBAA0B;AAsG1B,IAAAA,sBAA2C;AArG3C,IAAI,8BAA8B,EAAE,OAAO;AAAA,EACzC,WAAW,EAAE,KAAK,EAAE,SAAS;AAAA,EAC7B,SAAS,EAAE,KAAK,EAAE,SAAS;AAC7B,CAAC;AACD,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,WAAW,SAAS,YAAY,IAAI,KAAK,SAAS,SAAS,IAAI;AAAA,MAC/D,SAAS,SAAS,UAAU,IAAI,KAAK,SAAS,OAAO,IAAI;AAAA,IAC3D;AAAA,IACA,UAAU,EAAY,2BAA2B;AAAA,EACnD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,kBAAkB,SAAS,EAAE;AAChE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,WAAW,KAAK,aAAa;AAAA,QAC7B,SAAS,KAAK,WAAW;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,UAAU,UAAU,MAAM;AACtC,gBAAM;AAAA,YACJA,GAAE,6CAA6C;AAAA,cAC7C,MAAM,UAAU;AAAA,YAClB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,6BAA6B,EAAE,CAAC;AAAA,kBAC9D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,qBAAqB;AAAA,kBACrB,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,kBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,aAAa;AAAA,kBACb,qBAAqB;AAAA,kBACrB,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAUA,GAAE,cAAc;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI,YAAY,EAAE;AAC9D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,qCAAqC,EAAE,CAAC,EAAE,CAAC;AAAA,UAC1I,oBAAAE,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUF,GAAE,0CAA0C,EAAE,CAAC;AAAA,IACjI,EAAE,CAAC;AAAA,IACH,CAAC,aAAa,gBAA4B,oBAAAE,KAAK,2BAA2B,EAAE,SAAS,CAAC;AAAA,EACxF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}