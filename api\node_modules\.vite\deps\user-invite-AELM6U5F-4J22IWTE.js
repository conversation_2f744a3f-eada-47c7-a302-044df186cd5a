import {
  useUserInviteTableQuery
} from "./chunk-AYBSQXJR.js";
import {
  require_copy_to_clipboard
} from "./chunk-TWHCESJX.js";
import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  RouteFocusModal
} from "./chunk-XMQMXYDG.js";
import {
  isFetchError
} from "./chunk-XBF43SLF.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import {
  format
} from "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateInvite,
  useDeleteInvite,
  useInvites,
  useResendInvite
} from "./chunk-6Q7JSKEY.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  __toESM as __toESM2
} from "./chunk-XMKQFEQ4.js";
import "./chunk-T7YBVUWZ.js";
import {
  Alert,
  ArrowPath,
  Button,
  Container,
  Heading,
  Input,
  Link,
  StatusBadge,
  Text,
  Tooltip,
  Trash,
  createColumnHelper,
  usePrompt
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/user-invite-AELM6U5F.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_copy_to_clipboard = __toESM2(require_copy_to_clipboard());
var InviteUserSchema = objectType({
  email: stringType().email()
});
var PAGE_SIZE = 10;
var PREFIX = "usr_invite";
var INVITE_URL = `${window.location.origin}${__BASE__ === "/" ? "" : __BASE__}/invite?token=`;
var InviteUserForm = () => {
  const { t: t2 } = useTranslation();
  const form = useForm({
    defaultValues: {
      email: ""
    },
    resolver: t(InviteUserSchema)
  });
  const { raw, searchParams } = useUserInviteTableQuery({
    prefix: PREFIX,
    pageSize: PAGE_SIZE
  });
  const {
    invites,
    count,
    isPending: isLoading,
    isError,
    error
  } = useInvites(searchParams);
  const columns = useColumns();
  const { table } = useDataTable({
    data: invites ?? [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { mutateAsync, isPending } = useCreateInvite();
  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await mutateAsync({ email: values.email });
      form.reset();
    } catch (error2) {
      if (isFetchError(error2) && error2.status === 400) {
        form.setError("root", {
          type: "manual",
          message: error2.message
        });
        return;
      }
    }
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { children: t2("users.inviteUser") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("users.inviteUserHint") })
          ] }),
          form.formState.errors.root && (0, import_jsx_runtime.jsx)(
            Alert,
            {
              variant: "error",
              dismissible: false,
              className: "text-balance",
              children: form.formState.errors.root.message
            }
          ),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2 gap-4", children: (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "email",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.email") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ) }),
            (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime.jsx)(
              Button,
              {
                size: "small",
                variant: "secondary",
                type: "submit",
                isLoading: isPending,
                children: t2("users.sendInvite")
              }
            ) })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t2("users.pendingInvites") }),
            (0, import_jsx_runtime.jsx)(Container, { className: "overflow-hidden p-0", children: (0, import_jsx_runtime.jsx)(
              _DataTable,
              {
                table,
                columns,
                count,
                pageSize: PAGE_SIZE,
                pagination: true,
                search: "autofocus",
                isLoading,
                queryObject: raw,
                prefix: PREFIX,
                orderBy: [
                  { key: "email", label: t2("fields.email") },
                  { key: "created_at", label: t2("fields.createdAt") },
                  { key: "updated_at", label: t2("fields.updatedAt") }
                ]
              }
            ) })
          ] })
        ] }) }) })
      ]
    }
  ) });
};
var InviteActions = ({ invite }) => {
  const { mutateAsync: revokeAsync } = useDeleteInvite(invite.id);
  const { mutateAsync: resendAsync } = useResendInvite(invite.id);
  const prompt = usePrompt();
  const { t: t2 } = useTranslation();
  const handleDelete = async () => {
    const res = await prompt({
      title: t2("general.areYouSure"),
      description: t2("users.deleteInviteWarning", {
        email: invite.email
      }),
      cancelText: t2("actions.cancel"),
      confirmText: t2("actions.delete")
    });
    if (!res) {
      return;
    }
    await revokeAsync();
  };
  const handleResend = async () => {
    await resendAsync();
  };
  const handleCopyInviteLink = () => {
    const inviteUrl = `${INVITE_URL}${invite.token}`;
    (0, import_copy_to_clipboard.default)(inviteUrl);
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(ArrowPath, {}),
              label: t2("users.resendInvite"),
              onClick: handleResend
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Link, {}),
              label: t2("users.copyInviteLink"),
              onClick: handleCopyInviteLink
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t2("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const { t: t2 } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("email", {
        header: t2("fields.email"),
        cell: ({ getValue }) => {
          return getValue();
        }
      }),
      columnHelper.accessor("accepted", {
        header: t2("fields.status"),
        cell: ({ getValue, row }) => {
          const accepted = getValue();
          const expired = new Date(row.original.expires_at) < /* @__PURE__ */ new Date();
          if (accepted) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("users.acceptedOnDate", {
                  date: format(
                    new Date(row.original.updated_at),
                    "dd MMM, yyyy"
                  )
                }),
                children: (0, import_jsx_runtime.jsx)(StatusBadge, { color: "green", children: t2("users.inviteStatus.accepted") })
              }
            );
          }
          if (expired) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("users.expiredOnDate", {
                  date: format(
                    new Date(row.original.expires_at),
                    "dd MMM, yyyy"
                  )
                }),
                children: (0, import_jsx_runtime.jsx)(StatusBadge, { color: "red", children: t2("users.inviteStatus.expired") })
              }
            );
          }
          return (0, import_jsx_runtime.jsx)(
            Tooltip,
            {
              content: (0, import_jsx_runtime.jsx)(
                Trans,
                {
                  i18nKey: "users.validFromUntil",
                  components: [
                    (0, import_jsx_runtime.jsx)("span", { className: "font-medium" }, "from"),
                    (0, import_jsx_runtime.jsx)("span", { className: "font-medium" }, "untill")
                  ],
                  values: {
                    from: format(
                      new Date(row.original.created_at),
                      "dd MMM, yyyy"
                    ),
                    until: format(
                      new Date(row.original.expires_at),
                      "dd MMM, yyyy"
                    )
                  }
                }
              ),
              children: (0, import_jsx_runtime.jsx)(StatusBadge, { color: "orange", children: t2("users.inviteStatus.pending") })
            }
          );
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime.jsx)(InviteActions, { invite: row.original })
      })
    ],
    [t2]
  );
};
var UserInvite = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(InviteUserForm, {}) });
};
export {
  UserInvite as Component
};
//# sourceMappingURL=user-invite-AELM6U5F-4J22IWTE.js.map
