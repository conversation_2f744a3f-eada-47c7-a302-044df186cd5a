import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Badge,
  Tooltip,
  clx
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-BKJC5BGQ.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var BadgeListSummary = ({
  list,
  className,
  inline,
  rounded = false,
  n = 2
}) => {
  const { t } = useTranslation();
  const title = t("general.plusCount", {
    count: list.length - n
  });
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "text-ui-fg-subtle txt-compact-small gap-x-2 overflow-hidden",
        {
          "inline-flex": inline,
          flex: !inline
        },
        className
      ),
      children: [
        list.slice(0, n).map((item) => {
          return (0, import_jsx_runtime.jsx)(Badge, { rounded: rounded ? "full" : "base", size: "2xsmall", children: item }, item);
        }),
        list.length > n && (0, import_jsx_runtime.jsx)("div", { className: "whitespace-nowrap", children: (0, import_jsx_runtime.jsx)(
          Tooltip,
          {
            content: (0, import_jsx_runtime.jsx)("ul", { children: list.slice(n).map((c) => (0, import_jsx_runtime.jsx)("li", { children: c }, c)) }),
            children: (0, import_jsx_runtime.jsx)(
              Badge,
              {
                rounded: rounded ? "full" : "base",
                size: "2xsmall",
                className: "cursor-default whitespace-nowrap",
                children: title
              }
            )
          }
        ) })
      ]
    }
  );
};

export {
  BadgeListSummary
};
//# sourceMappingURL=chunk-MDQQ3RPH.js.map
