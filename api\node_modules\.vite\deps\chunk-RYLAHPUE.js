import {
  Text<PERSON><PERSON>,
  TextHeader
} from "./chunk-7HUCBNCQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-ZJRFL6ZN.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useCustomerGroupTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.name") }),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: getValue() || "-" })
      }),
      columnHelper.accessor("customers", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("customers.domain") }),
        cell: ({ getValue }) => {
          var _a;
          const count = ((_a = getValue()) == null ? void 0 : _a.length) ?? 0;
          return (0, import_jsx_runtime.jsx)(TextCell, { text: count });
        }
      })
    ],
    [t]
  );
};

export {
  useCustomerGroupTableColumns
};
//# sourceMappingURL=chunk-RYLAHPUE.js.map
