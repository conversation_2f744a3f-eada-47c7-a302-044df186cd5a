{"version": 3, "sources": ["../../@medusajs/dashboard/dist/sales-channel-list-WMLELPSX.mjs"], "sourcesContent": ["import {\n  useSalesChannelTableColumns,\n  useSalesChannelTableEmptyState,\n  useSalesChannelTableFilters,\n  useSalesChannelTableQuery\n} from \"./chunk-44QN6VEG.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-4BTG27L5.mjs\";\nimport {\n  DataTable\n} from \"./chunk-3IIOXMXN.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-Q5PHSNDY.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-R2NIHOCX.mjs\";\nimport \"./chunk-CLRTJ6DI.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-UAZEQNCO.mjs\";\nimport \"./chunk-LDJKJLBJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-4GJJIXM6.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-RWU2ZKWZ.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport {\n  useDeleteSalesChannelLazy,\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-GH77ZQI2.mjs\";\n\n// src/routes/sales-channels/sales-channel-list/components/sales-channel-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport {\n  Container,\n  createDataTableColumnHelper,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useCallback, useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar SalesChannelListTable = () => {\n  const { t } = useTranslation();\n  const { store } = useStore();\n  const searchParams = useSalesChannelTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { sales_channels, count, isPending, isError, error } = useSalesChannels(\n    searchParams,\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useSalesChannelTableFilters();\n  const emptyState = useSalesChannelTableEmptyState();\n  const sales_channels_data = sales_channels?.map((sales_channel) => {\n    return {\n      ...sales_channel,\n      is_default: store?.default_sales_channel_id === sales_channel.id\n    };\n  }) ?? [];\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(Container, { className: \"p-0\", children: /* @__PURE__ */ jsx(\n    DataTable,\n    {\n      data: sales_channels_data,\n      columns,\n      rowCount: count,\n      getRowId: (row) => row.id,\n      pageSize: PAGE_SIZE,\n      filters,\n      isLoading: isPending,\n      emptyState,\n      heading: t(\"salesChannels.domain\"),\n      subHeading: t(\"salesChannels.subtitle\"),\n      action: {\n        label: t(\"actions.create\"),\n        to: \"/settings/sales-channels/create\"\n      },\n      rowHref: (row) => `/settings/sales-channels/${row.id}`\n    }\n  ) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const base = useSalesChannelTableColumns();\n  const { mutateAsync } = useDeleteSalesChannelLazy();\n  const handleDelete = useCallback(\n    async (salesChannel) => {\n      const confirm = await prompt({\n        title: t(\"general.areYouSure\"),\n        description: t(\"salesChannels.deleteSalesChannelWarning\", {\n          name: salesChannel.name\n        }),\n        verificationInstruction: t(\"general.typeToConfirm\"),\n        verificationText: salesChannel.name,\n        confirmText: t(\"actions.delete\"),\n        cancelText: t(\"actions.cancel\")\n      });\n      if (!confirm) {\n        return;\n      }\n      await mutateAsync(salesChannel.id, {\n        onSuccess: () => {\n          toast.success(t(\"salesChannels.toast.delete\"));\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      });\n    },\n    [t, prompt, mutateAsync]\n  );\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.action({\n        actions: (ctx) => {\n          const disabledTooltip = ctx.row.original.is_default ? t(\"salesChannels.tooltip.cannotDeleteDefault\") : void 0;\n          return [\n            [\n              {\n                icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n                label: t(\"actions.edit\"),\n                onClick: () => navigate(\n                  `/settings/sales-channels/${ctx.row.original.id}/edit`\n                )\n              }\n            ],\n            [\n              {\n                icon: /* @__PURE__ */ jsx(Trash, {}),\n                label: t(\"actions.delete\"),\n                onClick: () => handleDelete(ctx.row.original),\n                disabled: ctx.row.original.is_default,\n                disabledTooltip\n              }\n            ]\n          ];\n        }\n      })\n    ],\n    [base, handleDelete, navigate, t]\n  );\n};\n\n// src/routes/sales-channels/sales-channel-list/sales-channel-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar SalesChannelList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"sales_channel.list.before\"),\n        after: getWidgets(\"sales_channel.list.after\")\n      },\n      hasOutlet: true,\n      children: /* @__PURE__ */ jsx2(SalesChannelListTable, {})\n    }\n  );\n};\nexport {\n  SalesChannelList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,mBAAqC;AAGrC,yBAAoB;AAkHpB,IAAAA,sBAA4B;AAjH5B,IAAI,YAAY;AAChB,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,MAAM,IAAI,SAAS;AAC3B,QAAM,eAAe,0BAA0B;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,gBAAgB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC3D;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,4BAA4B;AAC5C,QAAM,aAAa,+BAA+B;AAClD,QAAM,uBAAsB,iDAAgB,IAAI,CAAC,kBAAkB;AACjE,WAAO;AAAA,MACL,GAAG;AAAA,MACH,aAAY,+BAAO,8BAA6B,cAAc;AAAA,IAChE;AAAA,EACF,OAAM,CAAC;AACP,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,WAAW,EAAE,WAAW,OAAO,cAA0B;AAAA,IAClF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,MACV,UAAU,CAAC,QAAQ,IAAI;AAAA,MACvB,UAAU;AAAA,MACV;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA,SAAS,EAAE,sBAAsB;AAAA,MACjC,YAAY,EAAE,wBAAwB;AAAA,MACtC,QAAQ;AAAA,QACN,OAAO,EAAE,gBAAgB;AAAA,QACzB,IAAI;AAAA,MACN;AAAA,MACA,SAAS,CAAC,QAAQ,4BAA4B,IAAI,EAAE;AAAA,IACtD;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,OAAO,4BAA4B;AACzC,QAAM,EAAE,YAAY,IAAI,0BAA0B;AAClD,QAAM,mBAAe;AAAA,IACnB,OAAO,iBAAiB;AACtB,YAAM,UAAU,MAAM,OAAO;AAAA,QAC3B,OAAO,EAAE,oBAAoB;AAAA,QAC7B,aAAa,EAAE,2CAA2C;AAAA,UACxD,MAAM,aAAa;AAAA,QACrB,CAAC;AAAA,QACD,yBAAyB,EAAE,uBAAuB;AAAA,QAClD,kBAAkB,aAAa;AAAA,QAC/B,aAAa,EAAE,gBAAgB;AAAA,QAC/B,YAAY,EAAE,gBAAgB;AAAA,MAChC,CAAC;AACD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,YAAM,YAAY,aAAa,IAAI;AAAA,QACjC,WAAW,MAAM;AACf,gBAAM,QAAQ,EAAE,4BAA4B,CAAC;AAAA,QAC/C;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,GAAG,QAAQ,WAAW;AAAA,EACzB;AACA,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,OAAO;AAAA,QAClB,SAAS,CAAC,QAAQ;AAChB,gBAAM,kBAAkB,IAAI,IAAI,SAAS,aAAa,EAAE,2CAA2C,IAAI;AACvG,iBAAO;AAAA,YACL;AAAA,cACE;AAAA,gBACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,gBAC1C,OAAO,EAAE,cAAc;AAAA,gBACvB,SAAS,MAAM;AAAA,kBACb,4BAA4B,IAAI,IAAI,SAAS,EAAE;AAAA,gBACjD;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE;AAAA,gBACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,gBACnC,OAAO,EAAE,gBAAgB;AAAA,gBACzB,SAAS,MAAM,aAAa,IAAI,IAAI,QAAQ;AAAA,gBAC5C,UAAU,IAAI,IAAI,SAAS;AAAA,gBAC3B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,MAAM,cAAc,UAAU,CAAC;AAAA,EAClC;AACF;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,2BAA2B;AAAA,QAC9C,OAAO,WAAW,0BAA0B;AAAA,MAC9C;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,uBAAuB,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}