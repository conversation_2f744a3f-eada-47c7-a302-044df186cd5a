import {
  CountrySelect
} from "./chunk-U63OHBJU.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-XMQMXYDG.js";
import "./chunk-XGFC5LFP.js";
import {
  t
} from "./chunk-GYCHI7LC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-DPO7J5IQ.js";
import {
  useCreateCustomerAddress
} from "./chunk-FSXJE4G7.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-create-address-P4YQFUI5.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateCustomerAddressSchema = objectType({
  address_name: stringType().min(1),
  address_1: stringType().min(1),
  address_2: stringType().optional(),
  country_code: stringType().min(2).max(2),
  city: stringType().optional(),
  postal_code: stringType().optional(),
  province: stringType().optional(),
  company: stringType().optional(),
  phone: stringType().optional()
});
var CreateCustomerAddressForm = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      address_name: "",
      address_1: "",
      address_2: "",
      city: "",
      company: "",
      country_code: "",
      phone: "",
      postal_code: "",
      province: ""
    },
    resolver: t(CreateCustomerAddressSchema)
  });
  const { mutateAsync, isPending } = useCreateCustomerAddress(id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        address_name: values.address_name,
        address_1: values.address_1,
        address_2: values.address_2,
        country_code: values.country_code,
        city: values.city,
        postal_code: values.postal_code,
        province: values.province,
        company: values.company,
        phone: values.phone
      },
      {
        onSuccess: () => {
          toast.success(t2("customers.addresses.create.successToast"));
          handleSuccess(`/customers/${id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("customers.addresses.create.header") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("customers.addresses.create.hint") })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2 gap-4", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "address_name",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("customers.addresses.fields.addressName") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      size: "small",
                      autoComplete: "address_name",
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address_1",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.address") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        size: "small",
                        autoComplete: "address_1",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address_2",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.address2") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        size: "small",
                        autoComplete: "address_2",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "postal_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.postalCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        size: "small",
                        autoComplete: "postal_code",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "city",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.city") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", autoComplete: "city", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "country_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.country") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      CountrySelect,
                      {
                        autoComplete: "country_code",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "province",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.state") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        size: "small",
                        autoComplete: "province",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "company",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.company") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        size: "small",
                        autoComplete: "company",
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "phone",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.phone") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", autoComplete: "phone", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var CustomerCreateAddress = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateCustomerAddressForm, {}) });
};
export {
  CustomerCreateAddress as Component
};
//# sourceMappingURL=customer-create-address-P4YQFUI5-MLFXDHTX.js.map
