import {
  Text<PERSON><PERSON>,
  TextHeader
} from "./chunk-7HUCBNCQ.js";
import {
  useDeleteProductCategoryAction
} from "./chunk-TM7HU4GE.js";
import {
  getCategoryPath,
  getIsActiveProps,
  getIsInternalProps
} from "./chunk-RGG2AVZE.js";
import {
  StatusCell
} from "./chunk-U7GMH3JP.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-K2WOPNBK.js";
import "./chunk-733UOLIB.js";
import "./chunk-CZLMELES.js";
import "./chunk-QL4D35SJ.js";
import {
  SingleColumnPage
} from "./chunk-C4NOUVUU.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-PPSLIQFU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-AGRADJYQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-TP2BI5T3.js";
import {
  ActionMenu
} from "./chunk-EUY4KBIF.js";
import "./chunk-KJC3C3MI.js";
import "./chunk-26EN2TVL.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-VHD2ND4K.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useProductCategories
} from "./chunk-ZJNBJBHK.js";
import "./chunk-FBCJANGI.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-GHF5EYBG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-XMKQFEQ4.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  IconButton,
  PencilSquare,
  Text,
  Trash,
  TriangleRightMini,
  clx,
  createColumnHelper
} from "./chunk-RJPG7Y6U.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-WYTGSSQF.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/category-list-VKQRZO2Z.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useCategoryTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react2.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.name") }),
        cell: ({ getValue, row }) => {
          const expandHandler = row.getToggleExpandedHandler();
          if (row.original.parent_category !== void 0) {
            const path = getCategoryPath(row.original);
            return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center gap-1 overflow-hidden", children: path.map((chip, index) => (0, import_jsx_runtime.jsxs)(
              "div",
              {
                className: clx("overflow-hidden", {
                  "text-ui-fg-muted flex items-center gap-x-1": index !== path.length - 1
                }),
                children: [
                  (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", className: "truncate", children: chip.name }),
                  index !== path.length - 1 && (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "/" })
                ]
              },
              chip.id
            )) });
          }
          return (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full items-center gap-x-3 overflow-hidden", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "flex size-7 items-center justify-center", children: row.getCanExpand() ? (0, import_jsx_runtime.jsx)(
              IconButton,
              {
                type: "button",
                onClick: (e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  expandHandler();
                },
                size: "small",
                variant: "transparent",
                className: "text-ui-fg-subtle",
                children: (0, import_jsx_runtime.jsx)(
                  TriangleRightMini,
                  {
                    className: clx({
                      "rotate-90 transition-transform will-change-transform": row.getIsExpanded()
                    })
                  }
                )
              }
            ) : null }),
            (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: getValue() })
          ] });
        }
      }),
      columnHelper.accessor("handle", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.handle") }),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime.jsx)(TextCell, { text: `/${getValue()}` });
        }
      }),
      columnHelper.accessor("is_active", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("fields.status") }),
        cell: ({ getValue }) => {
          const { color, label } = getIsActiveProps(getValue(), t);
          return (0, import_jsx_runtime.jsx)(StatusCell, { color, children: label });
        }
      }),
      columnHelper.accessor("is_internal", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: t("categories.fields.visibility.label") }),
        cell: ({ getValue }) => {
          const { color, label } = getIsInternalProps(getValue(), t);
          return (0, import_jsx_runtime.jsx)(StatusCell, { color, children: label });
        }
      })
    ],
    [t]
  );
};
var useCategoryTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(["q", "offset", "order"], prefix);
  const searchParams = {
    q: raw.q,
    limit: pageSize,
    offset: raw.offset ? Number(raw.offset) : 0,
    order: raw.order
  };
  return {
    raw,
    searchParams
  };
};
var PAGE_SIZE = 20;
var CategoryListTable = () => {
  const { t } = useTranslation();
  const { raw, searchParams } = useCategoryTableQuery({ pageSize: PAGE_SIZE });
  const query = raw.q ? {
    include_ancestors_tree: true,
    fields: "id,name,handle,is_active,is_internal,parent_category",
    ...searchParams
  } : {
    include_descendants_tree: true,
    parent_category_id: "null",
    fields: "id,name,category_children,handle,is_internal,is_active",
    ...searchParams
  };
  const { product_categories, count, isLoading, isError, error } = useProductCategories(
    {
      ...query
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const { table } = useDataTable({
    data: product_categories || [],
    columns,
    count,
    getRowId: (original) => original.id,
    getSubRows: (original) => original.category_children,
    enableExpandableRows: true,
    pageSize: PAGE_SIZE
  });
  const showRankingAction = !!product_categories && product_categories.length > 0;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: t("categories.domain") }),
        (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("categories.subtitle") })
      ] }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        showRankingAction && (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "organize", children: t("categories.organize.action") }) }),
        (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "create", children: t("actions.create") }) })
      ] })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        columns,
        count,
        pageSize: PAGE_SIZE,
        isLoading,
        navigateTo: (row) => row.id,
        queryObject: raw,
        search: true,
        pagination: true
      }
    )
  ] });
};
var CategoryRowActions = ({
  category
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductCategoryAction(category);
  return (0, import_jsx_runtime2.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
              to: `${category.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper2 = createColumnHelper();
var useColumns = () => {
  const base = useCategoryTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper2.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(CategoryRowActions, { category: row.original });
        }
      })
    ],
    [base]
  );
};
var CategoryList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_category.list.after"),
        before: getWidgets("product_category.list.before")
      },
      hasOutlet: true,
      children: (0, import_jsx_runtime3.jsx)(CategoryListTable, {})
    }
  );
};
export {
  CategoryList as Component
};
//# sourceMappingURL=category-list-VKQRZO2Z-Y6K3QNGS.js.map
